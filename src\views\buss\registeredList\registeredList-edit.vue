<!-- 应用编辑弹窗 -->
<template>
  <div>
    <!-- 新增 -->
    <a-modal width="80%" :maskClosable="false" :visible="visible" :confirm-loading="loading" :forceRender="true"
      title="报废设备库清单" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible" centered="true">
      <div>
        <div style="display: flex;">
          <!-- <div style="margin-right: 10px;">
            <a-button class="filter-button" style="display: flex;align-items: center;justify-content: space-between;" @click="isShowSearch = !isShowSearch">
             <i class="iconfont icon-search" style="margin-right: 6px;"></i>
            筛选
          </a-button>
          </div> -->
          <div>
            <a-input v-model:value="queryParams.searchText" placeholder="请输入设备编号" :style="{ width: '240px' }"
              @keyup.enter="handleSearch">
              <template #suffix>
                <search-outlined class="search-icon" @click="handleSearch" />
              </template>
            </a-input>
          </div>
        </div>
        <!-- 搜索工具栏 -->
        <!-- <div class="search-form" v-if="isShowSearch">
          <div class="search-row">
            <div class="search-row1">
              <div class="search-item">
                <span class="label">设备分类:</span>
                <a-tree-select v-model:value="sblxList" show-search
                  :dropdown-style="{ maxHeight: '440px', overflow: 'auto' }" placeholder="请选择" allow-clear multiple
                  :show-checked-strategy="SHOW_ALL" :tree-data="equipmentTypeList"
                  :field-names="{ label: 'name', value: 'id' }" @select="selectSelect" @change="changeSelect"
                  tree-node-filter-prop="name" max-tag-count="responsive" style="width: 260px;"></a-tree-select>
              </div>
              <div class="search-item">
                <span class="label">存放地点:</span>
                <a-select v-model:value="queryParams.storageLocationStr" placeholder="全部" style="width: 240px">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option v-for="item in cfddList" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </div>
              <div class="search-item">
                <span class="label">设备状态:</span>
                <a-select v-model:value="queryParams.equCondition" placeholder="全部" style="width: 100px">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option v-for="item in equConditionOptions" :key="item.label" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </div>
            </div>

            <div>
              <a-button type="primary" class="search-button" @click="handleSearch">查询</a-button>
              <a-button class="reset-button" @click="handleReset" style="margin-left: 20px;">重置</a-button>
            </div>

          </div>
        </div> -->
        <a-table :columns="columns" :data-source="tableData" @change="handleTableChange" :pagination="false"
          :rowKey="(record) => record.equId" :scroll="{ x: 'max-content' }" :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange, onSelect: onSelects, onSelectAll: onSelectAll
          }" class="custom-table">
          <template #emptyText>
            <div class="custom-empty">
              <img src="@/assets/images/noData.png" />
              <!-- <p>抱歉，暂时还没有数据</p> -->
            </div>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleView(record)">查看</a>
                <!-- <a @click="handleEdit(record)">编辑</a> -->
              </a-space>
            </template>
          </template>
        </a-table>
        <div class="table-footer" :class="{ 'follow-page': isShowSearch }">
          <div class="total-info">
            已选{{ selectedRowKeys.length }}数据，共{{ pagination.total }}条数据
          </div>
          <a-pagination v-model:current="pagination.current" :total="pagination.total" :pageSize="pagination.pageSize"
            @change="handleTableChange" :showSizeChanger="false" />
        </div>
      </div>
      <template #footer>
        <a-button key="back" @click="updateVisible(false)">取消</a-button>
        <a-button key="submit" type="primary" :loading="loading" @click="handleOk">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { SysAppApi } from '@/api/system/app/SysAppApi';
import { equipmentScrappingApi } from '@/api/buss/equipmentScrappingApi';
import { BasicInformationApi } from '@/api/buss/BasicInformationApi';
import { UnitApi } from '@/api/common/UnitApi'
import { EnumApi } from '@/api/common/enum';

export default {
  name: 'deviceListEdit',
  components: {},
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Array
  },
  emits: ['done', 'update:visible', 'getList'],
  data() {
    return {
      // 表单数据
      list: this.data,
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      isShowSearch: false,
      queryParams: {
        searchText: null, //搜索内容
        equCondition: null,
        storageLocationStr: null,
        equType: null,//设备类别
        equSubType: null,//设备种类
        equModel: null,//规格型号
        equName: null,//设备名称
        isLock: false,
        pageSize: 10,
        pageNo: 1
      },
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共 ${total} 条`
      },
      arr: [],
      arr1: [],
      columns: [
        // 左侧固定列
        {
          title: '序号', dataIndex: 'serialNo', width: 60,
          customRender: ({ text, record, index }) => {
            return `${(this.pagination.current - 1) * this.pagination.pageSize + index + 1}`;
          }
        },
        { title: '设备编号', dataIndex: 'code', width: 120 },
        { title: '设备名称', dataIndex: 'equNameStr', width: 120 },
        { title: '设备类别/种类', dataIndex: 'equSubTypeStr', width: 120 },
        { title: '规格型号', dataIndex: 'equModelStr', width: 100, },
        { title: '存放地点', dataIndex: 'storageLocationStr', width: 120 },
        { title: '设备状态', dataIndex: 'equConditionStr', width: 120 },
        { title: '管理状态', dataIndex: 'managementStatusStr' },

        // { title: '测试', dataIndex: 'equModelInfo', width: 120 },
      ],
      tableData: [],
      selectedRowKeys: [], //保存的选择的keyId列表
      selectedRows: [], //保存的所有选择数据列表
      selectedRowKeys1: [],
      selectedRows1: [],
      selects: [],
      equipmentTypeList: [],
      cfddList: [],
      equConditionOptions: [],
      sblxList: [],
      sList: [],
      arrList: [],
      arrList1: [],
    };
  },
  // mounted() {
  //   this.init();
  // },
  // watch: {
  //   data() {
  //     this.init();
  //   }
  // },
  created() {
    this.fetchData()
    // this.getAddrOrgList()
    const dataArr = JSON.parse(JSON.stringify(this.data))
    if (dataArr?.length > 0) {
      this.selectedRows = dataArr
      for (let i = 0; i < dataArr.length; i++) {
        if (dataArr[i].typeObj == '2') {
          // this.selectedRows.push(dataArr[i])
          this.selectedRowKeys.push(dataArr[i].equId)
        }
      }
    }
  },
  methods: {
    // 获取列表数据
    fetchData() {
      this.queryParams.pageNo = this.pagination.current
      this.queryParams.pageSize = this.pagination.pageSize
      try {
        const res = equipmentScrappingApi.getRegistered(this.queryParams)
        res.then(res => {
          console.log('res--------', res)
          console.log('this.datasss', this.data)
          this.tableData = res.rows
          for (let i = 0; i < this.tableData.length; i++) {
            this.tableData[i].equId = this.tableData[i].id
            this.tableData[i].typeObj = '2'
            delete this.tableData[i].id
          }
          console.log('this.tableData--------', this.tableData)
          this.pagination = {
            ...this.pagination,
            total: res.totalRows || 0
          };


          console.log('this.selectedRowKeys :', this.selectedRowKeys);
          console.log('this.selectedRows :', this.selectedRows);
        })
      } catch (error) {
        console.error('获取数据失败:', error);
      }
    },

    selectSelect(value, node, extra) {
      this.sList.push({
        value: value,
        level: node.level
      })
    },
    changeSelect(value, node, extra) {
      this.arrList = []
      this.queryParams.equType = []
      this.queryParams.equSubType = []
      this.queryParams.equModel = []
      this.queryParams.equName = []
      this.$nextTick(() => {
        for (let i = 0; i < value.length; i++) {
          for (let s = 0; s < this.sList.length; s++) {
            if (this.sList[s].value == value[i]) {
              this.arrList.push({
                value: value[i],
                level: this.sList[s].level
              })
            }
          }

        }
        this.arrList1 = this.getArray(this.arrList)
        console.log('this.arrList', this.getArray(this.arrList));

      })
    },

    getArray(selectData) {
      let map = new Map()
      for (let item of selectData) {
        if (!map.has(item.value)) {
          map.set(item.value, item)
        }
      }
      const list = [...map.values()]
      console.log(list, '数组去重后')
      return list
    },

    handleSearch() {
      this.pagination.current = 1;
      this.queryParams.pageNo = this.pagination.current;
      this.queryParams.pageSize = this.pagination.pageSize;
      const arr = []
      const arr1 = []
      const arr2 = []
      const arr3 = []
      console.log('this.arrList', this.arrList);

      for (let i = 0; i < this.arrList.length; i++) {
        if (this.arrList[i].level == '1') {
          arr.push(this.arrList[i].value)
        }
        if (this.arrList[i].level == '2') {
          arr1.push(this.arrList[i].value)
        }
        if (this.arrList[i].level == '3') {
          arr2.push(this.arrList[i].value)
        }
        if (this.arrList[i].level == '4') {
          arr3.push(this.arrList[i].value)
        }
      }
      this.queryParams.equType = arr.join(',')
      this.queryParams.equSubType = arr1.join(',')
      this.queryParams.equName = arr2.join(',')
      this.queryParams.equModel = arr3.join(',')
      this.fetchData()
    },

    handleReset() {
      this.pagination.current = 1
      this.sblxList = []
      this.sList = []
      this.arrList = []
      this.arrList1 = []
      this.queryParams = {
        searchText: null, //搜索内容
        equCondition: null,
        storageLocationStr: null,
        equType: null,//设备类别
        equSubType: null,//设备种类
        equModel: null,//规格型号
        equName: null,//设备名称
        pageSize: 10,
        pageNo: 1
      }
      this.fetchData()
    },

    handleTableChange(current) {
      console.log('this.selectedRows', this.selectedRows)
      console.log('this.selectedRowKeys', this.selectedRowKeys)
      this.pagination.current = current
      this.queryParams.pageNo = current
      this.queryParams.pageSize = this.pagination.pageSize
      this.fetchData();
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      // for (let val of selectedRows) {
      //   if (!this.selectedRows.find((item) => item.equId === val.equId)) {
      //     this.selectedRows.push(val);
      //   }
      // }
      // this.selectedRowKeys = selectedRowKeys
      // this.selectedRows = selectedRows
      // this.selectedRows = this.selectedRows.filter((item) =>
      //   this.selectedRowKeys.includes(item.equId)
      // );
    },
    onSelects(record, selected, selectedRowsData, nativeEvent) {
      // 选择
      if (selected) {
        this.selectedRowKeys.push(record.equId)
        this.selectedRows.push(record)
      } else {
        // 取消选中
        this.selectedRowKeys = this.selectedRowKeys.filter((v) => v !== record.equId)
        this.selectedRows.splice(
          this.selectedRows.findIndex((x) => x.equId === record.equId), 1)
      }
      this.getArray(this.selectedRows)
    },

    onSelectAll(selected, selectedRows, changeRows) {
      // 全选
      if (selected) {
        changeRows.map((x) => {
          this.selectedRowKeys.push(x.equId) //选中id
        })
        this.selectedRows = this.selectedRows.concat(changeRows)
      } else {
        // 取消全选
        changeRows.forEach((item) => {
          // 去掉选择取消的keyID
          this.selectedRowKeys = this.selectedRowKeys.filter((v) => v !== item.equId)
        })
        this.selectedRows = this.selectedRows.filter((x) => !changeRows.find((i) => i.equId === x.equId))
      }
      this.getArray(this.selectedRows)
    },

    getArray(selectData) {
      let map = new Map()
      for (let item of selectData) {
        if (!map.has(item.equId)) {
          map.set(item.equId, item)
        }
      }
      this.selectedRows = [...map.values()]
      console.log(this.selectedRows, '数组去重后')
    },
    /**
     * 保存和编辑
     *
     * <AUTHOR>
     * @date 2021/4/7 11:00
     */
    handleOk() {
      // 校验表单
      this.selects = this.selectedRows
      this.$emit('getList1', this.selects)
      this.$emit('done');
      this.updateVisible(false);
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2021/4/7 11:00
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style lang="less" scoped>
.search-form {

  padding: 16px 16px; // 统一内边距
  border-radius: 8px; // 增加圆角

  .search-row1 {
    display: flex;
    width: 80%;
    justify-content: space-between;
  }

  .search-row {
    display: flex;
    flex-wrap: wrap;
    gap: 24px; // 增加间距
    margin-bottom: 12px; // 增加行间距
    justify-content: space-between;

    &:last-child {
      margin-bottom: 0;
    }

    .search-item {
      display: flex;
      align-items: center;
      // min-width: 300px;
      // flex: 1;

      .label {
        min-width: 80px;
        // margin-right: 12px; // 增加标签和输入框的间距
        color: #666;
        font-size: 14px;
      }

      :deep(.ant-select),
      :deep(.ant-input) {
        width: 64%;
        height: 32px;

        .ant-select-selector {
          background: #fff; // 确保选择器背景为白色
          border-radius: 4px;
        }
      }

      :deep(.ant-input) {
        background: #fff; // 确保输入框背景为白色
        border-radius: 4px;
      }
    }

    .search-button,
    .reset-button {
      height: 32px;
      min-width: 80px;
      margin-left: auto;
      border-radius: 4px; // 统一按钮圆角
    }

    .search-button {
      background: #1890ff; // 查询按钮使用主题蓝色
    }

    .reset-button {
      background: #fff; // 重置按钮使用白色背景
      border: 1px solid #d9d9d9;
    }
  }
}

.custom-table {
  margin-top: 16px;

  :deep(.ant-table) {

    // 提高固定列的层级
    .ant-table-fixed-left,
    .ant-table-fixed-right {
      background: #fff;
      z-index: 3; // 增加层级
    }

    .ant-table-cell {
      white-space: nowrap !important; // 强制不换行
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      line-height: 1 !important;
      font-size: 14px !important;

      >span,
      >div {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }
    }

    // 大屏幕样式（默认）
    @media screen and (min-width: 1920px) {
      thead .ant-table-cell {
        padding: 20px 20px !important;
        height: 60px !important;
      }

      .ant-table-row {
        height: 60px !important;
      }
    }

    // 中等屏幕样式
    @media screen and (min-width: 1366px) and (max-width: 1919px) {
      thead .ant-table-cell {
        padding: 10px 20px !important;
        height: 40px !important;
      }

      .ant-table-row {
        height: 40px !important;
      }
    }

    // 小屏幕样式
    @media screen and (max-width: 1365px) {
      thead .ant-table-cell {
        padding: 4px 8px !important;
        height: 32px !important;
      }

      .ant-table-row {
        height: 32px !important;
      }
    }

    // 调整固定列单元格样式
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      z-index: 3 !important; // 增加层级
      background: #ECF4FE !important;
    }

    // 调整表头固定列样式
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right {
        z-index: 4 !important; // 确保表头在最上层
        background: #DAECFF !important;
      }
    }

    // 优化阴影效果
    .ant-table-fixed-right::before,
    .ant-table-fixed-left::before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      width: 10px;
      pointer-events: none;
      z-index: 2; // 阴影层级低于固定列
      transition: box-shadow .3s;
    }

    .ant-table-fixed-left::before {
      right: 0;
      box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
    }

    .ant-table-fixed-right::before {
      left: 0;
      box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
    }

    // 设置表格内容的层级
    .ant-table-content {
      z-index: 1;
    }

    // 确保滚动区域正确显示
    .ant-table-body {
      overflow-x: auto !important;
      overflow-y: auto !important;
    }

    // 固定列不换行
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .ant-table-row {
      height: 24px !important;
    }

    // 表头固定列不换行
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.table-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}

:deep(.ant-table-tbody .ant-table-cell) {
  padding: 10px 16px !important;
}
.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>
