<template>
  <div class="test-container">
    <h2>CityTreeSelect 组件测试</h2>
    
    <div class="demo-section">
      <h3>单选模式</h3>
      <city-tree-select
        v-model:value="selectedCity"
        :options="cityOptions"
        :multiple="false"
        placeholder="请选择城市"
        @change="handleCityChange"
      />
      <div class="result">
        <p>选中值: {{ selectedCity }}</p>
      </div>
    </div>
    
    <div class="demo-section">
      <h3>多选模式</h3>
      <city-tree-select
        v-model:value="selectedCities"
        :options="cityOptions"
        :multiple="true"
        placeholder="请选择多个城市"
        @change="handleMultiCityChange"
      />
      <div class="result">
        <p>选中值: {{ selectedCities }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import CityTreeSelect from '@/components/CityTreeSelect/index.vue';

// 单选模式选中值
const selectedCity = ref('');

// 多选模式选中值
const selectedCities = ref([]);

// 城市数据示例
const cityOptions = ref([
  {
    id: 'root',
    name: '中国',
    children: [
      {
        id: 'bj',
        name: '北京',
        children: [
          { id: 'bjhd', name: '海淀区' },
          { id: 'bjcy', name: '朝阳区' },
          { id: 'bjft', name: '丰台区' }
        ]
      },
      {
        id: 'sh',
        name: '上海',
        children: [
          { id: 'shpd', name: '浦东新区' },
          { id: 'shxh', name: '徐汇区' },
          { id: 'shjd', name: '静安区' }
        ]
      },
      {
        id: 'gz',
        name: '广州',
        children: [
          { id: 'gzth', name: '天河区' },
          { id: 'gzhy', name: '海珠区' },
          { id: 'gzlw', name: '荔湾区' }
        ]
      },
      {
        id: 'sz',
        name: '深圳',
        children: [
          { id: 'szft', name: '福田区' },
          { id: 'szns', name: '南山区' },
          { id: 'szlh', name: '罗湖区' }
        ]
      }
    ]
  }
]);

// 单选变化事件处理
const handleCityChange = (value, fullPath) => {
  console.log('单选城市变化:', value);
  console.log('完整路径:', fullPath);
};

// 多选变化事件处理
const handleMultiCityChange = (values, fullPaths) => {
  console.log('多选城市变化:', values);
  console.log('完整路径列表:', fullPaths);
};
</script>

<style lang="less" scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  
  h3 {
    margin-top: 0;
    margin-bottom: 16px;
  }
}

.result {
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  
  p {
    margin: 0;
  }
}
</style>