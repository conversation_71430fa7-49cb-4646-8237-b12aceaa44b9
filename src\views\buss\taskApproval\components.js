import { markRaw } from 'vue'

// 使用异步方式加载组件，但改进错误处理和加载机制
const modules = import.meta.glob('@/views/buss/taskApproval/*.vue')
const resultComps = {}

// 创建一个加载状态的Promise
const componentsLoadingPromise = Promise.all(
  Object.entries(modules).map(async ([path, importFn]) => {
    try {
      const module = await importFn()
      // 只获取文件名，不要包含路径
      const fileName = path.split('/').pop().replace('.vue', '')
      if (module.default) {
        // 使用 markRaw 防止组件被转换为响应式对象
        resultComps[fileName] = markRaw(module.default)
        console.log(`成功注册组件: ${fileName}`)
        return { success: true, fileName }
      } else {
        console.warn(`组件注册失败 - ${fileName}: module.default 不存在`)
        return { success: false, fileName, error: 'module.default 不存在' }
      }
    } catch (error) {
      console.error(`加载组件失败 - ${path}:`, error)
      return { success: false, path, error: error.message }
    }
  })
).then(results => {
  const successCount = results.filter(r => r.success).length
  const failCount = results.length - successCount
  
  console.log(`组件加载完成: 成功 ${successCount}, 失败 ${failCount}`)
  console.log('可用组件列表:', Object.keys(resultComps))
  
  return {
    components: resultComps,
    results
  }
}).catch(error => {
  console.error('组件加载过程中发生错误:', error)
  return {
    components: resultComps,
    error
  }
})

// 导出组件对象和加载Promise
export default resultComps
export { componentsLoadingPromise }



