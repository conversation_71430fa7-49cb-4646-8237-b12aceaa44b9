<template>
    <div class="ele-body">
        <div class="reg-body">
            <!-- 顶部按钮组 -->
            <div class="header-tools">
                <div class="left">
                    <a-button class="filter-button" @click="isShowSearch = !isShowSearch">
                        <template #icon>
                            <FilterOutlined />
                        </template>
                        筛选
                    </a-button>
                    <div class="search-input">
                        <a-input v-model:value="searchText" placeholder="支持设备编号/设备代码搜索" :style="{ width: '240px' }"
                            @keyup.enter="handleSearch">
                            <template #suffix>
                                <SearchOutlined class="search-icon" @click="handleSearch" />
                            </template>
                        </a-input>
                    </div>
                </div>
                <div class="right">
                    <a-radio-group v-model:value="activeTab" @change="handleTabChange" class="custom-radio-group">
                        <a-radio-button value="submitted">已提交</a-radio-button>
                        <a-radio-button value="todo">待办</a-radio-button>
                        <a-radio-button value="done">已办</a-radio-button>
                    </a-radio-group>
                </div>
            </div>

            <!-- 搜索工具栏 -->
            <div class="search-form" v-if="isShowSearch">
                <div class="search-row">
                    <div class="search-item">
                        <span class="label">申请日期:</span>
                        <a-date-picker v-model:value="queryParams.applyStartDate" placeholder="请选择日期"
                            style="width: 64%" />
                    </div>
                    <div class="search-item">
                        <span class="label">结束日期:</span>
                        <a-date-picker v-model:value="queryParams.applyEndDate" placeholder="请选择日期"
                            style="width: 64%" />
                    </div>
                    <div class="search-item">
                        <span class="label">审批状态:</span>
                        <a-select v-model:value="queryParams.processStatus" placeholder="全部">
                            <a-select-option value="">全部</a-select-option>
                            <a-select-option value="0">草稿</a-select-option>
                            <a-select-option value="1">审批中</a-select-option>
                            <a-select-option value="-1">已取消</a-select-option>
                            <a-select-option value="99">已通过</a-select-option>
                            <a-select-option value="100">未通过</a-select-option>
                        </a-select>
                    </div>
                    <a-button type="primary" class="search-button" @click="handleSearch">查询</a-button>
                    <a-button class="reset-button" @click="handleReset">重置</a-button>
                </div>
            </div>

            <!-- 数据表格 -->
            <a-table :columns="columns" :data-source="tableData" @change="handleTableChange" :pagination="false"
                :scroll="{ x: 'max-content' }" class="custom-table" style="width: 99%;">
                <template #emptyText>
          <div class="custom-empty">
            <img src="@/assets/images/noData.png"/>
            <!-- <p>抱歉，暂时还没有数据</p> -->
          </div>
        </template>
                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'action'">
                        <div class="action-column">
                            <a @click="handleView(record)">查看</a>
                        </div>
                    </template>
                </template>
            </a-table>

            <!-- 表格底部统计信息 -->
            <div class="table-footer" :class="{ 'follow-page': isShowSearch }">
                <div class="total-info">
                    已选{{ selectedRowKeys.length }}数据，共{{ pagination.total }}条数据
                </div>
                <a-pagination v-model:current="pagination.current" :total="pagination.total"
                    :pageSize="pagination.pageSize" @change="handleTableChange" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { HandleTaskApi } from '@/api/workflow/HandleTaskApi';
import { AppliedApi } from '@/api/workflow/AppliedApi';
import { EnumApi } from '@/api/common/enum';
import { useRouter } from 'vue-router';
import { FilterOutlined, SearchOutlined } from '@ant-design/icons-vue';

const router = useRouter();

// 初始化响应式状态
const activeTab = ref('submitted');
const searchText = ref('');
const isShowSearch = ref(false);
const tableData = ref([]);
const selectedRowKeys = ref([]);

// 分页配置
const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: (total) => `共 ${total} 条`
});

// 查询参数
const queryParams = ref({
    applyStartDate: '',
    applyEndDate: '',
    processStatus: '',
});

// 表格列定义
const columns = [
    {
        title: '序号',
        dataIndex: 'serialNo',
        width: 60,
        align: 'center'
    },
    {
        title: '业务标题',
        dataIndex: 'businessTitle',
        width: 160,
        ellipsis: true
    },
    {
        title: '申请设备台数',
        dataIndex: 'equipmentCount',
        width: 100,
        align: 'center'
    },
    {
        title: '处理状态',
        dataIndex: 'processStatus',
        width: 80,
        align: 'center'
    },
    {
        title: '申请日期',
        dataIndex: 'applyDate',
        width: 100,
        align: 'center'
    },
    {
        title: '结束日期',
        dataIndex: 'endDate',
        width: 100,
        align: 'center'
    },
    {
        title: '',  // 空白分隔列
        dataIndex: 'spacer',
        width: 250,
        align: 'center'
    },
    {
        title: '操作',
        key: 'action',
        width: 140,
        align: 'center',
        fixed: 'right'
    }
];

// 添加状态选项的响应式变量
const statusOptions = ref([]);

// 初始化枚举数据
const initEnumData = async () => {
    try {
        // 获取审批状态枚举
        const statusData = await EnumApi.getEnumList({ enumName: 'WorkSheetStatusEnum' });
        statusOptions.value = statusData.data;
    } catch (error) {
        message.error('获取枚举数据失败');
    }
};

// 获取列表数据
const fetchData = async () => {
    const params = {
        pageNo: pagination.value.current,
        pageSize: pagination.value.pageSize,
        keyword: searchText.value,
        key: "equipment_acceptance_batch,equipment_acceptance,equipment_acceptance_edit",
        ...queryParams.value
    }; key:bussWorksheet.value.type

    try {
        let response;
        switch (activeTab.value) {
            case 'todo':
                response = await HandleTaskApi.todoTaskPage(params);
                break;
            case 'submitted':
                response = await AppliedApi.findPage(params);
                break;
            case 'done':
                response = await HandleTaskApi.doneTaskPage(params);
                break;
        }

        if (response?.rows) {
            tableData.value = response.rows.map((item, index) => {
                const worksheet = item.worksheet || {};
                return {
                    serialNo: (pagination.value.current - 1) * pagination.value.pageSize + index + 1,
                    businessTitle: worksheet.name || item.name,
                    equipmentCount: worksheet.applyCount || 0,
                    processStatus: formatProcessStatus(worksheet.status),
                    applyDate: formatDate(worksheet.applyStartDate),
                    endDate: formatDate(worksheet.applyEndDate),
                    ...item
                };
            });

            pagination.value = {
                ...pagination.value,
                total: response.totalRows || 0
            };
        }
    } catch (error) {
        console.error('获取数据失败:', error);
    }
};

// 格式化处理状态
const formatProcessStatus = (status) => {
    const statusItem = statusOptions.value.find(item => item.value === status);
    return statusItem ? statusItem.label : status;
};

// 格式化日期
const formatDate = (date) => {
    if (!date) return '';
    if (typeof date === 'string' && date.startsWith('SQ')) {
        return date;
    }
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
};

// 事件处理函数
const handleTabChange = () => {
    tableData.value = [];
    pagination.value.current = 1;
    fetchData();
};

const handleSearch = () => {
    // 重置分页到第一页
    pagination.value.current = 1;
    // 构建查询参数，包含分页信息
    const params = {
        pageNo: pagination.value.current,
        pageSize: pagination.value.pageSize,
        keyword: searchText.value,
        ...queryParams.value,
        key: "equipment_acceptance_batch,equipment_acceptance,equipment_acceptance_edit"
    };
    // 调用查询接口
    fetchData(params);
};

const handleReset = () => {
    searchText.value = '';
    queryParams.value = {
        applyStartDate: '',
        applyEndDate: '',
        processStatus: '',
    };
    pagination.value.current = 1;
    fetchData();
};

const handleTableChange = (current) => {
    pagination.value.current = current;
    const params = {
        pageNo: current,
        pageSize: pagination.value.pageSize,
        keyword: searchText.value,
        ...queryParams.value,
        key: "equipment_acceptance_batch,equipment_acceptance,equipment_acceptance_edit"

    };
    fetchData();
};

const onSelectChange = (keys) => {
    selectedRowKeys.value = keys;
};

const handleView = (record) => {
    const worksheet = record.worksheet || {}; // 获取工作单信息

    switch (activeTab.value) {
        case 'todo':
            router.push({
                path: '/buss/todo',
                query: {
                    id: record.procIns.id,
                    taskId: record.id,
                    processDefinitionId: record.procIns.procDef.id,
                    actId: record.activityId,
                    procInsId: record.procIns.id,
                    worksheetId: worksheet.id, // 传递工作单ID到待办页面
                }
            });
            break;
        case 'submitted':
            router.push({
                path: '/buss/completed',
                query: {
                    id: record.worksheet.procInstanceId,
                    taskId: record.id,
                    processDefinitionId: record.procDef.id,
                    actId: record.activityId,
                    procInsId: record.worksheet.procInstanceId,
                    worksheetId: worksheet.id, // 传递工作单ID到待办页面
                }
            });
            break;
        case 'done':
            router.push({
                path: '/buss/done',
                query: {
                    id: record.procIns.id,
                    taskId: record.id,
                    processDefinitionId: record.procIns.procDef.id,
                    actId: record.activityId,
                    procInsId: record.procIns.id,
                    worksheetId: worksheet.id, // 传递工作单ID到待办页面
                }
            });
            break;
    }

};

const handleEdit = (record) => {
    router.push({
        path: '/buss/edit',
        query: {
            id: record.id,
            type: activeTab.value
        }
    });
};

// 生命周期钩子
onMounted(() => {
    initEnumData();
    fetchData();
});
</script>

<style lang="less" scoped>
.reg-body {
    height: 100%;
    overflow: auto;
    background: rgba(255, 255, 255, 0.4);
    box-sizing: border-box;
    border: 1px solid #FFFFFF;
    border-radius: 6px;
    backdrop-filter: blur(398px);
    box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);
    padding: 16px 16px;
    width: 100%;
    max-width: 1920px;
    margin: 0 auto;

    .header-tools {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 16px;

        .left {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;

            .filter-button {
                min-width: 80px;
                height: 32px;
                border-radius: 7px;
            }

            .search-input {
                width: clamp(280px, 20vw, 320px);

                :deep(.ant-input) {
                    width: 100%;
                    height: 24px;

                }

                .search-icon {
                    cursor: pointer;
                }
            }
        }

        .right {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;

            .tool-button {
                height: 32px;
                border-radius: 7px;
            }

            .link-button {
                height: 32px;
                padding: 0 8px;
            }
        }
    }

    .search-form {

        padding: 16px 16px; // 统一内边距
        border-radius: 8px; // 增加圆角


        .search-row {
            display: flex;
            flex-wrap: wrap;
            gap: 24px; // 增加间距
            margin-bottom: 12px; // 增加行间距

            &:last-child {
                margin-bottom: 0;
            }

            .search-item {
                display: flex;
                align-items: center;
                min-width: 300px;
                flex: 1;

                .label {
                    min-width: 80px;
                    margin-right: 12px; // 增加标签和输入框的间距
                    color: #666;
                    font-size: 14px;
                }

                :deep(.ant-select),
                :deep(.ant-input) {
                    width: 64%;
                    height: 32px;

                    .ant-select-selector {
                        background: #fff; // 确保选择器背景为白色
                        border-radius: 4px;
                    }
                }

                :deep(.ant-input) {
                    background: #fff; // 确保输入框背景为白色
                    border-radius: 4px;
                }
            }

            .search-button,
            .reset-button {
                height: 32px;
                min-width: 80px;
                margin-left: auto;
                border-radius: 4px; // 统一按钮圆角
            }

            .search-button {
                background: #1890ff; // 查询按钮使用主题蓝色
            }

            .reset-button {
                background: #fff; // 重置按钮使用白色背景
                border: 1px solid #d9d9d9;
            }
        }
    }

    .custom-radio-group .ant-radio-button-wrapper-checked {
        background: #FFFFFF !important;
        border-color: #1890ff !important;
        border-radius: 2px !important;
    }

    .custom-radio-group[data-v-7c8a2acd] .ant-radio-button-wrapper {
        background: #E0E9F4;
    }

    :deep(.ant-table) {
        .ant-table-body {
            overflow-x: auto;
            overflow-y: auto;
        }

        .ant-table-fixed-left,
        .ant-table-fixed-right {
            background: #fff;
            box-shadow: none; // 移除原有阴影
            z-index: 2; // 提高固定列的层级
        }

        // 确保表格内容正确显示
        .ant-table-content {
            z-index: 0;
        }



        // 修复固定列单元格层级
        .ant-table-cell-fix-left,
        .ant-table-cell-fix-right {
            z-index: 2 !important;
            background: #fff !important;
        }

        // 修复表头固定列层级
        .ant-table-thead th.ant-table-cell-fix-left,
        .ant-table-thead th.ant-table-cell-fix-right {
            z-index: 3 !important;
        }
    }

    .table-footer {
        position: fixed; // 默认固定定位
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        width: calc(100% - 32px);
        max-width: 1888px;
        margin: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 16px;
        padding: 16px 24px;
        border-radius: 8px;

        z-index: -100;

        .total-info {
            color: #666;
            font-size: 14px;
        }

        // 当筛选展开时的样式
        &.follow-page {
            position: static; // 改为静态定位
            transform: none;
            width: 100%;
            margin-top: 16px;
        }
    }


    .custom-table {
        margin-top: 16px;

        :deep(.ant-table) {

            // 提高固定列的层级
            .ant-table-fixed-left,
            .ant-table-fixed-right {
                background: #fff;
                z-index: 3; // 增加层级
            }

            .ant-table-cell {
                white-space: nowrap !important; // 强制不换行
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                line-height: 1 !important;
                font-size: 14px !important;

                >span,
                >div {
                    white-space: nowrap !important;
                    overflow: hidden !important;
                    text-overflow: ellipsis !important;
                }
            }

            // 大屏幕样式（默认）
            @media screen and (min-width: 1920px) {
                .ant-table-cell {
                    padding: 20px 20px !important;
                    height: 60px !important;
                }

                .ant-table-row {
                    height: 60px !important;
                }
            }

            // 中等屏幕样式
            @media screen and (min-width: 1366px) and (max-width: 1919px) {
                .ant-table-cell {
                    padding: 10px 20px !important;
                    height: 40px !important;
                }

                .ant-table-row {
                    height: 40px !important;
                }
            }

            // 小屏幕样式
            @media screen and (max-width: 1365px) {
                .ant-table-cell {
                    padding: 4px 8px !important;
                    height: 32px !important;
                }

                .ant-table-row {
                    height: 32px !important;
                }
            }

            // 调整固定列单元格样式
            .ant-table-cell-fix-left,
            .ant-table-cell-fix-right {
                z-index: 3 !important; // 增加层级
                background: #ECF4FE !important;
            }

            // 调整表头固定列样式
            .ant-table-thead {

                th.ant-table-cell-fix-left,
                th.ant-table-cell-fix-right {
                    z-index: 4 !important; // 确保表头在最上层
                    background: #DAECFF !important;
                }
            }

            // 优化阴影效果
            .ant-table-fixed-right::before,
            .ant-table-fixed-left::before {
                content: '';
                position: absolute;
                top: 0;
                bottom: 0;
                width: 10px;
                pointer-events: none;
                z-index: 2; // 阴影层级低于固定列
                transition: box-shadow .3s;
            }

            .ant-table-fixed-left::before {
                right: 0;
                box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
            }

            .ant-table-fixed-right::before {
                left: 0;
                box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
            }

            // 设置表格内容的层级
            .ant-table-content {
                z-index: 1;
            }

            // 确保滚动区域正确显示
            .ant-table-body {
                overflow-x: auto !important;
                overflow-y: auto !important;
            }

            // 固定列不换行
            .ant-table-cell-fix-left,
            .ant-table-cell-fix-right {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .ant-table-row {
                height: 24px !important;
            }

            // 表头固定列不换行
            .ant-table-thead {

                th.ant-table-cell-fix-left,
                th.ant-table-cell-fix-right {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
    }
}

// 响应式布局
@media screen and (max-width: 1600px) {
    .reg-body {
        .search-form {
            .search-row {
                .search-item {
                    min-width: calc(32.33% - 40px);
                }
            }
        }
    }
}

@media screen and (max-width: 1366px) {
    .reg-body {
        .search-form {
            .search-row {
                .search-item {
                    min-width: calc(50% - 16px);
                }
            }
        }
    }
}

@media screen and (max-width: 1024px) {
    .reg-body {
        .header-tools {

            .left,
            .right {
                width: 100%;
                justify-content: space-between;
            }
        }

        .search-form {
            .search-row {
                .search-item {
                    min-width: 100%;
                }
            }
        }

        .table-footer {
            flex-direction: column;
            text-align: center;

            .total-info {
                width: 100%;
            }
        }
    }
}

// 表格响应式
:deep(.ant-table) {
    .ant-table-content {
        overflow-x: auto;
    }

    @media screen and (max-width: 1024px) {
        .ant-table-cell {
            white-space: nowrap;
        }
    }
}

// 固定列样式
:deep(.ant-table) {
    .ant-table-body {
        overflow-x: auto;
        overflow-y: auto;
    }

    .ant-table-fixed-left,
    .ant-table-fixed-right {
        background: #fff;
        box-shadow: none; // 移除原有阴影
        z-index: 2; // 提高固定列的层级
    }

    // 确保表格内容正确显示
    .ant-table-content {
        z-index: 0;
    }

    // 修复固定列单元格层级
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
        z-index: 2 !important;
        background: #fff !important;
    }

    // 修复表头固定列层级
    .ant-table-thead th.ant-table-cell-fix-left,
    .ant-table-thead th.ant-table-cell-fix-right {
        z-index: 3 !important;
    }
}

.custom-radio-group {
    :deep(.ant-radio-button-wrapper) {
        background: transparent;
        border: none;
        color: #666;
        padding: 0 16px;
        height: 32px;
        line-height: 32px;

        &-checked {
            background: #E2F0FF;
            color: #1890ff;
            border-radius: 4px;
        }

        &:hover {
            color: #1890ff;
        }

        &::before {
            display: none;
        }
    }
}

// 表格容器样式
.custom-table {
    margin: 0;
    width: 100% !important;
    overflow-x: auto; // 添加横向滚动

    :deep(.ant-table) {
        max-width: calc(100vw - 32px); // 减去左右padding

        // 表头最后一列（操作列）样式
        .ant-table-thead>tr>th:last-child {
            .ant-table-column-title {
                text-align: center; // 表头文字居中
                margin-right: 50px;
            }
        }

        // 表格内容最后一列（操作列）样式
        .ant-table-tbody>tr>td:last-child {
            text-align: center; // 单元格内容居中
            padding-right: 50px !important;
        }
    }

    // 操作按钮容器样式
    .action-column {
        display: flex;
        justify-content: center; // 操作按钮居中
        align-items: center; // 垂直居中
        margin-right: -30px;

        a {
            margin: 0 8px; // 按钮两侧间距改为均等
        }
    }
}

// 调整列宽和间距
:deep(.ant-table) {

    .ant-table-thead>tr>th,
    .ant-table-tbody>tr>td {
        &:not(:last-child) {
            max-width: 150px;
        }
    }
}

// 表格底部样式调整
.table-footer {
    width: 1200px; // 与表格同宽
    margin: 16px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

// 确保分页器也对齐
:deep(.ant-pagination) {

    margin: 16px 0;
    display: flex;
    justify-content: flex-end;
}
.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>
