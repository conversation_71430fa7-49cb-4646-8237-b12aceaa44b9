import Request from '@/utils/request-util';

/**
 * 表单管理api
 *
 * <AUTHOR>
 * @date 2021/7/5 21:14
 */
export class transferApi {
  /**
   * 分页获取列表
   *
   * <AUTHOR>
   * @date 2022/5/8 20:36
   */
  static todoTaskPage(params) {
    return Request.getAndLoadData('/flowableTodoTask/page', params);
  }

  /**
   * 分页获取列表
   *
   * <AUTHOR>
   * @date 2022/5/8 20:36
   */
  static doneTaskPage(params) {
    return Request.getAndLoadData('/flowableDoneTask/page', params);
  }

  /**
     * 分页获取列表
     *
     * <AUTHOR>
     * @date 2022/5/8 20:36
     */
  static findPage(params) {
    return Request.getAndLoadData('/flowableInstance/my', params);
  }

  static details() {
    return Request.get('/apiBus/transferTransferFormEquipment/newBuilt');
  }

  static async saveDraft(params) {
    return await Request.post('/apiBus/transferTransferFormEquipment/saveDraft', params);
  }

  static getToDo(params) {
    return Request.get('/apiBus/transferTransferFormEquipment/getWorksheetInfo', params);
  }

  static submit(params) {
    return Request.post('/flowableHandleTask/submit', params);
  }

  static start(params) {
    return Request.post('/flowableHandleTask/start', params);
  }
  static end(params) {
    return Request.post('/flowableInstance/end', params);
  }

  static getNewBuilt() {
    return Request.get('/apiBus/transferTransferFormEquipment/newBuilt');
  }

static getEquipmentByCode(params) {
    return Request.get('/apiBus/equipmentClassificationCode/detail', params);
  }

  static getTransferFormEquipmentById(params) {
    return Request.get(`/apiBus/transferTransferFormEquipment/getTransferEquipmentByBeforeTransferId`, params);
  }

  static editTransferEquipment(params) {
    return Request.post('/apiBus/transferTransferFormEquipment/editTransferEquipment', params);
  }

}
