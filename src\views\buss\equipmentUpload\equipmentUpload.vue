<template>
  <a-spin :spinning="spinning">
    <div class="ele-body">
      <div class="acceptance-form">

        <div class="form-title">设备入账验收转资单</div>
        <!-- 申请信息部分 -->
        <div class="section">
          <div class="section-title">
            <div class="blue-bar"></div>
            申请信息
          </div>

          <div class="form-grid">
            <div class="form-item">
              <div class="label">申请人</div>
              <div class="approvalFont">{{ bussTransferForm.applyUserStr }}</div>
            </div>
            <div class="form-item">
              <div class="label">申请日期</div>
              <div class="approvalFont">{{ bussTransferForm.applyDate }}</div>
            </div>
            <div class="form-item">
              <div class="label">申请单位</div>
              <div class="approvalFont">{{ bussTransferForm.applyOrgStr }}</div>
            </div>

          </div>
          <div class="form-grid">
            <div class="form-item">
              <div class="label required">业务标题</div>
              <div class="value">
                <a-input placeholder="请输入业务标题" v-model:value="businessTitle" :disabled="currentStep == 3"
                  maxlength="16" />
              </div>
            </div>
          </div>
        </div>

        <div class="section">
          <div class="section-title1">
            <div class="blue-bar"></div>
            设备信息
          </div>

          <!-- 步骤指示器 -->
          <div class="steps">
            <div class="step" :class="{ 'step-done': currentStep > 1, 'step-current': currentStep === 1 }">
              <div class="step-circle">
                <img v-if="currentStep > 1" src="@/assets/equipment/iconSuccess.png" class="success-step-icon"
                  alt="success" />
                <span v-else>1</span>
              </div>
              <div class="step-content">
                <div class="step-title">上传文件</div>
                <div class="step-desc">请将Excel文件导入设备信息</div>
              </div>
            </div>
            <div class="step" :class="{ 'step-done': currentStep > 2, 'step-current': currentStep === 2 }">
              <div class="step-circle">
                <img v-if="currentStep > 2" src="@/assets/equipment/iconSuccess.png" class="success-step-icon"
                  alt="success" />
                <span v-else>2</span>
              </div>
              <div class="step-content">
                <div class="step-title">数据校验</div>
                <div class="step-desc">请核对上传设备信息是否正确</div>
              </div>
            </div>
            <div class="step" :class="{ 'step-current': currentStep === 3 }">
              <div class="step-circle">
                <span>3</span>
              </div>
              <div class="step-content">
                <div class="step-title">导入数据</div>
                <div class="step-desc">将文件中的设备数据导入系统</div>
              </div>
            </div>
          </div>

          <!-- 下载提示 -->
          <div class="download-hint" v-if="!tips && errorCount === 0 && successCount === 0 && currentStep == 1">
            <div class="left">
              <!--<FileExcelOutlined class="check-icon" /> -->
              <img src="/public/icon/office-excel.svg" style="width:16px;height: 17px;margin-right: 6px;">
              <span>请下载并使用「标准模板」填充相关数据;上传时，请确保表头未被修改，以避免上传失败</span>
            </div>
            <a href="/在籍设备导入模板.xls" download class="download-btn">
              <DownloadOutlined />
              「标准模板」下载
            </a>
          </div>

          <!-- 第一种错误提示 - Excel表头不一致 -->
          <div class="error-message" v-if="tips && errorCount === 0 && successCount === 0"
            style="background-color: #FFF2F0; margin: 0 auto 24px auto; max-width: 1200px; padding: 8px 16px; border-radius: 2px; display: flex; justify-content: space-between; align-items: center;">
            <div style="display: flex; align-items: center;">
              <ExclamationCircleOutlined style="color: #FF4D4F; margin-right: 8px;" />
              <span>{{ importMsg }}</span>
            </div>
            <a href="/在籍设备导入模板.xls" download class="download-btn">
              <DownloadOutlined />
              「标准模板」下载
            </a>
          </div>

          <!-- 第二种错误提示 - 数据校验失败 -->
          <div class="error-message" v-if="!tips && errorCount > 0"
            style="background-color: #FFF2F0; margin: 0 auto 24px auto; max-width: 1200px; padding: 8px 16px; border-radius: 2px; display: flex; justify-content: space-between; align-items: center;">
            <div style="display: flex; align-items: center;">
              <ExclamationCircleOutlined style="color: #FF4D4F; margin-right: 8px;" />
              <span>{{ successCount }}条数据校验成功，{{ errorCount }}条数据校验失败</span>
            </div>
            <a-button type="link" style="color: #1890FF;" @click="downloadErrorTemplate">
              下载错误填报文件
            </a-button>
          </div>
          <div class="upload-result" v-if="currentStep == 2" style="max-width: 1200px;margin: 0 auto 0px auto;">
            <div class="success-message" v-if="successCount > 0 && errorCount === 0">
              <div class="success-header">
                <CheckCircleOutlined class="success-icon" />
                <span>数据校验成功，请核对相关数据</span>
              </div>
            </div>
          </div>

          <!-- 上传区域 -->
          <div class="upload-container">
            <!-- 上传组件 -->
            <div class="upload-area" @click="triggerFileSelect" @drop.prevent="handleFileDrop" @dragover.prevent
              v-if="currentStep == 1">
              <input type="file" ref="fileInput" style="display: none" accept=".xlsx,.xls" @change="handleFileSelect" />
              <div class="upload-content" :style="{ textAlign: isFileUploaded ? 'center' : 'center' }">
                <!-- 未上传状态 -->
                <template v-if="!isFileUploaded">
                  <img src="@/assets/equipment/uploadDone.svg" alt="上传图标" style="width: 48px;  margin-bottom: 16px;" />
                  <div class="upload-text">选择文件</div>
                  <div class="upload-desc">仅支持文件大小不超过 1MB的Excel文件，且最多 100条数据。</div>
                </template>
                <!-- 已上传状态 -->
                <template v-else>
                  <div>
                    <img src="@/assets/equipment/uploadDone.svg" alt="上传图标"
                      style="width: 48px;  margin-bottom: 16px;" />
                    <div>
                      <a-button type="primary" style="margin-bottom: 8px;">重新上传</a-button>
                    </div>

                  </div>
                  <div style="color: #999; font-size: 12px;">仅支持文件大小不超过 1MB的Excel文件，且最多 100条数据。</div>
                </template>
              </div>
            </div>

            <!-- 上传后的展示区域 -->
            <div class="upload-result" v-if="currentStep == 2">


              <!-- 成功表格 -->
              <div class="success-message" v-if="successCount > 0 && errorCount === 0">

                <a-table :columns="columns" :data-source="tableData" :pagination="false"
                  :scroll="{ x: 'max-content', y: '450px' }" class="custom-table" >
                  <template #emptyText>
                    <div class="custom-empty">
                      <img src="@/assets/images/noData.png" />
                      <!-- <p>抱歉，暂时还没有数据</p> -->
                    </div>
                  </template>
                </a-table>

                <div class="bottom-buttons">
                  <a-button class="prev-btn" @click="handlePrevStep">上一步</a-button>
                  <a-button type="primary" :loading="submitting" class="submit-btn" @click="handleSubmit">提交</a-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第三步：提交成功页面 -->
        <div class="success-page" v-if="currentStep === 3">
          <div class="success-content">
            <img src="@/assets/equipment/success.png" class="success-icon1" alt="success" />
            <h2 class="success-title">提交成功</h2>
            <p class="success-desc">设备信息上传成功，请等待审批</p>
            <a-button type="primary" @click="goToList" style="float:right;margin-top: 40px;">返回台账列表</a-button>
          </div>

        </div>


      </div>

    </div>
    <div>

    </div>
  </a-spin>
</template>

<script setup>
import { computed, ref, reactive, onMounted, h } from 'vue';
import { message } from 'ant-design-vue';
import { CheckCircleOutlined, DownloadOutlined, FolderOutlined, ExclamationCircleOutlined, InfoCircleOutlined, CheckCircleFilled } from '@ant-design/icons-vue';
import { useUserStore } from '@/store/modules/user';
import { EquipmentAcceptanceApi } from '@/api/buss/EquipmentAcceptanceApi'
import { EnumApi } from '@/api/common/enum';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';

const router = useRouter();
const spinning = ref(false)

// 获取用户信息


// 表单数据
const bussTransferForm = reactive({
  applyTitle: "",
  applyOrg: "",
  applyOrgStr: "",
  applyUser: "",
  applyUserStr: "",
  propertyOrgId: "",
  propertyOrgStr: "",
  applyDate: "",  // 确保有这个字段
});

// 步骤控制
const currentStep = ref(1);
const tableData = ref([]);
const businessTitle = ref("")

// 上传结果相关数据
const uploadErrors = ref([]);
const successCount = ref(0);
const errorCount = ref(0);


const columns = [
  // 左侧固定列
  {
    title: '序号',
    width: 60,
    fixed: 'left',
    customRender: ({ index }) => index + 1
  },
  {
    title: '设备编号',
    dataIndex: 'code',
    width: 100,
    fixed: 'left',
    ellipsis: true,
  },
  { title: '设备类别', dataIndex: 'equTypeStr', width: 120 },
  { title: '设备种类', dataIndex: 'equSubTypeStr', width: 120 },
  { title: '设备名称', dataIndex: 'equNameStr', width: 120,  ellipsis: true},
  {title: '规格型号', dataIndex: 'equModelStr', width: 120,ellipsis: true},
  { title: '型号备注', dataIndex: 'equModelInfo', width: 120 },
  { title: '设备性质', dataIndex: 'equNatureStr', width: 120 },
  { title: '购置年度', dataIndex: 'purchaseYear', width: 120 },
  { title: '设备来源', dataIndex: 'equSourceStr', width: 120 },
  { title: '单位', dataIndex: 'unit', width: 120 },
  { title: '设备合同价(含税)', dataIndex: 'equContractPriceTax', width: 150 },
  { title: '生产厂家', dataIndex: 'manufacturer', width: 160 },
  { title: '出厂编号', dataIndex: 'factoryNumber', width: 120 },
  { title: '出厂日期', dataIndex: 'productionDate', width: 120 },
  { title: '合同编号', dataIndex: 'contractNumber', width: 120 },
  { title: '验收单号', dataIndex: 'acceptanceNumber', width: 120 },
  { title: '验收日期', dataIndex: 'acceptanceDate', width: 120 },
  { title: '功率', dataIndex: 'power', width: 120 },
  { title: '设备重要性', dataIndex: 'importanceStr', width: 120 },
  { title: '设备型号编码', dataIndex: 'modelCode', width: 140 },
  { title: '使用单位', dataIndex: 'useOrgStr', width: 120 },
  { title: '使用单位代码', dataIndex: 'useOrgCode', width: 120 },
  { title: '存放地点', dataIndex: 'storageLocationStr', width: 120 },

  { title: '折旧方式', dataIndex: 'depreciationMethodStr', width: 120 },
  { title: '折旧年限', dataIndex: 'depreciationPeriod', width: 120 },
  { title: '历史原值', dataIndex: 'historicalOriginalValue', width: 120 },

  { title: '财务原值', dataIndex: 'financialOriginalValue', width: 120 },
  { title: '净值', dataIndex: 'netWorth', width: 120 },
  { title: '税率', dataIndex: 'taxRate', width: 120 },
  { title: '残值率', dataIndex: 'residualRate', width: 120 },
  { title: '财务组织', dataIndex: 'financialOrg', width: 120 },
  { title: '财务卡片编号', dataIndex: 'financialNumber', width: 130, ellipsis: true },
  { title: '固定资产分类', dataIndex: 'fixedAssetsStr', width: 120 },
];


// 下载模板
const handleDownloadTemplate11 = async () => {
  try {
    // TODO: 调用下载模板API
    // await api.downloadTemplate();
    window.location.href = '/在籍设备导入模板.xls';
  } catch (error) {
    message.error('模板下载失败');
  }
};

// 文件输入引用
const fileInput = ref(null);
const isFileUploaded = ref(false)
// 触发文件选择
const triggerFileSelect = () => {
  fileInput.value.click();
};

// 处理文件选择
const handleFileSelect = (event) => {
  const file = event.target.files[0];
  if (file) {
    validateAndUploadFile(file);
  }
  // 清空input，以便可以选择相同的文件
  event.target.value = '';
};

// 处理文件拖放
const handleFileDrop = (event) => {
  const file = event.dataTransfer.files[0];
  if (file) {
    validateAndUploadFile(file);
  }
};

// 验证并上传文件
const validateAndUploadFile = (file) => {
  // 验证文件类型
  const validTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
    'application/vnd.ms-excel', 'application/wps-office.xls' // xls
  ];
  if (!validTypes.includes(file.type)) {
    message.error('请上传Excel文件（.xlsx或.xls格式）');
    return;
  }

  // 验证文件大小（2MB）
  const maxSize = 2 * 1024 * 1024;
  if (file.size > maxSize) {
    message.error('文件大小不能超过2MB');
    return;
  }
  isFileUploaded.value = true;
  // 执行上传
  handleCustomUpload({ file });
};
// 在组件顶部声明响应式变量
const importMsg = ref(''); // 错误提示信息
const tips = ref(false);
// 自定义上传处理
const handleCustomUpload = async ({ file }) => {
  spinning.value = true
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('secretFlag', 0)

    // 调用上传API
    const res = await EquipmentAcceptanceApi.uploadExcel(formData);


    if (res.success) {
      tips.value = false;
      // 上传成功
      currentStep.value = 2;
      successCount.value = res.data.successCount;
      errorCount.value = res.data.errorCount;


      if (res.data.errorCount > 0) {
        importMsg.value = res.data.importMsg; // 保存importMsg到响应式变量
      } else {
        // 全部成功
        tableData.value = res.data.managementRegisteredEquipmentImportList
        uploadErrors.value = [];
      }
      spinning.value = false
      message.success('文件上传成功');
    } else {
      spinning.value = false
      throw new Error(res.message, { data: res.data });
    }
  } catch (error) {
    spinning.value = false
    console.log('完整响应11111：', error);
    console.error('上传错误：', error); // 添加错误日志
    message.error(error.exceptionTip);
    if (error?.code == "110002") {
      if (error.data.errorCount > 0) {
        tips.value = false;
        errorCount.value = error.data.errorCount;
        successCount.value = error.data.successCount;
        importMsg.value = error.data.importMsg; // 保存importMsg到响应式变量
      }
    } else {
      if (error?.exceptionTip) {
        tips.value = true;
        errorCount.value = 0;
        successCount.value = 0;
        importMsg.value = error.exceptionTip;
      }
    }


    //currentStep.value = 2; // 即使失败也进入第二步
    //errorCount.value = file.name ? 1 : 0; // 如果有文件名就设置错误数为1
    successCount.value = 0;
    uploadErrors.value = [error.message || '上传失败，请检查文件格式是否正确'];

  }


};

const handlePrevStep = () => {
  currentStep.value = 1;
};
const submitting = ref(false);
const processDefinitionId = ref('');

// 获取流程定义ID
const getProcessDefinitionId = async () => {
  try {
    const { data } = await EnumApi.getProcessDefinitionByKey({
      key: 'equipment_acceptance_batch'
    });
    if (data) {
      processDefinitionId.value = data;
    } else {
      message.error('获取流程定义ID失败');
    }
  } catch (error) {
    message.error('获取流程定义ID失败：' + error.message);
  }
};

// 提交
const handleSubmit = async () => {
  if (submitting.value) return; // 防止多次提交
  try {
    submitting.value = true;
    console.log(businessTitle.value)
    const innerFormData = {
      formDatas: {
        bussWorksheet: {
          name: businessTitle.value
        },
        bussTransferForm: {
          ...bussTransferForm,
          applyTitle: businessTitle.value,
        },
        bussEquipmentProcessTrackingList: tableData.value
      }
    };

    // 使用动态获取的 processDefinitionId
    const submitData = {
      processDefinitionId: processDefinitionId.value.id,
      variables: {
        formData: JSON.stringify(innerFormData)
      }
    };

    const res = await EquipmentAcceptanceApi.submit(submitData);

    if (res.success) {
      message.success('提交成功');
      currentStep.value = 3;
    } else {
      message.error(res.message || '提交失败');
    }
  } catch (error) {
    message.error(error.message.split(",")[0]);
  } finally {
    submitting.value = false;
  }
};

// 提交匹配
const handleSubmitMatch = async () => {
  try {
    // TODO: 调用匹配确认API
    // await api.confirmMatch({
    //   tableData: tableData.value,
    //   orgId: loginUser.organizationId,
    //   bussTitle: formData.bussTitle
    // });

    currentStep.value = 3;
    message.success('数据匹配成功');
  } catch (error) {
    message.error(error.message);
  }
};

// 显示审批记录
const showApprovalRecord = () => {
  // TODO: 实现审批记录显示逻辑
};

// 跳转到指定步骤
const goToStep = (step) => {
  // 只允许跳转到已完成的步骤
  if (step < currentStep.value) {
    currentStep.value = step;
  }
};

// 文件上传成功后的处理
const handleUploadSuccess = () => {
  currentStep.value = 2;
};

// 匹配提交成功后的处理
const handleMatchSuccess = () => {
  currentStep.value = 3;
};

// 简单版本的下载错误模板函数
const downloadErrorTemplate = () => {
  // 创建Blob对象
  const blob = new Blob([importMsg.value || '无错误信息'], {
    type: 'application/msword'
  });

  // 创建下载链接
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = '错误信息.doc';

  // 触发下载
  document.body.appendChild(link);
  link.click();

  // 清理
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

onMounted(async () => {
  await getProcessDefinitionId();
  const userStore = useUserStore();
  const loginUser = computed(() => userStore.info ?? {});

  // 初始化表单数据
  bussTransferForm.applyOrgStr = loginUser.value.organizationName;
  bussTransferForm.applyOrg = loginUser.value.organizationId;
  bussTransferForm.applyUser = loginUser.value.userId;
  bussTransferForm.applyUserStr = loginUser.value.realName;
  bussTransferForm.propertyOrgStr = loginUser.value.organizationName;
  bussTransferForm.propertyOrgId = loginUser.value.organizationId;

  // 设置申请日期为当前日期时间
  bussTransferForm.applyDate = dayjs().format('YYYY-MM-DD');
});

// 添加返回列表方法
const goToList = () => {
  router.push('/buss/regEquLedger');
};

</script>

<style lang="less" scoped>
.approval-record-btn {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  z-index: 100;

  .bg-image {
    display: block;
    max-width: 100%;
    height: auto;
    object-fit: contain;
    background-color: transparent;
  }

  .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .icon {
      width: 15px;
      height: 15px;
      margin-bottom: 4px;
    }

    .text {
      writing-mode: vertical-lr;
      font-size: 14px;
      color: #333;
      letter-spacing: 2px;
    }
  }
}

.acceptance-form {

  width: 100%;
  max-width: 1920px; // 最大宽度限制
  height: calc(100vh - 100px);
  overflow: auto;
  margin: 0 auto; // 居中
  padding: 24px 9.06vw 24px 8.18vw; // 转换为vw: 174/1920*100=9.06, 157/1920*100=8.18
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);

  .form-title {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 24px;
  }

  .title {
    font-size: 20px;
    color: #1e2329;
    text-align: center;
    margin-bottom: 24px;
  }

  .section {

    padding: 20px 32;


    .section-title {
      max-width: 1200px;
      margin: 0 auto 24px auto;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #333;

      .blue-bar {
        width: 4px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
      }
    }


    .section-title1 {
      max-width: 1200px;
      margin: 0 auto 0px auto;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #333;

      .blue-bar {
        width: 4px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
      }
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: clamp(16px, 2vw, 24px);
    margin: 0 auto 24px auto;
    justify-content: space-between;
    max-width: 1200px;
    width: 100%;

    .form-item {
      display: flex;
      flex-direction: column;
      min-width: 300px;
      max-width: 300px;

      .label {
        height: 32px;
        line-height: 32px;
        margin-bottom: 8px;
        color: #0C0B0B;

        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 4px;
        }
      }

      .value {
        width: 100%;
        height: 32px;
        color: #666;

        :deep(.ant-input),
        :deep(.ant-select),
        :deep(.ant-picker),
        :deep(.ant-input-number) {
          width: 100%;
          height: 32px;
        }

        :deep(.ant-input-number-input) {
          height: 16px;
          line-height: 16px;
        }

        :deep(.ant-select-selector) {
          height: 32px !important;

          .ant-select-selection-item {
            line-height: 32px;
          }
        }

        :deep(.ant-picker) {
          .ant-picker-input {
            height: 16px;
            line-height: 16px;

            input {
              height: 16px;
              line-height: 16px;
            }
          }
        }
      }
    }
  }

  .steps {
    display: flex;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto 0px auto;
    padding: 20px;
    position: relative;
    gap: 100px;

    .step {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: flex-start;
      flex: 1;
      max-width: 300px;

      .step-circle {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #D9D9D9; // 默认使用灰色背景
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        margin-right: 12px;
        transition: all 0.3s;
        border: 2px solid #D9D9D9; // 默认使用灰色边框
        flex-shrink: 0;

        .success-step-icon {
          width: 100%;
          /* 调整图片大小 */
          height: 100%;
        }
      }

      .step-content {
        text-align: left;

        .step-title {
          font-size: 16px;
          color: #333;
          margin-bottom: 4px;
        }

        .step-desc {
          font-size: 12px;
          color: #999;
          white-space: nowrap;
        }
      }

      // 当前步骤样式（蓝色）
      &.step-current {
        .step-circle {
          background: #1890ff;
          border-color: #1890ff;
          color: #fff;
        }

        .step-title {
          color: #1890ff;
        }

        .step-desc {
          color: #666;
        }
      }

      // 已完成步骤样式（蓝色带对号）
      &.step-done {
        .step-circle {
          background: rgba(24, 144, 255, 0.1); // 浅蓝色背景，使用rgba设置透明度
          border: none; // 移除边框

          i.iconfont.icon-check {
            color: #1890ff; // 蓝色对勾
            font-size: 16px;
          }
        }

        .step-title {
          color: #1890ff;
        }
      }
    }
  }

  .download-hint {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    padding: 12px 16px;
    border-radius: 4px;
    margin: 0 auto 24px auto;
    max-width: 1200px;

    .left {
      display: flex;
      align-items: center;

      .check-icon {
        color: #52c41a;
        margin-right: 8px;
      }
    }

    .download-btn {
      color: #1890ff;
      padding: 0;

      .anticon {
        margin-right: 4px;
      }
    }
  }

  .upload-container {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
  }

  .upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    padding: 24px;
    text-align: center;

    cursor: pointer;
    transition: border-color 0.3s;
    margin-top: 16px;

    &:hover {
      border-color: #1890ff;
    }

    .upload-content {
      .folder-icon {
        font-size: 48px;
        color: #1890ff;
        margin-bottom: 16px;
      }

      .upload-text {
        font-size: 16px;
        color: #1890ff;
        margin-bottom: 8px;
      }

      .upload-desc {
        color: #666;
        font-size: 14px;
      }
    }
  }

  .upload-result {
    //margin-top: 24px;

    .error-message {
      background: #fff2f0;
      border: 1px solid #ffccc7;
      border-radius: 4px;
      padding: 16px;

      .error-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .error-icon {
          color: #ff4d4f;
          font-size: 16px;
          margin-right: 8px;
        }
      }

      .error-detail {
        .error-title {
          color: #333;
          margin-bottom: 12px;

          .anticon {
            margin-right: 8px;
            color: #ff4d4f;
          }
        }

        .error-list {
          color: #666;
          padding-left: 20px;
          margin: 0;

          li {
            line-height: 24px;
          }
        }
      }
    }

    .success-message {
      .success-header {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
        padding: 16px;
        background: #f6ffed;
        border: 1px solid #b7eb8f;
        border-radius: 4px;

        .success-icon {
          color: #52c41a;
          font-size: 16px;
          margin-right: 8px;
        }

        .bottom-buttons {
          float: right;
          display: flex;
          gap: 42px;
          padding: 16px 24px;

          z-index: 100;

          .prev-btn {
            min-width: 88px;
            height: 32px;
            padding: 0 16px;
            border: 1px solid #DCDFE6;
            color: #606266;
            background: #fff;
            border-radius: 6px;

            &:hover {
              border-color: #409EFF;
              color: #409EFF;
            }
          }

          .submit-btn {
            min-width: 88px;
            height: 32px;
            padding: 0 16px;
            background: #1890FF;
            border-radius: 6px;
            border: none;

            &:hover {
              background: #40A9FF;
            }
          }
        }
      }
    }
  }
}

// 添加鼠标样式
.step {
  cursor: pointer;

  &.step-done {
    cursor: pointer;
  }
}

.success-icon1 {
  margin-bottom: 20px;
}

// 调整固定列单元格样式
:deep(.ant-table) {

  .ant-table-cell-fix-left,
  .ant-table-cell-fix-right {
    z-index: 3 !important; // 增加层级
    background: #ECF4FE !important;
  }

  // 调整表头固定列样式
  .ant-table-thead {

    th.ant-table-cell-fix-left,
    th.ant-table-cell-fix-right {
      z-index: 4 !important; // 确保表头在最上层
      background: #DAECFF !important;
    }
  }
}

.bottom-buttons {
  float: right;
  display: flex;
  gap: 42px;
  padding: 16px 24px;

  z-index: 100;

  .prev-btn {
    min-width: 88px;
    height: 32px;
    padding: 0 16px;
    border: 1px solid #DCDFE6;
    color: #606266;
    background: #fff;
    border-radius: 6px;

    &:hover {
      border-color: #409EFF;
      color: #409EFF;
    }
  }

  .submit-btn {
    min-width: 88px;
    height: 32px;
    padding: 0 16px;
    background: #1890FF;
    border-radius: 6px;
    border: none;

    &:hover {
      background: #40A9FF;
    }
  }
}

// 为了防止底部内容被固定按钮遮挡，给主内容添加底部间距
.acceptance-form {
  padding-bottom: 10px;
}

.success-page {
  display: flex;
  justify-content: center;
  align-items: center;


  margin: 10px auto;
  max-width: 1200px;
}

.success-content {
  text-align: center;
  width: 100%;
}

.return-button {
  position: absolute;
  bottom: 24px;
  right: 24px;
}

/* 如果需要调整按钮样式 */
.return-button .ant-btn-primary {
  background: #1890FF;
  border-radius: 4px;
  height: 32px;
  padding: 0 16px;
}

.success-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 16px;
}

.success-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 24px;
}

.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>
