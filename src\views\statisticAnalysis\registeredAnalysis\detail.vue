<template>
  <div class="ele-body">
    <div class="reg-body">
      <!-- 顶部按钮组 -->
      <div class="header-tools">
        <div class="left">
          <div class="fonts">
            {{ title }}
          </div>

        </div>
        <div class="right">
          <div type="link" style="display: flex;align-items: center;justify-content: space-between;">
            <span style="font-size: 14px;margin-right: 10px; color: #999;" :style="{color: flag ? '#176DF4' : '#999'}">零值隐藏</span>
            <a-switch v-model:checked="flag" @change="changeFlag"/>
          </div>
          <a-button class="tool-button" style="display: flex;align-items: center;justify-content: space-between;"
            @click="handleExport">
            <i class="iconfont icon-rongqi-copy" style="margin-right: 6px;"></i>
            导出
          </a-button>
        </div>


      </div>

      <!-- 数据表格 -->
      <a-table :columns="columns"  :data-source="tableData" :loading="loading" :expandable="expandableConfig"
        @expand="handleExpandNode" :scroll="scrolls" bordered :pagination="false" :key="refreshKey" :expandIconColumnIndex="1">
      <template #emptyText>
          <div class="custom-empty">
            <img src="@/assets/images/noData.png"/>
            <!-- <p>抱歉，暂时还没有数据</p> -->
          </div>
        </template>

        <template #summary>
          <a-table-summary-row class="fixed-summary-row">
            <template v-for="(col, idx) in columns" :key="col.dataIndex">
              <a-table-summary-cell v-if="idx == 0" :index="idx">合计</a-table-summary-cell>
              <a-table-summary-cell v-else-if="idx == 1" :index="idx">/</a-table-summary-cell>
              <a-table-summary-cell v-else-if="col.children?.length>0" v-for="(col1, idx1) in col.children" :key="col1.dataIndex"
                :index="idx1">{{ calculateColumnSum(col1.dataIndex) }}</a-table-summary-cell>
              <a-table-summary-cell v-else-if="col.dataIndex == 'intactRate'" :index="idx">{{ calculateColumnSum1(calculateColumnSum('intactValue'),calculateColumnSum('totalValue')) }}%</a-table-summary-cell>
              <a-table-summary-cell v-else-if="col.dataIndex == 'useRate'" :index="idx">{{ calculateColumnSum2(calculateColumnSum('inRentValue'),calculateColumnSum('outRentValue'),calculateColumnSum('selfUseValue'),calculateColumnSum('totalValue')) }}%</a-table-summary-cell>
              <a-table-summary-cell v-else :index="idx">{{ calculateColumnSum(col.dataIndex) }}</a-table-summary-cell>
            </template>
          </a-table-summary-row>
        </template>
      </a-table>
    </div>
  </div>

</template>
<script lang="ts">
import { defineComponent, ref, onMounted, nextTick } from 'vue';
import { registeredApi } from '@/api/analysis/registeredApi';
import { useRoute } from 'vue-router';

const scrolls = {
  x: 'max-content', y: null
}

const refreshKey = ref(0);
const title = ref()

const flag = ref(false);

const columns = [
  {
    title: '序号', dataIndex: 'serialNumber', key: 'serialNumber',
    width: 80,
    numeric: false,
  },
  {
    title: '设备类型',
    dataIndex: 'typeName',
    key: 'typeName',
    width: 200,
    numeric: false
  },
  {
    title: '合计',
    dataIndex: 'totalValue',
    key: 'totalValue',
    width: 80,
    numeric: true
  },
  {
    title: '管理状态',
    dataIndex: 'glstatus',
    key: 'glstatus',

    children: [
      {
        title: '内部租赁',
        dataIndex: 'inRentValue',
        key: 'inRentValue',
        width: 90,
        numeric: true
      },
      {
        title: '对外租赁',
        dataIndex: 'outRentValue',
        key: 'outRentValue',
        width: 90,
        numeric: true
      },
      {
        title: '自用',
        dataIndex: 'selfUseValue',
        key: 'selfUseValue',
        width: 80,
        numeric: true
      },
      {
        title: '在库',
        dataIndex: 'inWarehouseValue',
        key: 'inWarehouseValue',
        width: 80,
        numeric: true
      },
      {
        title: '代管',
        dataIndex: 'escrowValue',
        key: 'escrowValue',
        width: 80,
        numeric: true
      },
      {
        title: '封存',
        dataIndex: 'safekeepingValue',
        key: 'safekeepingValue',
        width: 80,
        numeric: true
      },
    ] // This will be populated from API response
  },
  {
    title: '设备状态',
    dataIndex: 'sbstatus',
    key: 'sbstatus',
    children: [
      {
        title: '完好',
        dataIndex: 'intactValue',
        key: 'intactValue',
        width: 80,
        numeric: true
      },
      {
        title: '待修',
        dataIndex: 'toRepairedValue',
        key: 'toRepairedValue',
        width: 80,
        numeric: true
      },
      {
        title: '待报废',
        dataIndex: 'toScrappedValue',
        key: 'toScrappedValue',
        width: 80,
        numeric: true
      },

    ] // This will be populated from API response
  },
  {
    title: '设备分布情况',
    dataIndex: 'address',
    width: 120,
    key: 'address',
    children: [] // This will be populated from API response
  },
  {
    title: '完好率', dataIndex: 'intactRate', key: 'intactRate', width: 80,
    numeric: false
  },
  {
    title: '使用率',
    dataIndex: 'useRate',
    key: 'useRate',
    width: 80,
    numeric: false
  },
];
interface DataItem {
  key: number;
  name: string;
  age: number;
  address: string;
  serialNumber: string,
  children?: DataItem[];
}



export default defineComponent({
  setup() {
    const updateTimes = ref()
    const tableData = ref<DataItem[]>([]);
    const loading = ref<boolean>(false);
    const dynamicColumns = ref(columns);
    const checkboxList = ref([])
    const router = useRoute();
    console.log('router', router.query);
    title.value = router.query.name
    // Row selection configuration
    const rowSelection = {
      onChange: (selectedRowKeys: (string | number)[], selectedRows: DataItem[]) => {
        checkboxList.value = selectedRows
        console.log('checkboxList.value', checkboxList.value);

        console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
      },
      onSelect: (record: DataItem, selected: boolean, selectedRows: DataItem[]) => {
        console.log(record, selected, selectedRows);
      },
      onSelectAll: (selected: boolean, selectedRows: DataItem[], changeRows: DataItem[]) => {
        console.log(selected, selectedRows, changeRows);
      },
    };

    // 处理节点展开的方法
    const handleExpandNode = async (expanded: boolean, record: any) => {
      console.log('Expand event:', expanded, record);

      // 只在展开父节点且没有子节点数据时请求
      if (expanded && record.hasChildren && (!record.children || record.children.length === 0)) {
        loading.value = true;
        try {
          console.log('Fetching children for:', record.key);
          // 调用API获取子节点数据
          const response = await registeredApi.getChildrenList({ parentClassificationCode: record.typeId, pLevel: record.level, isZeroShow: !flag.value });

          if (response.data) {
            // 将获取到的子节点数据添加到当前记录
            record.children = response.data.map((item: any, index: number) => ({
              key: item.serialNumber,
              ...item,
              // 明确标记哪些是父节点，需要展开时加载子节点
              hasChildren: item.children.length == 0 ? true : true

            }));

            // 强制更新表格数据
            tableData.value = [...tableData.value];
          }
          console.log('Children loaded:', response.data);
        } catch (error) {
          console.error('Failed to load children:', error);
        } finally {
          loading.value = false;
        }
      }
    };

    // 表格展开配置
    const expandableConfig = {
      // 展开/收起时的回调
      onExpand: handleExpandNode,
      // 指示节点是否可以展开
      rowExpandable: (record) => record.hasChildren === true,
      // 将展开图标放在第二列
      expandIconColumnIndex: 1
    };

    const changeFlag = (val) => {
      flag.value = val;
      fetchData();
    }

    // Fetch data from API
    const fetchData = async () => {
      loading.value = true;
      try {
        const response = await registeredApi.reportRegisteredMonth({reportSheetId:router.query.id,isZeroShow:!flag.value});
        if (response.data.updateTime) {
          updateTimes.value = response.data.updateTime
        }
        // Update the address column's children with reportTitle data
        const addressColumnIndex = dynamicColumns.value.findIndex(col => col.key === 'address');
        if (addressColumnIndex !== -1 && response.data.reportTitle) {
          scrolls.y = 'calc(100vh - 250px)'
          refreshKey.value += 1;

          dynamicColumns.value[addressColumnIndex].children = response.data.reportTitle.map((title: any, dataIndex: number) => ({
            title: title.title,
            dataIndex: title.dataIndex,
            key: title.dataIndex,
            width: 130,
          }));
        }
        if (response.data.reportTitle.length==0) {
          scrolls.y = null
          refreshKey.value += 1;
        }
        console.log('scrolls.y',scrolls);


        // Update table data with reportData
        if (response.data.reportData) {
          tableData.value = response.data.reportData.map((item: any, index: number) => ({
            key: index,
            ...item,
            // 明确标记哪些是父节点，需要展开时加载子节点
            hasChildren: item.children.length == 0 ? true : true
          }));
        }

        console.log('Data loaded successfully:', response.data);
      } catch (error) {
        console.error('Failed to fetch data:', error);
      } finally {
        loading.value = false;
      }
    };

    // 计算列合计
const calculateColumnSum = (dataIndex) => {
  return tableData.value.reduce((sum, row) => {
    const value = parseFloat(row[dataIndex]) || 0;
    return sum + value;
  }, 0);
};

const calculateColumnSum1 = (data1,data2) => {
  const data = (data1/data2*100).toFixed(2)
  if (data>0) {
    return data
  }else{
    return 100.00
  }

};

const calculateColumnSum2 = (data1,data2,data3,data4) => {
  const data = ((data1+data2+data3)/data4*100).toFixed(2)
  if (data>0) {
    return data
  }else{
    return 100.00
  }
};

    const handleExport = () => {
      // 构建导出参数，与查询参数保持一致
      console.log('rowSelection', rowSelection);
      // 直接调用导出，会打开新页面下载
      // window.open('/analysis/reportRegisteredReal/export')
      console.log('router.query.id',router.query.id);

      registeredApi.downLoad1({ reportSheetId: router.query.id, isZeroShow:(!flag.value).toString() });
    };
    console.log('router', router);
    // Load data when component mounts
    onMounted(() => {
      fetchData();

    });

    return {
      tableData,
      columns: dynamicColumns,
      rowSelection,
      loading,
      expandableConfig,
      updateTimes,
      checkboxList,
      router,
      scrolls,
      refreshKey,
      title,
      changeFlag,
      flag,
      // expandIconColumnIndex,
      calculateColumnSum,
      calculateColumnSum1,
      calculateColumnSum2,
      handleExport,
      handleExpandNode
    };
  },
});
</script>
<style lang="less" scoped>
.reg-body {
  height: 100%;
  // overflow: auto;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  border-radius: 6px;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);
  padding: 16px 16px;
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;

  .header-tools {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 10px;

    .left {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .filter-button {
        min-width: 80px;
        height: 32px;
        border-radius: 7px;
      }

      .search-input {
        width: clamp(280px, 20vw, 320px);

        :deep(.ant-input) {
          width: 100%;
          height: 24px;

        }

        .search-icon {
          cursor: pointer;
        }
      }
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .tool-button {
        height: 32px;
        border-radius: 7px;
      }

      .link-button {
        height: 32px;
        padding: 0 8px;
      }
    }
  }

  .search-form {

    padding: 16px 16px; // 统一内边距
    border-radius: 8px; // 增加圆角


    .search-row {
      display: flex;
      flex-wrap: wrap;
      gap: 24px; // 增加间距
      margin-bottom: 12px; // 增加行间距

      &:last-child {
        margin-bottom: 0;
      }

      .search-item {
        display: flex;
        align-items: center;
        // min-width: 300px;
        flex: 1;

        .label {
          min-width: 80px;
          // margin-right: 12px; // 增加标签和输入框的间距
          color: #666;
          font-size: 14px;
        }

        :deep(.ant-select),
        :deep(.ant-input) {
          width: 64%;
          height: 32px;

          .ant-select-selector {
            background: #fff; // 确保选择器背景为白色
            border-radius: 4px;
          }
        }

        :deep(.ant-input) {
          background: #fff; // 确保输入框背景为白色
          border-radius: 4px;
        }
      }

      .search-button,
      .reset-button {
        height: 32px;
        min-width: 80px;
        margin-left: auto;
        border-radius: 4px; // 统一按钮圆角
      }

      .search-button {
        background: #1890ff; // 查询按钮使用主题蓝色
      }

      .reset-button {
        background: #fff; // 重置按钮使用白色背景
        border: 1px solid #d9d9d9;
      }
    }
  }

  .table-footer {
    position: fixed; // 默认固定定位
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 32px);
    max-width: 1888px;
    margin: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px 24px;
    border-radius: 8px;

    z-index: -100;

    .total-info {
      color: #666;
      font-size: 14px;
    }

    // 当筛选展开时的样式
    &.follow-page {
      position: static; // 改为静态定位
      transform: none;
      width: 100%;
      margin-top: 16px;
    }
  }


  .custom-table {
    margin-top: 16px;

    :deep(.ant-table) {

      // 提高固定列的层级
      .ant-table-fixed-left,
      .ant-table-fixed-right {
        background: #fff;
        z-index: 3; // 增加层级
      }

      .ant-table-cell {
        white-space: nowrap !important; // 强制不换行
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        line-height: 1 !important;
        font-size: 14px !important;

        >span,
        >div {
          white-space: nowrap !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
        }
      }

      // 大屏幕样式（默认）
      @media screen and (min-width: 1920px) {
        .ant-table-cell {
          padding: 20px 20px !important;
          height: 60px !important;
        }

        .ant-table-row {
          height: 60px !important;
        }
      }

      // 中等屏幕样式
      @media screen and (min-width: 1366px) and (max-width: 1919px) {
        .ant-table-cell {
          padding: 10px 20px !important;
          height: 40px !important;
        }

        .ant-table-row {
          height: 40px !important;
        }
      }

      // 小屏幕样式
      @media screen and (max-width: 1365px) {
        .ant-table-cell {
          padding: 4px 8px !important;
          height: 32px !important;
        }

        .ant-table-row {
          height: 32px !important;
        }
      }

      // 调整固定列单元格样式
      .ant-table-cell-fix-left,
      .ant-table-cell-fix-right {
        z-index: 3 !important; // 增加层级
        background: #ECF4FE !important;
      }

      tr>.ant-table-cell-fix-left:nth-child(6) {
        display: none !important;
      }

      // 调整表头固定列样式
      .ant-table-thead {

        th.ant-table-cell-fix-left,
        th.ant-table-cell-fix-right {
          z-index: 4 !important; // 确保表头在最上层
          background: #DAECFF !important;
        }
      }

      // 优化阴影效果
      .ant-table-fixed-right::before,
      .ant-table-fixed-left::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 10px;
        pointer-events: none;
        z-index: 2; // 阴影层级低于固定列
        transition: box-shadow .3s;
      }

      .ant-table-fixed-left::before {
        right: 0;
        box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
      }

      .ant-table-fixed-right::before {
        left: 0;
        box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
      }

      // 设置表格内容的层级
      .ant-table-content {
        z-index: 1;
      }

      // 确保滚动区域正确显示
      .ant-table-body {
        overflow-x: auto !important;
        overflow-y: auto !important;
      }

      // 固定列不换行
      .ant-table-cell-fix-left,
      .ant-table-cell-fix-right {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .ant-table-row {
        height: 24px !important;
      }

      // 表头固定列不换行
      .ant-table-thead {

        th.ant-table-cell-fix-left,
        th.ant-table-cell-fix-right {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .reg-body {
    .search-form {
      .search-row {
        .search-item {
          width: 50%;
          // min-width: calc(32.33% - 40px);
        }
      }
    }
  }
}

@media screen and (max-width: 1366px) {
  .reg-body {
    .search-form {
      .search-row {
        .search-item {
          min-width: calc(50% - 16px);
        }
      }
    }
  }
}

@media screen and (max-width: 1024px) {
  .reg-body {
    .header-tools {

      .left,
      .right {
        width: 100%;
        justify-content: space-between;
      }
    }

    .search-form {
      .search-row {
        .search-item {
          min-width: 100%;
        }
      }
    }

    .table-footer {
      flex-direction: column;
      text-align: center;

      .total-info {
        width: 100%;
      }
    }
  }
}

// 表格响应式
:deep(.ant-table) {
  .ant-table-content {
    overflow-x: auto;
  }

  @media screen and (max-width: 1024px) {
    .ant-table-cell {
      white-space: nowrap;
    }
  }
}

// 固定列样式
:deep(.ant-table) {
  .ant-table-body {
    overflow-x: auto;
    overflow-y: auto;
  }

  .ant-table-fixed-left,
  .ant-table-fixed-right {
    background: #fff;
    box-shadow: none; // 移除原有阴影
    z-index: 2; // 提高固定列的层级
  }

  // 确保表格内容正确显示
  .ant-table-content {
    z-index: 0;
  }

  // 修复固定列单元格层级
  .ant-table-cell-fix-left,
  .ant-table-cell-fix-right {
    z-index: 2 !important;
    background: #fff !important;
  }

  // 修复表头固定列层级
  .ant-table-thead th.ant-table-cell-fix-left,
  .ant-table-thead th.ant-table-cell-fix-right {
    z-index: 3 !important;
  }
}

:deep(.ant-tree-switcher) {
  position: relative;
}

:deep(.ant-tree-switcher_close::before) {
  content: "";
  position: absolute;
  right: -200px;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 999;
}

:deep(.ant-tree-switcher_open::before) {
  content: "";
  position: absolute;
  right: -200px;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 999;
}

.fonts {
  font-family: 思源黑体;
  font-size: 20px;
  font-weight: normal;
  line-height: normal;
  text-align: center;
  letter-spacing: 0px;

  /* 正文色/正文色 */
  color: #1A1A1A;
}

/deep/ .ant-table-tbody>tr>td {
  // border-bottom: 1px solid #DAE4EF !important;
  // border-top: 1px solid #DAE4EF !important;
  // border-left: 1px solid #DAE4EF !important;
  // border-right: 1px solid #DAE4EF !important;
  transition: background 0.3s;
}

// /deep/ .ant-table-tbody>tr>td:nth-of-type(n+3) {
//   color: #1890ff;
//   cursor: pointer;
// }

/deep/ .ant-table-tbody>tr>td:nth-last-child(-n+2) {
  color: var(--text-color);
  ;
  cursor: pointer;
}

/deep/ .ant-table-thead>tr>th {
  border-bottom: 0 !important;
  border-right: 1px solid #C8D4E1 !important;
  border-top: 1px solid #C8D4E1 !important;
}

.ele-body {
  overflow: hidden !important;
}

.ant-table-sticky-holder {
  position: sticky !important;
  z-index: 3 !important;
}


.table-wrapper {
  position: relative;
  padding-bottom: 55px;
  /* 为合计行留出空间 */
}

/* 固定合计行样式 */
:deep(.fixed-summary-row) {
  position: sticky;
  bottom: 0;
  z-index: 1;
  background: #ECF5FE;
  font-weight: bold;

  td {
    border-top: 1px solid var(--border-color-split);
  }
}

/* 确保表格内容不遮挡合计行 */
:deep(.ant-table-body) {
  margin-bottom: 55px !important;
}


.allDataTable .ant-table-thead > tr > th, .ant-table-tbody > tr > td, .ant-table tfoot > tr > th, .ant-table tfoot > tr > td {
  padding: 11px 12px !important;
}


:deep(.ant-table-thead > tr > th) {
  padding: 5px 16px !important;
}

:deep(.ant-table-tbody .ant-table-cell) {
  padding: 16px 16px !important;
}
.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>
