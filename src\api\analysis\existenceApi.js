import Request from '@/utils/request-util';
import { param } from 'jquery';

/**
 * 设备验收API
 *
 * <AUTHOR> @date 2024/01/09
 */
export class existenceApi {
  /**
   * 获取新建数据
   *
   * <AUTHOR> @date 2024/01/09
   */
  static getData(params) {
    return Request.get('/analysis/reportRealHasReal/getReport', params);
  }



  static getChildrenList(params) {
    return Request.get('/analysis/reportRealHasReal/getReportDataList', params);
  }


  static downLoad(params) {
    return Request.downLoad('/api/analysis/reportRealHasReal/export', params);
  }

  static downLoad1(params) {
    return Request.downLoad('/api/analysis/analysisReportSheet/downLoadExcel', params);
  }

  static findPage(params) {
    return Request.get('/analysis/analysisReportSheet/findPage', params);
  }

  static reportRegisteredMonth(params) {
    return Request.get('/analysis/reportRealHasMonth/getReport',params);
  }

  static getReportManagementOrg() {
    return Request.get('/analysis/reportRealHasReal/getReportManagementOrg');
  }


}
