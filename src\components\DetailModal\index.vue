<template>
  <div>
    <a-modal
    v-model:visible="visible"
    :width="1000"
    :title="title"
    :maskClosable="false"
    :footer="null"
    @cancel="onClose"
    class="detail-modal"
    :destroyOnClose="true"
    centered="true"
  >
    <!-- 右上角历史记录按钮 -->
    <template #title>
      <div class="modal-title-container">
        <span>{{ title }}</span>
        <div
          v-if="detailData.type === 'reg'"
          class="history-link"
          @click="showHistory"
        >
          <span style="color: #1890ff; cursor: pointer;">历史记录 &gt;</span>
        </div>
      </div>
    </template>

    <!-- 标签页 -->
    <a-tabs v-model:activeKey="activeKey" @change="tabChange">
      <a-tab-pane :key="tabItem.key" :tab="tabItem.name" v-for="tabItem in tabList"></a-tab-pane>
    </a-tabs>

    <!-- 内容区域 -->
    <div class="modal-content">
      <!-- 只在模态框可见时渲染内容 -->
      <template v-if="visible">
        <!-- 设备信息 -->

        <device-form v-if="activeKey === '1'" :data="detailData" />
        <!-- 使用情况 -->
        <usage-form v-if="activeKey === '2'" :data="detailData" />
        <!-- 财务信息 -->
        <finance-form v-if="activeKey === '3'" :data="detailData" />
        <!-- 租赁费用 -->
        <lease-form v-if="activeKey === '4'" :data="detailData" />
        <!-- 报废信息 -->
        <scrap-form v-if="activeKey === '5' && detailData.type!=='reg'" :data="detailData" />
        <!-- 处置信息 -->
        <disposal-form v-if="activeKey === '8' && detailData.type==='disposal'" :data="detailData" />
        <!-- 主要附件 -->
        <main-form v-if="activeKey === '6'" :data="detailData" />
        <!-- 动态信息 -->
        <dynamic-form v-if="activeKey === '7'" :data="detailData" />
        <!-- 相关附件 -->
        <file-form v-if="activeKey === '9'&& detailData.type==='reg'" :data="detailData" />
      </template>
    </div>
  </a-modal>
  </div>

</template>

<script setup>
import { ref, watch } from 'vue';
import DeviceForm from '@/components/DetailDrawer/components/device-form.vue';
import UsageForm from '@/components/DetailDrawer/components/usage-form.vue';
import FinanceForm from '@/components/DetailDrawer/components/finance-form.vue';
import LeaseForm from '@/components/DetailDrawer/components/lease-form.vue';
import MainForm from '@/components/DetailDrawer/components/main-form.vue';
import DynamicForm from '@/components/DetailDrawer/components/dynamic-form.vue';
import ScrapForm from '@/components/DetailDrawer/components/scrap-form.vue';
import DisposalForm from '@/components/DetailDrawer/components/disposal-form.vue';
import FileForm from '@/components/DetailDrawer/components/file-form.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: '设备详情'
  },
});

const emit = defineEmits(['update:visible', 'close']);

const activeKey = ref('1');
const detailData = ref({
  type: 'reg',
  code: "",
  // 添加其他可能被访问的属性的默认值
  name: "",
  model: "",
  manufacturer: "",
  // 根据 device-form.vue 中访问的属性添加更多默认值
});

let tabList = ref([]);

watch(() => props.data, (val) => {
  console.log('props.data changed:', val);
  detailData.value = val;
  // if (val?.bussRegisteredEquipment) {
  //   // 使用合并而不是替换，保留默认值
  //   detailData.value = { ...detailData.value, ...val };
  // } else {
  //   // 如果 val 为 null 或 undefined，重置为默认值
  //   detailData.value = {
  //     type: 'reg',
  //     code: "",
  //     name: "",
  //     model: "",
  //     manufacturer: "",
  //     // 其他默认值
  //   };
  // }

  // 设置标签页列表
  if(detailData.value.type === 'reg'){
    tabList.value = [
      { key: '1', name: '设备信息' },
      { key: '2', name: '使用情况' },
      { key: '3', name: '财务信息' },
      { key: '4', name: '租赁费用' },
      { key: '6', name: '主要附件' },
      { key: '7', name: '动态信息' },
      { key: '9', name: '相关附件' }
    ];
  } else if(props.data?.type === 'scrap'){
    tabList.value = [
      { key: '1', name: '设备信息' },
      { key: '2', name: '使用情况' },
      { key: '3', name: '财务信息' },
      { key: '4', name: '租赁费用' },
      { key: '5', name: '报废信息' },
      { key: '6', name: '主要附件' },
      { key: '7', name: '动态信息' },
    ];
  } else if(props.data?.type === 'disposal'){
    if(detailData.value.scrapedEquipmentTransferForm){
      tabList.value = [
        { key: '1', name: '设备信息' },
        { key: '2', name: '使用情况' },
        { key: '3', name: '财务信息' },
        { key: '4', name: '租赁费用' },
        { key: '5', name: '报废信息' },
        { key: '6', name: '主要附件' },
        { key: '7', name: '动态信息' },
        { key: '8', name: '处置信息' },
      ];
    } else {
      tabList.value = [
        { key: '1', name: '设备信息' },
        { key: '2', name: '使用情况' },
        { key: '3', name: '财务信息' },
        { key: '4', name: '租赁费用' },
        { key: '6', name: '主要附件' },
        { key: '7', name: '动态信息' },
        { key: '8', name: '处置信息' },
      ];
    }
  }
}, { immediate: true });

const tabChange = (key) => {
  activeKey.value = key;
};

const onClose = () => {
  emit('update:visible', false);

  // 使用 setTimeout 延迟重置数据，等待模态框关闭动画完成
  setTimeout(() => {
    detailData.value = { type: 'reg', code: "" };
    emit('close');
  }, 300);
};

// 确保 visible 变化时也处理 detailData
watch(() => props.visible, (val) => {
  if (!val) {
    // 当模态框关闭时，延迟重置 detailData
    setTimeout(() => {
      detailData.value = { type: 'reg', code: "" };
    }, 300);
  }
});

// 历史记录功能（占位，需要实现）
const showHistory = () => {
  console.log('显示历史记录');
  // 这里可以实现显示历史记录的逻辑
};
</script>

<style scoped>
.detail-modal {
  /* top: 20px; */
  height: 500px;
}

.modal-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.history-link {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.history-link:hover span {
  text-decoration: underline;
}

.modal-content {
  /* max-height: calc(100vh - 300px); */
  height: 500px;
  overflow-y: auto;
  padding: 0 16px;
}

/* 自定义滚动条样式 */
.modal-content::-webkit-scrollbar {
  width: 6px;
}

.modal-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.05);
}

/* /deep/ .modal-content{
  height: 800px !important;
} */
</style>

