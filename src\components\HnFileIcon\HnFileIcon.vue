/**
    文件图标展示组件 王文胜 20250709
    使用方法
    <hn-file-icon :file-info="file" :file-info-reader="{fileOriginName:'fileName'}" :show-del="true" class="file-item" @handle-remove="handleRemove" />
    参数说明：
        属性:
            fileInfo: 文件信息对象 {}
            fileInfoReader: 文件信息对象字段映射关系，默认为{fileOriginName:'fileName',fileName: 'fileName', fileUrl: 'fileUrl',fileStatus:'status'} 其中fileOriginName为文件原始名称，会根据此名称进行文件类型识别
            iconKv: 文件图标映射关系,默认为 {"xls":"wrapper","xlsx":"wrapper","doc":'word',docx:'word',pdf:'pdf',mov:'mp4',mp4:'mp4',ppt:'ppt',pptx:'ppt',png:'jpg',jpg:'jpg',jpeg:'jpg',heif:'jpg'}
            showDel: 是否显示删除按钮
        事件:
            handleRemove(deledFile): 删除文件事件
*/
<template>
    <div class="file-item">
        <div class="file-icon">
            <img :src="iconSrc" />
        </div>
        <a :href="`${fileInfo[fileReader.fileUrl]}?filename=${fileInfo[fileReader.fileName]}`" target="_blank" class="file-link">
            {{ formatFileName(fileInfo[fileReader.fileName]) }}
        </a>
        <div class="file-actions">
            <sync-outlined v-if="fileInfo.status === 'uploading'" spin />
            <close-outlined v-else-if="showDel" class="delete-icon" @click="handleRemove" />
        </div>
    </div>
</template>

<script>
import { computed } from 'vue';

export default {
  name: 'HhFileIcon',
  inheritAttrs: true,
  props: {
    fileInfo: {
      type: Object,
      required: true
    },
    fileInfoReader:{  /// 解析
        type: Object,
        required: false,
        default: () => { 
            return {  };
        }
    },
    showDel:{
        type: Boolean,
        default: false
    },
    iconKv:{
        type: Object,
        required: false,
        default: () => { 
            return {  };
        }

    }
  },
  setup(props,{ emit }) {
    const iconSrc = computed(() => { 
        let fileInfoReader = Object.assign({fileOriginName:'fileName',fileName: 'fileName', fileUrl: 'fileUrl',fileStatus:'status'}, props.fileInfoReader);
        let fname = props.fileInfo[fileInfoReader.fileOriginName];
        let icon = "reader";
        let iconKv = Object.assign({"xls":"icon-wrapper","xlsx":"icon-wrapper","doc":'word',docx:'word',pdf:'pdf',mov:'mp4',mp4:'mp4',ppt:'ppt',pptx:'ppt',png:'jpg',jpg:'jpg',jpeg:'jpg',heif:'jpg'},props.iconKv);
        fname = fname.toLowerCase().split('.');
        icon = "/public/icon/"+(iconKv[fname[fname.length - 1]]||'reader')+".svg";
        return icon;
    });
    const fileReader = computed(() => { 
        let fileInfoReader = Object.assign({fileOriginName:'fileName',fileName: 'fileName', fileUrl: 'fileUrl',fileStatus:'status'}, props.fileInfoReader);
        return fileInfoReader;
    });
    const handleRemove = () => { 
        emit('handleRemove', props.fileInfo);
    };
    // 格式化文件名，如果太长则用省略号替代中间部分，但保留扩展名
    const formatFileName = (fileName) => {
        if (!fileName) return '';
        const maxLength = 20; // 最大显示长度
        if (fileName.length <= maxLength) {
            return fileName;
        }
        // 获取文件扩展名
        const lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex === -1) {
            // 没有扩展名
            return fileName.substring(0, maxLength - 3) + '...';
        }
        const extension = fileName.substring(lastDotIndex);
        const nameWithoutExt = fileName.substring(0, lastDotIndex);
        // 计算名称部分应该保留的长度
        const remainingLength = maxLength - extension.length - 3; // 3是省略号的长度
        if (remainingLength <= 0) {
            // 如果扩展名太长，只显示部分扩展名
            return fileName.substring(0, 5) + '...' + extension.substring(extension.length - 5);
        }
        // 保留文件名开头部分 + ... + 扩展名
        return nameWithoutExt.substring(0, remainingLength) + '...' + extension;
    };
    return {
        iconSrc,
        fileReader,
        handleRemove,
        formatFileName
    };
  }
};
</script>

<style lang="less" scoped>
.file-item {
    display: flex;
    align-items: center;
    padding: 8px;
    // background: #F7F9FC;
    border-radius: 2px;
    .file-link{
        //word-break:break-all;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .file-icon {
    margin-right: 8px;
    font-size: 16px;
    color: #2D5CF6;
    }

    .file-name {
    flex: 1;
    font-size: 14px;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    }

    .file-actions {
    margin-left: 8px;

    .delete-icon {
        color: #999;
        cursor: pointer;

        &:hover {
        color: #FF4B4B;
        }
    }

    .anticon-sync {
        color: #2D5CF6;
    }
    }
}
</style>
