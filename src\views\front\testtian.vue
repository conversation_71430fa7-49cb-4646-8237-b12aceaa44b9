<template>
  <div class="tianmap-container">
    <!-- 直接嵌入HTML内容 -->
    <div id="map" class="map"></div>
    <div class="controls">
        <div class="control-group">
            <label>Grayscale: <span id="grayscaleValue">100</span>%</label>
            <input type="range" id="grayscale" min="0" max="100" value="100">
        </div>
        <div class="control-group">
            <label>Sepia: <span id="sepiaValue">51</span>%</label>
            <input type="range" id="sepia" min="0" max="100" value="51">
        </div>
        <div class="control-group">
            <label>Invert: <span id="invertValue">100</span>%</label>
            <input type="range" id="invert" min="0" max="100" value="100">
        </div>
        <div class="control-group">
            <label>Saturate: <span id="saturateValue">350</span>%</label>
            <input type="range" id="saturate" min="0" max="500" value="350">
        </div>
        
        <!-- 上传按钮 -->
        <div class="control-group">
            <label>上传图标:</label>
            <input type="file" id="iconUpload" accept="image/*" style="margin-left: 10px;">
        </div>
    </div>

    <!-- 右上角功能按钮 -->
    <div class="function-buttons">
        <button id="addRandomMarkers" class="btn btn-primary">添加随机扎点</button>
        <button id="clearMarkers" class="btn btn-secondary">清除扎点</button>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue';

// 动态加载OpenLayers脚本
const loadOpenLayers = () => {
  return new Promise((resolve, reject) => {
    if (window.ol) {
      resolve(window.ol);
      return;
    }
    
    // 加载CSS
    const cssLink = document.createElement('link');
    cssLink.rel = 'stylesheet';
    cssLink.href = 'https://cdn.jsdelivr.net/gh/openlayers/openlayers.github.io@master/en/v6.5.0/css/ol.css';
    document.head.appendChild(cssLink);
    
    // 加载JS
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/gh/openlayers/openlayers.github.io@master/en/v6.5.0/build/ol.js';
    script.onload = () => resolve(window.ol);
    script.onerror = () => reject(new Error('OpenLayers加载失败'));
    document.head.appendChild(script);
  });
};

// 初始化地图和所有功能
const initMap = async () => {
  try {
    // 加载OpenLayers
    await loadOpenLayers();
    
    // 执行原HTML中的JavaScript代码
    const tk = 'b0cf25a9fe9839fe9cea00f386c84238';
    
    // 创建投影和分辨率配置
    const projection = window.ol.proj.get('EPSG:4326');
    const projectionExtent = projection.getExtent();
    const size = window.ol.extent.getWidth(projectionExtent) / 256;
    const resolutions = new Array(20);
    const matrixIds = new Array(20);
    
    // 渌口区王家洲村坐标（使用高德地图获取的坐标）
    const targetLonLat = [113.138728, 27.694423]; // WGS84坐标
    const targetCoordinate = window.ol.proj.fromLonLat(targetLonLat);
    
    for (let z = 0; z < 20; ++z) {
        resolutions[z] = size / Math.pow(2, z);
        matrixIds[z] = z;
    }

    // 创建天地图图层
    const layer = new window.ol.layer.Tile({
        className: 'base-layer',
        source: new window.ol.source.WMTS({
            url: `https://t5.tianditu.gov.cn/vec_c/wmts?tk=${tk}`,
            layer: 'vec',
            matrixSet: 'c',
            format: 'tiles',
            projection: projection,
            tileGrid: new window.ol.tilegrid.WMTS({
                origin: window.ol.extent.getTopLeft(projectionExtent),
                resolutions: resolutions,
                matrixIds: matrixIds
            }),
            style: 'default',
            wrapX: true,
            crossOrigin: 'anonymous'
        })
    });

    const layer_B = new window.ol.layer.Tile({
        className: 'base-layer',
        source: new window.ol.source.WMTS({
            url: `https://t5.tianditu.gov.cn/cva_c/wmts?tk=${tk}`,
            layer: 'cva',
            matrixSet: 'c',
            format: 'tiles',
            projection: projection,
            tileGrid: new window.ol.tilegrid.WMTS({
                origin: window.ol.extent.getTopLeft(projectionExtent),
                resolutions: resolutions,
                matrixIds: matrixIds
            }),
            style: 'default',
            wrapX: true,
            crossOrigin: 'anonymous'
        })
    });

    // 扎点相关变量
    let markers = [];
    let vectorSource = new window.ol.source.Vector();
    let vectorLayer = new window.ol.layer.Vector({
        source: vectorSource,
        className: 'marker-layer' // 添加类名
    });

    // 创建地图实例
    const map = new window.ol.Map({
        target: 'map',
        layers: [layer, layer_B, vectorLayer], // 添加矢量图层
        view: new window.ol.View({
            center: targetCoordinate,  // 初始定位到目标位置
            zoom: 14  // 调整到合适的缩放级别
        })
    });

    // 上传图标功能
    document.getElementById('iconUpload').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(event) {
                customIconUrl = event.target.result;
                console.log('图标上传成功');
            };
            reader.readAsDataURL(file);
        }
    });

    // 创建扎点 - 使用项目中的SVG图标
    function createMarker(lng, lat, name) {
        const feature = new window.ol.Feature({
            geometry: new window.ol.geom.Point(window.ol.proj.fromLonLat([lng, lat])),
            name: name,
            longitude: lng,
            latitude: lat
        });
        
        // 使用项目中的SVG图标
        const iconStyle = new window.ol.style.Style({
            image: new window.ol.style.Icon({
                src: '/public/icon/zhadian.svg',
                scale: 2,
                anchor: [0.5, 1]
            })
        });
        
        feature.setStyle(iconStyle);
        vectorSource.addFeature(feature);
        markers.push(feature);
        
        return feature;
    }

    // 添加随机扎点
    document.getElementById('addRandomMarkers').addEventListener('click', function() {
        const randomCount = Math.floor(Math.random() * 5) + 3; // 3-7个随机点
        
        for (let i = 0; i < randomCount; i++) {
            const lng = 113.0 + Math.random() * 0.5; // 在当前区域附近
            const lat = 27.5 + Math.random() * 0.5;
            const name = `随机站点${Date.now()}-${i}`;
            
            createMarker(lng, lat, name);
        }
        
        console.log(`成功添加 ${randomCount} 个随机扎点`);
    });

    // 清除扎点
    document.getElementById('clearMarkers').addEventListener('click', function() {
        vectorSource.clear();
        markers = [];
        console.log('已清除所有扎点');
    });

    // 添加地图点击事件监听
    map.on('click', function(evt) {
        // 检查点击位置是否有要素
        const feature = map.forEachFeatureAtPixel(evt.pixel, function(feature) {
            return feature;
        });
        
        if (feature) {
            // 点击了扎点
            const name = feature.get('name');
            const lng = feature.get('longitude');
            const lat = feature.get('latitude');
            
            console.log('点击了扎点:', {
                name: name,
                longitude: lng,
                latitude: lat
            });
            
            // 这里可以添加更多点击处理逻辑
            alert(`点击了扎点: ${name}\n坐标: ${lng}, ${lat}`);
            
            // 或者显示信息窗口
            // showMarkerInfo(feature, evt.coordinate);
        }
    });

    // 可选：添加鼠标悬停效果
    map.on('pointermove', function(evt) {
        const feature = map.forEachFeatureAtPixel(evt.pixel, function(feature) {
            return feature;
        });
        
        // 改变鼠标样式
        map.getTargetElement().style.cursor = feature ? 'pointer' : '';
    });

    // 获取滤镜控制元素
    const grayscale = document.getElementById('grayscale');
    const sepia = document.getElementById('sepia');
    const invert = document.getElementById('invert');
    const saturate = document.getElementById('saturate');

    // 初始化滤镜值
    let currentFilters = {
        grayscale: 100,
        sepia: 51,
        invert: 100,
        saturate: 350,
    };

    // 更新滤镜显示
    function updateDisplay() {
        document.getElementById('grayscaleValue').textContent = currentFilters.grayscale;
        document.getElementById('sepiaValue').textContent = currentFilters.sepia;
        document.getElementById('invertValue').textContent = currentFilters.invert;
        document.getElementById('saturateValue').textContent = currentFilters.saturate;
    }

    // 应用滤镜到地图 - 只对地图图层应用，不影响扎点
    function applyFilters() {
        const filterStr =
            `grayscale(${currentFilters.grayscale}%) ` +
            `sepia(${currentFilters.sepia}%) ` +
            `invert(${currentFilters.invert}%) ` +
            `saturate(${currentFilters.saturate}%)`;

        console.log('filterStr',filterStr);
        
        // 只对地图图层应用滤镜，不影响矢量图层
        const layerElements = document.querySelectorAll('.base-layer');
        layerElements.forEach(layerElement => {
            layerElement.style.filter = filterStr;
        });
        
        // 确保矢量图层不受滤镜影响
        const vectorElements = document.querySelectorAll('.ol-layer:not(.base-layer)');
        vectorElements.forEach(element => {
            element.style.filter = 'none';
        });
    }

    // 事件监听
    grayscale.addEventListener('input', (e) => {
        currentFilters.grayscale = e.target.value;
        updateDisplay();
        applyFilters();
    });

    sepia.addEventListener('input', (e) => {
        currentFilters.sepia = e.target.value;
        updateDisplay();
        applyFilters();
    });

    invert.addEventListener('input', (e) => {
        currentFilters.invert = e.target.value;
        updateDisplay();
        applyFilters();
    });

    saturate.addEventListener('input', (e) => {
        currentFilters.saturate = e.target.value;
        updateDisplay();
        applyFilters();
    });

    // 初始化显示
    updateDisplay();
    applyFilters();
    
  } catch (error) {
    console.error('地图初始化失败:', error);
  }
};

onMounted(() => {
  initMap();
});
</script>

<style scoped>
.tianmap-container {
  width: 100%;
  height: 100vh;
  position: relative;
}

.map {
    width: 100%;
    height: 90vh;
}

.controls {
    position: absolute;
    top: 10px;
    left: 50px;
    background: rgba(255, 255, 255, 0.8);
    padding: 10px;
    border-radius: 5px;
    z-index: 1000;
}

.control-group {
    margin: 5px 0;
}

label {
    display: inline-block;
    width: 120px;
}

/* 右上角功能按钮 */
.function-buttons {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 5px;
    z-index: 1000;
}

.btn {
    padding: 8px 16px;
    margin: 0 5px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn:hover {
    opacity: 0.8;
}

/* 确保扎点图层不受滤镜影响 */
:deep(.marker-layer) {
    filter: none !important;
}
</style>






















