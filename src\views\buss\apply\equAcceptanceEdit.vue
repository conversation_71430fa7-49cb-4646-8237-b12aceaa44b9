<template>
  <div class="ele-body">
    <div class="equipment-acceptance">
      <!-- 使用 CommonDrawer 替换 Modal -->
      <!-- <common-drawer v-model:visible="drawerVisible" title="审批记录" :width="600" @close="handleDrawerClose">
        <ApprovalRecord :records="approvalRecords" />
      </common-drawer>
      <div class="approval-record-btn" @click="showApprovalRecord">
        <img src="@/assets/equipment/shenpi.png" alt="审批记录" class="bg-image" />
        <div class="content">
          <img src="../../../assets/equipment/approval.png" alt="" class="icon" />
          <span class="text">审批记录</span>
        </div>
      </div> -->
      <div class="form-title">设备台账编辑单</div>

      <!-- 申请信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          申请信息
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">申请人</div>
            <div class="approvalFont">{{ bussTransferForm.applyUserStr }}</div>
          </div>
          <div class="form-item">
            <div class="label">申请日期</div>
            <div class="approvalFont">{{ bussTransferForm.applyDate }}</div>
          </div>
          <div class="form-item">
            <div class="label">申请单位</div>
            <div class="approvalFont">{{ bussTransferForm.applyOrgStr }}</div>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label required">业务标题</div>
            <a-input v-model:value="businessTitle" placeholder="请输入业务标题" :maxlength="16" />
          </div>
        </div>


      </div>

      <!-- 设备信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          设备信息
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label required">设备编号</div>
            <a-input v-model:value="formData.code" placeholder="请输入唯一编码" disabled />
          </div>
          <div class="form-item" data-field="equType">
            <div class="label required">设备类别</div>
            <div class="value">
              <a-select v-model:value="formData.equTypeStr" placeholder="请选择设备类别" class="full-width"
                @change="handleEquTypeChange" disabled>
                <a-select-option v-for="item in equTypeOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>
          <div class="form-item">
            <div class="label required">设备种类</div>
            <div class="value">
              <a-select v-model:value="formData.equSubTypeStr" placeholder="请选择设备种类" class="full-width" disabled
                @change="handleEquSubTypeChange">
                <a-select-option v-for="item in equSubTypeOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>
          <div class="form-item">
            <div class="label required">设备名称</div>
            <div class="value">
              <a-select v-model:value="formData.equNameStr" placeholder="请选择设备名称" class="full-width" disabled
                @change="handleEquNameChange">
                <a-select-option v-for="item in equNameOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>
          <div class="form-item">
            <div class="label required">规格型号</div>
            <div class="value">
              <a-select v-model:value="formData.equModelStr" placeholder="请选择规格型号" class="full-width" disabled>
                <a-select-option v-for="item in equModelOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>
          <div class="form-item">
            <div class="label">型号备注</div>
            <div class="value">
              <a-input v-model:value="formData.equModelInfo" placeholder="请输入型号备注"
                :disabled="!initialValueStatus.equModelInfo" :maxlength="50" />
            </div>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label required">设备性质</div>
            <div class="value">
              <a-select v-model:value="formData.equNature" placeholder="请选择设备性质" class="full-width" disabled>
                <a-select-option v-for="item in equNatureOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>
          <div class="form-item">
            <div class="label required">购置年度</div>
            <div class="value">
              <a-input v-model:value="formData.purchaseYear" disabled />
            </div>
          </div>
          <div class="form-item">
            <div class="label">设备来源</div>
            <div class="value">
              <!-- 设备来源 -->
              <a-select v-model:value="formData.equSource" placeholder="请选择设备来源" class="full-width"
                :disabled="!initialValueStatus.equSource">
                <a-select-option v-for="item in equSourceOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label required">单位</div>
            <a-input v-model:value="formData.unit" placeholder="台/套/个" disabled />
          </div>
          <div class="form-item">
            <div class="label">数量</div>
            <a-input-number v-model:value="formData.num" :min="1" :precision="0" disabled />
          </div>
          <div class="form-item">
            <div class="label required">设备合同价(含税)</div>
            <a-input-number v-model:value="formData.equContractPriceTax" placeholder="请输入设备合同价" disabled />
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label required">生产厂家</div>
            <a-input v-model:value="formData.manufacturer" placeholder="请输入生产厂家" disabled />
          </div>
          <div class="form-item">
            <div class="label">出厂编号</div>
            <a-input v-model:value="formData.factoryNumber" placeholder="请输入出厂编号"
              :disabled="!initialValueStatus.factoryNumber" />
          </div>
          <div class="form-item" data-field="productionDate">
            <div class="label required">出厂日期</div>
            <div class="value">
              <a-date-picker v-model:value="formData.productionDate" placeholder="选择日期" style="width: 100%"
                value-format="YYYY-MM-DD" disabled />
            </div>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item" data-field="contractNumber">
            <div class="label">合同编号</div>
            <div class="value">
              <a-input v-model:value="formData.contractNumber" placeholder="请输入合同编号"
                :disabled="!initialValueStatus.contractNumber" :maxlength="20" />
            </div>
          </div>
          <div class="form-item" data-field="acceptanceNumber">
            <div class="label">验收单号</div>
            <div class="acceptance-no">


              <a-date-picker v-model:value="formData.acceptanceNumberYan" picker="year" placeholder="选择年份"
                value-format="YYYY" :format="yearFormat" class="first-input"
                :disabled="!initialValueStatus.acceptanceNumberYan" />
              <span class="separator">验资</span>
              <a-input-number v-model:value="formData.acceptanceChar" placeholder="数字" class="second-input"
                :maxlength="4" :min="1" :precision="0" :disabled="!initialValueStatus.acceptanceChar" />
              <span class="separator">号</span>
            </div>
          </div>
          <div class="form-item" data-field="acceptanceDate">
            <div class="label ">验收日期</div>
            <div class="value">
              <a-date-picker v-model:value="formData.acceptanceDate" placeholder="选择日期" style="width: 100%"
                value-format="YYYY-MM-DD" :disabled="!initialValueStatus.acceptanceDate"
                :disabledDate="(current) => current && current > dayjs().endOf('day')" />
            </div>
          </div>
        </div>

        <!-- 技术资料 -->
        <div class="form-grid">
          <div class="form-item">
            <div class="label">功率</div>
            <div class="value">
              <a-input-number placeholder="请输入" v-model:value="formData.power" :disabled="!initialValueStatus.power"
                :maxlength="4" :min="1" :precision="0">
                <template #suffix>kw</template>
              </a-input-number>
            </div>
          </div>
          <div class="form-item">
            <div class="label">设备重要性</div>
            <div class="value">
              <a-select v-model:value="formData.importance" placeholder="请选择设备重要性" class="full-width"
                :disabled="!initialValueStatus.importance">
                <a-select-option v-for="item in equImportanceOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>
          <div class="form-item">
            <div class="label">设备型号编码</div>
            <div class="value">
              <a-input placeholder="请输入" v-model:value="formData.modelCode" :disabled="!initialValueStatus.modelCode"
                :maxlength="16" />
            </div>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">保养周期</div>
            <div class="value">
              <a-input placeholder="请输入" v-model:value="formData.serviceIntervalTypeStr" disabled />
            </div>
          </div>
          <div class="form-item">
            <div class="label">技术资料</div>
            <div class="value tech-files" ref="containerRef">
              <!-- <div v-show="initialValueStatus.techFileShow && techFileList.length == 0">
                <a-upload :maxCount="1" :action="uploadUrl" :headers="headers" :showUploadList="false"
                  :file-list="techFileList" @change="handleTechFileChange" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                  <div class="tech-upload-button">
                    <upload-outlined />
                    <span>选择文件</span>
                  </div>
                </a-upload>
                 <div class="upload-tip">支持PDF、Word文档和图片格式</div>
              </div> -->
              <div v-if="!initialValueStatus.techFileShow" class="tech-file-item">

                <!-- <div v-for="(file, index) in techFileList" :key="file.fileId" class="file-item">
                  <a :href="`${file.fileUrl}?filename=${file.fileName}`" target="_blank" class="file-link">
                    {{ file.fileName }}
                  </a>
                </div>
                <close-outlined class="delete-icon" @click="removeTechFile" v-if="initialValueStatus.techFileShow" /> -->
                <InputUploader :max-count="10" button-text="上传文件"
                  :accept-types="['.jpg', '.jpeg', '.png', '.mp4', '.mov', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', '.pdf']"
                  :initial-files="techFileList" :show-border="true" @file-uploaded="onTechFileUploaded"
                  @file-removed="onTechFileRemoved" @upload-status-change="onUploadStatusChange" :read-only="true" :width="containerWidth" :backgroundColor="'#fff'" />
              </div>
              <div v-else>
                <!-- <FileUploader :max-size="5" :max-count="1" button-text="点击上传" :accept-types="['.pdf', '.docx', '.doc']"
                  tip-text="仅支持上传1个文件，不超过5MB，支持PDF或Word" :initial-files="techFileList"
                  @file-uploaded="onTechFileUploaded" @file-removed="onTechFileRemoved" :tip-row="false"
                  :show-upload-button="isShowbutton" :show-del="showDelete" :isSingleFile="true"
                  @upload-status-change="onUploadStatusChange" /> -->
                <InputUploader :max-count="10" button-text="上传文件"
                  :accept-types="['.jpg', '.jpeg', '.png', '.mp4', '.mov', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', '.pdf']"
                  :initial-files="techFileList" :show-border="true" @file-uploaded="onTechFileUploaded"
                  @file-removed="onTechFileRemoved" @upload-status-change="onUploadStatusChange" :width="containerWidth" :backgroundColor="'#fff'"/>
              </div>

            </div>
          </div>
        </div>
      </div>

      <!-- 使用情况 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          使用情况
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label required">产权单位</div>
            <a-input v-model:value="formData.propertyOrgName" placeholder="请输入产权单位" disabled />
          </div>

          <div class="form-item">
            <div class="label required">管理单位</div>
            <a-input v-model:value="formData.managementOrgName" placeholder="请输入管理单位" disabled />
          </div>

          <div class="form-item">
            <div class="label required">使用单位</div>
            <div class="value">
              <a-input v-model:value="formData.useOrgStr" placeholder="请输入使用单位" disabled />
            </div>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label required">管理状态</div>
            <div class="value">
              <a-select v-model:value="formData.managementStatus" placeholder="请选择管理状态" class="full-width" disabled>
                <a-select-option v-for="item in managementStatusOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>
          <div class="form-item">
            <div class="label required">设备状态</div>
            <div class="value">
              <a-select v-model:value="formData.equCondition" placeholder="请选择设备状态" class="full-width" disabled>
                <a-select-option v-for="item in equConditionOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>

          <div class="form-item">
            <div class="label">存放地点</div>
            <!-- <a-select v-model:value="formData.storageLocationStr" placeholder="请选择存放地点" class="full-width"
              :options="storageLocationOptions" @change="handleStorageLocationChange"
              :disabled="!initialValueStatus.storageLocationStr" /> -->
            <a-input v-model:value="formData.storageLocationStr" placeholder="请输入存放地点" />
          </div>
        </div>
      </div>
      <!-- 财务信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          财务信息
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">财务卡片编号</div>
            <a-input v-model:value="formData.financialNumber" placeholder="请输入财务卡片编号"
              :disabled="!initialValueStatus.financialNumberShow" />
          </div>
          <div class="form-item">
            <div class="label ">财务组织</div>
            <a-input v-model:value="formData.financialOrg" placeholder="请输入财务组织"
              :disabled="!initialValueStatus.financialOrgShow" />
          </div>
          <div class="form-item">
            <div class="label">财务原值</div>
            <a-input-number v-model:value="formData.financialOriginalValue" placeholder="请输入财务原值"
              :disabled="!initialValueStatus.financialOriginalValueShow" :min="0" :step="0.01"
              :precision="2" :maxlength="15"/>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label ">固定资产分类</div>

            <a-select v-model:value="formData.fixedAssets" placeholder="请选择固定资产分类" class="full-width"
              :disabled="!initialValueStatus.fixedAssetsShow">
              <a-select-option v-for="item in fixedAssetsOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
          <div class="form-item">
            <div class="label">资金来源</div>
            <a-input v-model:value="formData.sourceOfFunds" placeholder="请输入资金来源"
              :disabled="!initialValueStatus.sourceOfFundsShow" />
          </div>
          <div class="form-item">
            <div class="label">递延收益</div>
            <a-input-number v-model:value="formData.deferredIncome" placeholder="请输入调整收益"
              :disabled="!initialValueStatus.deferredIncomeShow" :min="0" :step="0.01"
              :precision="2" :maxlength="15"/>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">已计提折旧月份</div>
            <a-input-number v-model:value="formData.alreadyAccruedMonths" placeholder="请输入已计提折旧月份"
              :disabled="!initialValueStatus.alreadyAccruedMonthsShow" :min="0" :step="0.01"
              :precision="2" :maxlength="15"/>
          </div>
          <div class="form-item">
            <div class="label required">折旧方式</div>
            <a-select v-model:value="formData.depreciationMethod" placeholder="请选择折旧方式" disabled>
              <a-select-option v-for="item in depreciationMethodOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
          <div class="form-item">
            <div class="label required">折旧年限</div>
            <a-input-number v-model:value="formData.depreciationPeriod" placeholder="请输入折旧年限" disabled />
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">折旧月份</div>
            <a-input-number v-model:value="formData.depreciationMonth" placeholder="请输入折旧月份"
              :disabled="!initialValueStatus.depreciationMonthShow" :min="0" :step="0.01"
              :precision="2" :maxlength="15"/>
          </div>
          <div class="form-item">
            <div class="label">残值率</div>
            <div class="value">
            <a-input-number v-model:value="formData.residualRate" :min="0" :max="100" :precision="2" style="width: 100%" :maxlength="15"
              placeholder="请输入残值率" >
              <template #addonAfter>%</template>
              </a-input-number>
                </div>
          </div>
          <div class="form-item">
            <div class="label ">预计净残值</div>
            <a-input-number v-model:value="formData.netSalvage" placeholder="请输入预计净残值"
              :disabled="!initialValueStatus.netSalvageShow" :min="0" :step="0.01"
              :precision="2" :maxlength="15"/>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label required">历史原值</div>
            <a-input-number v-model:value="formData.historicalOriginalValue" placeholder="请输入历史原值"
              :disabled="!initialValueStatus.historicalOriginalValueShow" :min="0" :step="0.01"
              :precision="2" :maxlength="15"/>
          </div>
          <div class="form-item">
            <div class="label">累计折旧额</div>
            <div class="percentage-input">
              <a-input-number v-model:value="formData.depreciationAmount" placeholder="请输入累计折旧额"
                :disabled="!initialValueStatus.depreciationAmountShow" :min="0" :step="0.01"
              :precision="2" :maxlength="15"/>
            
            </div>
          </div>
          <div class="form-item">
            <div class="label">当月折旧额</div>
            <a-input-number v-model:value="formData.currentMonthDepreciationAmount" placeholder="请输入当月折旧额"
              :disabled="!initialValueStatus.currentMonthDepreciationAmountShow" :min="0" :step="0.01"
              :precision="2" :maxlength="15"/>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">净值</div>
            <a-input-number v-model:value="formData.netWorth" placeholder="请输入净值"
              :disabled="!initialValueStatus.netWorthShow" :min="0" :step="0.01"
              :precision="2" :maxlength="15"/>
          </div>
          <div class="form-item">
            <div class="label">税率</div>
            <div class="percentage-input">
              <a-input-number v-model:value="formData.taxRate" placeholder="请输入税率" :min="0" :step="0.01" :maxlength="15"
                style="width: 100%" :precision="2">
                <template #addonAfter>%</template>
              </a-input-number>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要附件 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          主要附件
        </div>

        <a-table sticky :columns="initialValueStatus.addAttachmentShow ? columns : columns1" :data-source="attachments"
          bordered :pagination="false" :scroll="{ x: 'max-content' }" class="custom-table" ref="myTable">
          <template #bodyCell="{ column, index, record }">
            <template v-if="column.dataIndex === 'nameModel' && initialValueStatus.addAttachmentShow">
              <div class="form-item" style="margin-bottom: 0;">
                <a-input v-model:value="record.nameModel" placeholder="请输入名称及型号" :maxlength="16" />
              </div>
            </template>
            <template v-if="column.dataIndex === 'unit' && initialValueStatus.addAttachmentShow">
              <div class="form-item" style="margin-bottom: 0;">
                <a-input v-model:value="record.unit" placeholder="单位" :maxlength="6" />
              </div>
            </template>
            <template v-if="column.dataIndex === 'num' && initialValueStatus.addAttachmentShow">
              <div class="form-item" style="margin-bottom: 0;">
                <a-input-number v-model:value="record.num" :min="1" :precision="0" />
              </div>
            </template>
            <template v-if="column.dataIndex === 'manufacturer' && initialValueStatus.addAttachmentShow">
              <div class="form-item" style="margin-bottom: 0;">
                <a-input v-model:value="record.manufacturer" placeholder="请输入生产厂家" :maxlength="20" />
              </div>
            </template>
            <template v-if="column.dataIndex === 'factoryNumber' && initialValueStatus.addAttachmentShow">
              <div class="form-item" style="margin-bottom: 0;">
                <a-input v-model:value="record.factoryNumber" placeholder="请输入出厂编号" :maxlength="20" />
              </div>
            </template>
            <template v-if="column.key === 'action' && initialValueStatus.addAttachmentShow">
              <a-space>
                <a-button type="link" danger @click="removeAttachment(index)" style="padding: 0;">
                  <i class="iconfont icon-delete" style="margin-right: 6px;"></i>
                </a-button>
              </a-space>
            </template>
          </template>
          <template #footer v-if="initialValueStatus.addAttachmentShow">
            <a @click="addAttachment" style="width: 100%; display: inline-block; text-align: center;">
              <plus-outlined />
              新增一行
            </a>
          </template>
        </a-table>

        <!-- <div class="attachment-table">
          <div class="table-header">
            <div class="col-serial">序号</div>
            <div class="col-name">名称及型号</div>
            <div class="col-unit">单位</div>
            <div class="col-quantity">数量</div>
            <div class="col-manufacturer">生产厂家</div>
            <div class="col-serial-no">出厂编号</div>
            <div class="col-action" v-if="initialValueStatus.addAttachmentShow">操作</div>
          </div>

          <template v-if="initialValueStatus.addAttachmentShow">
            <div v-for="(item, index) in attachments" :key="index" class="table-row">
              <div class="col-serial">{{ index + 1 }}</div>
              <div class="col-name">
                <a-input v-model:value="item.nameModel" placeholder="请输入名称及型号" :maxlength="16" />
              </div>
              <div class="col-unit">
                <a-input v-model:value="item.unit" placeholder="单位" :maxlength="6" />
              </div>
              <div class="col-quantity">
                <a-input-number v-model:value="item.num" :min="1" :precision="0" />
              </div>
              <div class="col-manufacturer">
                <a-input v-model:value="item.manufacturer" placeholder="请输入生产厂家" :maxlength="20" />
              </div>
              <div class="col-serial-no">
                <a-input v-model:value="item.factoryNumber" placeholder="请输入出厂编号" :maxlength="20" />
              </div>
              <div class="col-action">
                <a class="delete-btn" @click="removeAttachment(index)">删除</a>
              </div>
            </div>
            <div class="add-row" style="text-align: center;">
              <a @click="addAttachment">
                <plus-outlined />
                新增一行
              </a>
            </div>
          </template>
          <template v-else>
            <div v-for="(item, index) in attachments" :key="index" class="table-row">
              <div class="col-serial">{{ index + 1 }}</div>
              <div class="col-name">{{ item.nameModel }}</div>
              <div class="col-unit">{{ item.unit }}</div>
              <div class="col-quantity">{{ item.num }}</div>
              <div class="col-manufacturer">{{ item.manufacturer }}</div>
              <div class="col-serial-no">{{ item.factoryNumber }}</div>
            </div>
          </template>
        </div> -->
      </div>

      <!-- 附件 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          附件
        </div>

        <div class="upload-section1">




          <div v-if="!initialValueStatus.fileListShow" class="upload-section1">

            <div class="file-list">
              <div v-for="(file, index) in fileViewList" :key="file.uid" class="file-item">
                <div class="file-icon">
                  <!-- <img src="/public/icon/icon-wrapper.svg"
                    v-if="file.fileName.endsWith('.xls') || file.fileName.endsWith('.xlsx')" />
                  <img src="/public/icon/word.svg"
                    v-else-if="file.fileName.endsWith('.doc') || file.fileName.endsWith('.docx')" />
                  <img src="/public/icon/pdf.svg" v-if="file.fileName.endsWith('.pdf')" />
                  <img src="/public/icon/mp4.svg"
                    v-else-if="file.fileName.endsWith('.mov') || file.fileName.endsWith('.mp4')" />
                  <file-outlined v-else /> -->
                  <img src="/public/icon/icon-wrapper.svg"
                    v-if="file.fileName.endsWith('.xls') || file.fileName.endsWith('.xlsx')" />
                  <img src="/public/icon/word.svg"
                    v-else-if="file.fileName.endsWith('.doc') || file.fileName.endsWith('.docx')" />
                  <img src="/public/icon/pdf.svg" v-else-if="file.fileName.endsWith('.pdf')" />
                  <img src="/public/icon/mp4.svg"
                    v-else-if="file.fileName.endsWith('.mov') || file.fileName.endsWith('.mp4')" />
                  <img src="/public/icon/ppt.svg"
                    v-else-if="file.fileName.endsWith('.ppt') || file.fileName.endsWith('.pptx')" />
                  <img src="/public/icon/jpg.svg"
                    v-else-if="file.fileName.endsWith('.png') || file.fileName.endsWith('.jpg')" />
                  <img src="/public/icon/reader.svg" v-else />
                </div>
                <a :href="`${file.fileUrl}?filename=${file.fileName}`" target="_blank" class="file-link">
                  {{ file.fileName }}
                </a>
                <div class="file-actions" v-if="initialValueStatus.fileListShow">
                  <sync-outlined v-if="file.status === 'uploading'" spin />
                  <close-outlined v-else class="delete-icon" @click="removeFile(index)" />
                </div>
              </div>
            </div>

          </div>
          <div v-else>
            <FileUploader :max-size="5" :max-count="10" button-text="上传文件"
              :accept-types="['.jpg', '.jpeg', '.png', '.mp4', '.mov', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', '.pdf']"
              :initial-files="fileViewList" @file-uploaded="onFileUploaded" @file-removed="onFileRemoved"
              :showUploadButton="initialValueStatus.fileListShow" :tip-row="false" :show-del="showFileDelete"
              @upload-status-change="onUploadStatusChange" />
          </div>



        </div>



      </div>
      <!-- 底部按钮 -->
      <div class="bottom-buttons">
        <!-- <a-button @click="handleSave" :loading="saving" class="save-btn">保存</a-button> -->
        <a-button type="primary" @click="handleSubmit" :loading="submitting" :disabled="isUploading"
          class="submit-btn">提交</a-button>
      </div>
    </div>

  </div>
</template>
<script>

export default {
  name: 'BussApplyEquAcceptanceEdit'
}
</script>
<script setup>

import { computed, ref, onMounted, nextTick, watch, reactive, watchEffect } from 'vue';
import { message } from 'ant-design-vue';
import { EquipmentAcceptanceApi } from '@/api/buss/EquipmentAcceptanceApi';
import { BasicInformationApi } from '@/api/buss/BasicInformationApi';
import { FileUploadUrl } from '@/api/system/operation/FileApi';
import { getToken } from '@/utils/token-util';
import { EnumApi } from '@/api/common/enum';
import { API_BASE_PREFIX } from '@/config/setting';
import { useUserStore } from '@/store/modules/user';
import dayjs from 'dayjs';
import { SearchOutlined } from '@ant-design/icons-vue';
import { useRouter, useRoute } from 'vue-router';
import ApprovalRecord from '../components/ApprovalRecord.vue';
import CityTreeSelect from '@/components/CityTreeSelect/index.vue';
import FileUploader from '@/components/FileUploader/index.vue';
import InputUploader from '@/components/InputUploader/index.vue';
// 组件注册
const components = {
  ApprovalRecord,
  FileUploader
};

// 抽屉显示状态
const drawerVisible = ref(false);

// 显示审批记录
const showApprovalRecord = () => {
  drawerVisible.value = true;
};

// 关闭抽屉
const handleDrawerClose = () => {
  drawerVisible.value = false;
};

// 审批记录数据
const approvalRecords = ref([
  {
    name: '张三',
    role: '第三审核人',
    time: '2024-01-10 10:30',
    status: 'approved',
    statusText: '已通过'
  },
  {
    name: '李四',
    role: '第二审核人',
    time: '2024-01-10 10:50',
    status: 'pending',
    statusText: '已通过'
  },
  {
    name: '李五',
    role: '第一审核人',
    time: '2024-01-10 11:00',
    status: 'pending',
    statusText: '待审核'
  }
]);

const userStore = useUserStore();
const loading = ref(false);
const saving = ref(false);
const submitting = ref(false);
const router = useRouter();

// 业务标题和关联投资计划的独立字段
const businessTitle = ref('');
const investmentPlan = ref('');
const isTransfer = ref('true'); // 添加是否转入设备字段，默认为'1'（否）

// 上传相关配置
const uploadUrl = `${API_BASE_PREFIX}${FileUploadUrl}?secretFlag=0`;
const headers = {
  Authorization: getToken()
};

// 文件列表相关
const fileList = ref([]);
const fileViewList = ref([]);
const techFileList = ref([]); // 技术资料上传组件的文件列表
const techFileViewList = ref([]); // 技术资料显示列表
const uploadedFiles = ref([]);

const onFileUploaded = (file) => {
  console.log('文件上传成功:', file);
  if (!fileList.value) fileList.value = [];
  if (!fileViewList.value) fileViewList.value = [];
  fileList.value.push(file);
  fileViewList.value.push(file);
};

const onFileRemoved = (file) => {
  console.log('文件已删除:', file);
  fileList.value = fileList.value.filter(f => f.uid !== file.uid);
  fileViewList.value = fileViewList.value.filter(f => f.uid !== file.uid);
};

const isShowbutton = ref(true);
const showDelete = ref(true);
const showFileDelete = ref(true);
const onTechFileUploaded = (file) => {
  console.log('技术文件上传成功:', file);

  if (!techFileList.value) {
    techFileList.value = [];
  }
 
  techFileList.value.push(file);
  techFileViewList.value.push(file);
  isShowbutton.value = false;
  showDelete.value = true;
};

const onTechFileRemoved = (file) => {
  console.log('技术文件已删除:', file);
  techFileList.value = techFileList.value.filter(f => f.uid !== file.uid);
  techFileViewList.value = techFileViewList.value.filter(f => f.uid !== file.uid);
  isShowbutton.value = true;
  showDelete.value = false;
};
// 在 script 部分添加上传状态变量
const isUploading = ref(false);

// 添加上传状态变更处理函数
const onUploadStatusChange = (status) => {
  isUploading.value = status;
};
// 定义验证规则
const rules = {
  contractNumber: [{ required: true, message: '请输入合同编号' }],
  equType: [{ required: true, message: '请选择设备类别' }],
  equSubType: [{ required: true, message: '请选择设备种类' }],
  equName: [{ required: true, message: '请输入设备名称' }],
  equModel: [{ required: true, message: '请输入规格型号' }],
  equNature: [{ required: true, message: '请选择设备性质' }],
  equSource: [{ required: true, message: '请选择设备来源' }],
  importance: [{ required: true, message: '请选择设备重要性' }],
  propertyOrg: [{ required: true, message: '请选择产权单位' }],
  managementOrg: [{ required: true, message: '请选择管理单位' }],
  useOrg: [{ required: true, message: '请选择使用单位' }],
  equCondition: [{ required: true, message: '请输入设备状态' }],
  storageLocationStr: [{ required: true, message: '请输入存放地点' }]
};

const columns = [
  {
    title: '序号',
    width: 80,
    customRender: ({ text, record, index }) => {
      return `${index + 1}`;
    }
  },
  { title: '名称及型号', dataIndex: 'nameModel', width: 200, },
  { title: '单位', dataIndex: 'unit', width: 150, },
  { title: '数量', dataIndex: 'num', width: 120, },
  { title: '生产厂家', dataIndex: 'manufacturer', width: 200, },
  { title: '出厂编号', dataIndex: 'factoryNumber', width: 200, },
  // 右侧固定列
  { title: '操作', key: 'action', width: 80, fixed: 'right' }
]

const columns1 = [
  {
    title: '序号',
    width: 80,
    customRender: ({ text, record, index }) => {
      return `${index + 1}`;
    }
  },
  { title: '名称及型号', dataIndex: 'nameModel', width: 200, },
  { title: '单位', dataIndex: 'unit', width: 150, },
  { title: '数量', dataIndex: 'num', width: 120, },
  { title: '生产厂家', dataIndex: 'manufacturer', width: 200, },
  { title: '出厂编号', dataIndex: 'factoryNumber', width: 200, },
]

// 验证表单数据
const validateForm = () => {
  // 按顺序定义需要验证的字段
  const fieldsToValidate = [
    // { field: 'contractNumber', label: '合同编号', type: 'input' },
    // { field: 'acceptanceDate', label: '验收日期', type: 'date' },
    // { field: 'productionDate', label: '出厂日期', type: 'date' }, // 确保包含出厂日期
    // { field: 'equType', label: '设备类别', type: 'select' },
    // { field: 'equSubType', label: '设备种类', type: 'select' },
    // { field: 'equName', label: '设备名称', type: 'input' },
    // { field: 'equModel', label: '规格型号', type: 'input' },
    // { field: 'equNature', label: '设备性质', type: 'select' },
    // { field: 'equSource', label: '设备来源', type: 'select' },
    // { field: 'importance', label: '设备重要性', type: 'select' },
    // { field: 'propertyOrg', label: '产权单位', type: 'select' },
    // { field: 'managementOrg', label: '管理单位', type: 'select' },
    // { field: 'useOrg', label: '使用单位', type: 'select' },
    // { field: 'equCondition', label: '设备状态', type: 'input' },
    // { field: 'storageLocationStr', label: '存放地点', type: 'input' }
  ];

  // 依次验证每个字段
  for (const { field, label, type } of fieldsToValidate) {
    if (!formData.value[field]) {
      message.error(`请${type === 'select' ? '选择' : type === 'date' ? '选择' : '输入'}${label}`);

      const formItem = document.querySelector(`[data-field="${field}"]`);
      if (formItem) {
        // 滚动到可视区域，并确保元素在视图中间
        formItem.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // 使用 nextTick 确保 DOM 更新后再执行点击操作
        nextTick(() => {
          setTimeout(() => {
            switch (type) {
              case 'input': {
                const input = formItem.querySelector('input');
                input?.focus();
                break;
              }
              case 'select': {
                const select = formItem.querySelector('.ant-select-selector');
                select?.click();
                break;
              }
              case 'date': {
                const datePicker = formItem.querySelector('.date-picker');
                if (datePicker) {
                  // 先聚焦
                  const input = datePicker.querySelector('input');
                  input?.focus();
                  // 然后触发点击以打开日期选择面板
                  setTimeout(() => {
                    datePicker.click();
                  }, 100);
                }
                break;
              }
            }
          }, 500); // 等待滚动完成后再聚焦
        });
      }
      return false;
    }
  }

  return true;
};

// 构建提交数据的方法
const buildRequestData = () => {
  const requestData = {
    bussWorksheet: {
      name: businessTitle.value
    },
    bussTransferForm: {
      ...bussTransferForm.value,
      applyTitle: businessTitle.value,
      investmentPlan: investmentPlan.value,
      isTransfer: isTransfer.value
    },
    bussEquipmentProcessTrackingList: [
      {
        ...formData.value,
        technicalFileList: techFileList.value,
        fileList: fileViewList.value,
        bussEquipmentAccessoryList: attachments.value.map(item => ({
          nameModel: item.nameModel,
          unit: item.unit,
          num: item.num,
          manufacturer: item.manufacturer,
          factoryNumber: item.factoryNumber
        })),
      }
    ]
  };

  return requestData;
};

// 上传前验证文件
const beforeFileUpload = (file) => {
  // 检查文件数量限制
  if (fileList.value.length >= 10) {
    message.error('最多只能上传10个文件！');
    return false;
  }

  // // 检查文件大小（5MB）
  const isLessThan5M = file.size / 1024 / 1024 < 5;
  if (!isLessThan5M) {
    message.error('文件大小不能超过5MB！');
    return false;
  }

  return true;
};

// 保存方法
const handleSave = async () => {
  try {
    saving.value = true;
    const params = buildRequestData();
    const res = await EquipmentAcceptanceApi.save(params);
    if (res.success) {
      message.success('保存成功');
      // 从返回值中完全覆盖表单数据
      if (res.data) {
        // 完全覆盖bussTransferForm数据
        if (res.data.bussTransferForm) {
          bussTransferForm.value = res.data.bussTransferForm;
        }

        // 完全覆盖bussWorksheet数据
        if (res.data.bussWorksheet) {
          bussWorksheet.value = res.data.bussWorksheet;
          businessTitle.value = res.data.bussWorksheet.name;
        }


      }
    } else {
      message.error('保存失败');
    }
  } catch (error) {
    message.error(error.message.split(",")[0]);
  } finally {
    saving.value = false;
  }
};

const processDefinitionId = ref('');

// 获取流程定义ID
const getProcessDefinitionId = async () => {
  try {
    const { data } = await EnumApi.getProcessDefinitionByKey({
      key: 'equipment_acceptance_edit'
    });
    if (data) {
      processDefinitionId.value = data;
    } else {
      message.error('获取流程定义ID失败');
    }
  } catch (error) {
    message.error('获取流程定义ID失败');
  }
};

// 在提交和保存前处理验收单号
const processAcceptanceNumberSubmit = () => {
  console.log(formData.value.acceptanceNumberYan, formData.value.acceptanceChar);
  // 检查两个字段是否都有值或都没有值
  const hasYan = !!formData.value.acceptanceNumberYan;
  const hasChar = !!formData.value.acceptanceChar;

  // 如果一个有值一个没值，提示用户
  if (hasYan !== hasChar) {
    message.warning('验收单号的年份和数字必须同时填写');
    formData.value.acceptanceNumber = null;
    return false; // 返回false表示验证不通过
  }

  // 如果都没值，设为null
  if (!hasYan && !hasChar) {
    formData.value.acceptanceNumber = null;
    return true; // 返回true表示验证通过
  }

  // 如果都有值，生成完整验收单号
  formData.value.acceptanceNumber =
    `${formData.value.acceptanceNumberYan}验资${formData.value.acceptanceChar}号`;
  return true; // 返回true表示验证通过
};
// 提交方法
const handleSubmit = async () => {
  try {
    // 执行验证
    if (!validateForm()) {
      return; // 验证不通过直接返回,不执行提交
    }


    // 处理验收单号
    if (!processAcceptanceNumberSubmit()) {
      submitting.value = false;
      return;
    }

    submitting.value = true;

    // 构建内部formData结构
    const innerFormData = {
      formDatas: {
        bussWorksheet: {
          name: businessTitle.value
        },
        bussTransferForm: {
          ...bussTransferForm.value,
          apply_title: businessTitle.value,
          investmentPlan: investmentPlan.value,
          isTransfer: isTransfer.value
        },
        bussEquipmentProcessTrackingEdit:
        {
          ...formData.value,
          equId: route.query.id,
          technicalFileList: techFileList.value.map(file => ({
            fileId: file.fileId
          })),
          bussEquipmentAccessoryList: attachments.value.map(item => ({
            equId: item.equId,
            baseCode: item.baseCode,
            nameModel: item.nameModel,
            unit: item.unit,
            num: item.num,
            manufacturer: item.manufacturer,
            factoryNumber: item.factoryNumber
          })),
          fileList: fileViewList.value.map(file => ({
            fileId: file.fileId
          }))
        }
      }
    };

    // 构建最终提交数据结构
    const submitData = {
      processDefinitionId: processDefinitionId.value.id,
      variables: {
        formData: JSON.stringify(innerFormData)
      }
    };

    const res = await EquipmentAcceptanceApi.submit(submitData);

    if (res.success) {
      message.success('提交成功');
      router.push('/buss/regEquLedger');
    } else {
      message.error('提交失败');
    }
  } catch (error) {
    message.error(error.message.split(",")[0]);
  } finally {
    submitting.value = false;
  }
};
const bussTransferForm = ref({
  applyUser: '', // 申请人ID
  applyUserStr: '', // 申请人姓名
  applyOrg: '', // 申请单位ID
  applyOrgStr: '', // 申请单位名称
  applyDate: '', // 申请日期
  apply_title: '', // 申请标题
  investmentPlan: '', // 投资计划
  isTransfer: '1' // 是否转入设备
});
const bussWorksheet = ref({
  id: null,
  name: ''
});
const formData = ref({
  equId: null,
  id: null,
  baseCode: '', // 基础编码
  code: '', // 编码
  equType: null, // 设备类别
  equSubType: null, // 设备种类
  equName: null, // 设备名称
  equModel: null, // 规格型号
  equModelInfo: '', // 型号备注
  equNature: null, // 设备性质
  purchaseYear: '', // 购置年度
  equSource: null, // 设备来源
  unit: '', // 单位
  num: null, // 数量
  equContractPriceTax: null, // 设备合同价（含税）
  manufacturer: '', // 生产厂家
  factoryNumber: '', // 出厂编号
  productionDate: null, // 出厂日期
  contractNumber: '', // 合同编号
  acceptanceNumberYan: null, // 验收单号
  acceptanceChar: '', // 验字
  acceptanceDate: null, // 验收日期
  power: '', // 功率
  importance: null, // 设备重要性
  modelCode: '', // 设备型号编码
  serviceInterval: '', // 保养周期
  propertyOrg: null, // 产权单位
  managementStatus: null, // 管理状态
  managementOrg: null, // 管理单位
  equCondition: null, // 设备状态
  useOrg: null, // 使用单位
  useOrgStr: '', // 使用单位
  storageLocationStr: null, // 存放地点
  financialNumber: '', // 财务卡片编号
  financialOrg: '', // 财务组织
  financialOriginalValue: null, // 财务原值
  fixedAssets: '', // 固定资产分类
  sourceOfFunds: '', // 资金来源
  deferredIncome: '', // 递延收益
  alreadyAccruedMonths: '', // 已计提折旧月份
  depreciationMethod: null, // 折旧方式
  depreciationPeriod: '', // 折旧年限
  depreciationMonth: '', // 折旧月份
  residualRate: null, // 残值率
  netSalvage: null, // 预计净残值
  historicalOriginalValue: null, // 历史原值
  depreciationAmount: null, // 累计折旧额
  currentMonthDepreciationAmount: null, // 当月折旧额
  netWorth: null, // 净值
  taxRate: 13, // 税率
});

// 附件列表数据
const attachments = ref([

]);

// 添加新附件
const addAttachment = () => {
  attachments.value.push({
    nameModel: '',
    unit: '',
    num: '',
    manufacturer: '',
    factoryNumber: ''
  });
};

// 删除附件
const removeAttachment = (index) => {
  attachments.value.splice(index, 1);
};

// 处理普通附件上传
const handleChange = (info) => {
  console.log('当前上传的文件:', info.file);
  console.log('所有文件列表:', info.fileList);

  // 保持原始fileList与上传组件同步
  //如果fileList有的话。直接插入
  if (fileList.value.length > 0) {
    if (info.fileList.length > 0) {
      for (var i = 0; i < info.fileList.length; i++) {
        fileList.value.push(info.fileList[i]);
      }
    }

  } else {
    fileList.value = info.fileList;
  }


  // 更新显示用的文件列表
  fileViewList.value = info.fileList
    .filter(file => file.status === 'done')  // 只保留上传完成的文件
    .map(file => ({
      fileId: file.response.data.fileId, fileUrl: file.response.data.fileUrl,
      fileName: file.name,
      uid: file.uid,
      status: file.status
    }));

  // 处理单个文件的状态提示
  if (info.file.status === 'error') {
    message.error('上传失败');
  }
};

// 删除文件
const removeFile = (index) => {
  fileViewList.value.splice(index, 1);
  fileList.value.splice(index, 1);
};

// 处理技术资料上传
const handleTechFileChange = (info) => {
  // 保持原始列表与上传组件同步
  techFileList.value = info.fileList;
  techFileList.value[0].fileName = info.file.name;
  console.log(techFileList.value)
  if (info.file.status === 'done' && info.file.response) {
    const response = info.file.response;
    if (response.success) {
      // 更新显示列表
      techFileList.value = [{
        fileId: response.data.fileId,
        fileName: info.file.name,
        fileUrl: response.data.fileUrl,
        uid: info.file.uid,
        status: 'done'
      }];

      // 更新formData中的techFile用于显示
      formData.value.techFile = {
        fileId: response.data.fileId,
        fileName: info.file.name
      };
    } else {
      message.error('技术资料上传失败');
    }
  } else if (info.file.status === 'error') {
    message.error('上传失败');
  }
};

// 删除技术资料
const removeTechFile = () => {
  formData.value.techFile = null;
  techFileList.value = [];
};

// 删除普通附件
const handleRemove = (file) => {
  // 从显示列表中删除
  const viewIndex = fileViewList.value.findIndex(f => f.uid === file.uid);
  if (viewIndex > -1) {
    fileViewList.value.splice(viewIndex, 1);
  }

  // 从上传列表中删除
  const fileIndex = fileList.value.findIndex(f => f.uid === file.uid);
  if (fileIndex > -1) {
    fileList.value.splice(fileIndex, 1);
  }
};


// 定义初始值状态对象
const initialValueStatus = ref({
  acceptanceNumberYan: false,
  acceptanceChar: false,
  power: false,
  importance: false,
  modelCode: false,
  equSource: false,
  fileListShow: false,
  storageLocationStr: false,
  addAttachmentShow: false,
  techFileShow: false,
  contractNumber: false,
  financialNumberShow: false,
  financialOrgShow: false,
  financialOriginalValueShow: false,
  fixedAssetsShow: false,
  sourceOfFundsShow: false,
  deferredIncomeShow: false,
  alreadyAccruedMonthsShow: false,
  depreciationMethodShow: false,
  depreciationPeriodShow: false,
  depreciationMonthShow: false,
  residualRateShow: false,
  netSalvageShow: false,
  historicalOriginalValueShow: false,
  depreciationAmountShow: false,
  currentMonthDepreciationAmountShow: false,
  netWorthShow: false,
  taxRateShow: false,
  factoryNumber: false,
  // ... 其他需要判断的字段
});

// 解析验收单号
const parseAcceptanceNumber = (acceptanceNumber) => {
  if (!acceptanceNumber) return;

  const match = acceptanceNumber.match(/^(.+)验资(.+)号$/);
  if (match) {
    formData.value.acceptanceNumberYan = match[1];
    formData.value.acceptanceChar = match[2];
  }
};

const storageLocationOptions = ref([]);
// 处理存放地点变化
const handleStorageLocationChange = (value) => {
  formData.value.storageLocationStr = value;  // 保存数字 ID
};
const initializeData = async (id) => {
  loading.value = true;
  try {
    const loginUser = computed(() => userStore.info ?? {});
    // 获取存放地点列表
    console.log(1111)
    // 新记录时初始化申请信息
    bussTransferForm.value = {
      applyUser: loginUser.value.userId,
      applyUserStr: loginUser.value.realName,
      applyOrg: loginUser.value.organizationId,
      applyOrgStr: loginUser.value.organizationName,
      applyDate: dayjs().format('YYYY-MM-DD')
    };
    const { data } = await EquipmentAcceptanceApi.getEditData({ equId: id });
    console.log(1111, data);
    if (data.bussRegisteredEquipment) {
      formData.value = data.bussRegisteredEquipment;
      const equipmentData = data.bussRegisteredEquipment;
      // 先解析验收单号
      parseAcceptanceNumber(formData.value.acceptanceNumber);

      initialValueStatus.value.acceptanceNumberYan = !formData.value.acceptanceNumberYan;
      initialValueStatus.value.acceptanceChar = !formData.value.acceptanceChar;
      initialValueStatus.value.contractNumber = !formData.value.contractNumber;
      // 设置其他字段的初始值状态
      initialValueStatus.value.power = !formData.value.power;
      initialValueStatus.value.importance = !formData.value.importance;
      initialValueStatus.value.modelCode = !formData.value.modelCode;
      initialValueStatus.value.equSource = !formData.value.equSource;
      initialValueStatus.value.fileListShow = equipmentData.fileList?.length == 0;
      initialValueStatus.value.storageLocationStr = !formData.value.storageLocationStr;
      initialValueStatus.value.addAttachmentShow = !(equipmentData.bussEquipmentAccessoryList?.length > 0);
      initialValueStatus.value.techFileShow = equipmentData.technicalFileList?.length == 0;
      initialValueStatus.value.acceptanceDate = !formData.value.acceptanceDate;
      initialValueStatus.value.equModelInfo = !formData.value.equModelInfo;
      initialValueStatus.value.financialNumberShow = !formData.value.financialNumber;
      initialValueStatus.value.financialOrgShow = !formData.value.financialOrg;
      initialValueStatus.value.financialOriginalValueShow = !formData.value.financialOriginalValue;
      initialValueStatus.value.fixedAssetsShow = !formData.value.fixedAssets;
      initialValueStatus.value.sourceOfFundsShow = !formData.value.sourceOfFunds;
      initialValueStatus.value.deferredIncomeShow = !formData.value.deferredIncome;
      initialValueStatus.value.alreadyAccruedMonthsShow = !formData.value.alreadyAccruedMonths;
      initialValueStatus.value.depreciationMethodShow = !formData.value.depreciationMethod;
      initialValueStatus.value.depreciationPeriodShow = !formData.value.depreciationPeriod;
      initialValueStatus.value.depreciationMonthShow = !formData.value.depreciationMonth;
      initialValueStatus.value.residualRateShow = !formData.value.residualRate;
      initialValueStatus.value.netSalvageShow = !formData.value.netSalvage;
      initialValueStatus.value.historicalOriginalValueShow = !formData.value.historicalOriginalValue;
      initialValueStatus.value.depreciationAmountShow = !formData.value.depreciationAmount;
      initialValueStatus.value.currentMonthDepreciationAmountShow = !formData.value.currentMonthDepreciationAmount;
      initialValueStatus.value.netWorthShow = !formData.value.netWorth;
      initialValueStatus.value.taxRateShow = !formData.value.taxRate;
      initialValueStatus.value.factoryNumber = !formData.value.factoryNumber;
      // 处理主要配件数据
      if (equipmentData.bussEquipmentAccessoryList?.length > 0) {
        attachments.value = equipmentData.bussEquipmentAccessoryList.map(item => ({
          nameModel: item.nameModel || '',
          unit: item.unit || '',
          num: item.num || '',
          manufacturer: item.manufacturer || '',
          factoryNumber: item.factoryNumber || ''
        }));
      } else {
        attachments.value = [{
          nameModel: '',
          unit: '',
          num: '',
          manufacturer: '',
          factoryNumber: ''
        }]
      }
      console.log(1111)
      // 处理附件数据
      if (equipmentData.fileList?.length > 0) {
        fileViewList.value = equipmentData.fileList.map(file => ({
          fileId: file.fileId,
          fileName: file.fileOriginName,  // 后台返回的是 fileName，映射到前端的 fileName
          fileUrl: file.fileUrl,
          uid: file.fileId
        })) || [];

        showFileDelete.value = false;
      }
      console.log(222)
      // 处理技术资料
      if (equipmentData.technicalFileList?.length > 0) {
        techFileList.value = equipmentData.technicalFileList
        techFileList.value[0].fileName = equipmentData.technicalFileList[0].fileOriginName;
        techFileList.value[0].fileUrl = equipmentData.technicalFileList[0].fileUrl;
        techFileList.value[0].uid = equipmentData.technicalFileList[0].fileId;
        techFileList.value[0].status = 'done';

        // // 更新formData中的技术资料显示
        // formData.value.techFile = {
        //   fileId: techFileViewList.value[0].fileId,
        //   fileName: techFileViewList.value[0].fileOriginName
        // };

        //如果有技术资料，则隐藏上传按钮
        //如果没有。可以正常使用上传
        isShowbutton.value = false;
        showDelete.value = false;

      }
      console.log(4444)
      // const addressRes = await EnumApi.getAddrOrgList({ pId: formData.value.useOrg });
      // storageLocationOptions.value = addressRes.data
      formData.value.propertyOrgName = formData.value.propertyOrgStr;
      formData.value.managementOrgName = formData.value.managementOrgStr;
    }
  } catch (error) {
    message.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

// 添加流程图数据的响应式变量
const flowableData = ref(null);
const route = useRoute();

const containerRef = ref()
const containerWidth = ref(0)

const updateContainerWidth = () => {
  if (containerRef.value) {
    containerWidth.value = containerRef.value.offsetWidth
  }
}

onMounted(async () => {
  //从router获取query中的id的值。
  var id = route.query.id;
  if (id * 1) {
    initializeData(route.query.id);
    formData.value.equId = route.query.id;
    localStorage.setItem("equId", route.query.id)
  } else {
    id = localStorage.getItem("equId");
    if (id * 1) {
      initializeData(id);
      formData.value.equId = id;
    }
  }


  // var id = localStorage.getItem("equId");
  // console.log(1111,id);
  // console.log(route.query.id)
  // if(id*1){
  //   initializeData(id);
  //   formData.value.equId = id;
  //   localStorage.setItem("equId",id)
  // }else if(route.query.id){
  //   initializeData(route.query.id);
  //   formData.value.equId = route.query.id;
  //   localStorage.setItem("equId",route.query.id)
  // }


  //如果没有

  getEquTypeOptions();
  getProcessDefinitionId();
  initEnumData();
  // try {

  //   const res = await EquipmentAcceptanceApi.getFlowAbleMap();
  //   if (res.success) {
  //     flowableData.value = res.data;
  //   }
  // } catch (error) {
  //   console.error('获取流程图数据失败:', error);
  // }

  updateContainerWidth()
  window.addEventListener('resize', updateContainerWidth)

});


// 在 setup 中添加
const equTypeOptions = ref([]);      // 设备类别选项
const equSubTypeOptions = ref([]);   // 设备种类选项
const equNameOptions = ref([]);      // 设备名称选项
const equModelOptions = ref([]);     // 规格型号选项
const equNatureOptions = ref([]);    // 设备性质选项
const equSourceOptions = ref([]);    // 设备来源选项
const equImportanceOptions = ref([]); // 设备重要性选项
const managementStatusOptions = ref([]); // 管理状态选项
const equConditionOptions = ref([]); // 设备状态选项
const fixedAssetsOptions = ref([]); // 固定资产分类选项
const depreciationMethodOptions = ref([]); // 折旧方式选项

// 获取设备类别（第一级）
const getEquTypeOptions = async () => {
  try {
    const res = await BasicInformationApi.getEquData();
    equTypeOptions.value = res.data;
  } catch (error) {
    message.error('获取设备类别失败');
  }
};

// 设备类别改变
const handleEquTypeChange = async (value) => {
  formData.value.equSubType = null;
  formData.value.equName = null;
  formData.value.equModel = null;
  equSubTypeOptions.value = [];
  equNameOptions.value = [];
  equModelOptions.value = [];

  if (value) {
    try {
      const res = await BasicInformationApi.getEquData({ pId: value });
      equSubTypeOptions.value = res.data;
    } catch (error) {
      message.error('获取设备种类失败');
    }
  }
};

// 设备种类改变
const handleEquSubTypeChange = async (value) => {
  formData.value.equName = null;
  formData.value.equModel = null;
  equNameOptions.value = [];
  equModelOptions.value = [];

  if (value) {
    try {
      const res = await BasicInformationApi.getEquData({ pId: value });
      equNameOptions.value = res.data;
    } catch (error) {
      message.error('获取设备名称失败');
    }
  }
};

// 设备名称改变
const handleEquNameChange = async (value) => {
  formData.value.equModel = null;
  equModelOptions.value = [];

  if (value) {
    try {
      const res = await BasicInformationApi.getEquData({ pId: value });
      equModelOptions.value = res.data;
    } catch (error) {
      message.error('获取规格型号失败');
    }
  }
};

// 初始化枚举数据
const initEnumData = async () => {
  try {
    // 获取设备性质枚举
    const equNatureData = await EnumApi.getEnumList({ enumName: 'EquNatureEnum' });
    equNatureOptions.value = equNatureData.data;

    // 获取设备来源枚举
    const equSourceData = await EnumApi.getEnumList({ enumName: 'EquSourceEnum' });
    equSourceOptions.value = equSourceData.data;

    // 获取设备重要性枚举
    const equImportanceData = await EnumApi.getEnumList({ enumName: 'EquImportanceEnum' });
    equImportanceOptions.value = equImportanceData.data;

    // 获取管理状态枚举
    const managementStatusData = await EnumApi.getEnumList({ enumName: 'ManagementStatusEnum' });
    managementStatusOptions.value = managementStatusData.data;

    // 获取设备状态枚举
    const equConditionData = await EnumApi.getEnumList({ enumName: 'EquConditionEnum' });
    equConditionOptions.value = equConditionData.data;

    const fixedAssetsOptionsData = await EnumApi.getEnumList({ enumName: 'FixedAssetsEnum' });
    fixedAssetsOptions.value = fixedAssetsOptionsData.data;


    // 获取折旧方式枚举
    const depreciationMethodData = await EnumApi.getEnumList({ enumName: 'DepreciationMethodEnum' });
    depreciationMethodOptions.value = depreciationMethodData.data;

    // 设置默认值
    formData.value.managementStatus = 1;
    formData.value.equCondition = 1;
  } catch (error) {
    message.error('获取枚举数据失败');
  }
};

// 转入设备编号
const transferEquipmentCode = ref('');

// 查询转入设备信息
const searchTransferEquipment = async () => {
  if (!transferEquipmentCode.value) {
    message.warning('请输入转入设备编号');
    return;
  }

  try {
    // 调用查询接口
    const { data } = await EquipmentApi.getEquipmentByCode(transferEquipmentCode.value);

    if (data) {
      // 查询到设备，更新表单数据
      formData.value = {
        ...formData.value,
        code: data.code,
        equType: data.equType,
        equSubType: data.equSubType,
        equName: data.equName,
        equModel: data.equModel,
        equModelNote: data.equModelNote,
        equNature: data.equNature,
        purchaseYear: data.purchaseYear,
        equSource: data.equSource,
        unit: data.unit,
        num: data.num,
        equContractPriceTax: data.equContractPriceTax,
        manufacturer: data.manufacturer,
        factoryNumber: data.factoryNumber,
        productionDate: data.productionDate,
        contractNumber: data.contractNumber,
        // ... 其他需要复制的字段
      };

      // 触发相关联的数据加载
      if (data.equType) {
        await handleEquTypeChange(data.equType);
      }
      if (data.equSubType) {
        await handleEquSubTypeChange(data.equSubType);
      }
      if (data.equName) {
        await handleEquNameChange(data.equName);
      }

      message.success('设备信息加载成功');
    } else {
      message.warning('未找到对应的设备信息');
    }
  } catch (error) {
    message.error('查询设备信息失败');
  }
};

// 监听是否为转入设备的变化
watch(() => isTransfer.value, (newValue) => {
  if (newValue === '1') {
    // 如果切换为非转入设备，清空转入设备编号
    transferEquipmentCode.value = '';
  }
});

const addressTreeData = ref([]);
const addressLoading = ref(false);


const loadAddressTree = async () => {
  try {
    addressLoading.value = true;
    const res = await EnumApi.getAddressTree();
    addressTreeData.value = res.data;
    console.log('Address tree data:', res.data); // 调试用
  } catch (error) {
    message.error('加载存放地点数据失败');
  } finally {
    addressLoading.value = false;
  }
};

nextTick(() => {
  setTimeout(() => {
    loadAddressTree();
  }, 0);
});

</script>

<style lang="less" scoped>
.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.approval-record-btn {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  z-index: 100;

  .bg-image {
    display: block;
    max-width: 100%;
    height: auto;
    object-fit: contain;
    background-color: transparent;
  }

  .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .icon {
      width: 15px;
      height: 15px;
      margin-bottom: 4px;
    }

    .text {
      writing-mode: vertical-lr;
      font-size: 14px;
      color: #333;
      letter-spacing: 2px;
    }
  }
}

.equipment-acceptance {
  width: 100%;
  max-width: 1920px; // 最大宽度限制
  height: calc(100vh - 100px);
  overflow: auto;
  margin: 0 auto; // 居中
  padding: 24px 9.06vw 24px 8.18vw; // 转换为vw: 174/1920*100=9.06, 157/1920*100=8.18
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);

  .form-title {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 24px;
  }

  .section {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;

      .blue-bar {
        width: 3px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
      }
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: clamp(16px, 2vw, 24px);
    margin-bottom: 24px;
    justify-content: space-between;

    .form-item {
      display: flex;
      flex-direction: column;
      min-width: 300px; // 最小宽度
      max-width: 300px; // 最大宽度

      .label {
        height: 32px;
        line-height: 32px;
        margin-bottom: 8px;
        color: #0C0B0B;

        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 4px;
        }
      }

      .value {
        width: 100%;
        height: 32px;
        color: #666;

        :deep(.ant-input),
        :deep(.ant-select),
        :deep(.ant-picker),
        :deep(.ant-input-number) {
          width: 100%;
          height: 32px;
        }

        :deep(.ant-input-number-input) {
          height: 16px;
          line-height: 16px;
        }

        :deep(.ant-select-selector) {
          height: 32px !important;

          .ant-select-selection-item {
            line-height: 32px;
          }
        }

        :deep(.ant-picker) {
          .ant-picker-input {
            height: 16px;
            line-height: 16px;

            input {
              height: 16px;
              line-height: 16px;
            }
          }
        }
      }
    }
  }

  .acceptance-no {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    max-width: 300px; // 与其他表单项保持一致的最大宽度

    :deep(.first-input) {
      width: 65% !important; // 验资前面的输入框占65%
    }

    :deep(.second-input) {
      width: 35% !important; // 号前面的输入框占35%
    }

    .separator {
      color: #666;
      white-space: nowrap;
    }
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .equipment-acceptance {
    padding: 24px 6vw;

    .form-grid {
      gap: 16px;
    }
  }
}

@media screen and (max-width: 1366px) {
  .equipment-acceptance {
    padding: 24px 4vw;

    .form-grid {
      grid-template-columns: repeat(2, minmax(300px, 1fr));
    }
  }
}

@media screen and (max-width: 1024px) {
  .equipment-acceptance {
    padding: 24px 2vw;

    .form-grid {
      grid-template-columns: repeat(1, 1fr);

      .form-item {
        max-width: none;
      }
    }
  }
}

.percentage-input {
  position: relative;
  display: flex;
  align-items: center;

  .ant-input {
    width: 100%;
  }

  .percentage-symbol {
    position: absolute;
    right: 12px;
    color: #666;
  }
}

.attachment-table {
  width: 89%;
  border: 1px solid #e8e8e8;
  border-radius: 2px;


  .table-header,
  .table-row,
  .add-row {
    // 添加add-row到统一高度设置中
    display: flex;
    padding: 12px 8px;

    border-bottom: 1px solid #e8e8e8;
    align-items: center;
    gap: 12px;
    min-height: 56px; // 统一设置最小高度
  }

  .table-header {
    font-weight: 500;
  }

  .table-row:last-child {
    border-bottom: none;
  }

  // 使用百分比和flex布局
  .col-serial {
    width: 5%; // 序号列较窄
    min-width: 40px;
  }

  .col-name {
    width: 25%; // 名称及型号需要较大空间
    min-width: 180px;
  }

  .col-unit {
    width: 10%; // 单位列较窄
    min-width: 80px;
  }

  .col-quantity {
    width: 10%; // 数量列较窄
    min-width: 80px;
  }

  .col-manufacturer {
    width: 20%; // 生产厂家需要适中空间
    min-width: 150px;
  }

  .col-serial-no {
    width: 20%; // 出厂编号需要适中空间
    min-width: 150px;
  }

  .col-action {
    width: 10%; // 操作列较窄
    min-width: 60px;
    text-align: center;

    .delete-btn {
      color: #FF4D4F;
      cursor: pointer;
    }
  }

  :deep(.ant-input),
  :deep(.ant-input-number) {
    width: 100%;
  }

  // 响应式调整
  @media screen and (max-width: 1366px) {
    .col-name {
      width: 22%; // 较小屏幕时稍微压缩名称列
    }

    .col-manufacturer,
    .col-serial-no {
      width: 18%; // 压缩这两列
    }
  }

  @media screen and (max-width: 1024px) {
    overflow-x: auto; // 当屏幕太小时允许横向滚动

    .table-header,
    .table-row {
      min-width: 900px; // 确保在小屏幕上内容不会过度压缩
    }
  }

  .add-row {
    justify-content: center; // 水平居中
    align-items: center; // 垂直居中
    cursor: pointer;
    border-bottom: none; // 最后一行不需要底部边框

    a {
      display: flex;
      align-items: center; // 图标和文字垂直居中
      color: #1890ff;

      .anticon {
        margin-right: 4px;
      }
    }

    &:hover {
      background: #f5f5f5;
    }
  }
}

.upload-section1 {
  padding: 16px;


  .upload-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .upload-button {
      display: inline-flex;
      align-items: center;
      padding: 4px 15px;
      background: #0085FF;
      border-radius: 2px;
      color: white;
      cursor: pointer;
      font-size: 14px;

      .anticon {
        margin-right: 4px;
      }



    }

    .upload-tip {
      margin-left: 16px;
      color: #999;
    }
  }

  .file-list {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    //display: grid;
    //grid-template-columns: repeat(3, 0.3fr);
    //gap: 16px;

    .file-item {
      display: flex;
      align-items: center;
      padding: 8px;
      // background: #F7F9FC;
      border-radius: 2px;
      width: 32%;

      .file-link {
        word-break: break-all;
      }

      .file-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #2D5CF6;
      }

      .file-name {
        flex: 1;
        font-size: 14px;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-actions {
        margin-left: 8px;

        .delete-icon {
          color: #999;
          cursor: pointer;

          &:hover {
            color: #FF4B4B;
          }
        }

        .anticon-sync {
          color: #2D5CF6;
        }
      }
    }
  }
}

.tech-file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 12px;

  border-radius: 2px;

  .file-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      color: #2D5CF6;
    }

    .file-name {
      color: #333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .delete-icon {
    color: #999;
    cursor: pointer;

    &:hover {
      color: #ff4d4f;
    }
  }
}

.tech-upload-button {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border: 1px solid #0085FF;
  border-radius: 2px;
  cursor: pointer;
  color: #0085FF;
  font-size: 14px;

  .anticon {
    margin-right: 4px;
    font-size: 14px;
  }

  &:hover {
    color: #2D5CF6;
    border-color: #2D5CF6;
  }
}

.value {
  :deep(.ant-input-affix-wrapper) {
    .ant-input-suffix {
      color: #666;
    }
  }
}

.bottom-buttons {
  margin-top: 80px;
  margin-bottom: 89rpx;
  bottom: 180px; // 修改为距离底部80px
  left: 0;
  right: 0;
  padding: 0 32px; // 调整左右内边距
  text-align: right;
  z-index: 100;

  .save-btn {
    height: 32px;
    padding: 0 16px;
    border: 1px solid #DCDFE6;
    color: #606266;
    background: transparent !important;
    border-radius: 4px;

    &:hover {
      border-color: #409EFF;
      color: #409EFF;
    }
  }

  .submit-btn {
    height: 32px;
    padding: 0 16px;
    margin-left: 27px; // 调整按钮之间的间距
    background: #409EFF;
    border-radius: 4px;
    border: none;

    &:hover {
      background: #66B1FF;
    }
  }
}

:deep(.equipment-acceptance .form-grid .form-item .value .ant-input-number-input) {
  height: 2rem !important;
  line-height: 2rem !important;
  display: flex !important;
  align-items: center !important;
}

:deep(.ant-input-number-input::placeholder) {
  line-height: 2rem !important;
  vertical-align: middle !important;
}
</style>
