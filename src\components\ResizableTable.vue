<template>
  <div class="resizable-table-container">
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :scroll="scroll"
      :pagination="pagination"
      @resizeColumn="handleResizeColumn"
      class="resizable-table"
      v-bind="$attrs"
    >
      <template #headerCell="props">
        <slot name="headerCell" v-bind="props"></slot>
      </template>
      <template #bodyCell="props">
        <slot name="bodyCell" v-bind="props"></slot>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  columns: {
    type: Array,
    required: true
  },
  dataSource: {
    type: Array,
    default: () => []
  },
  scroll: {
    type: Object,
    default: () => ({ x: 'max-content' })
  },
  pagination: {
    type: [Object, Boolean],
    default: false
  },
  // 是否保存列宽设置到 localStorage
  saveColumnWidth: {
    type: Boolean,
    default: false
  },
  // localStorage 的 key
  storageKey: {
    type: String,
    default: 'table-column-widths'
  }
})

const emit = defineEmits(['resizeColumn'])

// 响应式的列配置
const columns = ref([...props.columns])

// 处理列宽拖拽
const handleResizeColumn = (width, column) => {
  console.log('列宽调整:', width, column)
  
  // 更新列宽
  const targetColumn = columns.value.find(col => col.dataIndex === column.dataIndex || col.key === column.key)
  if (targetColumn) {
    targetColumn.width = width
  }
  
  // 如果启用了保存功能，保存到 localStorage
  if (props.saveColumnWidth) {
    saveColumnWidths()
  }
  
  // 触发父组件事件
  emit('resizeColumn', width, column)
}

// 保存列宽设置
const saveColumnWidths = () => {
  const widths = {}
  columns.value.forEach(col => {
    if (col.width && (col.dataIndex || col.key)) {
      widths[col.dataIndex || col.key] = col.width
    }
  })
  localStorage.setItem(props.storageKey, JSON.stringify(widths))
}

// 加载列宽设置
const loadColumnWidths = () => {
  if (!props.saveColumnWidth) return
  
  try {
    const savedWidths = localStorage.getItem(props.storageKey)
    if (savedWidths) {
      const widths = JSON.parse(savedWidths)
      columns.value.forEach(col => {
        const key = col.dataIndex || col.key
        if (key && widths[key]) {
          col.width = widths[key]
        }
      })
    }
  } catch (error) {
    console.warn('加载列宽设置失败:', error)
  }
}

// 监听 props.columns 变化
watch(() => props.columns, (newColumns) => {
  columns.value = [...newColumns]
  loadColumnWidths()
}, { immediate: true })

// 组件挂载时加载设置
loadColumnWidths()
</script>

<style scoped>
.resizable-table-container {
  width: 100%;
}

.resizable-table {
  width: 100%;
}
</style>

<style>
/* 修复表格对齐问题 */
.resizable-table {
  table-layout: fixed;
}

.resizable-table .ant-table-body {
  overflow-x: auto;
  overflow-y: auto;
}

/* 固定列样式优化 */
.resizable-table .ant-table-fixed-left,
.resizable-table .ant-table-fixed-right {
  background: #fff;
  box-shadow: none;
  z-index: 2;
}

.resizable-table .ant-table-content {
  z-index: 0;
}

.resizable-table .ant-table-cell-fix-left,
.resizable-table .ant-table-cell-fix-right {
  z-index: 2 !important;
  background: #fff !important;
}

.resizable-table .ant-table-thead th.ant-table-cell-fix-left,
.resizable-table .ant-table-thead th.ant-table-cell-fix-right {
  z-index: 3 !important;
}

/* 确保表头和表体对齐 */
.resizable-table .ant-table-thead > tr > th,
.resizable-table .ant-table-tbody > tr > td {
  box-sizing: border-box;
  padding: 16px;
  text-align: left;
  vertical-align: middle;
}

/* 修复固定列的对齐 */
.resizable-table .ant-table-thead th.ant-table-cell-fix-left,
.resizable-table .ant-table-thead th.ant-table-cell-fix-right,
.resizable-table .ant-table-tbody td.ant-table-cell-fix-left,
.resizable-table .ant-table-tbody td.ant-table-cell-fix-right {
  padding: 16px;
}

/* 列宽拖拽提示 */
.resizable-table .ant-table-thead > tr > th {
  position: relative;
}

.resizable-table .ant-table-thead > tr > th::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  cursor: col-resize;
  background: transparent;
  transition: background-color 0.2s;
}

.resizable-table .ant-table-thead > tr > th:hover::after {
  background: rgba(24, 144, 255, 0.3);
}

/* 响应式设计 */
@media screen and (max-width: 1024px) {
  .resizable-table .ant-table-cell {
    white-space: nowrap;
  }
}
</style>