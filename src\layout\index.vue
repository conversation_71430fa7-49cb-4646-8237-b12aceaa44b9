<template>
  <a-layout class="layout">
    <!-- 顶部导航 -->
    <a-layout-header class="header">
      <div class="header-left">
        <img src="@/assets/logo.png" alt="logo" class="header-logo" />
        <span class="header-title">{{ projectName }}</span>
      </div>
      <div class="header-right">
        <header-tools :fullscreen="fullscreen" @fullscreen="onFullscreen" />
      </div>
    </a-layout-header>

    <!-- main布局 -->
    <a-layout class="main-container">
      <!-- 侧边栏 -->
      <a-layout-sider v-model:collapsed="collapsed" :trigger="null" collapsible class="sider" :width="siderWidth">
        <div class="menu-container">

          <a-menu v-model:selectedKeys="selectedKeys" v-model:openKeys="openKeys" mode="inline" class="custom-menu"
            :inline-collapsed="collapsed" @select="handleMenuSelect" @openChange="handleOpenChange">
            <template v-for="menu in menus" :key="menu.path">
              <!-- 只有当hide不为true时才显示菜单 -->
              <template v-if="!menu.meta?.hide">
                <template v-if="menu.children && menu.children.length">
                  <a-sub-menu :key="menu.path">
                    <template #icon>
                      <!-- <component :is="menu.meta?.icon || getMenuIcon(menu.meta?.icon)" /> -->
                      <img v-if="menu.meta?.icon" :src="getMenuIcon(menu.meta?.icon, isMenuActive(menu))"
                        class="menu-icon-img" />
                    </template>
                    <template #title>{{ menu.meta?.title }}</template>
                    <!-- 递归渲染子菜单 -->
                    <template v-for="subMenu in menu.children" :key="subMenu.path">
                      <!-- 子菜单同样需要判断hide -->
                      <template v-if="!subMenu.meta?.hide">
                        <template v-if="subMenu.children && subMenu.children.length">
                          <a-sub-menu :key="subMenu.path">
                            <template #icon>
                              <!-- 修改这里：使用 subMenu 的图标而不是 menu 的图标 -->
                              <img v-if="subMenu.meta?.icon && subMenu.meta.icon.includes(',')"
                                :src="getMenuIcon(subMenu.meta.icon, isMenuActive(subMenu))" class="menu-icon-img" />
                              <component v-else-if="subMenu.meta?.icon" :is="subMenu.meta.icon" />
                            </template>
                            <template #title>{{ subMenu.meta?.title }}</template>
                            <template v-for="item in subMenu.children" :key="item.path">
                              <a-menu-item v-if="!item.meta?.hide" :key="item.path">
                                <template #icon>
                                  <img v-if="item.meta?.icon && item.meta.icon.includes(',')"
                                    :src="getMenuIcon(item.meta.icon, isMenuActive(item))" class="menu-icon-img" />
                                  <component v-else-if="item.meta?.icon" :is="item.meta?.icon" />
                                </template>
                                {{ item.meta?.title }}
                              </a-menu-item>
                            </template>
                          </a-sub-menu>
                        </template>
                        <template v-else>
                          <a-menu-item :key="subMenu.path">
                            <template #icon>
                              <!-- 修改这里：先判断是否包含逗号，如果包含则使用img，否则使用component -->
                              <img v-if="subMenu.meta?.icon && subMenu.meta.icon.includes(',')"
                                :src="getMenuIcon(subMenu.meta.icon, isMenuActive(subMenu))" class="menu-icon-img" />
                              <component v-else-if="subMenu.meta?.icon && !subMenu.meta.icon.includes(',')"
                                :is="subMenu.meta.icon" />
                            </template>
                            {{ subMenu.meta?.title }}
                          </a-menu-item>
                        </template>
                      </template>
                    </template>
                  </a-sub-menu>
                </template>
                <template v-else>
                  <a-menu-item :key="menu.path">
                    <template #icon>
                      <!-- 修改这里：添加条件判断 -->
                      <img v-if="menu.meta?.icon && menu.meta.icon.includes(',')"
                        :src="getMenuIcon(menu.meta.icon, isMenuActive(menu))" class="menu-icon-img" />
                      <component v-else-if="menu.meta?.icon && !menu.meta.icon.includes(',')" :is="menu.meta.icon" />
                    </template>
                    {{ menu.meta?.title }}
                  </a-menu-item>
                </template>
              </template>
            </template>
          </a-menu>
        </div>

        <!-- 固定的收起导航按钮 -->
        <div class="collapse-btn">
          <div class="expand-content" v-if="collapsed" @click="toggleCollapsed">
            <menu-unfold-outlined />
          </div>

          <div v-else class="expand-content" @click="toggleCollapsed" style="text-align: center;">
            <menu-fold-outlined />
            <span class="collapse-text">收起导航</span>
          </div>
        </div>
      </a-layout-sider>

      <!-- 优化后的指示器 -->
      <div class="menu-indicator" v-if="collapsed" :style="{
        '--n': activeIndex,
        'opacity': isVisible ? 1 : 0
      }">
        <div class="indicator-wrapper">
          <!-- 外部轮廓 -->
          <div class="indicator-outline">
            <div style="margin-left: 14px; display: flex;">
              <!-- <div style="width: 3px; height: 40px; background-color: #EBF5FE;"></div> -->
              <div style="position: relative;right: 4px;">
                <img style="height: 50px;" src="@/assets/images/aty2.png" alt="">
              </div>
            </div>
            <!-- <div class="outline-curve-left"></div>
              <div class="outline-curve-right"></div> -->
          </div>
          <!-- 内部白色背景 -->
          <div class="indicator-bg">
            <div class="curve-top"></div>
            <div class="curve-bottom"></div>
          </div>
          <!-- <div class="dot"></div> -->
        </div>
      </div>

      <!-- 内容区域 -->
      <a-layout-content class="content">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-container">
          <a-breadcrumb>
            <a-breadcrumb-item v-for="(item, index) in currentBreadcrumb" :key="index">
              <router-link v-if="index < currentBreadcrumb.length - 1" :to="item.path">
                {{ item.title }}
              </router-link>
              <span v-else>{{ item.title }}</span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>

        <!-- 标签页导航 -->
        <div class="tabs-wrapper">
          <a-tabs v-model:activeKey="activeTab" type="editable-card" hide-add @edit="onTabEdit" @change="onTabChange">
            <a-tab-pane v-for="tab in tabs" :key="tab.key" :tab="tab.title" :closable="tab.closable" />
          </a-tabs>
        </div>

        <!-- 路由视图容器 -->
        <!-- <div class="router-view-container">
          <router-view v-if="activeTab === route.path" />
        </div> -->
        <div class="router-view-container">
          <router-view v-slot="{ Component }">
            <keep-alive :include="cachedViews">
              <component :is="Component" :key="getRouterViewKey()" />
            </keep-alive>
          </router-view>
        </div>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup>
import { ref, computed, onMounted, watch, onBeforeUnmount, onUnmounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  HomeOutlined,
  UserOutlined,
  DownOutlined
} from '@ant-design/icons-vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/store/modules/user';
import { useThemeStore } from '@/store/modules/theme';
import HeaderTools from './components/header-tools.vue';
import PageFooter from './components/page-footer.vue';
import MenuTitle from './components/menu-title.vue';
import { useSystemStore } from '@/store/modules/system';
import { GunsWebsocket } from '@/utils/websocket';
import { useNoticeStore } from '@/store/modules/notice';
import { WEBSOCKET_MESSAGE_TYPE } from '@/config/setting';
import cancelImg from '@/assets/equipment/shouye.png';
import passedImg from '@/assets/equipment/shouye.png';
import rejectedImg from '@/assets/equipment/shouye.png';

// 检查菜单或其子菜单是否被选中
const isMenuActive = (menu) => {
  // 直接检查当前菜单是否被选中
  if (selectedKeys.value.includes(menu.path)) {
    return true;
  }

  // 检查子菜单是否被选中
  if (menu.children && menu.children.length) {
    return menu.children.some(child => {
      // 直接检查子菜单
      if (selectedKeys.value.includes(child.path)) {
        return true;
      }

      // 递归检查孙子菜单
      if (child.children && child.children.length) {
        return child.children.some(grandChild =>
          selectedKeys.value.includes(grandChild.path)
        );
      }

      return false;
    });
  }

  return false;
};
//修改页面图标
const getMenuIcon = (icon, isSelected = false) => {
  // 增加安全检查
  if (!icon || typeof icon !== 'string' || !icon.includes(',')) {
    return '';
  }

  try {
    // 分割图标路径，格式应为: "path1,path2"
    const iconPaths = icon.split(",");

    // 确保路径有效
    if (iconPaths.length === 0) return '';

    // 如果是选中状态，返回第一个图标路径，否则返回第二个
    const selectedPath = iconPaths[0];
    const normalPath = iconPaths.length > 1 ? iconPaths[1] : iconPaths[0];

    // return isSelected ? selectedPath : normalPath;
    return normalPath; // 直接返回第一个图标路径
  } catch (error) {
    console.warn('图标路径解析错误:', icon, error);
    return '';
  }
};

// 初始化路由和store
const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const themeStore = useThemeStore();
const routerselect = ref()
const routerselect1 = ref()
const routerselect2 = ref()
const routerselect3 = ref()
const routerselect4 = ref()
const routerselect5 = ref()
const routerselect6 = ref()
const routerselect7 = ref()
const routerselect8 = ref()
const routerselect9 = ref()
const routerselect10 = ref()
const routerselect11 = ref()
const routerselect12 = ref()
const routerselect13 = ref()
const routerselect14 = ref()
const routerselect15 = ref()
const routerselect16 = ref()
const routerselect17 = ref()
const routerselect18 = ref()
const router1 = router.currentRoute.value.fullPath

// 从store中获取tabs
//TODO
const { showTabs, tabs } = storeToRefs(themeStore);
// 初始化tabs
if (!tabs.value || !Array.isArray(tabs.value)) {
  tabs.value = [{ key: "/buss/regEquLedger", title: "在籍设备台账", closable: true, query: {} }];
}

// 初始化其他变量
const collapsed = ref(false);
const fullscreen = ref(false);
const logo = ref('');
const projectName = ref('');
const activeTab = ref(route.path);
const siderWidth = ref(230);
const selectedKeys = ref([route.path]);
const openKeys = ref([]);
const lastOpenKey = ref('');
const activeIndex = ref(0);
const hoverIndex = ref(0);
const isHovering = ref(false);
const singleMenus = computed(() => menus.value.filter(m => !m.children?.length));
const isVisible = ref(true);

// 定义本地存储的键名
const TABS_STORAGE_KEY = 'app_tabs_cache';
const ACTIVE_TAB_KEY = 'app_active_tab';

// 保存标签页到本地存储
const saveTabs = () => {
  //每次保存的时候如果是1个就看有没有。没有就插入。有的话就直接赋值
  if (tabs.value.length === 1) {
    const tab = tabs.value[0];
    const tabStorage = sessionStorage.getItem(TABS_STORAGE_KEY);
    //判定是不是存在不存在就加进去
    var tabsStorage = JSON.parse(tabStorage);
    if (!tabsStorage) {
      tabsStorage = [];
    }
    const isExist = tabsStorage.find(item => item.key === tab.key);
    if (!isExist) {
      tabsStorage.push(tab);
      sessionStorage.setItem(TABS_STORAGE_KEY, JSON.stringify(tabsStorage));
      sessionStorage.setItem(ACTIVE_TAB_KEY, tab.key);

    }

  } else if (tabs.value?.length > 1) {
    sessionStorage.setItem(TABS_STORAGE_KEY, JSON.stringify(tabs.value));
    sessionStorage.setItem(ACTIVE_TAB_KEY, activeTab.value);
  }
};



// 从本地存储恢复标签页
// 从本地存储恢复标签页
// 从本地存储恢复标签页
const restoreTabs = () => {
  try {
    var savedTabs1 = sessionStorage.getItem(TABS_STORAGE_KEY);
    const savedActiveTab1 = sessionStorage.getItem(ACTIVE_TAB_KEY);

    if (!savedTabs1 || JSON.parse(savedTabs1)?.length == 0) {
      savedTabs1 = JSON.stringify([{ key: "/buss/regEquLedger", title: "在籍设备台账", closable: true, query: {} }]);
    }
    if (savedTabs1 && tabs.value) {
      const parsedTabs = JSON.parse(savedTabs1);
      if (parsedTabs.length > 0) {
        // 暂时移除路由监听器，防止在恢复过程中触发添加标签页
        const unwatchRoute = watch(() => route.path, () => { }, { immediate: false });
        const unwatchTabs = watch(() => tabs.value ? [...tabs.value] : [], () => { }, { deep: true });

        // 设置标签页
        tabs.value = parsedTabs;

        // 恢复激活的标签页
        if (savedActiveTab1 && tabs.value.some(tab => tab.key === savedActiveTab1)) {
          activeTab.value = savedActiveTab1;
          const activeTabObj = tabs.value.find(tab => tab.key === savedActiveTab1);
          router.push({
            path: savedActiveTab1,
            query: activeTabObj.query || {}
          });
        } else {
          activeTab.value = tabs.value[0].key;
          router.push({
            path: tabs.value[0].key,
            query: tabs.value[0].query || {}
          });
        }

        // 恢复完成后，重新设置监听器
        setTimeout(() => {
          unwatchRoute();
          unwatchTabs();

          // 处理当前激活标签页的菜单绑定
          const currentActiveTab = activeTab.value;
          
          // 先检查是否是隐藏菜单
          const isHandled = handleMenuWithActive(menus.value, currentActiveTab);
          
          if (isHandled) {
            // 如果是隐藏菜单，只处理隐藏菜单的选中样式，不清除其他样式
            nextTick(() => {
              handleHiddenMenuStyle(currentActiveTab);
            });
          } else {
            // 如果不是隐藏菜单，按正常逻辑处理
            selectedKeys.value = [currentActiveTab];
            if (!collapsed.value) {
              const menuPath = findMenuPath(menus.value, currentActiveTab);
              if (menuPath) {
                openKeys.value = menuPath.slice(0, -1);
              }
            }
          }

          // 如果是折叠状态，更新指示器位置
          if (collapsed.value) {
            updateIndicatorPosition(currentActiveTab);
          }

          // 重新设置标签页监听
          watch(() => tabs.value ? [...tabs.value] : [], () => {
            if (tabs.value) {
              saveTabs();
            }
          }, { deep: true });

          // 重新设置路由监听
          watch(() => route.path, (newPath) => {
            const currentRoute = route.matched[route.matched.length - 1];
            const title = currentRoute.meta?.title || '未命名';

            const existingTab = tabs.value.find(tab => tab.key === newPath);
            if (!existingTab) {
              tabs.value.push({
                key: newPath,
                title: title,
                closable: true,
                query: { ...route.query }
              });
            }

            activeTab.value = newPath;
            
            // 处理菜单选中状态
            const isHiddenHandled = handleMenuWithActive(menus.value, newPath);
            if (!isHiddenHandled) {
              selectedKeys.value = [newPath];
              if (!collapsed.value) {
                const menuPath = findMenuPath(menus.value, newPath);
                if (menuPath) {
                  openKeys.value = menuPath.slice(0, -1);
                }
              }
            } else {
              nextTick(() => {
                handleHiddenMenuStyle(newPath);
              });
            }
            
            if (collapsed.value) {
              updateIndicatorPosition(newPath);
            }
          }, { immediate: true });

          console.log('恢复标签页完成，重新启用监听器');
        }, 500);
      }
    }
  } catch (error) {
    console.error('恢复标签页失败:', error);
  }
};

// 监听标签页变化，保存到本地存储
watch(() => tabs.value ? [...tabs.value] : [], () => {
  if (tabs.value) {
    saveTabs();
  }
}, { deep: true });

// 监听激活标签页变化，保存到本地存储
watch(activeTab, () => {
  sessionStorage.setItem(ACTIVE_TAB_KEY, activeTab.value);
});

//缓存或者不缓存
// 获取路由视图的key
// 获取路由视图的key - 直接使用路径
const getRouterViewKey = () => {
  const shouldCache = shouldCachePage(route.path, null, {});
  console.log(`当前路由 ${route.path} 的缓存策略: ${shouldCache ? '缓存' : '不缓存'}`);
  if (shouldCache) {
    // 缓存的页面使用固定的路径key
    return route.path;
  } else {
    // 不缓存的页面使用带时间戳的key强制重新渲染
    return `${route.path}_${Date.now()}`;
  }
};
// 根据文件夹路径判断是否需要缓存
// 根据菜单配置判断是否需要缓存
const shouldCachePage = (routePath, routeName, routeMeta) => {
  // 1. 优先使用路由meta中的配置
  if (routeMeta?.keepAlive !== undefined) {
    return routeMeta.keepAlive;
  }

  // 2. 从菜单数据中查找对应路径的 meta.isAlive 配置
  const findMenuByPath = (menuList, targetPath) => {
    for (const menu of menuList) {
      if (menu.path === targetPath) {
        return menu;
      }
      if (menu.children && menu.children.length) {
        const found = findMenuByPath(menu.children, targetPath);
        if (found) return found;
      }
    }
    return null;
  };

  const menuItem = findMenuByPath(menus.value, routePath);
  if (menuItem && menuItem.meta?.isAlive !== undefined) {
    return menuItem.meta.isAlive;
  }

  // 3. 默认不缓存
  return false;
};

// 添加调试信息，方便查看缓存策略
const debugCacheStrategy = (routePath) => {
  const shouldCache = shouldCachePage(routePath, null, {});
  return shouldCache;
};



// 在组件挂载时恢复标签页
onMounted(() => {

  // 禁用默认的路由监听，我们将在恢复标签页后重新启用
  const defaultRouteWatch = watch(() => route.path, () => { }, { immediate: false });

  // 加载用户信息和菜单数据后恢复标签页
  userStore.fetchUserInfo().then(() => {
    // 确保tabs已经初始化
    if (tabs.value) {
      // 停止默认路由监听
      defaultRouteWatch();
      // 恢复标签页
      restoreTabs();
    }
  });

  // 添加窗口卸载事件监听器
  window.addEventListener('beforeunload', saveTabs);
});

// 在页面刷新或关闭前保存标签页状态
onBeforeUnmount(() => {
  if (tabs.value) {
    saveTabs();
  }
});

onBeforeUnmount(() => {
  window.removeEventListener('beforeunload', saveTabs);
});

// 处理菜单展开收起
const handleOpenChange = (keys) => {
  // 找出新展开的菜单项

  // const latestOpenKey = keys.find(key => !openKeys.value.includes(key));
  // if (!latestOpenKey) {
  //   // 如果是收起操作，保持当前状态
  //   openKeys.value = keys;
  //   return;
  // }

  const latestOpenKey = keys[keys.length - 1]

  // 获取一级菜单的路径
  const rootMenuKeys = menus.value
    .filter(item => item.children && item.children.length)
    .map(item => item.path);

  // 判断是否是一级菜单
  const isRootMenu = rootMenuKeys.includes(latestOpenKey);

  if (isRootMenu) {
    // 如果是一级菜单，关闭其他所有一级菜单及其子菜单
    openKeys.value = [latestOpenKey];
  } else {
    // 如果是子菜单
    // 找到当前子菜单所属的一级菜单
    // const parentKey = rootMenuKeys.find(key => latestOpenKey.startsWith(key));
    const parentKey = null
    // 保留当前子菜单的父级菜单，但关闭其他所有子菜单
    const newOpenKeys = parentKey ? [parentKey, latestOpenKey] : [latestOpenKey];

    // 过滤掉其他已展开的子菜单，但保留其他一级菜单
    openKeys.value = [...new Set([
      ...openKeys.value.filter(key => rootMenuKeys.includes(key)),
      ...newOpenKeys
    ])];
  }
};

// 获取菜单数据
// 获取菜单数据
const { menus } = storeToRefs(userStore);
//过滤menus没有子项的都过滤
// const filteredMenus = computed(() => {
//   return menus.value.filter(menu => menu.children && menu.children.length > 0);
// });
// // 不要直接修改 menus.value，而是使用 filteredMenus

// menus.value = filteredMenus.value;

// 面包屑数据
const breadcrumbItems = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title);
  return matched.map(item => ({
    path: item.path,
    title: item.meta.title
  }));
});

// 加载store数据
const systemStore = useSystemStore();
const noticeStore = useNoticeStore();

// 切换侧边栏折叠状态
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value;
  siderWidth.value = collapsed.value ? 89 : 230;
};

// 全屏切换
const onFullscreen = () => {
  try {
    fullscreen.value = !fullscreen.value;
    if (fullscreen.value) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  } catch (e) {
    console.error('您的浏览器不支持全屏模式');
  }
};

// // 监听路由变化，自动添加标签页
// watch(() => route.path, (newPath) => {
//   const currentRoute = route.matched[route.matched.length - 1];
//   const title = currentRoute.meta?.title || '未命名';

//   // 检查标签是否已存在
//   const existingTab = tabs.value.find(tab => tab.key === newPath);
//   if (!existingTab) {
//     // 添加新标签
//     tabs.value.push({
//       key: newPath,
//       title: title,
//       closable: true,
//       query: { ...route.query }
//     });
//   }

//   // 激活当前标签
//   activeTab.value = newPath;
// }, { immediate: true });

// 标签页编辑（关闭）处理
const onTabEdit = (targetKey, action) => {
  if (tabs.value.length === 1) return;
  if (action === 'remove') {
    // 找到要关闭的标签索引
    const targetIndex = tabs.value.findIndex(tab => tab.key === targetKey);

    // 如果关闭的是当前激活的标签，需要激活其他标签
    if (activeTab.value === targetKey) {
      if (targetIndex === 0) {
        // 如果关闭的是第一个，激活下一个
        activeTab.value = tabs.value[1]?.key;
      } else {
        // 否则激活前一个
        activeTab.value = tabs.value[targetIndex - 1].key;
      }
    }

    // 从数组中移除标签
    tabs.value = tabs.value.filter(tab => tab.key !== targetKey);

    // 路由跳转到新激活的标签
    if (activeTab.value) {
      router.push(activeTab.value);
    }

    // 保存更新后的标签状态
    saveTabs();
  }
};

onMounted(async () => {
  // 加载主题信息
  const result = await systemStore.loadThemeInfo();
  logo.value = result.gunsMgrLogo;
  projectName.value = result.gunsMgrName;

  // 加载用户信息和菜单数据
  await userStore.fetchUserInfo();

  // 注册消息通知的websocket
  try {
    const gunsWebsocket = new GunsWebsocket(userStore.$state?.info?.wsUrl);
    await gunsWebsocket.initWebSocket(result => {
      if (WEBSOCKET_MESSAGE_TYPE.server.SYS_NOTICE_MSG_TYPE === result.serverMsgType) {
        noticeStore.addNotice(result.data);
      }
    });
  } catch (e) {
    console.error(e);
  }
});

// 监听路由变化，更新选中菜单
watch(() => route.path, (newPath) => {
  selectedKeys.value = [newPath];
}, { immediate: true });

// 监听选中项变化
watch(selectedKeys, (newVal) => {
  const allMenuItems = [...menus.value.filter(m => !m.children?.length),
  ...menus.value.filter(m => m.children?.length)]

  const parentIndex = allMenuItems.findIndex(item => {
    if (item.path === newVal[0]) return true
    if (item.children) {
      return item.children.some(child => child.path === newVal[0])
    }
    return false
  })

  if (parentIndex !== -1) {
    isVisible.value = false
    setTimeout(() => {
      activeIndex.value = parentIndex
      isVisible.value = true
    }, 100)
  }
});

// 处理菜单hover效果
const handleMenuHover = (index) => {
  if (collapsed.value) {
    hoverIndex.value = index;
    isHovering.value = true;
  }
};

// 添加鼠标离开菜单的处理
onMounted(() => {
  const menu = document.querySelector('.custom-menu');
  menu?.addEventListener('mouseleave', () => {
    isHovering.value = false;
    hoverIndex.value = activeIndex.value;
  });
});

// 在组件顶部添加菜单导航标记
const isMenuNavigation = ref(false);
const isTabNavigation = ref(false);
// 处理菜单选择
const handleMenuSelect = ({ key }) => {
  selectedKeys.value = [key];

  const findMenuItem = (menus, targetKey) => {
    for (const menu of menus) {
      if (menu.path === targetKey) {
        return menu;
      }
      if (menu.children) {
        const found = findMenuItem(menu.children, targetKey);
        if (found) return found;
      }
    }
    return null;
  };

  const selectedMenu = findMenuItem(menus.value, key);

  // 只有当选中的是叶子节点时才进行页面跳转和标签页操作
  if (selectedMenu && !selectedMenu.children?.length) {
    // 标记这是菜单导航
    isMenuNavigation.value = true;

    // 添加新标签页
    const newTab = {
      key: key,
      title: selectedMenu.meta?.title || '未命名',
      closable: true,
      query: { ...route.query }
    };

    // 检查标签是否已存在
    const existingTab = tabs.value.find(tab => tab.key === key);
    if (!existingTab) {
      tabs.value.push(newTab);
    }

    // 激活标签页
    activeTab.value = key;

    // 路由跳转
    router.push(key);
  }
};


// 强制刷新不缓存的页面
const refreshNonCachedPage = () => {
  const currentRoute = route.matched[route.matched.length - 1];
  const shouldCache = currentRoute.meta?.keepAlive === true;

  if (!shouldCache) {
    // 对于不缓存的页面，通过改变key强制重新渲染
    const timestamp = Date.now();
    router.replace({
      path: route.path,
      query: { ...route.query, _t: timestamp }
    });
  }
};
function kebabToPascal(str) {
  return str
    .replace(/[^a-zA-Z0-9]/g, '-')   // 把其它非法字符都转成分隔符
    .split('-')                       // 按分隔符拆
    .filter(Boolean)                  // 去掉空串
    .map(s => s.charAt(0).toUpperCase() + s.slice(1).toLowerCase())
    .join('');                        // 合并
}
// 修改缓存视图管理
const cachedViews = computed(() => {
  const cached = [];
  tabs.value.forEach(tab => {
    const shouldCache = shouldCachePage(tab.key, null, {});
    console.log(`路径: ${tab.key} -> 缓存策略: ${shouldCache ? '缓存' : '不缓存'}`);
    if (shouldCache) {
      // 从菜单数据中查找对应的组件名
      const findMenuByPath = (menuList, targetPath) => {
        for (const menu of menuList) {
          if (menu.path === targetPath) {
            return menu;
          }
          if (menu.children && menu.children.length) {
            const found = findMenuByPath(menu.children, targetPath);
            if (found) return found;
          }
        }
        return null;
      };

      const menuItem = findMenuByPath(menus.value, tab.key);
      let componentName = null;

      // if (menuItem && menuItem.meta?.componentName) {
      //   // 如果后台配置了组件名，直接使用
      //   componentName = menuItem.meta.componentName;
      // } else if (menuItem && menuItem.meta?.title) {
      //   // 如果没有配置组件名，使用标题生成（作为备用方案）
      //   componentName = menuItem.meta.title.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '');
      // }


      componentName = menuItem.path
        .split('/')
    .filter(Boolean)                       // 去掉空串
    .map(seg => seg.charAt(0).toUpperCase() + seg.slice(1))
    .join('');
      ;
      console.log(`菜单项: ${menuItem?.meta?.title} -> 组件名: ${componentName}`);
      if (componentName && !cached.includes(componentName)) {
        cached.push(componentName);
      }
    }
  });
  return cached;
});


// 查找菜单项的辅助函数
const findMenuPath = (menus, targetKey, parentPath = []) => {
  for (const menu of menus) {
    const currentPath = [...parentPath, menu.path];
    if (menu.path === targetKey) {
      return currentPath;
    }
    if (menu.children) {
      const found = findMenuPath(menu.children, targetKey, currentPath);
      if (found) return found;
    }
  }
  return null;
};

// 清除所有手动添加的选中样式
const clearAllSelectedStyles = () => {
  // 清除所有 ant-menu-item-selected 样式
  document.querySelectorAll('.ant-menu-item-selected').forEach(el => {
    el.classList.remove('ant-menu-item-selected');
  });


};

// 动态处理隐藏菜单的选中样式
// 动态处理隐藏菜单的选中样式
const handleHiddenMenuStyle = (currentPath) => {
  // 动态从菜单数据中获取隐藏菜单映射关系
  const getHiddenMenuMap = (menuList) => {
    const map = {};

    const traverse = (menus) => {
      for (const menu of menus) {
        if (menu.children) {
          for (const child of menu.children) {
            // 如果子菜单有 meta.active 且 meta.hide 为 true
            if (child.meta?.active && child.meta?.hide) {
              map[child.path] = child.meta.active;
            }
            // 递归处理更深层级
            if (child.children) {
              traverse([child]);
            }
          }
        }
      }
    };

    traverse(menuList);
    return map;
  };

  // 动态获取映射关系
  const hiddenMenuMap = getHiddenMenuMap(menus.value);

  // 检查当前路径是否是隐藏菜单
  const activeKey = hiddenMenuMap[currentPath];

  if (activeKey) {


    // 通过 activeKey 动态获取对应的菜单元素
    const targetElement = document.querySelector(`li[data-menu-id="${activeKey}"]`)?.childNodes[2];

    if (targetElement) {
      targetElement.classList.add('ant-menu-item-selected');

    } else {
      
    }

    return true;
  }

  return false;
};

const handleMenuWithActive = (menus, targetKey) => {
  // 查找所有菜单中有 meta.active 的隐藏菜单项
  const findHiddenMenuWithActive = (menuList) => {
    for (const menu of menuList) {
      if (menu.children) {
        for (const child of menu.children) {
          if (child.meta?.active && child.meta?.hide && child.path === targetKey) {
            return child;
          }
          // 递归查找更深层级
          const found = findHiddenMenuWithActive([child]);
          if (found) return found;
        }
      }
    }
    return null;
  };

  const hiddenMenu = findHiddenMenuWithActive(menus);

  if (hiddenMenu) {


    // 找到 activeKey 的父级菜单
    const activeParent = findParentMenu(menus, hiddenMenu.meta.active);

    if (activeParent) {


      // 确保父级菜单有 children 数组
      if (!activeParent.children) {
        activeParent.children = [];
      }

      // 检查是否已经存在这个隐藏菜单项
      const existingChild = activeParent.children.find(child => child.path === targetKey);
      if (!existingChild) {
        // 在父级菜单中新增一个隐藏的 children
        const newMenuItem = {
          path: targetKey, // 使用隐藏菜单的路径
          meta: {
            title: hiddenMenu.meta.title || '未命名',
            icon: hiddenMenu.meta.icon,
            hide: true, // 保持隐藏，不在侧边栏显示
            active: hiddenMenu.meta.active, // 保留 active 信息
            isAlive: hiddenMenu.meta.isAlive || false // 保留 isAlive 信息
          }
        };

        activeParent.children.push(newMenuItem);

      }

      // 设置选中状态为 activeKey
      selectedKeys.value = [hiddenMenu.meta.active];


      // 展开父级菜单
      if (!openKeys.value.includes(activeParent.path)) {
        openKeys.value.push(activeParent.path);

      }

      return true; // 表示已处理
    }
  }

  return false; // 表示未处理
};

// 辅助函数：通过路径查找菜单项
const findMenuByPath = (menus, targetPath) => {
  for (const menu of menus) {
    if (menu.path === targetPath) {
      return menu;
    }
    if (menu.children) {
      const found = findMenuByPath(menu.children, targetPath);
      if (found) return found;
    }
  }
  return null;
};

// 辅助函数：查找菜单项的父级
const findParentMenu = (menus, targetPath) => {
  for (const menu of menus) {
    if (menu.children) {
      // 检查直接子菜单
      const hasChild = menu.children.some(child => child.path === targetPath);
      if (hasChild) {
        return menu;
      }

      // 递归检查更深层级
      const found = findParentMenu(menu.children, targetPath);
      if (found) return found;
    }
  }
  return null;
};

// 修改路由监听
watch(() => route.path, (newPath) => {
  const currentRoute = route.matched[route.matched.length - 1];
  const title = currentRoute.meta?.title || '未命名';

  // 判断是否需要缓存 - 使用菜单配置
  const shouldCache = shouldCachePage(newPath, currentRoute.name, currentRoute.meta);

  // 检查标签是否已存在
  const existingTab = tabs.value.find(tab => tab.key === newPath);
  if (!existingTab) {
    // 添加新标签
    tabs.value.push({
      key: newPath,
      title: title,
      closable: true,
      query: { ...route.query },
      keepAlive: shouldCache
    });

    saveTabs();
  }

  // 激活当前标签
  activeTab.value = newPath;

  // 先清除所有手动添加的选中样式
  clearAllSelectedStyles();

  // 先处理 meta.active 逻辑
  const isHandled = handleMenuWithActive(menus.value, newPath);

  if (!isHandled) {
    // 如果不是隐藏菜单，按正常逻辑处理
    selectedKeys.value = [newPath];
    const menuPath = findMenuPath(menus.value, newPath);
    if (menuPath) {
      const rootMenuKeys = menus.value
        .filter(item => item.children && item.children.length)
        .map(item => item.path);
      const rootKey = menuPath.find(path => rootMenuKeys.includes(path));
      if (rootKey) {
        const parentKeys = menuPath.slice(0, -1);
        openKeys.value = [...new Set([rootKey, ...parentKeys])];
      }
    }
  } else {
    // 如果是隐藏菜单，额外处理选中样式
    nextTick(() => {
      handleHiddenMenuStyle(newPath);
    });
  }

  // 保存当前激活的标签
  sessionStorage.setItem(ACTIVE_TAB_KEY, newPath);
}, { immediate: true });




// 修改路由监听，添加 oldPath 参数和关闭逻辑
watch(() => route.path, (newPath, oldPath) => {
  const currentRoute = route.matched[route.matched.length - 1];
  const title = currentRoute.meta?.title || '未命名';

  // 查找菜单项的辅助函数
  const findMenuItem = (menus, targetKey) => {
    for (const menu of menus) {
      if (menu.path === targetKey) {
        return menu;
      }
      if (menu.children) {
        const found = findMenuItem(menu.children, targetKey);
        if (found) return found;
      }
    }
    return null;
  };

  // 检查旧路径是否需要关闭（只有非菜单导航且非标签页切换才关闭不可见页面）
  if (oldPath && oldPath !== newPath && !isMenuNavigation.value && !isTabNavigation.value) {
    const oldMenuItem = findMenuItem(menus.value, oldPath);
    const oldIsVisibleInMenu = oldMenuItem && !oldMenuItem.meta.hide;;
    if (!oldIsVisibleInMenu) {
      const oldTabIndex = tabs.value.findIndex(tab => tab.key === oldPath);
      if (oldTabIndex > -1) {
        tabs.value.splice(oldTabIndex, 1);
      }
    }
  }

  // 重置导航标记
  isMenuNavigation.value = false;
  isTabNavigation.value = false;

  // 检查标签是否已存在
  const existingTab = tabs.value.find(tab => tab.key === newPath);
  if (!existingTab) {
    // 添加新标签
    tabs.value.push({
      key: newPath,
      title: title,
      closable: true,
      query: { ...route.query }
    });
  }

  // 激活当前标签
  activeTab.value = newPath;

  selectedKeys.value = [newPath];
}, { immediate: true });

// 处理折叠状态变化
watch(() => collapsed.value, (newVal) => {
  if (newVal) {
    openKeys.value = [];
  }
});

// 监听路由变化时关闭所有展开的菜单
// watch(() => route.path, () => {
//   openKeys.value = [];
// });

// 折叠时关闭所有展开的菜单
watch(() => collapsed.value, (newVal) => {

  if (newVal) {
    openKeys.value = [];
  }
});

// 可选：添加面包屑组件到内容区域
const currentBreadcrumb = computed(() => {
  const currentTab = tabs.value.find(tab => tab.key === activeTab.value);
  return currentTab?.breadcrumb || [];
});

// 标签页切换处理
const onTabChange = (key) => {
  isTabNavigation.value = true;
  activeTab.value = key;
  const tab = tabs.value.find(t => t.key === key);
  if (tab && tab.query) {
    router.push({ path: key, query: tab.query });
  } else {
    router.push(key);
  }
  selectedKeys.value = [key];
  selectedKeys.value = [key];

  // 收缩状态下不展开子菜单
  if (collapsed.value) {
    openKeys.value = [];
    // 更新 indicator 位置
    updateIndicatorPosition(key);
    return;
  }
 
  // 先清除所有手动添加的选中样式
  clearAllSelectedStyles();
  // 先处理 meta.active 逻辑
  const isHandled = handleMenuWithActive(menus.value, key);

  if (!isHandled) {
    // 如果不是隐藏菜单，按正常逻辑处理
    selectedKeys.value = [key];
    const menuPath = findMenuPath(menus.value, key);
    if (menuPath) {
      const rootMenuKeys = menus.value
        .filter(item => item.children && item.children.length)
        .map(item => item.path);
      const rootKey = menuPath.find(path => rootMenuKeys.includes(path));
      if (rootKey) {
        const parentKeys = menuPath.slice(0, -1);
        openKeys.value = [...new Set([rootKey, ...parentKeys])];
      }
    }
  } else {
    // 如果是隐藏菜单，额外处理选中样式
    nextTick(() => {
      handleHiddenMenuStyle(key);
    });
  }

  // 保存当前激活的标签
  sessionStorage.setItem(ACTIVE_TAB_KEY, key);
};




// 添加更新 indicator 位置的函数
const updateIndicatorPosition = (key) => {
  // 获取所有一级菜单项
  const allMenuItems = menus.value;

  // 查找当前选中项对应的一级菜单索引
  const parentIndex = allMenuItems.findIndex(item => {
    if (item.path === key) return true;
    if (item.children) {
      // 递归检查所有层级的子菜单
      const hasPath = (children) => {
        return children.some(child => {
          if (child.path === key) return true;
          if (child.children) return hasPath(child.children);
          return false;
        });
      };
      return hasPath(item.children);
    }
    return false;
  });

  if (parentIndex !== -1) {
    isVisible.value = false;
    setTimeout(() => {
      activeIndex.value = parentIndex;
      isVisible.value = true;
    }, 100);
  }
};

// 确保在组件挂载时就正确定位
onMounted(() => {
  if (collapsed.value) {
    updateIndicatorPosition(route.path);
  }
});

// 修改 watch 监听，使用 immediate 确保立即执行
watch(selectedKeys, (newVal) => {
  if (collapsed.value && newVal.length > 0) {
    updateIndicatorPosition(newVal[0]);
  }
}, { immediate: true });

// 折叠状态变化时的处理
watch(() => collapsed.value, (newVal) => {
  if (newVal) {
    openKeys.value = [];
    if (selectedKeys.value.length > 0) {
      updateIndicatorPosition(selectedKeys.value[0]);
    } else {
      updateIndicatorPosition(route.path);
    }
  }
});

// // 路由变化时的处理
// watch(() => route.path, (newPath) => {
//   selectedKeys.value = [newPath];

//   if (newPath == '/statisticAnalysis/monthlySummary' || newPath == '/statisticAnalysis/detail') {
//     if (routerselect.value) {
//       routerselect.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect.value) {
//       routerselect.value.classList.remove('ant-menu-item-selected')
//     }
//   }

//   if (newPath == '/statisticAnalysis/syMonthlySummary' || newPath == '/statisticAnalysis/syDetail') {
//     if (routerselect1.value) {
//       routerselect1.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect1.value) {
//       routerselect1.value.classList.remove('ant-menu-item-selected')
//     }
//   }

//   if (newPath == '/buss/equipmentScrapping' || newPath == '/buss/scrapApplication' || newPath == '/buss/equipmentScrappingTwice') {
//     if (routerselect2.value) {
//       routerselect2.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect2.value) {
//       routerselect2.value.classList.remove('ant-menu-item-selected')
//     }
//   }

//   if (newPath == '/buss/disposalForm' || newPath == '/buss/disposalApplication' || newPath == '/buss/disposalFormTwice') {
//     if (routerselect3.value) {
//       routerselect3.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect3.value) {
//       routerselect3.value.classList.remove('ant-menu-item-selected')
//     }
//   }



//   if (newPath == '/dynamic/warehouse/create' || newPath == '/buss/warehouseTwice') {
//     if (routerselect4.value) {
//       routerselect4.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect4.value) {
//       routerselect4.value.classList.remove('ant-menu-item-selected')
//     }
//   }
//   if (newPath == '/dynamic/allocate/create' || newPath == '/buss/allocateTwice') {
//     if (routerselect5.value) {
//       routerselect5.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect5.value) {
//       routerselect5.value.classList.remove('ant-menu-item-selected')
//     }
//   }
//   if (newPath == '/dynamic/quittingNotice/create' || newPath == '/buss/quittingNoticeTwice') {
//     if (routerselect6.value) {
//       routerselect6.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect6.value) {
//       routerselect6.value.classList.remove('ant-menu-item-selected')
//     }
//   }
//   if (newPath == '/dynamic/returned/create' || newPath == '/buss/returnedTwice') {
//     if (routerselect7.value) {
//       routerselect7.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect7.value) {
//       routerselect7.value.classList.remove('ant-menu-item-selected')
//     }
//   }
//   if (newPath == '/dynamic/enabled/create' || newPath == '/buss/enabledTwice') {
//     if (routerselect8.value) {
//       routerselect8.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect8.value) {
//       routerselect8.value.classList.remove('ant-menu-item-selected')
//     }
//   }
//   if (newPath == '/dynamic/proxies/create' || newPath == '/buss/proxiesTwice') {
//     if (routerselect9.value) {
//       routerselect9.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect9.value) {
//       routerselect9.value.classList.remove('ant-menu-item-selected')
//     }
//   }
//   if (newPath == '/dynamic/leaseExternally/create' || newPath == '/buss/leaseExternallyTwice') {
//     if (routerselect10.value) {
//       routerselect10.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect10.value) {
//       routerselect10.value.classList.remove('ant-menu-item-selected')
//     }
//   }
//   if (newPath == '/dynamic/statusChange/create' || newPath == '/buss/statusChangeTwice') {
//     if (routerselect11.value) {
//       routerselect11.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect11.value) {
//       routerselect11.value.classList.remove('ant-menu-item-selected')
//     }
//   }
//   if (newPath == '/dynamic/selfUse/create' || newPath == '/buss/selfUseTwice') {
//     if (routerselect12.value) {
//       routerselect12.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect12.value) {
//       routerselect12.value.classList.remove('ant-menu-item-selected')
//     }
//   }
//   if (newPath == '/dynamic/leasedOut/create' || newPath == '/buss/leasedOutTwice') {
//     if (routerselect13.value) {
//       routerselect13.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect13.value) {
//       routerselect13.value.classList.remove('ant-menu-item-selected')
//     }
//   }
//   if (newPath == '/dynamic/transfer/create' || newPath == '/buss/transferTwice') {
//     if (routerselect14.value) {
//       routerselect14.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect14.value) {
//       routerselect14.value.classList.remove('ant-menu-item-selected')
//     }
//   }
//   if (newPath == '/dynamic/selfUseReturn/create' || newPath == '/buss/selfUseReturnTwice') {
//     if (routerselect15.value) {
//       routerselect15.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect15.value) {
//       routerselect15.value.classList.remove('ant-menu-item-selected')
//     }
//   }
//   if (newPath == '/dynamic/review/create' || newPath == '/buss/reviewTwice') {
//     if (routerselect16.value) {
//       routerselect16.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect16.value) {
//       routerselect16.value.classList.remove('ant-menu-item-selected')
//     }
//   }
//   if (newPath == '/buss/demandPlanJDC' || newPath == '/buss/demandPlanJDCTwice') {
//     if (routerselect17.value) {
//       routerselect17.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect17.value) {
//       routerselect17.value.classList.remove('ant-menu-item-selected')
//     }
//   }

//   if (newPath == '/buss/regApplicationRecord' || newPath == '/buss/apply/equipmentAcceptance') {
//     if (routerselect18.value) {
//       routerselect18.value.classList.add('ant-menu-item-selected')
//     }
//   } else {
//     if (routerselect18.value) {
//       routerselect18.value.classList.remove('ant-menu-item-selected')
//     }
//   }


//   if (collapsed.value) {
//     updateIndicatorPosition(newPath);
//   }
// }, { immediate: true });

// 在 setup 中添加初始化函数
const initializeMenu = () => {
  const currentPath = route.path;
  selectedKeys.value = [currentPath];

  // 如果不是折叠状态，展开对应的菜单
  if (!collapsed.value) {
    const menuPath = findMenuPath(menus.value, currentPath);
    if (menuPath) {
      // 获取除最后一个路径外的所有父级路径作为展开的 keys
      openKeys.value = menuPath.slice(0, -1);
    }
  } else {
    // 折叠状态更新 indicator 位置
    updateIndicatorPosition(currentPath);
  }
};





// 在 onMounted 中调用
onMounted(() => {
  initializeMenu();

  // routerselect.value = document.querySelector("li[data-menu-id='/statisticAnalysis/registeredAnalysis']")?.childNodes[2]
  // routerselect1.value = document.querySelector("li[data-menu-id='/statisticAnalysis/existenceAnalysis']")?.childNodes[2]
  // routerselect2.value = document.querySelector("li[data-menu-id='/buss/scrapEquipment/scrapEquipment']")?.childNodes[2]
  // routerselect3.value = document.querySelector("li[data-menu-id='/buss/disposalEquipment']")?.childNodes[2]
  // routerselect4.value = document.querySelector("li[data-menu-id='/dynamic/warehouse']")?.childNodes[2]
  // routerselect5.value = document.querySelector("li[data-menu-id='/dynamic/allocate']")?.childNodes[2]
  // routerselect6.value = document.querySelector("li[data-menu-id='/dynamic/quittingNotice']")?.childNodes[2]
  // routerselect7.value = document.querySelector("li[data-menu-id='/dynamic/returned']")?.childNodes[2]
  // routerselect8.value = document.querySelector("li[data-menu-id='/dynamic/enabled']")?.childNodes[2]
  // routerselect9.value = document.querySelector("li[data-menu-id='/dynamic/proxies']")?.childNodes[2]
  // routerselect10.value = document.querySelector("li[data-menu-id='/dynamic/leaseExternally/leaseExternally']")?.childNodes[2]
  // routerselect11.value = document.querySelector("li[data-menu-id='/dynamic/statusChange']")?.childNodes[2]
  // routerselect12.value = document.querySelector("li[data-menu-id='/dynamic/selfUse']")?.childNodes[2]
  // routerselect13.value = document.querySelector("li[data-menu-id='/dynamic/leasedOut']")?.childNodes[2]
  // routerselect14.value = document.querySelector("li[data-menu-id='/dynamic/transfer']")?.childNodes[2]
  // routerselect15.value = document.querySelector("li[data-menu-id='/dynamic/selfUseReturn']")?.childNodes[2]
  // routerselect16.value = document.querySelector("li[data-menu-id='/dynamic/review']")?.childNodes[2]
  // routerselect17.value = document.querySelector("li[data-menu-id='/buss/demandListJDC']")?.childNodes[2]
  // routerselect18.value = document.querySelector("li[data-menu-id='/buss/regEquLedger']")?.childNodes[2]
  // if (router1 == '/statisticAnalysis/monthlySummary'
  //   || router1 == '/statisticAnalysis/detail'
  // ) {
  //   routerselect.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect.value?.classList.remove('ant-menu-item-selected')
  // }
  // if (router1 == '/statisticAnalysis/syMonthlySummary'
  //   || router1 == '/statisticAnalysis/syDetail'
  // ) {
  //   routerselect1.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect1.value?.classList.remove('ant-menu-item-selected')
  // }
  // if (router1 == '/buss/equipmentScrapping'
  //   || router1 == '/buss/scrapApplication'
  //   || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/equipmentScrappingTwice'
  // ) {
  //   routerselect2.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect2.value?.classList.remove('ant-menu-item-selected')
  // }
  // if (router1 == '/buss/disposalForm'
  //   || router1 == '/buss/disposalApplication'
  //   || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/disposalFormTwice'
  // ) {
  //   routerselect3.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect3.value?.classList.remove('ant-menu-item-selected')
  // }


  // if (router1 == '/dynamic/warehouse/create' || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/applyTwice/warehouseTwice') {
  //   routerselect4.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect4.value?.classList.remove('ant-menu-item-selected')
  // }
  // if (router1 == '/dynamic/allocate/create' || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/applyTwice/allocateTwice') {
  //   routerselect5.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect5.value?.classList.remove('ant-menu-item-selected')
  // }
  // if (router1 == '/dynamic/quittingNotice/create' || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/applyTwice/quittingNoticeTwice') {
  //   routerselect6.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect6.value?.classList.remove('ant-menu-item-selected')
  // }
  // if (router1 == '/dynamic/returned/create' || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/applyTwice/returnedTwice') {
  //   routerselect7.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect7.value?.classList.remove('ant-menu-item-selected')
  // }
  // if (router1 == '/dynamic/enabled/create' || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/applyTwice/enabledTwice') {
  //   routerselect8.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect8.value?.classList.remove('ant-menu-item-selected')
  // }
  // if (router1 == '/dynamic/proxies/create' || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/applyTwice/proxiesTwice') {
  //   routerselect9.value.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect9.value?.classList.remove('ant-menu-item-selected')
  // }
  // if (router1 == '/dynamic/leaseExternally/create' || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/applyTwice/leaseExternallyTwice') {
  //   routerselect10.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect10.value?.classList.remove('ant-menu-item-selected')
  // }
  // if (router1 == '/dynamic/statusChange/create' || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/applyTwice/statusChangeTwice') {
  //   routerselect11.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect11.value?.classList.remove('ant-menu-item-selected')
  // }

  // if (router1 == '/dynamic/selfUse/create' || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/applyTwice/selfUseTwice') {
  //   routerselect12.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect12.value?.classList.remove('ant-menu-item-selected')
  // }

  // if (router1 == '/dynamic/leasedOut/create' || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/applyTwice/leasedOutTwice') {
  //   routerselect13.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect13.value?.classList.remove('ant-menu-item-selected')
  // }
  // if (router1 == '/dynamic/transfer/create' || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/applyTwice/ltransferTwice') {
  //   routerselect14.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect14.value?.classList.remove('ant-menu-item-selected')
  // }
  // if (router1 == '/dynamic/selfUseReturn/create' || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/applyTwice/selfUseReturnTwice') {
  //   routerselect15.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect15.value?.classList.remove('ant-menu-item-selected')
  // }

  // if (router1 == '/dynamic/review/create' || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/applyTwice/reviewTwice') {
  //   routerselect16.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect16.value?.classList.remove('ant-menu-item-selected')
  // }

  // if (router1 == '/buss/apply/demandPlanJDC' || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/applyTwice/demandPlanJDCTwice') {
  //   routerselect17.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect17.value?.classList.remove('ant-menu-item-selected')
  // }
  // if (router1 == '/buss/apply/equipmentAcceptance' || router1?.substring(0, router1.lastIndexOf("?")) == '/buss/applyTwice/equipmentAcceptanceTwice') {

  //   routerselect18.value?.classList.add('ant-menu-item-selected')
  // } else {
  //   routerselect18.value?.classList.remove('ant-menu-item-selected')
  // }

});

// 修改路由监听，保持展开状态
// watch(() => route.path, (newPath) => {
//   const currentRoute = route.matched[route.matched.length - 1];
//   const title = currentRoute.meta?.title || '未命名';

//   // 检查标签是否已存在
//   const existingTab = tabs.value.find(tab => tab.key === newPath);
//   if (!existingTab) {
//     // 添加新标签
//     tabs.value.push({
//       key: newPath,
//       title: title,
//       closable: true,
//       query: { ...route.query }
//     });

//     // 保存更新后的标签状态
//     saveTabs();
//   }

//   // 激活当前标签
//   activeTab.value = newPath;

//   // 保存当前激活的标签
//   sessionStorage‌.setItem(ACTIVE_TAB_KEY, newPath);

//   // 更新选中菜单
//   selectedKeys.value = [newPath];
//   if (collapsed.value) {
//     updateIndicatorPosition(newPath);
//   } else {
//     const menuPath = findMenuPath(menus.value, newPath);
//     if (menuPath) {
//       openKeys.value = menuPath.slice(0, -1);
//     }
//   }
// }, { immediate: true });
</script>

<style lang="less" scoped>
.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: url('@/assets/equipment/back.png');
  /* 替换为实际的背景图片路径 */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.ant-menu-inline {
  background: transparent !important;
}

.header {
  background: rgba(255, 255, 255, 0.4) !important;
  backdrop-filter: blur(50px) !important;
  box-shadow: 0px 4px 10px 0px rgba(78, 89, 105, 0.06) !important;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 45px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);


  .header-left {
    display: flex;
    align-items: center;
    width: 50%;
    /* 左侧占50% */
    font-size: 17px;

    .header-logo {
      width: 26px;
      height: 26px;
      margin-right: 8px;
    }

    .header-title {
      color: rgba(61, 61, 61, 1);
      font-size: 17px;
      font-weight: 700;
      white-space: nowrap;

    }
  }

  .header-right {
    //width: 28%; /* 右侧占50% */
    display: flex;
    align-items: center;
    justify-content: flex-end;
    /* 靠右对齐 */

    .home-btn {
      height: 48px;
      padding: 0 12px;
      color: #2d3436;
      font-size: 14px;
      border: none;
      border-radius: 0;
      transition: background-color 0.3s;

      &:hover {
        background: rgba(0, 0, 0, 0.025);
        color: #2d3436;
      }

      .anticon {
        font-size: 16px;
        margin-right: 4px;
      }
    }

    :deep(.header-tools) {
      display: flex;
      align-items: center;
      height: 48px;

      .tool-item {
        padding: 0 12px;
        height: 48px;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }

        .anticon {
          font-size: 16px;
          color: #2d3436;
        }
      }
    }

    .user-info {
      display: flex;
      align-items: center;
      height: 48px;
      padding: 0 8px;
      margin-left: 8px;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background: rgba(0, 0, 0, 0.025);
      }

      .avatar {
        width: 28px;
        height: 28px;
        background: #e6f7ff;
        border: 1px solid #91d5ff;

        :deep(.anticon) {
          color: #1890ff;
          font-size: 14px;
        }
      }

      .username {
        margin: 0 4px 0 8px;
        color: #2d3436;
        font-size: 14px;
        display: flex;
        align-items: center;

        .anticon {
          font-size: 12px;
          margin-left: 4px;
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  background: transparent;
  position: relative; // 为menu-indicator提供定位上下文
  margin-top: 16px;
  height: calc(100vh - 75px) !important;

  .sider {
    position: relative;
    height: calc(100vh - 68px);
    z-index: 1; // 确保在indicator下面
    margin-left: 16px;
    border-radius: 10px;
    border: 1px solid #FFFFFF;
    background: rgba(255, 255, 255, 0.4);
    box-sizing: border-box;
    backdrop-filter: blur(398px);
    box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);
    padding: 3px;

    // 折叠状态下的弹出菜单样式
    :deep(.ant-menu-inline-collapsed) {
      &+.ant-menu-submenu-popup {
        .ant-menu {
          .ant-menu-item {
            padding-left: 16px !important; // 重置padding

            &.ant-menu-item-selected {
              &::before {
                left: 6px; // 调整蓝点位置
              }
            }
          }
        }
      }
    }

    .custom-menu {
      background: transparent !important;
      /* 移除菜单背景 */
      border-right: none;
      padding: 8px 0;

      :deep(.ant-menu-item) {
        height: 40px;
        line-height: 40px;
        margin: 4px 0;
        padding-left: 16px !important;
        border-radius: 0;

        .anticon {
          font-size: 16px;
          color: #595959;
        }

        &::before {
          content: '';
          position: absolute;
          left: 16px;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 4px;
          border-radius: 50%;
          background: #1890ff;
          opacity: 0;
          transition: opacity 0.3s;
        }

        &.ant-menu-item-selected {
          background: transparent;
          color: #1890ff;

          &::before {
            opacity: 1;
          }

          .anticon {
            color: #1890ff;
          }
        }
      }

      :deep(.ant-menu-submenu) {
        .ant-menu-submenu-title {
          padding-left: 16px !important;
          margin: 0;
          height: 40px;
          line-height: 40px;

          .anticon {
            font-size: 16px;
            color: #595959;
          }
        }

        &.ant-menu-submenu-open {
          .ant-menu-submenu-title {
            color: #1890ff;

            .anticon {
              color: #1890ff;
            }
          }
        }

        .ant-menu-sub {
          background: transparent;

          .ant-menu-item {
            padding-left: 32px !important;
          }
        }
      }
    }

    .collapse-btn {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: transparent;
      cursor: pointer;
      border-top: 1px solid rgba(0, 0, 0, 0.06);

      .expand-content {
        display: flex;
        align-items: center;
        gap: 8px;

        .anticon {
          font-size: 16px;
          color: #595959;
        }

        .collapse-text {
          font-size: 14px;
          color: #595959;
        }
      }

      &:hover {

        .anticon,
        .collapse-text {
          color: #1890ff;
        }
      }
    }
  }

  .content {
    flex: 1;
    background: transparent;
    padding: 0px 16px 0;

    overflow: hidden;
    display: flex;
    flex-direction: column;
    margin-left: -6px;
    position: relative;
    z-index: 1;

    .content-header {
      background: transparent;
      padding: 0;
      margin-bottom: 16px;

      .tabs-container {
        display: flex;
        background: transparent !important;
        /* 移除标签容器背景 */
        border-radius: 2px;
        height: 40px;
        padding: 0 16px;
        align-items: center;
        box-shadow: none;

        :deep(.ant-tabs) {
          .ant-tabs-nav {
            margin: 0;
            border: none;

            &::before {
              display: none;
            }

            .ant-tabs-nav-list {
              border: none;

              .ant-tabs-tab {
                background: transparent;
                border: none;
                padding: 8px 0;
                margin: 0 24px 0 0;
                transition: all 0.3s;

                .ant-tabs-tab-btn {
                  color: #595959;
                  font-size: 14px;
                }

                &.ant-tabs-tab-active {
                  .ant-tabs-tab-btn {
                    color: #1890ff;
                  }
                }

                .anticon-close {
                  margin-left: 8px;
                  width: 12px;
                  height: 12px;
                  font-size: 12px;
                  color: #00000040;

                  &:hover {
                    color: #000000;
                  }
                }
              }
            }
          }
        }
      }
    }

    .content-body {
      flex: 1;
      background: transparent;
      /* 移除内容区域背景 */
      border-radius: 2px;
      padding: 24px;
      overflow: auto;
      box-shadow: none;

      :deep(.ant-table-wrapper) {
        .ant-table {
          background: transparent;

          .ant-table-thead>tr>th {
            background: transparent;
            color: #595959;
            font-weight: 500;
            border-bottom: 1px solid #f0f0f0;
          }

          .ant-table-tbody>tr>td {
            border-bottom: 1px solid #f0f0f0;
          }
        }
      }
    }
  }
}

.menu-indicator {
  position: absolute;
  width: 14px;
  height: 40px;
  left: 84px;
  right: 0;
  top: 24px;
  transform: translateY(calc(var(--n) * 60px));
  z-index: 100;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;

  .indicator-wrapper {
    position: relative;
    width: 100%;
    height: 100%;

    // 白色外边框双凸曲线
    .indicator-outline {
      position: absolute;
      right: -14px;
      top: 0;
      width: 30px;
      height: 100%;
      display: flex;
      align-items: center;
      /*clip-path: path('M14,0 Q25,3 24,19 Q25,30 14,40');
        background: rgba(255, 255, 255, 0.4);  // 外层白色背景*/
    }

    // &::after {  // 添加内层填充
    //   content: '';
    //   position: absolute;
    //   right: -12px;
    //   top: 1px;
    //   width: 28px;
    //   height: calc(100% - 2px);
    //   clip-path: path('M13,0 Q24,3 23,19 Q24,30 13,38');
    //   background: rgba(255, 255, 255, 0.4);
    // }

    // 内部浅蓝色双凸曲线
    .indicator-bg {
      position: absolute;
      right: 2px;
      top: 2px;
      width: 14px;
      height: calc(100% - 4px);
      clip-path: path('M10,0 Q16,10 10,20 Q16,30 10,36');
    }

    // 蓝点
    .dot {
      position: absolute;
      right: 4px;
      top: 50%;
      transform: translateY(-50%);
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: #1890ff;
      box-shadow: 0 0 4px rgba(24, 144, 255, 0.5);
    }
  }
}

// 确保选中菜单项的样式正确
.custom-menu {
  :deep(.ant-menu-item) {
    position: relative;
    transition: color 0.3s;

    &.ant-menu-item-selected {
      background: transparent;
      color: #1890ff;

      .anticon {
        color: #1890ff;
      }
    }
  }
}

// 响应式布局
@media screen and (max-width: 768px) {
  .sider {
    width: 89px !important;
    min-width: 89px !important;
  }
}

.tabs-wrapper {

  padding: 0px 16px 0;
  border-radius: 8px 8px 0 0;

  :deep(.ant-tabs) {
    .ant-tabs-nav {
      margin: 0;
      border: none;

      &::before {
        display: none; // 移除底部边框
      }

      .ant-tabs-nav-wrap {
        .ant-tabs-nav-list {
          // gap: 8px; // 标签之间的间距

          .ant-tabs-tab {
            margin: 0;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.27);
            border: none;
            border-radius: 8px 8px 0 0;
            transition: all 0.3s;

            .ant-tabs-tab-btn {
              color: #666;
              font-size: 14px;
            }

            &-active {
              background: rgba(255, 255, 255, 0.6);

              .ant-tabs-tab-btn {
                color: #1890ff;
              }
            }

            // 关闭按钮样式
            .ant-tabs-tab-remove {
              margin-left: 8px;
              color: #999;
              font-size: 12px;

              &:hover {
                color: #666;
              }
            }
          }
        }
      }
    }

    // 内容区域样式
    .ant-tabs-content {

      border-radius: 0 0 8px 8px;
    }
  }
}

.sider {
  .custom-menu {
    height: calc(100% - 48px); // 减去收起按钮的高度
    overflow-y: auto;
    overflow-x: hidden;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 3px;
    }
  }

  .collapse-btn {
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    cursor: pointer;
    width: 100%;

    .expand-content {
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: center;
      text-align: center;

      .collapse-text {
        margin-left: 8px;
      }
    }
  }
}

:deep(.ant-menu-inline-collapsed) {
  .ant-menu-submenu-popup {
    .ant-menu {
      background: #fff !important; // 设置实色背景
      box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
        0 6px 16px 0 rgba(0, 0, 0, 0.08),
        0 9px 28px 8px rgba(0, 0, 0, 0.05);

      .ant-menu-item {
        background: #fff;

        &:hover {
          background: #f5f5f5;
        }
      }
    }
  }
}

/* 确保弹出的子菜单样式正确 */
:deep(.ant-menu-submenu-popup) {
  .ant-menu {
    background: #fff !important;

    .ant-menu-item {
      margin: 0 !important;
      padding: 0 16px !important;
      height: 40px !important;
      line-height: 40px !important;

      &:hover {
        background: #f5f5f5;
      }

      &.ant-menu-item-selected {
        background: #e6f7ff;
        color: #1890ff;
      }
    }
  }
}

.menu-container {
  margin-top: 18px;
  overflow-x: hidden;
  overflow-y: auto;
  height: 91%;
  /* 隐藏滚动条但保持可滚动 */
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */

  /* Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }
}

/deep/ .ant-table-cell-fix-left-last {
  box-shadow: 2px 0px 2px 0px rgba(84, 102, 138, 0.08) !important;
}

// /deep/ .ant-table-cell-fix-right-first::after{
//   box-shadow: 2px 0px 2px 0px rgba(84, 102, 138, 0.08) !important;
// }
/deep/ .ant-table .ant-table-cell-fix-left,
.ant-table-cell-fix-right {
  box-shadow: 2px 0px 2px 0px rgba(84, 102, 138, 0.08) !important;
}
</style>

<style>
.ant-picker-cell {
  color: var(--text-color) !important;
}

.ant-picker-cell-disabled {
  color: #999 !important;
}
</style>
