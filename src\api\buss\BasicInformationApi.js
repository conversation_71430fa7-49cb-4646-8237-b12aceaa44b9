import Request from '@/utils/request-util';

/**
 * 设备验收API
 *
 * <AUTHOR> @date 2024/01/09
 */
export class BasicInformationApi {
  /**
   * 获取新建数据
   *
   * <AUTHOR> @date 2024/01/09
   */
  static getData(params) {
    return Request.get('/apiBus/equipmentClassificationCode/getSelectList',params);
  }

  static getEquipmentTypeTree() {
    return Request.get('/apiBus/equipmentClassificationCode/tree');
  }

  static getEquData(params) {
    return Request.get('/apiBus/equipmentClassificationCode/getSelectList',params);
  }
}
