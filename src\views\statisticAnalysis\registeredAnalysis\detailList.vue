<!-- 应用编辑弹窗 -->
<template>
  <div>
    <!-- 新增 -->
    <a-modal width="60%" :maskClosable="false" :visible="visible" :confirm-loading="loading" :forceRender="true"
      title="设备列表" :body-style="{ paddingBottom: '8px', paddingTop: '0' }" centered="true"
      @update:visible="updateVisible">
      <div>
        <!-- <a-button class="tool-button" style="display: flex;align-items: center;justify-content: space-between;"
            @click="handleExport">
            <i class="iconfont icon-rongqi-copy" style="margin-right: 6px;"></i>
            导出
          </a-button> -->
        <a-table :columns="columns" :data-source="tableData" @change="handleTableChange" :pagination="false"
          :rowKey="(record) => record.equId" :scroll="{ x: 'max-content' }" class="custom-table">
          <template #emptyText>
          <div class="custom-empty">
            <img src="@/assets/images/noData.png"/>
            <!-- <p>抱歉，暂时还没有数据</p> -->
          </div>
        </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="handleView(record)">查看</a>
              </a-space>
            </template>
          </template>
        </a-table>
        <div class="table-footer">
          <div class="total-info">
            <!-- 已选{{ selectedRows.length }}数据，共{{ pagination.total }}条数据 -->
          </div>
          <a-pagination v-model:current="pagination.current" :total="pagination.total" :pageSize="pagination.pageSize"
            @change="handleTableChange" :showSizeChanger="false" />
        </div>
      </div>
      <template #footer>
        <!-- <a-button key="back" @click="updateVisible(false)">关 闭</a-button> -->
      </template>
    </a-modal>
    <detail-modal v-model:visible="showDetail" :data="currents" title="设备信息" @close="handleDrawerClose" />
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { SysAppApi } from '@/api/system/app/SysAppApi';
import { registeredApi } from '@/api/analysis/registeredApi';
import DetailModal from '@/components/DetailModal/index.vue';
import { BasicInformationApi } from '@/api/buss/BasicInformationApi';
import { UnitApi } from '@/api/common/UnitApi'
import { EnumApi } from '@/api/common/enum';
import { EquipmentAcceptanceApi } from '@/api/buss/EquipmentAcceptanceApi';

export default {
  name: 'deviceListEdit',
  components: { DetailModal },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Array,
    queryParam: Object,
    obj: Object
  },
  emits: ['done', 'update:visible', 'getList'],
  data() {
    return {
      // 表单数据
      list: this.data,
      // 提交状态
      loading: false,
      queryParams: {
        pageSize: 10,
        pageNo: 1
      },
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共 ${total} 条`
      },
      arr: [],
      arr1: [],
      columns: [
        // 左侧固定列
        { title: '设备编号', dataIndex: 'code', width: 120, },
        { title: '财务卡片编号', dataIndex: 'financialNumber', width: 200, },
        { title: '设备名称', dataIndex: 'equNameStr', width: 200, },
        { title: '规格型号', dataIndex: 'equModelStr', width: 140, },
        { title: '型号备注', dataIndex: 'equModelInfo', width: 250, },
        { title: '管理单位', dataIndex: 'managementOrgStr', width: 200 },
        { title: '存放地点', dataIndex: 'storageLocationStr', width: 250, },
        { title: '使用单位', dataIndex: 'useOrgStr', width: 200, },
        { title: '财务原值', dataIndex: 'financialOriginalValue', width: 120 },
        { title: '净值', dataIndex: 'netWorth', width: 120 },
        { title: '管理状态', dataIndex: 'managementStatusStr', width: 100 },
        { title: '设备状态', dataIndex: 'equConditionStr', width: 100 },
        { title: '生产厂家', dataIndex: 'manufacturer', width: 150 },
        { title: '出厂日期', dataIndex: 'productionDate', width: 120 },
        { title: '出厂编号', dataIndex: 'factoryNumber', width: 120 },
        { title: '设备类别', dataIndex: 'equTypeStr', width: 200 },
        { title: '设备种类', dataIndex: 'equSubTypeStr', width: 200 },
        { title: '设备性质', dataIndex: 'equNatureStr', width: 120 },
        { title: '固定资产分类', dataIndex: 'fixedAssets', width: 160 },
        { title: '产权单位', dataIndex: 'propertyOrgStr', width: 120 },
        { title: '功率kw', dataIndex: 'power', width: 100 },
        { title: '设备型号编码', dataIndex: 'modelCode', width: 160 },
        { title: '购置年度', dataIndex: 'purchaseYear', width: 100 },
        { title: '操作', key: 'action', width: 80, fixed: 'right' }
      ],
      tableData: [],
      showDetail: false,
      currents: {}
    };
  },
  created() {
    this.fetchData()
  },
  methods: {
    handleView(record){
  //通过接口去获取
  EquipmentAcceptanceApi.getEquipmentFullInfo({ id: record.id }).then(res => {
    res.data.type = 'reg'
    this.currents = res.data;
    console.log('currents', this.currents);
    this.currents.type = 'reg'
    this.showDetail = true;
  })
},
    handleDrawerClose(){
      this.currents = null;
      this.showDetail = false;
    },
    // 获取列表数据
    fetchData() {
      this.queryParams.pageNo = this.pagination.current
      this.queryParams.pageSize = this.pagination.pageSize
      let queryp = {
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
        ...this.queryParam
      }
      console.log('queryp', queryp);

      try {
        const res = registeredApi.findEquipmentPage(queryp)
        res.then(res => {
          console.log('res', res);

          this.tableData = res.data.rows
          this.pagination = {
            ...this.pagination,
            total: res.data.totalRows || 0
          };
        })
      } catch (error) {
        console.error('获取数据失败:', error);
      }
    },

    handleTableChange(current) {
      this.pagination.current = current
      this.queryParams.pageNo = current
      this.queryParams.pageSize = this.pagination.pageSize
      this.queryParams.keyword = this.queryParams.searchText
      this.fetchData();

    },
    updateVisible(value) {
      this.$emit('update:visible', value);
    },

    handleExport () {
  // 直接调用导出，会打开新页面下载
  let obj2={
    ...this.queryParam,
    ...this.obj
  }
  console.log('obj2',obj2);

  registeredApi.downLoad2(obj2);
},
  }
};
</script>

<style lang="less" scoped>
.search-form {

  padding: 16px 16px; // 统一内边距
  border-radius: 8px; // 增加圆角

  .search-row1 {
    display: flex;
    width: 80%;
    justify-content: space-between;
  }

  .search-row {
    display: flex;
    flex-wrap: wrap;
    gap: 24px; // 增加间距
    margin-bottom: 12px; // 增加行间距
    justify-content: space-between;

    &:last-child {
      margin-bottom: 0;
    }

    .search-item {
      display: flex;
      align-items: center;
      // min-width: 300px;
      // flex: 1;

      .label {
        min-width: 80px;
        // margin-right: 12px; // 增加标签和输入框的间距
        color: #666;
        font-size: 14px;
      }

      :deep(.ant-select),
      :deep(.ant-input) {
        width: 64%;
        height: 32px;

        .ant-select-selector {
          background: #fff; // 确保选择器背景为白色
          border-radius: 4px;
        }
      }

      :deep(.ant-input) {
        background: #fff; // 确保输入框背景为白色
        border-radius: 4px;
      }
    }

    .search-button,
    .reset-button {
      height: 32px;
      min-width: 80px;
      margin-left: auto;
      border-radius: 4px; // 统一按钮圆角
    }

    .search-button {
      background: #1890ff; // 查询按钮使用主题蓝色
    }

    .reset-button {
      background: #fff; // 重置按钮使用白色背景
      border: 1px solid #d9d9d9;
    }
  }
}

.custom-table {
  margin-top: 16px;
  min-height: 400px;

  :deep(.ant-table) {

    // 提高固定列的层级
    .ant-table-fixed-left,
    .ant-table-fixed-right {
      background: #fff;
      z-index: 3; // 增加层级
    }

    .ant-table-cell {
      white-space: nowrap !important; // 强制不换行
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      line-height: 1 !important;
      font-size: 14px !important;

      >span,
      >div {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }
    }

    // 大屏幕样式（默认）
    @media screen and (min-width: 1920px) {
      thead .ant-table-cell {
        padding: 20px 20px !important;
        height: 60px !important;
      }

      .ant-table-row {
        height: 60px !important;
      }
    }

    // 中等屏幕样式
    @media screen and (min-width: 1366px) and (max-width: 1919px) {
      thead .ant-table-cell {
        padding: 10px 20px !important;
        height: 40px !important;
      }

      .ant-table-row {
        height: 40px !important;
      }
    }

    // 小屏幕样式
    @media screen and (max-width: 1365px) {
      thead .ant-table-cell {
        padding: 4px 8px !important;
        height: 32px !important;
      }

      .ant-table-row {
        height: 32px !important;
      }
    }

    // 调整固定列单元格样式
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      z-index: 3 !important; // 增加层级
      background: #fff !important;
    }

    // 调整表头固定列样式
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right {
        z-index: 4 !important; // 确保表头在最上层
        background: #DAECFF !important;
      }
    }

    // 优化阴影效果
    .ant-table-fixed-right::before,
    .ant-table-fixed-left::before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      width: 10px;
      pointer-events: none;
      z-index: 2; // 阴影层级低于固定列
      transition: box-shadow .3s;
    }

    .ant-table-fixed-left::before {
      right: 0;
      box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
    }

    .ant-table-fixed-right::before {
      left: 0;
      box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
    }

    // 设置表格内容的层级
    .ant-table-content {
      z-index: 1;
    }

    // 确保滚动区域正确显示
    .ant-table-body {
      overflow-x: auto !important;
      overflow-y: auto !important;
    }

    // 固定列不换行
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .ant-table-row {
      height: 24px !important;
    }

    // 表头固定列不换行
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.table-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}

:deep(.ant-table-tbody .ant-table-cell) {
  padding: 10px 16px !important;
}

/deep/ .ant-modal-body {
  padding: 0 24px !important;
}
.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>
