<!-- 组织机构编辑弹窗 -->
<template>
  <div>
    <!-- 编辑 -->
    <common-drawer :width="800" :visible="visible" title="编辑设备类型" @close="updateVisible(false)" v-if="isUpdate"
      :isShowTab="true">
      <template #extra>
        <div style="height: 32px">
          <a-button type="primary" @click="save" :loading="loading">确定</a-button>
          <a-button @click="updateVisible(false)" :loading="loading" style="margin-left: 20px;">取消</a-button>
        </div>
      </template>
      <!-- 基本信息 -->
      <a-form ref="formRef" :model="form" :rules="rules">
        <a-form-item label="上级类型:" name="parentId">
          <a-tree-select :disabled="form.parentId === '-1'" v-model:value="form.parentId" style="width: 100%"
            :tree-data="orgList" :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" placeholder="请选择上级机构"
            :fieldNames="{ children: 'children', label: 'name', key: 'id', value: 'id' }" allow-clear
            @select="selectOrg" />
        </a-form-item>
        <a-form-item label="类型名称:" name="name">
          <a-input v-model:value="form.name" placeholder="请输入类型名称" allow-clear />
        </a-form-item>
        <a-form-item label="类型编码:" name="code">
          <a-input v-model:value="form.code" placeholder="请输入类型编码" allow-clear />
        </a-form-item>
        <div style="display: flex; width: 100%;" v-if="address">
          <!-- <a-form-item label="保养周期:" name="maintenanceCycleNum">
            <a-input-number v-model:value="form.maintenanceCycleNum" style="width: 100px !important;" :min="0"/>
          </a-form-item> -->
          <a-form-item label="保养周期:" style="margin-left: 5px;" name="maintenanceCycleType">
            <a-select v-model:value="form.maintenanceCycleType" placeholder="请选择" style="width: 150px" allow-clear>
              <a-select-option v-for="item in xzList" :key="item.label" :value="item.value">
              {{ item.label }}
            </a-select-option>
            </a-select>
          </a-form-item>
        </div>
      </a-form>
    </common-drawer>

    <!-- 新增 -->
    <a-modal :width="800" :visible="visible" :confirm-loading="loading" :forceRender="true" :maskClosable="false"
      title="新建设备类型" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible" @ok="save" v-else
      @close="updateVisible(false)">
      <a-form ref="formRef" :model="form" :rules="rules">
        <a-form-item label="上级类型:" name="parentId">
          <a-tree-select :disabled="form.parentId === '-1'" v-model:value="form.parentId" style="width: 100%"
            :tree-data="orgList" :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" placeholder="请选择上级机构"
            :fieldNames="{ children: 'children', label: 'name', key: 'id', value: 'id' }" allow-clear
            @select="selectOrg" />
        </a-form-item>
        <a-form-item label="类型名称:" name="name">
          <a-input v-model:value="form.name" placeholder="请输入类型名称" allow-clear />
        </a-form-item>
        <a-form-item label="类型编码:" name="code">
          <div class="flex flex-row">
            <div class="fixed-code" title="前缀固定码">
              <div v-if="codePrefixs.length > 0">
                <span>
                  {{ codePrefixs[codePrefixs.length-1].fcode }}
                  <!--
                  <template v-for="(item, index) in codePrefixs" :key="'fixed-code_'+index">
                    {{ item.fcode }}
                  </template>
                  -->
                </span>
              </div>
            </div>
            <a-input v-model:value="form.code" placeholder="请输入类型编码" allow-clear class="flex-1"/>
          </div>
        </a-form-item>
        <div style="display: flex; width: 100%;" v-if="address">
          <!-- <a-form-item label="保养周期:" name="maintenanceCycleNum">
            <a-input-number v-model:value="form.maintenanceCycleNum" style="width: 100px !important;" :min="0"/>
          </a-form-item> -->
          <a-form-item label="保养周期:" style="margin-left: 5px;" name="maintenanceCycleType">
            <a-select v-model:value="form.maintenanceCycleType" placeholder="请选择" style="width: 150px" allow-clear>
              <a-select-option v-for="item in xzList" :key="item.label" :value="item.value">
              {{ item.label }}
            </a-select-option>
            </a-select>
          </a-form-item>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { message  } from 'ant-design-vue';
import { UserApi } from '@/api/system/user/UserApi';
import { EnumApi } from '@/api/common/enum';
import { equipmentTypeConfigurationApi } from '@/api/basicInformationConfiguration/equipmentTypeConfigurationApi';
import CityTreeSelect from '@/components/CityTreeSelect/index.vue';
import { aintenancecycleApi } from '@/api/basicInformationConfiguration/aintenancecycleApi';

export default {
  name: 'EquipmentTypeEdit',
  components: { CityTreeSelect },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 接收上级传过来的组织机构信息
    data: Object,
    // 组织机构列表
    orgList: Array,
    // 是否是编辑界面
    isUpdate: Boolean,
    // 默认选中
    defaultKey: String,
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      codePrefixs:[], /// 前缀码
      form: {
        code: null,
        parentId: null,
        name:null,
        maintenanceCycleType: null,
        maintenanceCycleNum: null,
      },
      // 表单验证规则
      /*
      rulesx: {
        parentId: [{ required: true, message: '请选择上级类型', type: 'string', trigger: 'blur' }],
        maintenanceCycleNum: [{ required: true, message: '请输入保养周期', trigger: 'blur' }],
        maintenanceCycleType: [{ required: true, message: '请选择保养周期', trigger: 'blur' }],
        name: [{ required: true, message: '请输入类型名称', type: 'string', trigger: 'blur' }],
        code: [
          { required: true, message: '请输入类型编码,数值类型', type: 'string', trigger: 'blur'}
        ]
      },*/
      // 提交状态
      loading: false,
      xzList: [],
      address: false,
      codeRules : {"1":2,"2":3,"3":2,"4":4} // 规则 20250704 孔军提供,超过4层都是4位
    };
  },
  computed: {
    // 获取枚举数据
    rules: function () {
      let rs = {
        parentId: [{ required: true, message: '请选择上级类型', type: 'string', trigger: 'blur' }],
        maintenanceCycleNum: [{ required: true, message: '请输入保养周期', trigger: 'blur' }],
        maintenanceCycleType: [{ required: true, message: '请选择保养周期', trigger: 'blur' }],
        name: [{ required: true, message: '请输入类型名称', type: 'string', trigger: 'blur' }],
        code: [
          { required: true,message: '请输入类型编码,数值类型', type: 'string', trigger: 'blur',pattern: /^[0-9]+$/ }
        ]
      }
      if(!this.isUpdate) // 编辑不限制数值类型
      {
        let len = 2;
        if(this.codePrefixs.length>0){
          len = this.codeRules[this.codePrefixs.length+1]||4;
        }
        //rs.code[0].pattern = new RegExp("^\\d{"+len+"}$");///^\d{1,4}$/;
        rs.code[0].len = len;
        rs.code[0].message = '请输入类型编码,数值类型,长度为'+len+'位';
        this.$refs.formRef?.clearValidate();
      }
      return rs;
    }
  },
  watch: {
    // 编辑时监听data是否变化
    data() {
      this.init();
      this.initEnumData()
      // 清空表单 20250704 王文胜
      this.resetForm();
    }
  },
  methods: {
    resetForm(){ // 清空表单 20250704 王文胜
      this.codePrefixs = [];
      this.$refs.formRef.clearValidate();
    },
    selectOrg(value, node, exea) {
      console.log(arguments);
      this.codePrefixs = [];
      if (node.level == 2) {
        this.address = true
      } else {
        this.address = false
      }
      //this.maintenanceCycleType = this.xzList[0]?.value // 这里有bug
      // 20250704 王文胜
      this.parseCodePrefix(value,node,{id:-1000,level:-1,children:this.orgList},this.codePrefixs);
    },
    parseCodePrefix(value,node,root,ret) /// 解析编码前缀 20250704 王文胜
    {
      if(root.level>0 && root.id==value)
      {
        ret.push({id:root.id,level:root.level,name:root.name,code:root.code,fcode:this.completeCode(root.level,root.code)});
        return true;
      }
      if(root.children && root.children.length>0)
        for(let i=0;i<root.children.length;i++)
        {
          if(this.parseCodePrefix(value,node,root.children[i],ret))
          {
            if(root.level>0)
              ret.unshift({id:root.id,level:root.level,name:root.name,code:root.code,fcode:this.completeCode(root.level,root.code)});
            return true;
          }
        }
      return false;
    },
    completeCode(level,value){  /// 补全代码 20250704 王文胜
      let len = 4; // 默认4位长度
      len = this.codeRules[level]||4;
      value = value+"";
      let offset = len - value.length;
      for(let i=0;i<offset;i++)
      {
        value = "0"+value;
      }
      return value;
    },
     async initEnumData() {
      let arr = await aintenancecycleApi.getSelectList();
      this.xzList = arr.data
    },
    // 初始化数据
    async init() {
      this.address = false
      if (this.data.id) {
        let res = await equipmentTypeConfigurationApi.detail(this.data);
        this.form = res.data
        console.log('this.form',this.form);

        if(this.form.level == '3'){
        this.address = true
      } else {
        this.address = false
      }
      } else {
        this.form = {}
      }
    },
    save() {
      // 校验表单
      this.$refs.formRef.validate().then(async valid => {
        if (valid) {
          // 修改加载框为正在加载
          this.loading = true;
          let result;
          // 执行编辑或修改用户方法
          if (this.isUpdate) {
            result = equipmentTypeConfigurationApi.edit(this.form);
          } else {
            let code = "";
            if(this.codePrefixs.length > 0)
            {
              code += this.codePrefixs[this.codePrefixs.length-1].fcode;
            }
            /*
            for(let i = 0; i < this.codePrefixs.length; i++)
            {
              code += this.codePrefixs[i].fcode;
            }*/
            code += this.form.code;
            // 合并数据
            let formData = Object.assign({}, this.form,{code:code});
            result = equipmentTypeConfigurationApi.add(formData);
          }
          result.then(res => {
            // 移除加载框
            this.loading = false;
            // 提示添加成功
            message.success(res.message);

            // 如果是新增，则form表单置空
            if (!this.isUpdate) {
              this.form = {};
            }
            // 关闭弹框，通过控制visible的值，传递给父组件
            this.updateVisible(false);
            this.$emit('done');
          })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2021/4/7 11:00
     */
    updateVisible(value) {
      this.codePrefixs = [];
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style>
.ant-modal-content{
  background-color: #F4F9FF;
}
.ant-modal-header{
  background-color: #F4F9FF;
}
.flex
{
  display: flex;
}
.flex-row
{
  flex-direction: row;
}
.flex-1
{
  flex:1;
}
.fixed-code > div
{
  margin-right: 6px;
  display: inline-block;
}
.fixed-code span
{
  letter-spacing: 2px;
  display: inline-block;
  border:1px solid #aaa;
  text-align: center;
  margin:0px 2px;
  padding: 3px 5px;
  border-radius: 3px;
  font-size: 16px;
  background-color: #eee;
}
</style>
