import Request from '@/utils/request-util';

/**
 * 菜单按钮管理api
 *
 * <AUTHOR>
 * @date 2021/4/13 16:06
 */
export class MenuButtonApi {
  /**
   * 获取分页数据
   *
   * @param {String} params.menuId - 菜单id
   * <AUTHOR>
   * @date 2021/4/1 16:07
   */
  static findPage(params) {
    return Request.getAndLoadData('/sysMenuButton/pageList', params);
  }

  /**
   * 获取菜单按钮列表
   *
   * <AUTHOR>
   * @date 2021/4/13 16:07
   */
  static getMenuButtonList(params) {
    return Request.get('/sysMenuButton/pageList', params);
  }

  /**
   * 新增菜单按钮
   *
   * <AUTHOR>
   * @date 2021/4/13 16:07
   */
  static addMenuButton(params) {
    return Request.post('/sysMenuButton/add', params);
  }

  /**
   * 添加系统菜单按钮-默认按钮
   *
   * <AUTHOR>
   * @date 2021/4/13 16:07
   */
  static addDefaultMenuButton(params) {
    return Request.post('/sysMenuButton/addSystemDefaultButton', params);
  }

  /**
   * 修改菜单按钮
   *
   * <AUTHOR>
   * @date 2021/4/13 16:07
   */
  static editMenuButton(params) {
    return Request.post('/sysMenuButton/edit', params);
  }

  /**
   * 删除菜单按钮
   *
   * <AUTHOR>
   * @date 2021/4/13 16:07
   */
  static deleteMenuButton(params) {
    return Request.post('/sysMenuButton/delete', params);
  }

  /**
   * 批量删除菜单按钮
   *
   * <AUTHOR>
   * @date 2021/4/13 16:07
   */
  static batchDeleteMenuButton(params) {
    return Request.post('/sysMenuButton/batchDelete', params);
  }
}
