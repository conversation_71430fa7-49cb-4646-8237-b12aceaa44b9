<!-- 设备信息卡片 -->
<template>
  <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
    :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }">
    <a-row :gutter="16">
      <a-col :md="24" :sm="24" :xs="24" style="padding-right: 0px;">
        <a-radio-group v-model:value="titles" style="margin-bottom: 36px">
          <a-radio-button value="dbjl">调拨记录</a-radio-button>
          <a-radio-button value="zlmx">租赁明细</a-radio-button>
          <a-radio-button value="ztbgjl">状态变更记录</a-radio-button>
          <a-radio-button value="byjl">保养记录</a-radio-button>
          <a-radio-button value="pdjl">盘点记录</a-radio-button>
          <a-radio-button value="wxjl">维修记录</a-radio-button>
          <a-radio-button value="dxjl">大修记录</a-radio-button>
          <a-radio-button value="jgjl">技改记录</a-radio-button>
        </a-radio-group>

        <a-table v-if="titles == 'dbjl'" :columns="columns" :data-source="datassdbjl" bordered :pagination="false"
          :scroll="{ x: 'max-content' }">
          <template #emptyText>
            <div class="custom-empty">
              <img src="@/assets/images/noData.png" />
              <!-- <p>抱歉，暂时还没有数据</p> -->
            </div>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'transFormId'">
              <a @click="gotoView(record)" target="_blank">
                {{ record.transFormId?.length > 13 ? record.transFormId.substring(0, 13) + '...' : record.transFormId }}
              </a>
            </template>
          </template>
        </a-table>
        <a-table v-else-if="titles == 'zlmx'" :columns="columns1" :data-source="datass" bordered :pagination="false"
          :scroll="{ x: 'max-content' }">
          <template #emptyText>
            <div class="custom-empty">
              <img src="@/assets/images/noData.png" />
              <!-- <p>抱歉，暂时还没有数据</p> -->
            </div>
          </template>
        </a-table>
        <a-table v-else-if="titles == 'ztbgjl'" :columns="columns2" :data-source="datassztbg" bordered
          :pagination="false" :scroll="{ x: 'max-content' }">
          <template #emptyText>
            <div class="custom-empty">
              <img src="@/assets/images/noData.png" />
              <!-- <p>抱歉，暂时还没有数据</p> -->
            </div>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'transFormId'">
              <a @click="gotoView(record)" target="_blank">
                {{ record.transFormId?.length > 13 ? record.transFormId.substring(0, 13) + '...' : record.transFormId }}
              </a>
            </template>
          </template>
        </a-table>
        <a-table v-else-if="titles == 'pdjl'" :columns="columns4" :data-source="datass" bordered :pagination="false"
          :scroll="{ x: 'max-content' }">
          <template #emptyText>
            <div class="custom-empty">
              <img src="@/assets/images/noData.png" />
              <!-- <p>抱歉，暂时还没有数据</p> -->
            </div>
          </template>
        </a-table>
        <a-table v-else-if="titles == 'byjl'" :columns="columns3" :data-source="datass" bordered :pagination="false"
          :scroll="{ x: 'max-content' }">
          <template #emptyText>
            <div class="custom-empty">
              <img src="@/assets/images/noData.png" />
              <!-- <p>抱歉，暂时还没有数据</p> -->
            </div>
          </template>
        </a-table>
        <a-table v-else-if="titles == 'wxjl'" :columns="columns5" :data-source="datass" bordered :pagination="false"
          :scroll="{ x: 'max-content' }">
          <template #emptyText>
            <div class="custom-empty">
              <img src="@/assets/images/noData.png" />
              <!-- <p>抱歉，暂时还没有数据</p> -->
            </div>
          </template>
        </a-table>
        <a-table v-else-if="titles == 'dxjl'" :columns="columns6" :data-source="datass" bordered :pagination="false"
          :scroll="{ x: 'max-content' }">
          <template #emptyText>
            <div class="custom-empty">
              <img src="@/assets/images/noData.png" />
              <!-- <p>抱歉，暂时还没有数据</p> -->
            </div>
          </template>
        </a-table>
        <a-table v-else="titles=='jgjl'" :columns="columns7" :data-source="datass" bordered :pagination="false"
          :scroll="{ x: 'max-content' }">
          <template #emptyText>
            <div class="custom-empty">
              <img src="@/assets/images/noData.png" />
              <!-- <p>抱歉，暂时还没有数据</p> -->
            </div>
          </template>
        </a-table>
      </a-col>
    </a-row>
  </a-form>
</template>

<script setup>
import { defineComponent, reactive, toRefs, ref, watch } from 'vue';
import { EquipmentAcceptanceApi } from '@/api/buss/EquipmentAcceptanceApi';
import iconData from 'ele-admin-pro/es/ele-icon-picker/icons';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: '设备详情'
  }
});
const titles = ref('dbjl');
const columns = [
  {
    title: '序号',
    width: 80,
    customRender: ({ text, record, index }) => {
      return `${index + 1}`;
    },
  },
  {
    title: '变更日期',
    dataIndex: 'confirmDate',
    width: 120,
  },
  {
    title: '管理单位(前)',
    dataIndex: 'preManagerOrgStr',
    width: 130,
    ellipsis: true,
  },
  {
    title: '使用单位(前)',
    dataIndex: 'preUseOrgStr',
    width: 130,
    ellipsis: true,
  },
  {
    title: '管理单位(后)',
    dataIndex: 'afterManagerOrgStr',
    width: 130,
    ellipsis: true,
  },
  {
    title: '使用单位(后)',
    dataIndex: 'afterUseOrgStr',
    width: 130,
    ellipsis: true,
  },
  {
    title: '申请类型',
    dataIndex: 'transFormTypeStr',
    width: 120,
  },
  {
    title: '关联流程',
    dataIndex: 'transFormId',
    width: 120,
  },

]
const columns1 = [
  {
    title: '序号',
    dataIndex: 'xh',
    width: 80,
  },
  {
    title: '相关申请流程',
    dataIndex: 'mc',
    width: 120,
  },
  {
    title: '租赁类型',
    dataIndex: 'dw',
  },
  {
    title: '申请单位',
    dataIndex: 'sl',

  },
  {
    title: '使用单位',
    dataIndex: 'sccj',
  },
  {
    title: '租赁总价',
    dataIndex: 'sccj',
  },
  {
    title: '租赁时长',
    dataIndex: 'sccj',
  },
  {
    title: '租赁起始日期',
    dataIndex: 'sccj',
  },
  {
    title: '租赁结束日期',
    dataIndex: 'ccbh',

  },
]
const datassztbg = ref([]);
const columns2 = [
  {
    title: '序号',
    width: 80,
    customRender: ({ text, record, index }) => {
      return `${index + 1}`;
    },
  },
  {
    title: '变更日期',
    dataIndex: 'confirmDate',

  },
  {
    title: '申请单位',
    dataIndex: 'applyOrgStr',
  },
  {
    title: '变更前状态',
    dataIndex: 'preChangeStr',

  },
  {
    title: '变更后状态',
    dataIndex: 'afterChangeStr',
  },
  {
    title: '关联流程',
    dataIndex: 'transFormId',
  },
]
const columns3 = [
  {
    title: '序号',
    dataIndex: 'xh',
    width: 80,
  },
  {
    title: '保养单号',
    dataIndex: 'mc',

  },
  {
    title: '保养人',
    dataIndex: 'dw',
  },
  {
    title: '保养部位',
    dataIndex: 'sl',

  },
  {
    title: '保养计划名称',
    dataIndex: 'sccj',
  },
  {
    title: '保养日期',
    dataIndex: 'ccbh',

  },
  {
    title: '管理单位',
    dataIndex: 'ccbh',

  },
  {
    title: '使用地点',
    dataIndex: 'ccbh',

  },
  {
    title: '使用状态',
    dataIndex: 'ccbh',

  },
  {
    title: '是否完好',
    dataIndex: 'ccbh',

  },
]
const columns4 = [
  {
    title: '序号',
    dataIndex: 'xh',
    width: 80,
  },
  {
    title: '名称及型号',
    dataIndex: 'mc',
    width: 120,
  },
  {
    title: '单位',
    dataIndex: 'dw',
  },
  {
    title: '数量',
    dataIndex: 'sl',
    width: 80,
  },
  {
    title: '生产厂家',
    dataIndex: 'sccj',
  },
  {
    title: '出场编号',
    dataIndex: 'ccbh',
    width: 160,
  },
]
const columns5 = [
  {
    title: '序号',
    dataIndex: 'xh',
    width: 80,
  },
  {
    title: '名称及型号',
    dataIndex: 'mc',
    width: 120,
  },
  {
    title: '单位',
    dataIndex: 'dw',
  },
  {
    title: '数量',
    dataIndex: 'sl',
    width: 80,
  },
  {
    title: '生产厂家',
    dataIndex: 'sccj',
  },
  {
    title: '出场编号',
    dataIndex: 'ccbh',
    width: 160,
  },
]
const columns6 = [
  {
    title: '序号',
    dataIndex: 'xh',
    width: 80,
  },
  {
    title: '名称及型号',
    dataIndex: 'mc',
    width: 120,
  },
  {
    title: '单位',
    dataIndex: 'dw',
  },
  {
    title: '数量',
    dataIndex: 'sl',
    width: 80,
  },
  {
    title: '生产厂家',
    dataIndex: 'sccj',
  },
  {
    title: '出场编号',
    dataIndex: 'ccbh',
    width: 160,
  },
]
const columns7 = [
  {
    title: '序号',
    dataIndex: 'xh',
    width: 80,
  },
  {
    title: '名称及型号',
    dataIndex: 'mc',
    width: 120,
  },
  {
    title: '单位',
    dataIndex: 'dw',
  },
  {
    title: '数量',
    dataIndex: 'sl',
    width: 80,
  },
  {
    title: '生产厂家',
    dataIndex: 'sccj',
  },
  {
    title: '出场编号',
    dataIndex: 'ccbh',
    width: 160,
  },
]
const detailData = ref({});
const datassdbjl = ref([]);

const gotoView = (record) => {
  var fileName = "";
  if (record.transFormType == "equipment_condition_change") {
    //状态变更
    fileName = "statusChangeApproval"
  } else if (record.transFormType == "equipment_self_use") {
    //设备自用
    fileName = "selfUseApproval"
  } else if (record.transFormType == "equipment_self_use_return") {
    //设备自用退库
    fileName = "selfUseReturnApproval"
  } else if (record.transFormType == "equipment_lease") {
    //设备对外租赁
    fileName = "leaseExternallyApproval"
  } else if (record.transFormType == "equipment_lease_return") {
    //设备外租退库
    fileName = "leasedOutApproval"
  } else if (record.transFormType == "equipment_outbound") {
    //设备出库
    fileName = "warehouseApproval"
  } else if (record.transFormType == "equipment_return_warehouse") {
    //设备退库
    fileName = "returnedApproval"
  } else if (record.transFormType == "equipment_allocation") {
    //设备调拨
    fileName = "allocateApproval"
  }
  console.log(555, fileName)
  console.log(record)
  var processDefinitionId = "";
  //获取流程实例ID
  if (record.procInstanceId) {
    //查询defineId
    EquipmentAcceptanceApi.getProcessDefinitionByProcessInstanceId({ processInstanceId: record.procInstanceId }).then(res => {
      if (res.data) {
        processDefinitionId = res.data
        console.log("流程定义ID", processDefinitionId);
        // 拼接查询参数
        const queryParams2 = new URLSearchParams({
          id: record.procInstanceId,
          procInsId: record.procInstanceId,
          fileName: fileName,
          processDefinitionId: processDefinitionId,
          actId:null,
        }).toString();
        console.log(queryParams2);

        window.open(`/done?${queryParams2}`, '_blank');
      } else {
        console.error("获取流程定义失败", res.message);
      }
    }).catch(err => {
      console.error("获取流程定义异常", err);
    });
  }

}
watch(() => props.data, (val) => {
  console.log(val)
  if (val) {
    // Assuming val contains the necessary data structure
    // Initialize form with the data
    datassdbjl.value = val.allocateResponseList || [];
    datassztbg.value = val.conditionChangeResponseList || [];
    //detailData.value = val.detailData;
    console.log(datassdbjl.value)
  }

}, { immediate: true });
</script>

<style lang="less" scoped>
.rows {
  display: flex;
  height: 53px;
  align-items: center;
  border: 1px solid rgba(172, 180, 201, 0.2);

  .rows_1 {
    width: 145px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #F1F8FF;
    border-right: 1px solid rgba(172, 180, 201, 0.2);

    .rows_1_1 {
      margin-right: 16px;
    }
  }

  .rows_2 {
    .rows_2_2 {
      margin-left: 16px;
    }
  }
}


.rows2 {
  display: flex;
  height: 53px;
  align-items: center;
  border-top: 1px solid rgba(172, 180, 201, 0.2);
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);

  .rows2_2 {
    width: 145px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: rgba(241, 248, 255, 1);
    border-right: 1px solid rgba(172, 180, 201, 0.2);

    .rows2_2_2 {
      margin-right: 16px;
    }
  }

  .rows2_3 {
    .rows2_3_3 {
      margin-left: 16px;
    }
  }
}

.rows1 {
  border-top: 0;
  border-left: 1px solid rgba(172, 180, 201, 0.2);
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);
}

.rows22 {
  border-top: 0;
  border-left: 0;
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);
}

:deep(.ant-table-thead > tr > th) {
  padding: 10px 16px !important;
}

:deep(.ant-table-tbody .ant-table-cell) {
  padding: 10px 16px !important;
}

.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}

/** radio 选中样式按照UI样式调整 王文胜 20250708 */
:deep(.ant-radio-button-wrapper > span) {
  color: #6C748A;
}

:deep(.ant-radio-button-checked) {
  background: rgba(0, 133, 255, 0.06);

}

:deep(.ant-radio-button-wrapper-checked) {
  border: 0.5px solid rgba(172, 180, 201, 0.4);
}

:deep(.ant-radio-button-wrapper.ant-radio-button-wrapper-checked > span) {
  color: rgba(23, 109, 244, 1);
  font-size: 14px;
  font-weight: 500;
}

:deep(.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within) {
  box-shadow: none;
}

:deep(.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before) {
  background-color: transparent;
}

:deep(.ant-radio-button-wrapper-checked:not([class*=" ant-radio-button-wrapper-disabled"]).ant-radio-button-wrapper:first-child) {
  border-color: rgba(172, 180, 201, 0.4);
}

/** radio 选中样式按照UI样式调整 王文胜 20250708 */
</style>
