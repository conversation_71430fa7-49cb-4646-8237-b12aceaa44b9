<!-- 组织机构编辑弹窗 -->
<template>
  <div>
    <!-- 新增 -->
    <a-modal :width="500" :visible="visible" :confirm-loading="loading" :forceRender="true"
      :maskClosable="false" :title="formParams.title" :body-style="{ paddingBottom: '8px' }"
      @update:visible="updateVisible" @ok="save" @close="updateVisible(false)">
      <a-form ref="formRef" :model="form" :rules="rules">
        <div style="display: flex; width: 100%;" v-if="!formParams.id">
          <a-form-item label="保养周期:" name="maintenanceInterval">
            <a-input-number v-model:value="form.maintenanceInterval" style="width: 150px !important;" :min="0" placeholder="请输入保养周期"/>
          </a-form-item>
          <a-form-item label="" style="margin-left: 5px;" name="maintenanceIntervalType">
            <a-select v-model:value="form.maintenanceIntervalType" placeholder="请选择保养周期" style="width: 200px" allow-clear>
              <a-select-option v-for="item in xzList" :key="item.label" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </div>
        <a-form-item label="提醒阈值:" name="thresholdValue">
          <a-input v-model:value="form.thresholdValue" placeholder="请输入提醒阈值" style="width: 355px" suffix="天"/>
        </a-form-item>

      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { UserApi } from '@/api/system/user/UserApi';
import { EnumApi } from '@/api/common/enum';
import { aintenancecycleApi } from '@/api/basicInformationConfiguration/aintenancecycleApi';
import CityTreeSelect from '@/components/CityTreeSelect/index.vue';

export default {
  name: 'EquipmentTypeEdit',
  components: { CityTreeSelect },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 接收上级传过来的组织机构信息
    formParams: Object,
    dataObj: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      form: {
        maintenanceIntervalType: null,
        maintenanceInterval: null,
        thresholdValue:null,
        id:null,
      },
      // 提交状态
      loading: false,
      xzList: [],
    };
  },
  computed: {
    // 获取枚举数据
    rules: function () {
      let rs = {
        maintenanceInterval: [{ required: true, message: '请输入保养周期', trigger: 'blur' }],
        maintenanceIntervalType: [{ required: true, message: '请选择保养周期', trigger: 'blur' }],
        thresholdValue: [{ required: true, message: '请输入提醒阈值', type: 'string', trigger: 'blur' }],
      }
      if (!this.isUpdate) // 编辑不限制数值类型
      return rs;
    }
  },
  mounted() {
    this.init();
    this.initEnumData()
    this.resetForm();
  },
  watch: {
    // 编辑时监听data是否变化
    visible() {
      // 清空表单
      this.init();
    }
  },
  methods: {
    resetForm() {
      console.log(1);

      this.$refs.formRef.clearValidate();
    },
    async initEnumData() {
      let arr = await EnumApi.getEnumList({ enumName: 'MaintenanceCycleTypeEnum' });
      this.xzList = arr.data
    },
    // 初始化数据
    async init() {
      this.resetForm();
      this.form = {}
      if (this.formParams.id) {
        let res = await aintenancecycleApi.detail(this.formParams.id);
        this.form = res.data
      } else {
        this.form = {}
      }
    },
    save() {
      // 校验表单
      this.$refs.formRef.validate().then(async valid => {
        if (valid) {
          // 修改加载框为正在加载
          this.loading = true;
          let result;
          if (this.formParams.id) {
             console.log('this.form',this.form);
            result = aintenancecycleApi.edit(this.form);
          }else{
            result = aintenancecycleApi.add(this.form);
          }
          result.then(res => {
            // 移除加载框
            this.loading = false;
            // 提示添加成功
            message.success(res.message);

            // 如果是新增，则form表单置空
            if (!this.formParams.id) {
              this.form = {};
            }
            // 关闭弹框，通过控制visible的值，传递给父组件
            this.updateVisible(false);
            this.$emit('done');
          })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style>
.ant-modal-content {
  background-color: #F4F9FF;
}

.ant-modal-header {
  background-color: #F4F9FF;
}

.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-1 {
  flex: 1;
}

.fixed-code>div {
  margin-right: 6px;
  display: inline-block;
}

.fixed-code span {
  letter-spacing: 2px;
  display: inline-block;
  border: 1px solid #aaa;
  text-align: center;
  margin: 0px 2px;
  padding: 3px 5px;
  border-radius: 3px;
  font-size: 16px;
  background-color: #eee;
}
</style>
