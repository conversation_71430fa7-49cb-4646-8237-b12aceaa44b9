<template>
  <a-select 
    v-model:value="modelValue"
    show-search
    :placeholder="placeholder"
    :options="options"
    :loading="loading"
    :filter-option="filterOption"
    style="width: 100%"
    @change="handleChange"
  />
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { UnitApi } from '@/api/common/UnitApi';

const props = defineProps({
  modelValue: [String, Number, Array],
  placeholder: {
    type: String,
    default: '请选择'
  },
  // 组织机构ID
  orgId: {
    type: [String, Number],
    required: false
  },
  // 模板ID
  templateId: {
    type: [String, Number],
    required: false
  },
  // 动态键值，用于区分不同类型的请求
  dynamicKey: {
    type: String,
    required: false
  },
  // 额外的请求参数
  extraParams: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const options = ref([]);
const loading = ref(false);

// 加载选项数据
const loadOptions = async () => {
  // 如果没有必要参数，不发起请求
  console.log('加载选项，参数:', props.orgId, props.templateId, props.dynamicKey, props.extraParams);
 if ((!props.orgId && !props.templateId) || !props.dynamicKey) {
  return;
}
  
  try {
    loading.value = true;
    
    // 组装请求参数
    const requestParams = {
      ...props.extraParams
    };
    
    // 添加 orgId
    if (props.orgId) {
      requestParams.orgId = props.orgId;
    }
    
    // 添加 templateId
    if (props.templateId) {
      requestParams.transferFormId = props.templateId;
    }
    
    // 根据 dynamicKey 设置不同的参数
    if (props.dynamicKey) {
      requestParams.workSheetTypeAndStep = props.dynamicKey;
    }
    
    console.log('请求参数:', requestParams);
    
    // 发起请求
    const result = await UnitApi.getUserListByOrg(requestParams);
    options.value = result || [];
    
  } catch (error) {
    console.error('加载选项失败:', error);
    options.value = [];
  } finally {
    loading.value = false;
  }
};

// 过滤选项
const filterOption = (input, option) => {
  if (!input || !option.label) return true;
  return option.label.toLowerCase().includes(input.toLowerCase());
};

// 处理值变化
const handleChange = (value) => {
  emit('update:modelValue', value);
  emit('change', value);
};

// 监听参数变化，重新加载选项
watch(
  () => [props.orgId, props.templateId, props.dynamicKey, props.extraParams],
  () => {
    loadOptions();
  },
  { deep: true, immediate: true }
);

onMounted(() => {
  loadOptions();
});
</script>