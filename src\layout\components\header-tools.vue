<!-- 顶栏右侧区域 -->
<template>
  <div class="ele-admin-header-tool">
    <div class="header-tools-container">
      <!-- 左侧区域 -->
      <div class="left-tools">
        <div class="ele-admin-header-tool-item">
          <div class="custom-logo1" @click="jumpGuns" v-privilege="'home:homePage:back'">
            <img src="@/assets/equipment/shouye.png" alt="" class="guns-img" />
            <span class="logo-text">返回首页</span>
          </div>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right-tools">
        <!-- 通知 -->
        <div class="ele-admin-header-tool-item notice-item">
          <header-notice  background-color="rgba(244, 249, 255, 1)"
    text-color="rgba(48, 49, 51, 1)"
    border-color="rgba(255, 255, 255, 1)"
    :isShowFooter="true"
    border-bottom-color="1px solid rgba(51, 51, 51, 0.1)" />
        </div>

        <!-- 用户信息 -->
        <a-dropdown placement="bottomRight" :overlay-style="{ minWidth: '120px' }">
          <div class="ele-admin-header-avatar">
            <a-avatar :src="loginUser.avatar" v-if="loginUser.avatar">
              <template #icon><user-outlined /></template>
            </a-avatar>
            <span class="user-name">{{ loginUser.realName }}</span>
            <down-outlined class="down-icon" />
          </div>
          <template #overlay>
            <a-menu @click="onUserDropClick">
              <!-- <a-menu-item key="profile">
                <div class="ele-cell">
                  <user-outlined />
                  <div class="ele-cell-content">个人中心</div>
                </div>
              </a-menu-item> -->
              <a-menu-item key="password">
                <div class="ele-cell">
                  <key-outlined />
                  <div class="ele-cell-content">修改密码</div>
                </div>
              </a-menu-item>
              <a-menu-divider />

              <!-- <a-menu-item v-if="antdvFrontType == 2" key="frontend">
                <div class="ele-cell">
                  <appstore-add-outlined />
                  <div class="ele-cell-content">切换前台</div>
                </div>
              </a-menu-item>
              <a-menu-item v-if="antdvFrontType == 1" key="backend">
                <div class="ele-cell">
                  <setting-outlined />
                  <div class="ele-cell-content">切换后台</div>
                </div>
              </a-menu-item> -->
              <!-- <a-menu-divider v-if="(menuType !== 1 && antdvFrontType) || (menuType !== 2 && !antdvFrontType)" /> -->
              <a-menu-item key="logout">
                <div class="ele-cell">
                  <logout-outlined />
                  <div class="ele-cell-content">退出登录</div>
                </div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </div>
  </div>
  <!-- 保持原有的组件引用 -->
  <password-modal v-model:visible="passwordVisible" />
  <setting-drawer v-model:visible="settingVisible" />
</template>

<script setup>
import { computed, createVNode, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { Modal } from 'ant-design-vue';
import {
  DownOutlined,
  MoreOutlined,
  UserOutlined,
  KeyOutlined,
  LogoutOutlined,
  ExclamationCircleOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  SettingOutlined,
  AppstoreAddOutlined
} from '@ant-design/icons-vue';
import HeaderNotice from './header-notice.vue';
import PasswordModal from './password-modal.vue';
import SettingDrawer from './setting-drawer.vue';
import I18nIcon from './i18n-icon.vue';
import { useUserStore } from '@/store/modules/user';
import { logout } from '@/utils/page-tab-util';
import { LoginApi } from '@/api/login/LoginApi';
import { BASE_URL, SESSION_KEY_MENU_TYPE, SSO_FLAG, GUNS_DEVOPS_URL } from '@/config/setting';
import { SsoUtil } from '@/utils/sso-util';
import { removeToken, getToken } from '@/utils/token-util';
import { useSystemStore } from '@/store/modules/system';

const emit = defineEmits(['fullscreen']);

defineProps({
  // 是否是全屏
  fullscreen: Boolean
});

const { push } = useRouter();
const { t } = useI18n();
const userStore = useUserStore();
const systemStore = useSystemStore();

// 是否显示修改密码弹窗
const passwordVisible = ref(false);

// 是否显示主题设置抽屉
const settingVisible = ref(false);

// 当前用户信息
const loginUser = computed(() => userStore.info ?? {});

// 显示的前后台类型
const menuType = computed(() => {
  return Number(localStorage.getItem('menuType'));
});

// 当前系统加载的前后台菜单类型
const antdvFrontType = computed(() => systemStore.antdvFrontType);

/* 用户信息下拉点击 */
const onUserDropClick = ({ key }) => {
  if (key === 'password') {
    passwordVisible.value = true;
  } else if (key === 'profile') {
    push('/personal/info');
  } else if (key === 'logout') {
    // 退出登录
    Modal.confirm({
      title: t('layout.logout.title'),
      content: t('layout.logout.message'),
      icon: createVNode(ExclamationCircleOutlined),
      maskClosable: true,
      onOk: async () => {
        // 清除本地菜单类型缓存
        if (localStorage.getItem('menuType')) {
          localStorage.removeItem('menuType');
        }

        // 如果开启了单点登录，跳转到单点的退出
        if (SSO_FLAG) {
          // 清除token
          removeToken();
          // 调用sso退出接口
          SsoUtil.ssoLogoutRedirect();
        } else {
          // 调用退出接口
          await LoginApi.logout();
          // 清除缓存token并退出
          logout();
        }
      }
    });
  } else if (key === 'frontend') {
    // 设置session值为前台菜单
    sessionStorage.setItem(SESSION_KEY_MENU_TYPE, '1');

    // 从新加载系统界面，跳转到首页
    window.location.replace(BASE_URL);
  } else if (key === 'backend') {
    // 设置session值为后台菜单
    sessionStorage.setItem(SESSION_KEY_MENU_TYPE, '2');

    // 从新加载系统界面，跳转到首页
    window.location.replace(BASE_URL);
  }
};

/* 切换全屏 */
const toggleFullscreen = () => {
  emit('fullscreen');
};

/* 打开主题设置抽屉 */
const openSetting = () => {
  settingVisible.value = true;
};

// 跳转到guns
const jumpGuns = () => {
  // window.open(GUNS_DEVOPS_URL + getToken());
  window.open("/map");
};
</script>

<style lang="less" scoped>
.ele-admin-header-tool {
  width: 100%;
  height: 100%;

  .header-tools-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    // padding: 0 16px;

    .left-tools {
      display: flex;
      align-items: center;
    }

    .right-tools {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }
}

.ele-admin-header-tool-item {
  display: flex;
  align-items: center;
  height: 100%;
  cursor: pointer;
  padding: 0 8px;
}

.custom-logo1 {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 36px;
  padding: 0 20px;
  background: linear-gradient(278deg, rgba(0, 140, 255, 0.2) 0%, rgba(0, 133, 255, 0.1) 59%, rgba(0, 221, 255, 0.05) 102%);
  cursor: pointer;
  border-radius: 18px;

  .guns-img {
    width: 20px;
    height: 20px;
  }

  .logo-text {
    font-size: 14px;
    color: #0085FF;
    white-space: nowrap;
  }
}

.notice-item {
  position: relative;

  :deep(.notice-badge) {
    position: absolute;
    top: 15px;
    right: -2px;
  }
}

.ele-admin-header-avatar {
  display: flex;
  align-items: center;
  padding: 4px 0;
  cursor: pointer;
  border-radius: 22px;
  transition: all 0.3s;
  padding-left: 12px;
  height: 45px;

  &:hover {
    background: rgba(0, 0, 0, 0.025);
    height: 45px;
  }

  .ant-avatar {
    flex-shrink: 0;
    margin-right: 10px;
    background: #e8f4ff;
  }

  .user-name {
    color: rgba(78, 89, 105, 1);
    font-size: 14px;
    margin-right: 8px;
    white-space: nowrap;
  }

  .down-icon {
    font-size: 12px;
    color: #666;
  }
}

// 下拉菜单项样式
.ele-cell {
  display: flex;
  align-items: center;
  padding: 2px 0;

  .anticon {
    font-size: 16px;
    margin-right: 8px;
  }

  &-content {
    flex: 1;
    color: #333;
  }
}

/* 响应式布局 */
@media screen and (max-width: 1200px) {
  .custom-logo1 {
    flex-direction: column;
    padding: 4px 12px;
    height: auto;

    .logo-text {
      font-size: 12px;
    }
  }
}

@media screen and (max-width: 768px) {
  .header-tools-container {
    .right-tools {
      gap: 8px;
    }
  }

  .custom-logo1 {
    padding: 4px 8px;

    .logo-text {
      display: none;
    }
  }

  .ele-admin-header-avatar {
    .user-name {
      display: none;
    }
  }
}
</style>
