<template>
  <div class="ele-body">
    <div class="reg-body">
      <!-- 顶部按钮组 -->
      <div class="header-tools">
        <div class="left">
          <a-button class="filter-button" style="display: flex;align-items: center;justify-content: space-between;"
            @click="isShowSearch = !isShowSearch">
            <i class="iconfont icon-search" style="margin-right: 6px;"></i>
            筛选
          </a-button>
          <div class="search-input">
            <a-input v-model:value="queryParams.searchText" placeholder="请输入业务标题" :style="{ width: '240px' }"
              @keyup.enter="handleSearch" allow-clear @change="handleChanges">
              <template #suffix>
                <SearchOutlined class="search-icon" @click="handleSearch" />
              </template>
            </a-input>
          </div>
        </div>
        <div class="right">
          <a-button :disabled="selectedRowKeys.length === 0" class="tool-button" style="color: #176DF4;"
            @click="handleChange(selectedRowKeys)" v-privilege="'maintenance:equipment:setMaintenanceUserAll'">
            <template #icon>
              <FormOutlined />
            </template>
            批量修改技术员
          </a-button>
        </div>
      </div>

      <!-- 搜索工具栏 -->
      <div class="search-form" v-if="isShowSearch">
        <a-row :gutter="16">
          <a-col :span="10">
            <a-form-item label="设备类型">
              <a-tree-select v-model:value="sblxList" show-search
                :dropdown-style="{ maxHeight: '440px', overflow: 'auto' }" placeholder="请选择" allow-clear multiple
                :show-checked-strategy="SHOW_ALL" :tree-data="equipmentTypeList"
                :field-names="{ label: 'name', value: 'id' }" @select="selectSelect" @change="changeSelect"
                tree-node-filter-prop="name" max-tag-count="responsive" style="width: 100%;">
              </a-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="使用单位">
              <a-tree-select v-model:value="useOrg" show-search
                :dropdown-style="{ maxHeight: '440px', overflow: 'auto' }" placeholder="请选择" allow-clear multiple
                :show-checked-strategy="SHOW_ALL" :tree-data="orgList" :field-names="{ label: 'name', value: 'id' }"
                tree-node-filter-prop="name" max-tag-count="responsive" style="width: 100%;"
                :treeExpandedKeys="expandedKeys" @treeExpand="handleTreeExpand">
                <template #title="node">
                  <span @click="(e) => handleTitleClick(e, node)" style="display: inline-block; width: 100%;">
                    {{ node.name }}
                  </span>
                </template>
              </a-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="2" style="display: flex; justify-content: end;">
            <a-button style="width: 100%;" type="primary" class="search-button" @click="handleSearch">查询</a-button>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="5">
            <a-form-item label="设备状态">
              <a-select v-model:value="queryParams.equCondition" placeholder="全部" style="width: 100%;">
                <a-select-option value="">全部</a-select-option>
                <a-select-option v-for="item in equConditionOptions" :key="item.label" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="保养状态">
              <a-select v-model:value="queryParams.maintenanceStatus" placeholder="全部" style="width: 100%;">
                <a-select-option value="">全部</a-select-option>
                <a-select-option v-for="item in byztList" :key="item.label" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="保养周期">
              <a-select v-model:value="queryParams.maintenanceCycle" placeholder="全部" style="width: 100%;">
                <a-select-option value="">全部</a-select-option>
                <a-select-option v-for="item in xzList" :key="item.label" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="7">
            <a-form-item label="保养日期">
              <a-range-picker v-model:value="byAll" picker="Date" :placeholder='placeholders'
                :value-format="yearFormats" style="width: 100%;" dropdownClassName="custom-datepicker-dropdown" />
            </a-form-item>
          </a-col>
          <a-col :span="2" style="display: flex; justify-content: end;">
            <a-button style="width: 100%;" class="reset-button" @click="handleReset">重置</a-button>
          </a-col>
        </a-row>
      </div>
      <!-- :row-selection="showCheckbox ? rowSelection : null" -->
      <!-- 数据表格 -->
      <a-table :columns="columns" :data-source="tableData" :pagination="false" :rowKey="(record) => record.id"
        :row-selection="{
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange, onSelect: onSelects, onSelectAll: onSelectAll
        }" :scroll="scroll" class="custom-table">
        <template #emptyText>
          <div class="custom-empty">
            <img src="@/assets/images/noData.png" />
            <!-- <p>抱歉，暂时还没有数据</p> -->
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a v-privilege="'maintenance:equipment:setMaintenanceUser'" @click="handleChange(record)">修改技术员</a>
              <a v-privilege="'maintenance:getByEquipmentId:deatil'" @click="handleView(record)">查看保养记录</a>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 表格底部统计信息 -->
      <div class="table-footer" :class="{ 'follow-page': isShowSearch }">
        <div class="total-info">
          <!-- 设备数量合计: {{ totalCount }}，财务原值合计：{{ totalFinanceValue }}元，净值合计：{{ totalNetValue }}万元 -->
        </div>
        <a-pagination style="z-index: 10;" v-model:current="pagination.current" :total="pagination.total"
          :showTotal="(total) => `共 ${total} 条`" :showLessItems="true" :showSizeChanger="true"
          @change="handleTableChange" :defaultPageSize="20" :pageSizeOptions="['20', '50', '100']"
          @showSizeChange="handleSizeChange" />
      </div>
    </div>
  </div>
  <changeUser v-model:visible="show" :data="data" @done="getList" />
  <deatil v-model:visible="show1" :data="data1" @done="getList" />
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue';
import { allocateApi } from '@/api/dynamic/allocateApi';
import { useRouter } from 'vue-router';
import { hasPermission } from '@/utils/permission';
import { FilterOutlined, SearchOutlined } from '@ant-design/icons-vue';
import changeUser from './changeUser.vue'
import deatil from './deatil.vue'
import { maintainequipmentApi } from '@/api/maintenanceManagement/maintainequipmentApi'
import { BasicInformationApi } from '@/api/buss/BasicInformationApi';
import { UnitApi } from '@/api/common/UnitApi';
import { EnumApi } from '@/api/common/enum';
import { aintenancecycleApi } from '@/api/basicInformationConfiguration/aintenancecycleApi';
const router = useRouter();
const scroll = ref({ x: 2000, y: 'calc(100vh - 270px)' })
const show = ref(false)
const show1 = ref(false)
const isShowSearch = ref(false);
const equConditionOptions = ref([]);
const xzList = ref([])
const sblxList = ref([])
const useOrg = ref([])
const byAll = ref([])
const yearFormats = 'YYYY-MM-DD';
const byztList = ref([{
  value: 0,
  label: '超期'
}, {
  value: 1,
  label: '正常'
}])
// 初始化枚举数据
const initEnumData = async () => {
  try {
    // 获取设备状态枚举
    const equConditionData = await EnumApi.getEnumList({ enumName: 'EquConditionEnum' });
    equConditionOptions.value = equConditionData.data;
    let arr = await aintenancecycleApi.getSelectList();
    xzList.value = arr.data
  } catch (error) {
    message.error('获取枚举数据失败');
  }
};

// 获取设备类型树
const equipmentTypeList = ref([]);
const getEquipmentTypeTree = async () => {
  try {
    const res = await BasicInformationApi.getEquipmentTypeTree();
    equipmentTypeList.value = res.data[0].children;
  } catch (error) {
    message.error('获取设备类型失败');
  }
};

const sList = ref([])
const selectSelect = (value, node, extra) => {
  sList.value.push({
    value: value,
    level: node.level
  })
}

const arrList = ref([])
const changeSelect = (value, node, extra) => {
  arrList.value = []
  queryParams.value.equType = []
  queryParams.value.equSubType = []
  queryParams.value.equModel = []
  queryParams.value.equName = []
  console.log('value.value', value);
  console.log('sList.value', sList.value);
  nextTick(() => {
    for (let i = 0; i < value.length; i++) {
      for (let s = 0; s < sList.value.length; s++) {
        if (sList.value[s].value == value[i]) {
          arrList.value.push({
            value: value[i],
            level: sList.value[s].level
          })
        }
      }

    }
    arrList.value = getArray(arrList.value)
    console.log('arrList.value', arrList.value);

  })
}

const getArray = (selectData) => {
  let map = new Map()
  for (let item of selectData) {
    if (!map.has(item.value)) {
      map.set(item.value, item)
    }
  }
  const list = [...map.values()]
  console.log(list, '数组去重后')
  return list
}

// 获取组织机构树
const orgList = ref([]);
const getOrgList = async () => {
  try {
    const res = await UnitApi.getUseOrgTree({});
    orgList.value = res[0].children;
    for (let i = 0; i < orgList.value.length; i++) {
      orgList.value[i].selectable = false
      orgList.value[i].key = orgList.value[i].value
    }
    console.log('orgList.value', orgList.value);
  } catch (error) {
    message.error('获取组织机构失败');
  }
};

const expandedKeys = ref([])
const handleTreeExpand = (node) => {
  expandedKeys.value = node
}
const handleTitleClick = (e, node) => {
  console.log('expandedKeys', expandedKeys.value);
  console.log('node', node);
  if (node.children.length > 0) {
    const key = orgList.value.find(item => item.name === node.name).id;
    if (expandedKeys.value.includes(key)) {
      expandedKeys.value = expandedKeys.value.filter(k => k !== key);
    } else {
      expandedKeys.value = [...expandedKeys.value, key];
    }
  }

};

const queryParams = ref({
  searchText: '',
  equType: null,//设备类别
  equSubType: null,//设备种类
  equModel: null,//规格型号
  equName: null,//设备名称
  pageSize: 20,
  pageNo: 1,
  equCondition: '',
  maintenanceStatus: '',
  maintenanceCycle: '',
  useOrg: '',
  maintenanceDayStart: '',
  maintenanceDayEnd: ''
})

const handleSearch = () => {
  if (byAll.value && byAll.value.length > 0) {
    queryParams.value.maintenanceDayStart = byAll.value[0]
    queryParams.value.maintenanceDayEnd = byAll.value[1]
  } else {
    queryParams.value.maintenanceDayStart = null
    queryParams.value.maintenanceDayEnd = null
  }
  queryParams.value.useOrg = useOrg.value.join(',')
  const arr = []
  const arr1 = []
  const arr2 = []
  const arr3 = []
  for (let i = 0; i < arrList.value.length; i++) {
    if (arrList.value[i].level == '1') {
      arr.push(arrList.value[i].value)
    }
    if (arrList.value[i].level == '2') {
      arr1.push(arrList.value[i].value)
    }
    if (arrList.value[i].level == '3') {
      arr2.push(arrList.value[i].value)
    }
    if (arrList.value[i].level == '4') {
      arr3.push(arrList.value[i].value)
    }
  }
  queryParams.value.equType = arr.join(',')
  queryParams.value.equSubType = arr1.join(',')
  queryParams.value.equName = arr2.join(',')
  queryParams.value.equModel = arr3.join(',')
  getList()
  console.log('queryParams.value', queryParams.value);
}

const handleChanges = (e) => {
  console.log('e', e.type);
  console.log('e', e.type);
  nextTick(() => {
    if (e.type === 'click') {
      handleSearch()
    }
  })
}

const handleReset = () => {
  queryParams.value = {
    searchText: '',
    equType: null,//设备类别
    equSubType: null,//设备种类
    equModel: null,//规格型号
    equName: null,//设备名称
    pageSize: 20,
    pageNo: 1,
    equCondition: '',
    maintenanceStatus: '',
    maintenanceCycle: '',
    useOrg: '',
    maintenanceDayStart: '',
    maintenanceDayEnd: ''
  }
  useOrg.value = []
  sblxList.value = []
  byAll.value = []
  getList()
}

const handleTableChange = (current) => {
  console.log(current);
  pagination.value.current = current
  queryParams.value.pageNo = current
  queryParams.value.pageSize = pagination.value.pageSize
  handleSearch();
};
const handleSizeChange = (current, size) => {
  console.log(size);
  pagination.value.pageSize = size;
  pagination.value.current = current; // 切换每页条数时重置为第一页
  queryParams.value.pageNo = current;
  queryParams.value.pageSize = size;
  handleSearch();
};

// const showCheckbox = ref(hasPermission('jsy:jsys:jsyss'));


// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0,
});

const tableData = ref([])

const selectedRowKeys = ref([]);
const selectedRow = ref([])
const onSelectChange = (selectedRowKey, selectedRows) => {
  console.log('selectedRowKey', selectedRowKey);
  console.log('selectedRows', selectedRows)
  selectedRowKeys.value = selectedRowKey;
  if (selectedRows.length > 0) {
    selectedRow.value = selectedRows[0].useOrg
  }
}
const onSelects = (record, selected, selectedRowsData, nativeEvent) => {
  console.log('record', record);
  console.log('selected', selected);
  console.log('selectedRowsData', selectedRowsData);
  console.log('nativeEvent', nativeEvent);
};
const onSelectAll = (selected, selectedRows, changeRows) => {
  console.log('selected', selected);
  console.log('selectedRows', selectedRows);
  console.log('changeRows', changeRows);
}


const data = ref({
  equIds: [],
  list: []
})

const data1 = ref([])

const handleChange = async (record) => {
  data.value.equIds = []
  show.value = true
  if (record.id) {
    data.value.equIds.push(record.id)
  }
  if (record.length > 0) {
    data.value.equIds = record
  }
  if (record.useOrg) {
    const res = await maintainequipmentApi.getMaintenanceUser(record.useOrg);
    data.value.list = res.data
  } else {
    const res = await maintainequipmentApi.getMaintenanceUser(selectedRow.value);
    data.value.list = res.data
  }
  // console.log('data.value', data.value);

}

const handleView = async (record) => {
  show1.value = true
  if (record.id) {
    let res = await maintainequipmentApi.getByEquipmentId({ equipmentId: record.id })
    data1.value = res.data
    //  data1.value=[{maintenanceRecords:'委屈委屈而且阿萨德擦委屈委屈而且阿萨德擦委屈委屈而且阿萨德擦委屈委屈而且阿萨德擦委屈委屈而且阿萨德擦委屈委屈而且阿萨德擦委屈委屈而且阿萨德擦委屈委屈而且阿萨德擦委屈委屈而且阿萨德擦委屈委屈而且阿萨德擦委屈委屈而且阿萨德擦委屈委屈而且阿萨德擦'}]
  }
}

const getList = async () => {
  let res = await maintainequipmentApi.getList(queryParams.value)
  tableData.value = res.data.rows
  pagination.value.total = res.data.totalRows
}

// 表格列定义
const columns = [
  {
    title: '序号',
    dataIndex: 'serialNo',
    width: 40,
    align: 'center',
    customRender: ({ text, record, index }) => {
      return `${(pagination.value.current - 1) * pagination.value.pageSize + index + 1}`;
    }
  },
  {
    title: '设备编号',
    dataIndex: 'code',
    width: 160,
    align: 'center'
  },
  {
    title: '设备名称',
    dataIndex: 'equNameStr',
    width: 140,
    align: 'center'
  },
  {
    title: '规格型号',
    dataIndex: 'equModelStr',
    width: 140,
    align: 'center'
  },
  {
    title: '设备状态',
    dataIndex: 'equConditionStr',
    width: 140,
    align: 'center'
  },
  {
    title: '保养状态',
    dataIndex: 'maintenanceStatusStr',
    width: 100,
    align: 'center'
  },
  {
    title: '保养周期',
    dataIndex: 'maintenanceCycleStr',
    width: 100,
    align: 'center'
  },
  {
    title: '最近保养日期',
    dataIndex: 'lastMaintenanceDate',
    width: 160,
    align: 'center'
  },
  {
    title: '计划保养日期',
    dataIndex: 'nextMaintenanceDate',
    width: 160,
    align: 'center'
  },
  {
    title: '使用单位',
    dataIndex: 'useOrgStr',
    width: 100,
    align: 'center'
  },
  {
    title: '技术员',
    dataIndex: 'maintenanceUserStr',
    width: 100,
    align: 'center'
  },
  {
    title: '操作',
    key: 'action',
    width: 140,
    align: 'center',
    fixed: 'right'
  }
];


watch(
  () => isShowSearch.value,
  (sum) => {
    if (sum) {
      scroll.value = { x: 2000, y: 'calc(100vh - 385px)' }
    } else {
      scroll.value = { x: 2000, y: 'calc(100vh - 270px)' }
    }
  }
);


// 生命周期钩子
onMounted(() => {
  getEquipmentTypeTree();
  getOrgList();
  initEnumData();
  getList();
});
</script>

<style lang="less" scoped>
.reg-body {
  height: 100%;
  overflow: auto;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  border-radius: 6px;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);
  padding: 16px 16px;
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;

  .header-tools {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;

    .left {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .filter-button {
        min-width: 80px;
        height: 32px;
        border-radius: 7px;
      }

      .search-input {
        width: clamp(280px, 20vw, 320px);

        :deep(.ant-input) {
          width: 100%;
          height: 24px;

        }

        .search-icon {
          cursor: pointer;
        }
      }
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .tool-button {
        height: 32px;
        border-radius: 7px;
      }

      .link-button {
        height: 32px;
        padding: 0 8px;
      }
    }
  }

  .search-form {

    padding: 0 16px; // 统一内边距
    border-radius: 8px; // 增加圆角


    .search-row {
      display: flex;
      flex-wrap: wrap;
      gap: 24px; // 增加间距
      margin-bottom: 12px; // 增加行间距

      &:last-child {
        margin-bottom: 0;
      }

      .search-item {
        display: flex;
        align-items: center;
        min-width: 300px;
        flex: 1;

        .label {
          min-width: 80px;
          margin-right: 12px; // 增加标签和输入框的间距
          color: #666;
          font-size: 14px;
        }

        :deep(.ant-select),
        :deep(.ant-input) {
          width: 64%;
          height: 32px;

          .ant-select-selector {
            background: #fff; // 确保选择器背景为白色
            border-radius: 4px;
          }
        }

        :deep(.ant-input) {
          background: #fff; // 确保输入框背景为白色
          border-radius: 4px;
        }
      }

      .search-button,
      .reset-button {
        height: 32px;
        min-width: 80px;
        margin-left: auto;
        border-radius: 4px; // 统一按钮圆角
      }

      .search-button {
        background: #1890ff; // 查询按钮使用主题蓝色
      }

      .reset-button {
        background: #fff; // 重置按钮使用白色背景
        border: 1px solid #d9d9d9;
      }
    }
  }

  .custom-radio-group .ant-radio-button-wrapper-checked {
    background: #FFFFFF !important;
    border-color: #1890ff !important;
    border-radius: 2px !important;
  }

  .custom-radio-group .ant-radio-button-wrapper {
    background: #E0E9F4;
  }

  .table-footer {
    position: fixed; // 默认固定定位
    bottom: 0px;
    //left: 50%;
    // transform: translateX(-50%);
    background: #ECF5FE;
    width: calc(100% - 32px);
    max-width: 1888px;
    margin: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px 24px;
    border-radius: 8px;

    z-index: 10;

    .total-info {
      color: #666;
      font-size: 14px;
    }

    // 当筛选展开时的样式
    &.follow-page {
      //position: static; // 改为静态定位
      transform: none;
      //width: 100%;
      margin-top: 16px;
    }
  }


  .custom-table {
    // margin-top: 16px;

    :deep(.ant-table) {

      // 提高固定列的层级
      .ant-table-fixed-left,
      .ant-table-fixed-right {
        background: #fff;
        z-index: 3; // 增加层级
      }

      .ant-table-cell {
        white-space: nowrap !important; // 强制不换行
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        line-height: 1 !important;
        font-size: 14px !important;

        >span,
        >div {
          white-space: nowrap !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
        }
      }

      // 大屏幕样式（默认）
      @media screen and (min-width: 1920px) {
        .ant-table-cell {
          padding: 20px 0 !important;
          height: 60px !important;
        }

        .ant-table-row {
          height: 60px !important;
        }
      }

      // 中等屏幕样式
      @media screen and (min-width: 1366px) and (max-width: 1919px) {
        .ant-table-cell {
          padding: 10px 0 !important;
          height: 40px !important;
        }

        .ant-table-row {
          height: 40px !important;
        }
      }

      // 小屏幕样式
      @media screen and (max-width: 1365px) {
        .ant-table-cell {
          padding: 4px 0 !important;
          height: 32px !important;
        }

        .ant-table-row {
          height: 32px !important;
        }
      }

      // 调整固定列单元格样式
      .ant-table-cell-fix-left,
      .ant-table-cell-fix-right {
        z-index: 3 !important; // 增加层级
        background: #ECF4FE !important;
      }

      // 调整表头固定列样式
      .ant-table-thead {

        th.ant-table-cell-fix-left,
        th.ant-table-cell-fix-right {
          z-index: 4 !important; // 确保表头在最上层
          background: #DAECFF !important;
        }

        .ant-table-cell-scrollbar {
          box-shadow: none;
        }
      }

      // 优化阴影效果
      .ant-table-fixed-right::before,
      .ant-table-fixed-left::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 10px;
        pointer-events: none;
        z-index: 2; // 阴影层级低于固定列
        transition: box-shadow .3s;
      }

      .ant-table-fixed-left::before {
        right: 0;
        box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
      }

      .ant-table-fixed-right::before {
        left: 0;
        box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
      }

      // 设置表格内容的层级
      .ant-table-content {
        z-index: 1;
      }

      // 确保滚动区域正确显示
      .ant-table-body {
        overflow-x: auto !important;
        overflow-y: auto !important;
      }

      // 固定列不换行
      .ant-table-cell-fix-left,
      .ant-table-cell-fix-right {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .ant-table-row {
        height: 24px !important;
      }

      // 表头固定列不换行
      .ant-table-thead {

        th.ant-table-cell-fix-left,
        th.ant-table-cell-fix-right {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .reg-body {
    .search-form {
      .search-row {
        .search-item {
          min-width: calc(32.33% - 40px);
        }
      }
    }
  }
}

@media screen and (max-width: 1366px) {
  .reg-body {
    .search-form {
      .search-row {
        .search-item {
          min-width: calc(50% - 16px);
        }
      }
    }
  }
}

@media screen and (max-width: 1024px) {
  .reg-body {
    .header-tools {

      .left,
      .right {
        width: 100%;
        justify-content: space-between;
      }
    }

    .search-form {
      .search-row {
        .search-item {
          min-width: 100%;
        }
      }
    }

    .table-footer {
      flex-direction: column;
      text-align: center;

      .total-info {
        width: 100%;
      }
    }
  }
}

// 表格响应式
:deep(.ant-table) {
  .ant-table-content {
    overflow-x: auto;
  }

  @media screen and (max-width: 1024px) {
    .ant-table-cell {
      white-space: nowrap;
    }
  }
}

// 固定列样式
:deep(.ant-table) {
  .ant-table-body {
    overflow-x: auto;
    overflow-y: auto;
  }

  .ant-table-fixed-left,
  .ant-table-fixed-right {
    background: #fff;
    box-shadow: none; // 移除原有阴影
    z-index: 2; // 提高固定列的层级
  }

  // 确保表格内容正确显示
  .ant-table-content {
    z-index: 0;
  }

  // 修复固定列单元格层级
  .ant-table-cell-fix-left,
  .ant-table-cell-fix-right {
    z-index: 2 !important;
    background: #fff !important;
  }

  // 修复表头固定列层级
  .ant-table-thead th.ant-table-cell-fix-left,
  .ant-table-thead th.ant-table-cell-fix-right {
    z-index: 3 !important;
  }
}

.custom-radio-group {
  :deep(.ant-radio-button-wrapper) {
    background: transparent;
    border: none;
    color: #666;

    &-checked {
      background: #E2F0FF;
      color: #1890ff;
    }

    &::before {
      display: none;
    }
  }
}

/deep/ .ant-row {
  flex-wrap: nowrap;
}

.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}

:deep(.ant-form-item-label) {
  overflow: visible !important;
}
</style>
