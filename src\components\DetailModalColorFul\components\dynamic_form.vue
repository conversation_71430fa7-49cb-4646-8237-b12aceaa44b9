<template>
  <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
    :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }">
    <a-row :gutter="16">
      <a-col :md="24" :sm="24" :xs="24" style="padding-right: 0px;">
        <a-radio-group v-model:value="titles" style="margin-bottom: 16px">
          <a-radio-button value="dbjl">调拨记录</a-radio-button>
          <a-radio-button value="zlmx">租赁明细</a-radio-button>
          <a-radio-button value="ztbgjl">状态变更记录</a-radio-button>
          <a-radio-button value="byjl">保养记录</a-radio-button>
          <a-radio-button value="pdjl">盘点记录</a-radio-button>
          <a-radio-button value="wxjl">维修记录</a-radio-button>
          <a-radio-button value="dxjl">大修记录</a-radio-button>
          <a-radio-button value="jgjl">技改记录</a-radio-button>
        </a-radio-group>

        <a-table v-if="titles=='dbjl'" :columns="columns" :data-source="datass" bordered :pagination="false">
        <template #emptyText>
              <div class="custom-emptyLeft">
                <img src="@/assets/images/noData.png" />
                <!-- <p>抱歉，暂时还没有数据</p> -->
              </div>
            </template>
        </a-table>
        <a-table v-else-if="titles=='zlmx'" :columns="columns1" :data-source="datass" bordered :pagination="false">
          <template #emptyText>
              <div class="custom-emptyLeft">
                <img src="@/assets/images/noData.png" />
                <!-- <p>抱歉，暂时还没有数据</p> -->
              </div>
            </template>
        </a-table>
        <a-table v-else-if="titles=='ztbgjl'" :columns="columns2" :data-source="datass" bordered :pagination="false">
          <template #emptyText>
              <div class="custom-emptyLeft">
                <img src="@/assets/images/noData.png" />
                <!-- <p>抱歉，暂时还没有数据</p> -->
              </div>
            </template>
        </a-table>
        <a-table v-else-if="titles=='pdjl'" :columns="columns4" :data-source="datass" bordered :pagination="false">
          <template #emptyText>
              <div class="custom-emptyLeft">
                <img src="@/assets/images/noData.png" />
                <!-- <p>抱歉，暂时还没有数据</p> -->
              </div>
            </template>
        </a-table>
        <a-table v-else-if="titles=='byjl'" :columns="columns3" :data-source="datass" bordered :pagination="false">
          <template #emptyText>
              <div class="custom-emptyLeft">
                <img src="@/assets/images/noData.png" />
                <!-- <p>抱歉，暂时还没有数据</p> -->
              </div>
            </template>
        </a-table>
        <a-table v-else-if="titles=='wxjl'" :columns="columns5" :data-source="datass" bordered :pagination="false">
          <template #emptyText>
              <div class="custom-emptyLeft">
                <img src="@/assets/images/noData.png" />
                <!-- <p>抱歉，暂时还没有数据</p> -->
              </div>
            </template>
        </a-table>
        <a-table v-else-if="titles=='dxjl'" :columns="columns6" :data-source="datass" bordered :pagination="false">
          <template #emptyText>
              <div class="custom-emptyLeft">
                <img src="@/assets/images/noData.png" />
                <!-- <p>抱歉，暂时还没有数据</p> -->
              </div>
            </template>
        </a-table>
        <a-table v-else="titles=='jgjl'" :columns="columns7" :data-source="datass" bordered :pagination="false">
          <template #emptyText>
              <div class="custom-emptyLeft">
                <img src="@/assets/images/noData.png" />
                <!-- <p>抱歉，暂时还没有数据</p> -->
              </div>
            </template>
        </a-table>
      </a-col>
    </a-row>
  </a-form>
</template>

<script>
// import { defineComponent, reactive, toRefs } from 'vue';
// import iconData from 'ele-admin-pro/es/ele-icon-picker/icons';
export default {
  name: 'MainForm',
  // components: { FieldExpandForm, CompanyForm },
  props: {
    form: {
      type: Object,
      default: {}
    },
    rules: Object,
    // 上级列表
    menuList: Array,
    // 所属应用列表
    appList: Array,
  },
  data() {
    return {
      columns: [
        {
          title: '序号',
          dataIndex: 'xh',
          width: 80,
        },
        {
          title: '调拨日期',
          dataIndex: 'mc',
          width: 120,
        },
        {
          title: '管理单位(前)',
          dataIndex: 'dw',
        },
        {
          title: '使用单位(前)',
          dataIndex: 'dw',
        },
        {
          title: '管理单位(后)',
          dataIndex: 'dw',
        },
        {
          title: '使用单位(后)',
          dataIndex: 'dw',
        },
        {
          title: '单据类型',
          dataIndex: 'sl',

        },
        {
          title: '流程穿透',
          dataIndex: 'sccj',
        },

      ],
      columns1: [
        {
          title: '序号',
          dataIndex: 'xh',
          width: 80,
        },
        {
          title: '相关申请流程',
          dataIndex: 'mc',
          width: 120,
        },
        {
          title: '租赁类型',
          dataIndex: 'dw',
        },
        {
          title: '申请单位',
          dataIndex: 'sl',

        },
        {
          title: '使用单位',
          dataIndex: 'sccj',
        },
        {
          title: '租赁总价',
          dataIndex: 'sccj',
        },
        {
          title: '租赁时长',
          dataIndex: 'sccj',
        },
        {
          title: '租赁起始日期',
          dataIndex: 'sccj',
        },
        {
          title: '租赁结束日期',
          dataIndex: 'ccbh',

        },
      ],
      columns2: [
        {
          title: '序号',
          dataIndex: 'xh',
          width: 80,
        },
        {
          title: '相关申请流程',
          dataIndex: 'mc',

        },
        {
          title: '申请单位',
          dataIndex: 'dw',
        },
        {
          title: '变更前状态',
          dataIndex: 'sl',

        },
        {
          title: '变更后状态',
          dataIndex: 'sccj',
        },
        {
          title: '变更日期',
          dataIndex: 'ccbh',

        },
      ],
      columns3: [
        {
          title: '序号',
          dataIndex: 'xh',
          width: 80,
        },
        {
          title: '保养单号',
          dataIndex: 'mc',

        },
        {
          title: '保养人',
          dataIndex: 'dw',
        },
        {
          title: '保养部位',
          dataIndex: 'sl',

        },
        {
          title: '保养计划名称',
          dataIndex: 'sccj',
        },
        {
          title: '保养日期',
          dataIndex: 'ccbh',

        },
        {
          title: '管理单位',
          dataIndex: 'ccbh',

        },
        {
          title: '使用地点',
          dataIndex: 'ccbh',

        },
        {
          title: '使用状态',
          dataIndex: 'ccbh',

        },
        {
          title: '是否完好',
          dataIndex: 'ccbh',

        },
      ],
      columns4: [
        {
          title: '序号',
          dataIndex: 'xh',
          width: 80,
        },
        {
          title: '名称及型号',
          dataIndex: 'mc',
          width: 120,
        },
        {
          title: '单位',
          dataIndex: 'dw',
        },
        {
          title: '数量',
          dataIndex: 'sl',
          width: 80,
        },
        {
          title: '生产厂家',
          dataIndex: 'sccj',
        },
        {
          title: '出场编号',
          dataIndex: 'ccbh',
          width: 160,
        },
      ],
      columns5: [
        {
          title: '序号',
          dataIndex: 'xh',
          width: 80,
        },
        {
          title: '名称及型号',
          dataIndex: 'mc',
          width: 120,
        },
        {
          title: '单位',
          dataIndex: 'dw',
        },
        {
          title: '数量',
          dataIndex: 'sl',
          width: 80,
        },
        {
          title: '生产厂家',
          dataIndex: 'sccj',
        },
        {
          title: '出场编号',
          dataIndex: 'ccbh',
          width: 160,
        },
      ],
      columns6: [
        {
          title: '序号',
          dataIndex: 'xh',
          width: 80,
        },
        {
          title: '名称及型号',
          dataIndex: 'mc',
          width: 120,
        },
        {
          title: '单位',
          dataIndex: 'dw',
        },
        {
          title: '数量',
          dataIndex: 'sl',
          width: 80,
        },
        {
          title: '生产厂家',
          dataIndex: 'sccj',
        },
        {
          title: '出场编号',
          dataIndex: 'ccbh',
          width: 160,
        },
      ],
      columns7: [
        {
          title: '序号',
          dataIndex: 'xh',
          width: 80,
        },
        {
          title: '名称及型号',
          dataIndex: 'mc',
          width: 120,
        },
        {
          title: '单位',
          dataIndex: 'dw',
        },
        {
          title: '数量',
          dataIndex: 'sl',
          width: 80,
        },
        {
          title: '生产厂家',
          dataIndex: 'sccj',
        },
        {
          title: '出场编号',
          dataIndex: 'ccbh',
          width: 160,
        },
      ],
      datass: [{
        xh: 1,
        mc: '合能',
        dw: '合肥',
        sl: '10',
        sccj: '陆洋',
        ccbh: '1555',
      }],
      titles:'dbjl'
    };
  },
}
// const columns = [
//       {
//         title: 'Name',
//         dataIndex: 'name',
//       },
//       {
//         title: 'Cash Assets',
//         className: 'column-money',
//         dataIndex: 'money',
//       },
//       {
//         title: 'Address',
//         dataIndex: 'address',
//       },
//     ];
// export default defineComponent({
//   props: {
//     form: {
//       type: Object,
//       default: {}
//     },
//     rules: Object,
//     // 上级列表
//     menuList: Array,
//     // 所属应用列表
//     appList: Array,
//   },
//   setup() {

//     const state = reactive({
//       // 图标列表
//       myIcons: iconData
//     });

//     return {
//       ...toRefs(state)
//     };
//   }
// });
</script>

<style lang="less" scoped>
.rows {
  display: flex;
  height: 53px;
  align-items: center;
  border: 1px solid rgba(172, 180, 201, 0.2);

  .rows_1 {
    width: 145px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #F1F8FF;
    border-right: 1px solid rgba(172, 180, 201, 0.2);

    .rows_1_1 {
      margin-right: 16px;
    }
  }

  .rows_2 {
    .rows_2_2 {
      margin-left: 16px;
    }
  }
}


.rows2 {
  display: flex;
  height: 53px;
  align-items: center;
  border-top: 1px solid rgba(172, 180, 201, 0.2);
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);

  .rows2_2 {
    width: 145px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: rgba(241, 248, 255, 1);
    border-right: 1px solid rgba(172, 180, 201, 0.2);

    .rows2_2_2 {
      margin-right: 16px;
    }
  }

  .rows2_3 {
    .rows2_3_3 {
      margin-left: 16px;
    }
  }
}

.rows1 {
  border-top: 0;
  border-left: 1px solid rgba(172, 180, 201, 0.2);
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);
}

.rows22{
  border-top: 0;
  border-left: 0;
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);
}
.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}

/deep/ .ant-table-thead > tr > th{
 background: rgba(174, 214, 255, .28) !important;
}
</style>
