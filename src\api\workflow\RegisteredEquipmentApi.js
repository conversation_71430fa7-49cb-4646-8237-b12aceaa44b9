import Request from '@/utils/request-util';

export class RegisteredEquipmentApi  {
  /**
   * 获取待办详情
   * @param {string} id - 审批单ID
   * @returns {Promise} 返回审批单详情数据
   */
  static getToDo(params) {
    return Request.get('/apiBus/registeredEquipment/getWorksheetInfo', params);
  }

  static taskFormData(params) {
    return Request.get('/flowableForm/taskFormData', params);
  }

  static globalFormData(params) {
    return Request.get('/flowableForm/globalFormData', params);
  }

  static getEditToDo(params) {
    return Request.get('/apiBus/registeredEditEquipment/getWorksheetInfo', params);
  }
  
  static getFlowAbleMap(params) {
    return Request.get('/flowableInstance/trace', params);
  }
 
  static getCommentHistory(params) {
    return Request.get('/flowableInstance/commentHistory', params);
  }

  static getEditToDo1(params) {
    return Request.get('/apiBus/registeredEditEquipment/getWorksheetInfo', params);
  }
  
}
