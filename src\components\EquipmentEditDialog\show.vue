<template>
  <a-modal v-model:visible="visible" style="min-width: 1100px;" :title="title" :maskClosable="false" :footer="null" @cancel="onClose" centered="true"
    class="editEqu" :destroyOnClose="true" >
    <!-- 左上角提示 -->
    <div style="margin-bottom: 16px;display: flex;">
      <div style="font-size: 18px;color:rgba(48, 49, 51, 1)">
        转让设备入账
      </div>
      <div style="margin-left: 3%;">
        <a-alert type="warning" show-icon
          :message="'设备编号：' + (props.record.beforeTransferCode || '') + ' 的原设备信息已带入，请填写新设备编号，并根据实际情况完成设备相关信息修改'"
          style="width: fit-content; min-width: 400px;" />
      </div>
    </div>
    <div class="ele-body1">
      <div class="equipment-acceptance">


        <!-- 设备信息 -->
       <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          设备信息
        </div>

        <div class="equipment-info-table">
          <div class="info-row">
            <div class="info-cell">
              <div class="label">设备编号</div>
              <div class="value">{{ formData.code }}</div>
            </div>
            <div class="info-cell">
              <div class="label">设备类别</div>
              <div class="value">{{ formData.equTypeStr }}</div>
            </div>
            <div class="info-cell">
              <div class="label">设备种类</div>
              <div class="value">{{ formData.equSubTypeStr }}</div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-cell">
              <div class="label">设备名称</div>
              <div class="value">{{ formData.equNameStr }}</div>
            </div>
            <div class="info-cell">
              <div class="label">规格型号</div>
              <div class="value">{{ formData.equModelStr }}</div>
            </div>
            <div class="info-cell">
              <div class="label">型号备注</div>
              <div class="value">

                 <a-tooltip placement="topLeft" :title="formData.equModelInfo">
                <span>{{ formData.equModelInfo?.length > 15 ? formData.equModelInfo.substring(0, 15) + '...' : formData.equModelInfo }}</span>
              </a-tooltip>
              </div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-cell">
              <div class="label">设备性质</div>
              <div class="value">{{ formData.equNatureStr }}</div>
            </div>
            <div class="info-cell">
              <div class="label">购置年度</div>
              <div class="value">{{ formData.purchaseYear }}</div>
            </div>
            <div class="info-cell">
              <div class="label">设备来源</div>
              <div class="value">{{ formData.equSourceStr }}</div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-cell">
              <div class="label">单位</div>
              <div class="value">{{ formData.unit }}</div>
            </div>
            <div class="info-cell">
              <div class="label">数量</div>
              <div class="value">{{ formData.num }}</div>
            </div>
            <div class="info-cell">
              <div class="label">设备合同价(含税)</div>
              <div class="value">{{ formData.equContractPriceTax }}</div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-cell">
              <div class="label">生产厂家</div>
              <div class="value">{{ formData.manufacturer }}</div>
            </div>
            <div class="info-cell">
              <div class="label">出厂编号</div>
              <div class="value">{{ formData.factoryNumber }}</div>
            </div>
            <div class="info-cell">
              <div class="label">出厂日期</div>
              <div class="value">{{ formData.productionDate }}</div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-cell">
              <div class="label">合同编号</div>
              <div class="value">{{ formData.contractNumber }}</div>
            </div>
            <div class="info-cell">
              <div class="label">验收单号</div>
              <div class="value">{{ formData.acceptanceNumber }}</div>
            </div>
            <div class="info-cell">
              <div class="label">验收日期</div>
              <div class="value">{{ formData.acceptanceDate }}</div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-cell">
              <div class="label">功率(kw)</div>
              <div class="value">{{ formData.power }}</div>
            </div>
            <div class="info-cell">
              <div class="label">设备重要性</div>
              <div class="value">{{ formData.importanceStr }}</div>
            </div>
            <div class="info-cell">
              <div class="label">保养周期</div>
              <div class="value">{{ formData.serviceIntervalTypeStr }}</div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-cell">
              <div class="label">设备型号编码</div>
              <div class="value">{{ formData.modelCode }}</div>
            </div>
            <div class="info-cell">
              <div class="label">技术资料</div>

              <div class="value tech-files" ref="containerRef">
                <template v-if="formData.technicalFileList && formData.technicalFileList.length > 0">
                  <!-- <div v-for="(file, index) in formData.technicalFileList" :key="file.fileId" class="file-item">
                    <a :href="`${file.fileUrl}?fileName=${file.fileOriginName}`" target="_blank" class="file-link">
                      {{ file.fileOriginName }}
                    </a>
                  </div> -->
                  <InputUploader
    :max-count="10"
    button-text="上传文件"
    :accept-types="['.jpg', '.jpeg', '.png', '.mp4', '.mov', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', '.pdf']"
    :initial-files="formData.technicalFileList"
    :show-border="false"
    @file-uploaded="onTechFileUploaded"
    @file-removed="onTechFileRemoved"
    @upload-status-change="onUploadStatusChange"
       :read-only="true"
       :width="containerWidth"
  />
                </template>
                <span v-else>无技术资料</span>
              </div>
            </div>
            <div class="info-cell">
              <div class="label"></div>
              <div class="value"></div>
            </div>
          </div>
        </div>
      </div>

        <!-- 使用情况 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          使用情况
        </div>

        <div class="equipment-info-table">
          <div class="info-row">
            <div class="info-cell">
              <div class="label">产权单位</div>
              <div class="value">{{ formData.propertyOrgStr }}</div>
            </div>

            <div class="info-cell">
              <div class="label">管理单位</div>
              <div class="value">{{ formData.managementOrgStr }}</div>
            </div>

             <div class="info-cell">
              <div class="label">使用单位</div>
              <div class="value">{{ formData.useOrgStr }}</div>
            </div>
          </div>

          <div class="info-row">

            <div class="info-cell">
              <div class="label">管理状态</div>
              <div class="value">{{ formData.managementStatusStr }}</div>
            </div>
            <div class="info-cell">
              <div class="label">设备状态</div>
              <div class="value">{{ formData.equConditionStr }}</div>
            </div>

            <div class="info-cell">
              <div class="label">存放地点</div>
              <div class="value">{{ formData.storageLocationStr }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 财务信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          财务信息
        </div>

        <div class="equipment-info-table">
          <div class="info-row">
            <div class="info-cell">
              <div class="label">财务卡片编号</div>
              <div class="value">
                <a-tooltip placement="topLeft" :title="formData.financialNumber">
                <span>{{ formData.financialNumber?.length > 15 ? formData.financialNumber.substring(0, 15) + '...' : formData.financialNumber }}</span>
              </a-tooltip>
              </div>
            </div>
            <div class="info-cell">
              <div class="label">财务组织</div>
              <div class="value">
                <a-tooltip placement="topLeft" :title="formData.financialOrg">
                <span>{{ formData.financialOrg?.length > 15 ? formData.financialOrg.substring(0, 15) + '...' : formData.financialOrg }}</span>
              </a-tooltip>
              </div>
            </div>
            <div class="info-cell">
              <div class="label">财务原值</div>
              <div class="value">{{ formData.financialOriginalValue }}</div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-cell">
              <div class="label">固定资产分类</div>
              <div class="value">{{ formData.fixedAssetsStr }}</div>
            </div>
            <div class="info-cell">
              <div class="label">资金来源</div>
              <div class="value">{{ formData.sourceOfFunds }}</div>
            </div>
            <div class="info-cell">
              <div class="label">递延收益</div>
              <div class="value">{{ formData.deferredIncome }}</div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-cell">
              <div class="label">已计提折旧月份</div>
              <div class="value">{{ formData.alreadyAccruedMonths }}</div>
            </div>
            <div class="info-cell">
              <div class="label">折旧方式</div>
              <div class="value">{{ formData.depreciationMethodStr }}</div>
            </div>
            <div class="info-cell">
              <div class="label">折旧年限</div>
              <div class="value">{{ formData.depreciationPeriod }}</div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-cell">
              <div class="label">折旧月份</div>
              <div class="value">{{ formData.depreciationMonth }}</div>
            </div>
            <div class="info-cell">
              <div class="label">残值率</div>
              <div class="value">{{ formData.residualRate }}<span v-if="formData.residualRate">%</span></div>
            </div>
            <div class="info-cell">
              <div class="label">预计净残值</div>
              <div class="value">{{ formData.netSalvage }}</div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-cell">
              <div class="label">历史原值</div>
              <div class="value">{{ formData.historicalOriginalValue }}</div>
            </div>
            <div class="info-cell">
              <div class="label">累计折旧额</div>
              <div class="value">{{ formData.depreciationAmount }}</div>
            </div>
            <div class="info-cell">
              <div class="label">当月折旧额</div>
              <div class="value">{{ formData.currentMonthDepreciationAmount }}</div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-cell">
              <div class="label">净值</div>
              <div class="value">{{ formData.netWorth }}</div>
            </div>
            <div class="info-cell">
              <div class="label">税率</div>
              <div class="value">{{ formData.taxRate }}<span v-if="formData.taxRate">%</span></div>
            </div>
            <div class="info-cell">
              <div class="label"></div>
              <div class="value"></div>
            </div>
          </div>
        </div>
      </div>


       <!-- 主要附件 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          主要附件
        </div>

        <a-table :columns="attachmentColumns" :data-source="attachments" :pagination="false" bordered>
          <template #bodyCell="{ column, text }">

          </template>
        </a-table>
      </div>

      <!-- 附件 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          附件
        </div>
        <div class="upload-section">
          <div class="file-list">
            <template v-if="formData.fileList && formData.fileList.length > 0">
              <div v-for="(file, index) in formData.fileList" :key="file.fileId" class="file-item">
                <div class="file-icon">
                 <img src="/public/icon/icon-wrapper.svg" v-if="file.fileOriginName.endsWith('.xls') || file.fileOriginName.endsWith('.xlsx')" />
                  <img src="/public/icon/word.svg" v-else-if="file.fileOriginName.endsWith('.doc') || file.fileOriginName.endsWith('.docx')" />
                  <img src="/public/icon/pdf.svg" v-else-if="file.fileOriginName.endsWith('.pdf')" />
                  <img src="/public/icon/mp4.svg" v-else-if="file.fileOriginName.endsWith('.mov') || file.fileOriginName.endsWith('.mp4')" />
                  <img src="/public/icon/ppt.svg" v-else-if="file.fileOriginName.endsWith('.ppt') || file.fileOriginName.endsWith('.pptx')" />
                  <img src="/public/icon/jpg.svg" v-else-if="file.fileOriginName.endsWith('.png') || file.fileOriginName.endsWith('.jpg') || file.fileOriginName.endsWith('.jpeg') || file.fileOriginName.endsWith('.heif')" />
                  <img src="/public/icon/reader.svg" v-else />
                </div>
                <a :href="`${file.fileUrl}?fileName=${file.fileOriginName}`" target="_blank" class="file-link">
                  {{ file.fileOriginName }}
                </a>
                <sync-outlined v-if="file.status === 'loading'" class="loading-icon" spin />
              </div>
            </template>
            <div v-else class="no-files">暂无附件</div>
          </div>
        </div>
      </div>
      </div>

    </div>
  </a-modal>
</template>

<script setup>

import { computed, ref, onMounted, nextTick, watch } from 'vue';
import { message } from 'ant-design-vue';
import { transferApi } from '@/api/dynamic/transferApi';
import { BasicInformationApi } from '@/api/buss/BasicInformationApi';
import { FileUploadUrl } from '@/api/system/operation/FileApi';
import { getToken } from '@/utils/token-util';
import { EnumApi } from '@/api/common/enum';
import { API_BASE_PREFIX } from '@/config/setting';
import { useUserStore } from '@/store/modules/user';
import dayjs from 'dayjs';
import { ConsoleSqlOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import InputUploader from '@/components/InputUploader/index.vue';
const userStore = useUserStore();
const loading = ref(false);
const saving = ref(false);
const submitting = ref(false);

const router = useRouter();
const processDefinitionId = ref(''); // 流程定义ID

// 业务标题和关联投资计划的独立字段
const businessTitle = ref('');
const investmentPlan = ref('');
const isTransfer = ref('false'); // 添加是否转入设备字段，默认为'1'（否）
const yearFormat = 'YYYY';
// 上传相关配置
const uploadUrl = `${API_BASE_PREFIX}${FileUploadUrl}?secretFlag=0`;
const headers = {
  Authorization: getToken()
};

// 文件列表相关
const fileList = ref([]);
const fileViewList = ref([]);
const techFileList = ref([]); // 技术资料上传组件的文件列表
const techFileViewList = ref([]); // 技术资料显示列表

// 定义验证规则
const rules = {
  // 申请信息

  // 设备信息
  code: [{ required: true, message: '请输入设备编号' }],
  equType: [{ required: true, message: '请选择设备类别' }],
  equSubType: [{ required: true, message: '请选择设备种类' }],
  equName: [{ required: true, message: '请选择设备名称' }],
  equModel: [{ required: true, message: '请选择规格型号' }],
  equNature: [{ required: true, message: '请选择设备性质' }],
  purchaseYear: [{ required: true, message: '请选择购置年度' }],
  unit: [{ required: true, message: '请输入单位' }],
  equContractPriceTax: [{ required: true, message: '请输入设备合同价(含税)' }],
  manufacturer: [{ required: true, message: '请输入生产厂家' }],
  factoryNumber: [{ required: true, message: '请输入出厂编号' }],
  productionDate: [{ required: true, message: '请选择出厂日期' }],


  // 使用情况
  propertyOrg: [{ required: true, message: '请输入产权单位' }],
  managementStatus: [{ required: true, message: '请选择管理状态' }],
  managementOrg: [{ required: true, message: '请输入管理单位' }],
  equCondition: [{ required: true, message: '请选择设备状态' }],
  useOrg: [{ required: true, message: '请选择使用单位' }],

  // 财务信息
  depreciationMethod: [{ required: true, message: '请选择折旧方式' }],
  historicalOriginalValue: [{ required: true, message: '请输入历史原值' }]
};

// 验证表单数据
const validateForm = () => {
  // 按顺序定义需要验证的字段
  // 首先验证业务标题
  const fieldsToValidate = [
    { field: 'code', label: '设备编号', type: 'input' },
    { field: 'equType', label: '设备类别', type: 'select' },
    { field: 'equSubType', label: '设备种类', type: 'select' },
    { field: 'equName', label: '设备名称', type: 'select' },
    { field: 'equModel', label: '规格型号', type: 'select' },
    { field: 'equNature', label: '设备性质', type: 'select' },
    { field: 'purchaseYear', label: '购置年度', type: 'date' },
    { field: 'unit', label: '单位', type: 'input' },
    { field: 'equContractPriceTax', label: '设备合同价(含税)', type: 'number' },
    { field: 'manufacturer', label: '生产厂家', type: 'input' },
    { field: 'factoryNumber', label: '出厂编号', type: 'input' },
    { field: 'productionDate', label: '出厂日期', type: 'date' },
    { field: 'propertyOrgName', label: '产权单位', type: 'input' },
    { field: 'managementStatus', label: '管理状态', type: 'select' },
    { field: 'managementOrgName', label: '管理单位', type: 'input' },
    { field: 'equCondition', label: '设备状态', type: 'select' },
    { field: 'useOrg', label: '使用单位', type: 'select' },
    { field: 'depreciationMethod', label: '折旧方式', type: 'select' },
    { field: 'depreciationPeriod', label: '折旧年限', type: 'input' },
    { field: 'historicalOriginalValue', label: '历史原值', type: 'number' }
  ];

  // 依次验证每个字段
  for (const { field, label, type } of fieldsToValidate) {
    if (!formData.value[field]) {
      message.error(`请${type === 'select' ? '选择' : type === 'date' ? '选择' : '输入'}${label}`);

      const formItem = document.querySelector(`[data-field="${field}"]`);
      if (formItem) {
        // 滚动到可视区域，并确保元素在视图中间
        formItem.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // 使用 nextTick 确保 DOM 更新后再执行点击操作
        nextTick(() => {
          setTimeout(() => {
            switch (type) {
              case 'input':
              case 'number': {
                const input = formItem.querySelector('input');
                input?.focus();
                break;
              }
              case 'select': {
                const select = formItem.querySelector('.ant-select-selector');
                select?.click();
                break;
              }
              case 'date': {
                const datePicker = formItem.querySelector('.date-picker');
                if (datePicker) {
                  // 先聚焦
                  const input = datePicker.querySelector('input');
                  input?.focus();
                  // 然后触发点击以打开日期选择面板
                  setTimeout(() => {
                    datePicker.click();
                  }, 100);
                }
                break;
              }
            }
          }, 500); // 等待滚动完成后再聚焦
        });
      }
      return false;
    }
  }

  return true;
};
// 定义表格列
const attachmentColumns = [
  {
    title: '序号',
    dataIndex: 'serial',
    width: '10%',
    customRender: ({ index }) => index + 1
  },
  {
    title: '名称及型号',
    dataIndex: 'nameModel',
    width: '25%'
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: '10%'
  },
  {
    title: '数量',
    dataIndex: 'num',
    width: '10%'
  },
  {
    title: '生产厂家',
    dataIndex: 'manufacturer',
    width: '25%'
  },
  {
    title: '出厂编号',
    dataIndex: 'factoryNumber',
    width: '25%'
  }
];

// 构建保存数据的方法
const buildSaveRequestData = () => {
  console.log(bussWorksheet.value)
  const requestData = {

    ...formData.value,
    technicalFileList: techFileList.value,
    fileList: fileViewList.value,
    transferCode: transferCode.value,
    bussEquipmentAccessoryList: attachments.value?.map(item => ({
      nameModel: item.nameModel,
      unit: item.unit,
      num: item.num,
      manufacturer: item.manufacturer,
      factoryNumber: item.factoryNumber
    })),
  };

  return requestData;
};
// 构建提交数据的方法
const buildRequestData = () => {
  const requestData = {


    bussEquipmentProcessTrackingList: [
      {
        ...formData.value,
        beforeTransferCode: props.record.beforeTransferCode,
        technicalFileList: techFileList.value,
        fileList: fileViewList.value,
        transferCode: transferCode.value,
        bussEquipmentAccessoryList: attachments.value?.map(item => ({
          nameModel: item.nameModel,
          unit: item.unit,
          num: item.num,
          manufacturer: item.manufacturer,
          factoryNumber: item.factoryNumber
        })),
      }
    ]
  };

  return requestData;
};

// 在提交和保存前处理验收单号
const processAcceptanceNumber = () => {
  if (!formData.value.acceptanceNumberYan && !formData.value.acceptanceChar) {
    formData.value.acceptanceNumber = null;
    return;
  }
  formData.value.acceptanceNumber =
    `${formData.value.acceptanceNumberYan}验资${formData.value.acceptanceChar}号`;
};

// 处理返回的验收单号，分割成两个字段
const parseAcceptanceNumber = (acceptanceNumber) => {
  if (!acceptanceNumber) return;

  const match = acceptanceNumber.match(/^(.+)验资(.+)号$/);
  if (match) {

    formData.value.acceptanceNumberYan = match[1];

    formData.value.acceptanceChar = match[2];
  }
};

// 保存方法
const handleSave = async () => {
  try {
    saving.value = true;
    // 处理验收单号
    processAcceptanceNumber();

    const params = buildSaveRequestData();
    const res = await transferApi.editTransferEquipment(params);

    if (res.success) {
      message.success('保存成功');
      // 从返回值中完全覆盖表单数据
      if (res.data) {
        // 完全覆盖bussTransferForm数据
        if (res.data.bussTransferForm) {
          bussTransferForm.value = res.data.bussTransferForm;
        }

        // 完全覆盖bussWorksheet数据
        if (res.data.bussWorksheet) {
          bussWorksheet.value = res.data.bussWorksheet;
          businessTitle.value = res.data.bussWorksheet.name;
        }

        // 处理验收单号的回显
        if (res.data.bussEquipmentProcessTrackingList?.[0]?.acceptanceNumber) {
          parseAcceptanceNumber(res.data.bussEquipmentProcessTrackingList[0].acceptanceNumber);
        }
      }
    } else {
      message.error('保存失败');
    }
  } catch (error) {
    message.error('保存失败');
  } finally {
    saving.value = false;
  }
};

// 提交方法
const handleSubmit = async () => {
  try {
    // 执行验证
    if (!validateForm()) {
      return;
    }

    submitting.value = true;
    // 处理验收单号
    //processAcceptanceNumber();

    const innerFormData = buildRequestData();
    console.log(innerFormData.bussEquipmentProcessTrackingList[0]);
    const res = await transferApi.editTransferEquipment(innerFormData.bussEquipmentProcessTrackingList[0]);
    console.log(res.success);
    // 如果提交成功，关闭弹窗

    if (res.success) {
      message.success('保存成功');
      //router.push('/buss/regEquLedger');
      try {
        emit('save', res.data);
      } catch (e) {
        // 忽略已卸载时的报错
      }
    } else {
      message.error('保存失败1');
    }
  } catch (error) {
    message.error('保存失败');
  } finally {
    submitting.value = false;
  }
};
const bussTransferForm = ref({
  applyUser: '', // 申请人ID
  applyUserStr: '', // 申请人姓名
  applyOrg: '', // 申请单位ID
  applyOrgStr: '', // 申请单位名称
  applyDate: '', // 申请日期
  applyTitle: '', // 申请标题
  investmentPlan: '', // 投资计划
  isTransfer: '1' // 是否转入设备
});
const bussWorksheet = ref({
  name: '',
  id: ""
});
const formData = ref({

  id: null,
  baseCode: '', // 基础编码
  code: '', // 编码
  equType: null, // 设备类别
  equSubType: null, // 设备种类
  equName: null, // 设备名称
  equModel: null, // 规格型号
  equModelInfo: '', // 型号备注
  equNature: null, // 设备性质
  purchaseYear: null, // 购置年度
  equSource: null, // 设备来源
  unit: '台', // 单位
  num: 1, // 数量
  equContractPriceTax: null, // 设备合同价（含税）
  manufacturer: '', // 生产厂家
  factoryNumber: '', // 出厂编号
  productionDate: null, // 出厂日期
  contractNumber: '', // 合同编号
  acceptanceNumber: null, // 验收单号
  acceptanceNumberYan: null, // 验收单号
  acceptanceChar: '', // 验字
  acceptanceDate: null, // 验收日期
  power: '', // 功率
  importance: null, // 设备重要性
  modelCode: '', // 设备型号编码
  serviceIntervalTypeStr: '', // 保养周期
  propertyOrg: null, // 产权单位
  managementStatus: null, // 管理状态
  managementOrg: null, // 管理单位
  equCondition: null, // 设备状态
  useOrg: null, // 使用单位
  storageLocationStr: null, // 存放地点
  financialNumber: '', // 财务卡片编号
  financialOrg: '', // 财务组织
  financialOriginalValue: null, // 财务原值
  fixedAssets: '', // 固定资产分类
  fixedAssetsStr:"",
  sourceOfFunds: '', // 资金来源
  deferredIncome: '', // 递延收益
  alreadyAccruedMonths: '', // 已计提折旧月份
  depreciationMethod: null, // 折旧方式
  depreciationPeriod: '', // 折旧年限
  depreciationMonth: '', // 折旧月份
  residualRate: null, // 残值率
  netSalvage: null, // 预计净残值
  historicalOriginalValue: null, // 历史原值
  depreciationAmount: null, // 累计折旧额
  currentMonthDepreciationAmount: null, // 当月折旧额
  netWorth: null, // 净值
  taxRate: 13, // 税率
  technicalFileList:[], // 技术资料列表
  fileList: [], // 附件列表
});

// 附件列表数据
const attachments = ref([

]);

// 添加新附件
const addAttachment = () => {
  attachments.value.push({
    nameModel: '',
    unit: '',
    num: 1,
    manufacturer: '',
    factoryNumber: ''
  });
};

// 删除附件
const removeAttachment = (index) => {
  attachments.value.splice(index, 1);
};

// 处理普通附件上传
// const handleChange = (info) => {
//   console.log('当前上传的文件:', info.file);
//   console.log('所有文件列表:', info.fileList);

//   // 获取当前已有的文件列表（不包括正在上传的文件）
//   const existingFiles = fileViewList.value || [];

//   // 处理新上传的文件
//   if (info.file.status === 'done' && info.file.response && info.file.response.success) {
//     // 从响应中获取文件ID和URL
//     const responseData = info.file.response.data;
//     const newFileId = responseData.fileId;
//     const newFileUrl = responseData.fileUrl;

//     // 检查文件是否已经存在（通过fileId比较）
//     const fileExists = existingFiles.some(file => file.fileId === newFileId);

//     // 如果文件不存在，则添加到列表中
//     if (!fileExists) {
//       fileViewList.value = [
//         ...existingFiles,
//         {
//           fileId: newFileId,
//           fileName: info.file.name,
//           fileUrl: newFileUrl,
//           uid: info.file.uid,
//           status: 'done'
//         }
//       ];
//     }
//   }

//   // 处理单个文件的状态提示
//   if (info.file.status === 'error') {
//     message.error('上传失败');
//   }
// };

// 处理技术资料上传
const handleTechFileChange = (info) => {
  // 保持原始列表与上传组件同步
  techFileList.value = info.fileList;
  techFileList.value[0].fileName = info.file.name;
  console.log(techFileList.value)
  if (info.file.status === 'done' && info.file.response) {
    const response = info.file.response;
    if (response.success) {
      // 更新显示列表
      techFileList.value = [{
        fileId: response.data.fileId,
        fileName: info.file.name,
        fileUrl: response.data.fileUrl,
        uid: info.file.uid,
        status: 'done'
      }];

      // 更新formData中的techFile用于显示
      formData.value.techFile = {
        fileId: response.data.fileId,
        fileName: info.file.name
      };
    } else {
      message.error('技术资料上传失败');
    }
  } else if (info.file.status === 'error') {
    message.error('上传失败');
  }
};

// 删除技术资料
const removeTechFile = () => {
  formData.value.techFile = null;
  techFileList.value = [];
};

// 删除普通附件
const handleRemove = (file) => {
  // 从显示列表中删除
  const viewIndex = fileViewList.value.findIndex(f => f.uid === file.uid);
  if (viewIndex > -1) {
    fileViewList.value.splice(viewIndex, 1);
  }

  // 从上传列表中删除
  const fileIndex = fileList.value.findIndex(f => f.uid === file.uid);
  if (fileIndex > -1) {
    fileList.value.splice(fileIndex, 1);
  }
};

// 审批记录相关方法
const showApprovalRecord = () => {
  // 处理审批记录的显示逻辑
  console.log('显示审批记录');
};

const autoSave = () => {

}
const emit = defineEmits(['update:visible', 'close']);
const initializeData = async () => {
  loading.value = true;

  try {
    //获取新建设备验收单数据
    console.log(111, props.record)
    const { data } = await transferApi.getTransferFormEquipmentById({ bussWorksheetId:props.record.bussWorksheetId,equipmentBeforeTransferId: props.record.id });
    const loginUser = computed(() => userStore.info ?? {});
    console.log(loginUser.value)
    // 获取使用单位列表
    const useOrgRes = await EnumApi.getUseOrgList({ pId: loginUser.value.organizationId,isLibrary:true });
    useOrgOptions.value = useOrgRes.data

    // 获取存放地点列表
    // const addressRes = await EnumApi.getAddrOrgList({ pId: loginUser.value.organizationId });
    // storageLocationOptions.value = addressRes.data


    const equipmentData = data;
    formData.value = equipmentData;
    if (equipmentData.bussEquipmentAccessoryList?.length > 0) {
      attachments.value = equipmentData.bussEquipmentAccessoryList.map(item => ({
        nameModel: item.nameModel || '',
        unit: item.unit || '',
        num: item.num || 1,
        manufacturer: item.manufacturer || '',
        factoryNumber: item.factoryNumber || ''
      }));
    } else {
      attachments.value = [];
    }

    if (equipmentData.fileList?.length > 0) {
      fileList.value = equipmentData.fileList.map(file => ({
        uid: file.fileId, // 使用fileId作为uid
        name: file.fileOriginName,
        status: 'done',
        url: file.fileUrl,
        response: { data: { fileId: file.fileId, fileUrl: file.fileUrl } }
      }));
    } else {
      fileList.value = [];
    }
    if (equipmentData.technicalFileList?.length > 0) {
      techFileList.value = equipmentData.technicalFileList.map(file => ({
        uid: file.fileId, // 使用fileId作为uid
        name: file.fileOriginName,
        status: 'done',
        url: file.fileUrl,
        response: { data: { fileId: file.fileId, fileUrl: file.fileUrl } }
      }));
    } else {
      techFileList.value = [];
    }



    // 处理验收单号的回显
    if (equipmentData.acceptanceNumber) {

      parseAcceptanceNumber(equipmentData.acceptanceNumber);
    }

    //连带框的数据回显

    console.log(formData.value.equType, formData.value.equSubType, formData.value.equName)
    if (formData.value.equType) {
      handleEquType1(formData.value.equType);
    }
    if (formData.value.equSubType) {
      handleEquSubType2(formData.value.equSubType);
    }
    if (formData.value.equName) {
      handleEquName3(formData.value.equName);
    }
    formData.value.propertyOrg = loginUser.value.organizationId;
    formData.value.managementOrg = loginUser.value.organizationId;
    formData.value.propertyOrgName = loginUser.value.organizationName;
    formData.value.managementOrgName = loginUser.value.organizationName;

    if(props.record.isOld){
       formData.value.id = "";
       formData.value.useOrg = "";
       formData.value.code = "";
    }

  } catch (error) {
    message.error('获取设备验收单数据失败');
  } finally {
    loading.value = false;
  }
};

const handleEquType1 = async (value) => {

  if (value) {
    try {
      const res = await BasicInformationApi.getEquData({ pId: value });
      equSubTypeOptions.value = res.data;
    } catch (error) {
      message.error('获取设备种类失败');
    }
  }
}

const handleEquSubType2 = async (value) => {
  if (value) {
    try {
      const res = await BasicInformationApi.getEquData({ pId: value });
      equNameOptions.value = res.data;
    } catch (error) {
      message.error('获取设备名称失败');
    }
  }
}

const handleEquName3 = async (value) => {
  if (value) {
    try {
      const res = await BasicInformationApi.getEquData({ pId: value });
      equModelOptions.value = res.data;
    } catch (error) {
      message.error('获取规格型号失败');
    }
  }
}

const initProcessDefinitionId = async () => {
  try {
    const { data } = await EnumApi.getProcessDefinitionByKey({
      key: 'equipment_acceptance'
    });
    processDefinitionId.value = data;
  } catch (error) {
    message.error('获取流程定义ID失败');
  }
};

const containerWidth = ref(0)
const containerRef = ref()
const updateContainerWidth = () => {
  if (containerRef.value) {
    containerWidth.value = containerRef.value.offsetWidth
  }
}


onMounted(() => {
  initializeData();
  getEquTypeOptions();
  initEnumData();
  initProcessDefinitionId();
  updateContainerWidth()
  window.addEventListener('resize', updateContainerWidth)
});


// 在 setup 中添加
const equTypeOptions = ref([]);      // 设备类别选项
const equSubTypeOptions = ref([]);   // 设备种类选项
const equNameOptions = ref([]);      // 设备名称选项
const equModelOptions = ref([]);     // 规格型号选项
const equNatureOptions = ref([]);    // 设备性质选项
const equSourceOptions = ref([]);    // 设备来源选项
const equImportanceOptions = ref([]); // 设备重要性选项
const managementStatusOptions = ref([]); // 管理状态选项
const equConditionOptions = ref([]); // 设备状态选项
const depreciationMethodOptions = ref([]); // 折旧方式选项
const fixedAssetsOptions = ref([]); // 固定资产选项

// 获取设备类别（第一级）
const getEquTypeOptions = async () => {
  try {

    const res = await BasicInformationApi.getEquData();
    equTypeOptions.value = res.data;
  } catch (error) {
    message.error('获取设备类别失败');
  }
};

// 设备类别改变
const handleEquTypeChange = async (value) => {
  formData.value.equSubType = null;
  formData.value.equName = null;
  formData.value.equModel = null;
  equSubTypeOptions.value = [];
  equNameOptions.value = [];
  equModelOptions.value = [];

  if (value) {
    try {
      const res = await BasicInformationApi.getEquData({ pId: value });
      equSubTypeOptions.value = res.data;
    } catch (error) {
      message.error('获取设备种类失败');
    }
  }
};

// 设备种类改变
const handleEquSubTypeChange = async (value) => {
  formData.value.equName = null;
  formData.value.equModel = null;
  equNameOptions.value = [];
  equModelOptions.value = [];

  if (value) {
    try {
      const res = await BasicInformationApi.getEquData({ pId: value });
      equNameOptions.value = res.data;
    } catch (error) {
      message.error('获取设备名称失败');
    }
  }
};

// 设备名称改变
const handleEquNameChange = async (value) => {
  formData.value.equModel = null;
  equModelOptions.value = [];

  if (value) {
    try {
      // 获取规格型号选项
      const res = await BasicInformationApi.getEquData({ pId: value });
      equModelOptions.value = res.data;

      // 获取保养周期
      const { data } = await transferApi.getEquipmentByCode({ id: value });
      if (data) {
        formData.value.serviceIntervalTypeStr = data.maintenanceCycleStr;
      }
    } catch (error) {
      message.error('获取规格型号失败');
    }
  }
};

// 规格型号改变
const handleEquModelChange = async (value) => {
  if (value) {
    try {
      // 获取设备型号编码
      const { data } = await transferApi.getEquipmentByCode({ id: value });
      if (data) {
        formData.value.modelCode = data.code;
      }
    } catch (error) {
      message.error('获取设备型号编码失败');
    }
  }
};

// 初始化枚举数据
const initEnumData = async () => {
  try {
    // 获取设备性质枚举
    const equNatureData = await EnumApi.getEnumList({ enumName: 'EquNatureEnum' });
    equNatureOptions.value = equNatureData.data;

    // 获取设备来源枚举
    const equSourceData = await EnumApi.getEnumList({ enumName: 'EquSourceEnum' });
    equSourceOptions.value = equSourceData.data;

    // 获取设备重要性枚举
    const equImportanceData = await EnumApi.getEnumList({ enumName: 'EquImportanceEnum' });
    equImportanceOptions.value = equImportanceData.data;

    // 获取管理状态枚举
    const managementStatusData = await EnumApi.getEnumList({ enumName: 'ManagementStatusEnum' });
    managementStatusOptions.value = managementStatusData.data;

    // 获取设备状态枚举
    const equConditionData = await EnumApi.getEnumList({ enumName: 'EquConditionEnum' });
    equConditionOptions.value = equConditionData.data;

    // 没用到 20250723 王文胜
    //const fixedAssetsOptionsData = await EnumApi.getEnumList({ enumName: 'FixedAssetsEnum' });
    //fixedAssetsOptions.value =  fixedAssetsOptionsData.data;
    // 获取折旧方式枚举
    const depreciationMethodData = await EnumApi.getEnumList({ enumName: 'DepreciationMethodEnum' });
    depreciationMethodOptions.value = depreciationMethodData.data;




    // 设置默认值
    formData.value.managementStatus = 1;
    formData.value.equCondition = 1;
  } catch (error) {
    console.error("获取枚举数据失败",error);
    message.error('获取枚举数据失败');
  }
};

// 转入设备编号
const transferCode = ref('');

// 查询转入设备信息
const searchTransferEquipment = async () => {
  if (!transferCode.value) {
    message.warning('请输入转入设备编号');
    return;
  }

  try {
    // 调用查询接口，获取设备信息
    const res = await transferApi.getDetailByEquCode({ code: transferCode.value });

    // 检查响应数据是否存在
    if (!res || !res.data) {
      message.warning('获取设备信息失败：返回数据为空');
      return;
    }

    const equipmentData = res.data.bussDisposaledEquipment;

    if (equipmentData) {
      // 更新基本表单数据 - 确保数据存在
      formData.value = equipmentData;
      formData.value.code = "";
      // 处理主要配件数据 - 确保数组存在
      if (equipmentData.bussEquipmentAccessoryList && Array.isArray(equipmentData.bussEquipmentAccessoryList)) {
        attachments.value = equipmentData.bussEquipmentAccessoryList.map(item => ({
          nameModel: item.nameModel || '',
          unit: item.unit || '',
          num: item.num || 1,
          manufacturer: item.manufacturer || '',
          factoryNumber: item.factoryNumber || ''
        }));
      } else {
        // 如果配件列表不存在或不是数组，设置为空数组
        attachments.value = [];
      }

      // 处理文件列表 - 确保数组存在
      if (equipmentData.fileList && Array.isArray(equipmentData.fileList)) {
        fileViewList.value = equipmentData.fileList.map(file => ({
          fileId: file.fileId || '',
          fileName: file.fileOriginName || '',
          fileUrl: file.fileUrl || '',
          uid: file.fileId || Date.now().toString() // 使用fileId作为uid，如果不存在则使用时间戳
        }));
      } else {
        // 如果文件列表不存在或不是数组，设置为空数组
        fileViewList.value = [];
      }

      // 处理技术资料 - 确保数组存在
      if (equipmentData.technicalFileList && Array.isArray(equipmentData.technicalFileList)) {
        techFileList.value = equipmentData.technicalFileList.map(file => ({
          fileId: file.fileId || '',
          fileName: file.fileOriginName || '',
          fileUrl: file.fileUrl || '',
          uid: file.fileId || Date.now().toString()
        }));
      } else {
        // 如果技术资料不存在或不是数组，设置为空数组
        techFileList.value = [];
      }

      // 处理验收单号的回显
      if (equipmentData.acceptanceNumber) {
        parseAcceptanceNumber(equipmentData.acceptanceNumber);
      }

      const addressRes = await EnumApi.getAddrOrgList({ pId: formData.value.useOrg });
      storageLocationOptions.value = addressRes.data;

      // 连带框的数据回显
      if (formData.value.equType) {
        handleEquType1(formData.value.equType);
      }
      if (formData.value.equSubType) {
        handleEquSubType2(formData.value.equSubType);
      }
      if (formData.value.equName) {
        handleEquName3(formData.value.equName);
      }

      // 设置组织名称
      formData.value.propertyOrgName = formData.value.propertyOrgStr || '';
      formData.value.managementOrgName = formData.value.managementOrgStr || '';

      message.success('设备信息加载成功');
    } else {
      message.warning('未找到对应的设备信息');
    }
  } catch (error) {
    console.error('查询设备信息失败:', error);
    message.error('查询设备信息失败');
  }
};
const onClose = () => {
  emit('update:visible', false);

};

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: () => ({})
  },
});

// 选项数据
const useOrgOptions = ref([]);
const storageLocationOptions = ref([]);


// 处理使用单位变化
const handleUseOrgChange = async (value) => {
  formData.value.useOrg = value;  // 保存数字 ID
  formData.value.storageLocationStr = null; // 清空存放地点的选择
  // storageLocationOptions.value = []; // 清空存放地点选项

  // if (value) {
  //   try {
  //     // 获取存放地点列表，使用选中的使用单位ID作为参数
  //     const addressRes = await EnumApi.getAddrOrgList({ pId: value });
  //     storageLocationOptions.value = addressRes.data;
  //   } catch (error) {
  //     message.error('获取存放地点列表失败');
  //   }
  // }
};

// 处理存放地点变化
const handleStorageLocationChange = (value) => {
  formData.value.storageLocationStr = value;  // 保存数字 ID
};

// 验证编码格式
const validateCodeFormat = (value) => {
  if (!value) return;
  if (!value.target.value) return;
  // 定义正则表达式：字母-数字-数字 格式
  const regex = /^[A-Za-z0-9]{2}-\d+-\d+$/;
  console.log(value.target.value + "")
  console.log(regex.test(value.target.value + ""))
  // 如果不符合格式，自动格式化或提示错误
  if (!regex.test(value.target.value + "")) {
    message.error("设备编号格式不正确,参考ZM-01-001");
    return;
  }

  // 确保总长度不超过10个字符
  if (value.length > 10) {
    formData.code = value.substring(0, 10);
  }
};

// 上传前验证文件类型
const beforeTechFileUpload = (file) => {
  // 定义允许的文件类型
  const allowedTypes = [
    'application/pdf',                                                  // PDF
    'application/msword',                                               // DOC
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
    'image/jpeg',                                                       // JPG/JPEG
    'image/png'                                                         // PNG
  ];

  // 检查文件类型是否在允许列表中
  const isAllowedType = allowedTypes.includes(file.type);

  if (!isAllowedType) {
    message.error('只能上传PDF、Word文档或图片文件！');
  }

  // 检查文件大小（例如限制为10MB）
  // const isLessThan10M = file.size / 1024 / 1024 < 10;

  // if (!isLessThan10M) {
  //   message.error('文件大小不能超过10MB！');
  // }

  // 返回验证结果，true表示允许上传，false表示阻止上传
  return isAllowedType;
};

// 自定义搜索过滤逻辑
const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};


// 上传状态
const uploading = ref(false);

// 上传前验证文件
const beforeFileUpload = (file) => {
  // 检查文件数量限制
  if (fileList.value.length >= 10) {
    message.error('最多只能上传10个文件！');
    return false;
  }

  // // 检查文件大小（5MB）
  const isLessThan5M = file.size / 1024 / 1024 < 5;
  if (!isLessThan5M) {
    message.error('文件大小不能超过5MB！');
    return false;
  }

  return true;
};

// 处理普通附件上传
// 处理普通附件上传
const handleChange = (info) => {
  console.log('当前上传的文件:', info.file);
  console.log('所有文件列表:', info.fileList);

  // 更新上传状态
  uploading.value = info.file.status === 'uploading';

  // 保持原始fileList与上传组件同步
  fileList.value = info.fileList;

  // 更新显示用的文件列表
  if (info.file.status === 'done') {
    if (fileViewList.value.length > 0) {
      let fileShowList = info.fileList
        .filter(file => file.status === 'done') // 只保留上传完成的文件
        .map(file => ({
          fileId: file.response.data.fileId,
          fileUrl: file.response.data.fileUrl,
          fileName: file.name,
          uid: file.uid,
          status: file.status
        }))
        .filter(newFile =>
          !fileViewList.value.some(existingFile =>
            existingFile.uid === newFile.uid || // 通过 uid 判断重复
            existingFile.fileId === newFile.fileId // 或通过 fileId 判断重复
          )
        );

      // 合并并更新列表
      fileViewList.value = [...fileViewList.value, ...fileShowList];

    } else {
      fileViewList.value = info.fileList
        .filter(file => file.status === 'done')  // 只保留上传完成的文件
        .map(file => ({
          fileId: file.response.data.fileId, fileUrl: file.response.data.fileUrl,
          fileName: file.name,
          uid: file.uid,
          status: file.status
        }));
    }
    // 上传成功提示
    message.success(`${info.file.name} 上传成功`);
  } else if (info.file.status === 'error') {
    // 处理单个文件的状态提示
    message.error(`${info.file.name} 上传失败`);
  }
};
</script>

<style lang="less" scoped>
.ant-modal-body{
  height: 95vh;
  overflow-y: auto;
}
.approval-record-btn {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  z-index: 100;

  .bg-image {
    display: block;
    max-width: 100%;
    height: auto;
    object-fit: contain;
    background-color: transparent;
  }

  .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .icon {
      width: 15px;
      height: 15px;
      margin-bottom: 4px;
    }

    .text {
      writing-mode: vertical-lr;
      font-size: 14px;
      color: #333;
      letter-spacing: 2px;
    }
  }
}

.equipment-acceptance {
  width: 100%;
  max-width: 1920px; // 最大宽度限制
  height: calc(100vh - 200px);
  overflow: auto;
  margin: 0 auto; // 居中
  //padding: 24px 9.06vw 24px 8.18vw; // 转换为vw: 174/1920*100=9.06, 157/1920*100=8.18
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);

  .form-title {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 24px;
  }

  .section {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;

      .blue-bar {
        width: 3px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
      }
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: clamp(16px, 2vw, 24px);
    margin-bottom: 24px;
    justify-content: space-between;

    .form-item {
      display: flex;
      flex-direction: column;
      min-width: 300px; // 最小宽度
      max-width: 300px; // 最大宽度

      .label {
        height: 32px;
        line-height: 32px;
        margin-bottom: 8px;
        color: #0C0B0B;

        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 4px;
        }
      }

      .value {
        width: 100%;
        height: 32px;
        background: rgba(255, 255, 255, 0.6);
        box-sizing: border-box;
        /* -line-列表 */
        border: 0.5px solid rgba(30, 41, 64, 0.08);

        :deep(.ant-input),
        :deep(.ant-select),
        :deep(.ant-picker),
        :deep(.ant-input-number) {
          width: 100%;
          height: 32px;
        }

        :deep(.ant-input-number-input) {
          height: 16px;
          line-height: 16px;
        }

        :deep(.ant-select-selector) {
          height: 32px !important;

          .ant-select-selection-item {
            line-height: 32px;
          }
        }

        :deep(.ant-picker) {
          .ant-picker-input {
            height: 16px;
            line-height: 16px;

            input {
              height: 16px;
              line-height: 16px;
            }
          }
        }
      }
    }
  }

  .acceptance-no {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    max-width: 300px; // 与其他表单项保持一致的最大宽度

    :deep(.first-input) {
      width: 65% !important; // 验资前面的输入框占65%
    }

    :deep(.second-input) {
      width: 35% !important; // 号前面的输入框占35%
    }

    .separator {
      color: #666;
      white-space: nowrap;
    }
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .equipment-acceptance {
    padding: 24px 6vw;

    .form-grid {
      gap: 16px;
    }
  }
}

@media screen and (max-width: 1366px) {
  .equipment-acceptance {
    padding: 24px 4vw;

    .form-grid {
      grid-template-columns: repeat(2, minmax(300px, 1fr));
    }
  }
}

@media screen and (max-width: 1024px) {
  .equipment-acceptance {
    padding: 24px 2vw;

    .form-grid {
      grid-template-columns: repeat(1, 1fr);

      .form-item {
        max-width: none;
      }
    }
  }
}

.percentage-input {
  position: relative;
  display: flex;
  align-items: center;

  .ant-input {
    width: 100%;
  }

  .percentage-symbol {
    position: absolute;
    right: 12px;
    color: #666;
  }
}

.section {
  .a-table {
    margin-top: 16px;
  }
}

.upload-section {
  .upload-header {
    display: flex;
    align-items: center;
    gap: 12px;

    .upload-button {
      display: inline-flex;
      align-items: center;
      padding: 4px 15px;
      background: #0085FF;
      border-radius: 2px;
      color: white;
      cursor: pointer;
      font-size: 14px;

      .anticon {
        margin-right: 4px;
      }
    }

    .upload-tip {
      color: #999;
      font-size: 12px;
    }
  }

  .file-list {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    //display: grid;
    //grid-template-columns: repeat(3, 0.3fr);
    //gap: 16px;

    .file-item {
      display: flex;
      align-items: center;
      padding: 8px;
      // background: #F7F9FC;
      border-radius: 2px;
      width: 32%;

      .file-link{
        word-break:break-all;
      }

      .file-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #2D5CF6;
      }

      .file-name {
        flex: 1;
        font-size: 14px;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-actions {
        margin-left: 8px;

        .delete-icon {
          color: #999;
          cursor: pointer;

          &:hover {
            color: #FF4B4B;
          }
        }

        .anticon-sync {
          color: #2D5CF6;
        }
      }
    }
  }
}

.tech-file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;

  .file-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      color: #2D5CF6;
    }

    .file-name {
      color: #333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .delete-icon {
    color: #999;
    cursor: pointer;

    &:hover {
      color: #ff4d4f;
    }
  }
}

.tech-upload-button {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  color: #666;
  font-size: 14px;

  .anticon {
    margin-right: 4px;
    font-size: 14px;
  }

  &:hover {
    color: #2D5CF6;
    border-color: #2D5CF6;
  }
}

.value {
  :deep(.ant-input-affix-wrapper) {
    .ant-input-suffix {
      color: #666;
    }
  }
}

.bottom-buttons {
  margin-top: 80px;
  margin-bottom: 89rpx;
  bottom: 180px; // 修改为距离底部80px
  left: 0;
  right: 0;
  padding: 0 32px; // 调整左右内边距
  text-align: right;
  z-index: 100;

  .reject-btn {
    height: 32px;
    padding: 0 16px;
    border: 1px solid #f30d05;
    color: #f30d05;
    background: transparent !important;
    border-radius: 4px;

    &:hover {
      border-color: #409EFF;
      color: #409EFF;
    }
  }

  .submit-btn {
    height: 32px;
    padding: 0 16px;
    margin-left: 27px; // 调整按钮之间的间距
    background: #409EFF;
    border-radius: 4px;
    border: none;

    &:hover {
      background: #66B1FF;
    }
  }
}

.equipment-info-table {
  border-radius: 4px;
  overflow: visible;
  background: #fff;
  border: 0.5px solid rgba(30, 41, 64, 0.08);
  ; // 加深整体边框颜色

  .info-row {
    display: flex;

    &:last-child {
      border-bottom: none;
    }

    .info-cell {
      flex: 1;
      display: flex;
      min-height: 44px;
      border-bottom: 0.5px solid #d9d9d9; // 加深底部边框颜色

      .label {
        width: 120px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        background: #E2F0FF;
        color: #333;
        font-size: 14px;
        padding: 0 16px;
        box-sizing: border-box;
        border-bottom: none; // 移除label的单独底边
        text-align: right; // 20250723 王文胜
      }

      .value {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 0 16px;
        background: #fff;
        color: #333;
        font-size: 14px;
        border-bottom: none; // 移除value的单独底边
      }

      border-right: 1px solid #d9d9d9; // 加深右侧边框颜色

      &:last-child {
        border-right: none;
      }
    }

    &:last-child {
      .info-cell {
        border-bottom: none;
      }
    }
  }

  // 技术资料样式
  .tech-files {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    .preview-btn,
    .view-btn {
      display: inline-flex;
      align-items: center;
      color: #1890FF;
      cursor: pointer;
      margin-right: 16px;

      .anticon {
        margin-right: 4px;
      }

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.section {
  :deep(.ant-table-thead) {
    >tr>th {
      background: #E2F0FF;
      box-sizing: border-box;
      border: 0.5px solid rgba(30, 41, 64, 0.08);
      padding: 10px 20px !important;

      // 重置 hover 状态的背景色
      &:hover {
        background: #E2F0FF !important;
      }
    }
  }

  // 确保表格边框样式一致
  :deep(.ant-table) {
    border: 0.5px solid rgba(30, 41, 64, 0.08);
  }

  :deep(.ant-table-cell) {
    border: 0.5px solid rgba(30, 41, 64, 0.08) !important;
  }
}

.approval-description {
  padding: 16px 0;
  background: transparent;

  .ant-textarea {
    width: 100%;
    border: 1px solid #E2F0FF;
    border-radius: 4px;
    box-sizing: border-box;

    &:hover,
    &:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }


}

  .value {
  flex: 1;
  min-width: 0; /* 确保内容可以正确收缩 */
  max-width: calc(100% - 120px); /* 限制最大宽度 */
}
.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>
