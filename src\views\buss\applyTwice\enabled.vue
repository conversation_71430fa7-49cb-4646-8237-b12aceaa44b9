<template>
  <div class="ele-body">
    <div class="equipment-acceptance">

      <div class="form-title">设备启用申请单</div>

      <!-- 申请信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          申请信息
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">申请人</div>
            <div class="value">
              {{ bussTransferForm.applyUserStr }}
            </div>

          </div>
          <div class="form-item">
            <div class="label">申请单位</div>
            <div class="value">
              {{ bussTransferForm.applyOrgStr }}
            </div>

          </div>

          <div class="form-item">
            <div class="label">申请日期</div>
            <div class="value">
              {{ bussTransferForm.applyDate }}
            </div>
          </div>

        </div>

        <div class="form-grid">

          <div class="form-item" data-field="applyTitle">
            <div class="label required">业务标题</div>
            <a-input v-model:value="bussTransferForm.applyTitle" placeholder="请输入业务标题" maxlength="16"/>
          </div>
        </div>
      </div>

      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          启用信息
        </div>


        <div class="form-grid">
          <div class="form-item" data-field="propertyOrg">
            <div class="label required">产权单位</div>
            <div class="value">
              <a-select v-model:value="bussTransferForm.propertyOrg" placeholder="请选择产权单位" style="width: 100%;"
                @change="handlePropertyOrgChange">
                <a-select-option v-for="item in propertyOrgOptions" :key="item.value" :value="item.value" >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>
          <div class="form-item">
            <div class="label">管理单位</div>
            <div class="value">
              {{ bussTransferForm.managementOrgStr }}
            </div>
          </div>

          <div class="form-item" data-field="useOrg">
            <div class="label required">使用单位</div>
            <div class="value">
              <!-- {{ bussTransferForm.useOrgStr }} -->
              <a-select v-model:value="bussTransferForm.useOrg" placeholder="请选择使用单位" style="width: 100%;"
                @change="handleUseOrgChange">
                <a-select-option v-for="item in useOrgOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item" data-field="planActivationDate">
            <div class="label required">预计启用日期</div>
            <div class="value">
              <a-date-picker v-model:value="bussTransferForm.planActivationDate" placeholder="请选择预计启用日期"
                style="width: 100%" value-format="YYYY-MM-DD" :class="['date-picker']" />
            </div>
          </div>
        </div>
      </div>


      <!-- 设备信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          设备明细
        </div>

        <div data-field="tables">
          <div>
            <a-button type="link" style="margin-bottom: 5px; padding-left: 0; display: flex; align-items: center;"
              @click="goTodeviceList">
              <i class="iconfont icon-equipment" style="margin-right: 6px;height: 100%;"></i>
              在籍设备选择
            </a-button>
          </div>
          <a-table :columns="columns" :data-source="formData.bussEquipmentProcessTrackingList" bordered
            :pagination="false" :scroll="{ x: 'max-content' }" class="custom-table" ref="myTable">

            <template #bodyCell="{ column, index, record }">
              <template v-if="column.dataIndex === 'code'">
                <div class="form-item" style="margin-bottom: 0;">
                  <a-input v-model:value="record.code" placeholder="请输入设备编码" :style="{ width: '240px' }"
                    @keyup.enter="handleSearch(record)">
                    <template #suffix>
                      <search-outlined style="cursor: pointer;" class="search-icon" @click="handleSearch(record)" />
                    </template>
                  </a-input>
                </div>
              </template>
              <!-- <template v-if="column.dataIndex === 'activationManagerChange'">
                <div class="form-item" style="margin-bottom: 0; width: 150px;">
                  <a-select v-model:value="record.activationManagerChange" placeholder="请选择变更管理状态" style="width: 100%;"
                    :dropdownMatchSelectWidth="false" disabled>
                    <a-select-option v-for="item in equConditionOptions" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </div>
              </template> -->
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" danger @click="handleDelete(record)" style="padding: 0;">
                    <i class="iconfont icon-delete" style="margin-right: 6px;"></i>
                  </a-button>
                </a-space>
              </template>
            </template>
            <template #footer>
              <a @click="addAttachment" style="width: 100%; display: inline-block; text-align: center;">
                <plus-outlined />
                新增一行
              </a>
            </template>
          </a-table>
        </div>
      </div>


      <!-- 底部按钮 -->
      <div class="bottom-buttons">
        <!-- <a-button @click="handleSave" :loading="saving" class="save-btn">保存</a-button> -->
        <a-button type="primary" @click="handleSubmit" :loading="submitting" class="submit-btn">提交</a-button>
      </div>
    </div>
    <deviceList-edit v-model:visible="showEdit" @getList="getList" :data="formData.bussEquipmentProcessTrackingList"
      @done="reload" v-if="showEdit" :queryParam="queryParam"></deviceList-edit>
  </div>
</template>
<script>
export default {
  name: 'BussApplyTwiceEnabledTwice',
}
</script>
<script setup>

import { ref, onMounted, nextTick, computed } from 'vue';
import { message } from 'ant-design-vue';
import { enabledApi } from '@/api/dynamic/enabledApi';
import { FileUploadUrl } from '@/api/system/operation/FileApi';
import { getToken } from '@/utils/token-util';
import { API_BASE_PREFIX } from '@/config/setting';
import DeviceListEdit from '@/views/dynamic/deviceList/deviceList-edit.vue';
import { useUserStore } from '@/store/modules/user';
import { OrganizationApi } from '@/api/system/organization/OrganizationApi';
import { useRouter,useRoute } from 'vue-router';
import { EnumApi } from '@/api/common/enum';
import dayjs from 'dayjs';
const userStore = useUserStore();
const router = useRouter();
const loading = ref(false);
const saving = ref(false);
const submitting = ref(false);



// 业务标题和关联投资计划的独立字段
const investmentPlan = ref('');
const isTransfer = ref('1'); // 添加是否转入设备字段，默认为'1'（否）

// 上传相关配置
const uploadUrl = `${API_BASE_PREFIX}${FileUploadUrl}?secretFlag=0`;
const headers = {
  Authorization: getToken()
};

const columns = [
  {
    title: '序号',
    width: 80,
    customRender: ({ text, record, index }) => {
      return `${index + 1}`;
    }
  },
  { title: '设备编号', dataIndex: 'code', width: 120, },
  { title: '财务卡片编号', dataIndex: 'financialNumber', width: 130, },
  { title: '设备名称', dataIndex: 'equNameStr', width: 200, },
  { title: '规格型号', dataIndex: 'equModelStr', width: 140, },
  { title: '型号备注', dataIndex: 'equModelInfo', width: 250, },
  { title: '管理单位', dataIndex: 'managementOrgStr', width: 200 },
  { title: '存放地点', dataIndex: 'storageLocationStr', width: 250, },
  { title: '使用单位', dataIndex: 'useOrgStr', width: 200, },
  { title: '财务原值', dataIndex: 'financialOriginalValue', width: 120 },
  { title: '净值', dataIndex: 'netWorth', width: 120 },
  { title: '管理状态', dataIndex: 'managementStatusStr', width: 100 },
  { title: '设备状态', dataIndex: 'equConditionStr', width: 100 },
  { title: '生产厂家', dataIndex: 'manufacturer', width: 150 },
  { title: '出厂日期', dataIndex: 'productionDate', width: 120 },
  { title: '出厂编号', dataIndex: 'factoryNumber', width: 120 },
  { title: '设备类别', dataIndex: 'equTypeStr', width: 200 },
  { title: '设备种类', dataIndex: 'equSubTypeStr', width: 200 },
  { title: '设备性质', dataIndex: 'equNatureStr', width: 120 },
  { title: '固定资产分类', dataIndex: 'fixedAssetsStr', width: 160 },
  { title: '产权单位', dataIndex: 'propertyOrgStr', width: 120 },
  { title: '功率kw', dataIndex: 'power', width: 100 },
  { title: '设备型号编码', dataIndex: 'modelCode', width: 160 },
  { title: '购置年度', dataIndex: 'purchaseYear', width: 100 },
  {
    title: '变更管理状态',
    dataIndex: 'activationManagerChangeStr',
    width: 160,
    // customRender: () => '内部租赁', // 固定显示"内部租赁"
    fixed: 'right'
  },
  // 右侧固定列
  { title: '操作', key: 'action', width: 80, fixed: 'right' }
]

// 是否显示编辑弹窗
const showEdit = ref(false)
// 当前编辑数据
const current = ref(null)
// 文件列表相关
const KeyId = ref()
const getProcessDefinitionByKey = async () => {
  try {
    const id = await enabledApi.getProcessDefinitionByKey({ key: 'equipment_activation' });
    KeyId.value = id.data.id
  } catch (error) {

  }
}
// 验证表单数据
const validateForm = () => {
  // 按顺序定义需要验证的字段
  const fieldsToValidate = [
    { field: 'applyTitle', label: '业务标题', type: 'input' },
    { field: 'propertyOrg', label: '产权单位', type: 'select' },
    { field: 'useOrg', label: '使用单位', type: 'select' },
    { field: 'planActivationDate', label: '预计启用日期', type: 'input' },
  ];

  // 依次验证每个字段
  for (const { field, label, type } of fieldsToValidate) {
    if (!bussTransferForm.value[field]) {
      message.error(`请${type === 'select' ? '选择' : type === 'date' ? '选择' : '输入'}${label}`);

      const formItem = document.querySelector(`[data-field="${field}"]`);
      if (formItem) {
        // 滚动到可视区域，并确保元素在视图中间
        formItem.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
        const tableBody = document.querySelector('.ant-table-body');
        if (tableBody) {
          // 方式一：直接滚动到最右边
          tableBody.scrollTo({
            left: tableBody.scrollWidth,  // scrollWidth 是内容的总宽度
            behavior: 'smooth'
          });
        }
        // 使用 nextTick 确保 DOM 更新后再执行点击操作
        nextTick(() => {
          setTimeout(() => {
            switch (type) {
              case 'input': {
                const input = formItem.querySelector('input');
                input?.focus();
                break;
              }
              case 'select': {
                const select = formItem.querySelector('.ant-select-selector');
                select?.click();
                break;
              }
              case 'date': {
                const datePicker = formItem.querySelector('.date-picker');
                if (datePicker) {
                  // 先聚焦
                  const input = datePicker.querySelector('input');
                  input?.focus();
                  // 然后触发点击以打开日期选择面板
                  setTimeout(() => {
                    datePicker.click();
                  }, 100);
                }
                break;
              }
            }
          }, 500); // 等待滚动完成后再聚焦
        });
      }
      return false;
    }
  }
  return true;
};

const bussTransferForm = ref({
  applyUserStr: '',
  applyOrgStr: '',
  applyDate: '',
  applyTitle: '',
  handledUser: '',
  handledOrg: '',
  applyOrg: '',
  applyUser: '',
  remark: '',
  applyTitle: "",
  useProjectDep: "",
  managementOrg: '', // 管理单位ID
  managementOrgStr: '', // 管理单位名称
  propertyOrg: null, // 产权单位ID
  propertyOrgStr: '', // 产权单位名称
  useOrg: null, // 使用单位ID
  useOrgStr: null, // 使用单位名称
  planActivationDate: '', // 预计启用日期
});
const bussWorksheet = ref({
  name: '',
  id: ""
});

// 构建保存数据的方法
const buildRequestDataSave = () => {
  const requestData = {

    bussWorksheet: {
      ...bussWorksheet.value,
      name: bussTransferForm.value.applyTitle
    },
    bussTransferForm: {
      ...bussTransferForm.value,
      applyTitle : bussTransferForm.value.applyTitle
    },

    bussEquipmentProcessTrackingList: formData.value.bussEquipmentProcessTrackingList,

  };

  console.log('提交的数据：', requestData);
  return requestData;
};
// 构建提交数据的方法
const buildRequestData = () => {
  const requestData = {
    formDatas: {
      bussWorksheet: {
        ...bussWorksheet.value,
        name: bussTransferForm.value.applyTitle
      },
      bussTransferForm: {
        ...bussTransferForm.value,
        applyTitle : bussTransferForm.value.applyTitle
      },

      bussEquipmentProcessTrackingList: formData.value.bussEquipmentProcessTrackingList,
    }
  };

  if (datalistAll.value.bussWorksheet) {
    requestData.formDatas.bussWorksheet.id = datalistAll.value.bussWorksheet.id
    requestData.formDatas.bussTransferForm.id = datalistAll.value.bussTransferForm.id
    requestData.formDatas.bussTransferForm.worksheetId = datalistAll.value.bussTransferForm.worksheetId
  }

  console.log('提交的数据：', requestData);
  return requestData;
};
// 保存方法
const handleSave = async () => {
  try {
    saving.value = true;
    let params = {};
    params = buildRequestDataSave()

    console.log('params--', params)
    let res = await enabledApi.save(params);
    console.log('res--', res.success)
    if (res.success) {
       // 完全覆盖bussTransferForm数据
        if (res.data.bussTransferForm) {
          bussTransferForm.value = res.data.bussTransferForm;
        }

        // 完全覆盖bussWorksheet数据
        if (res.data.bussWorksheet) {
          bussWorksheet.value = res.data.bussWorksheet;
        }

      message.success('保存成功');
      console.log('res.data', res.data)
    } else {
      message.error('保存失败');
    }
  } catch (error) {
    message.error(error.message.split(",")[0]);
  } finally {
    saving.value = false;
  }
};

// 提交方法
const handleSubmit = async () => {
  try {
    // 执行验证
    if (!validateForm()) {
      console.log('validateForm()', !validateForm())
      return; // 验证不通过直接返回,不执行提交
    }
    if (!formData.value.bussEquipmentProcessTrackingList || formData.value.bussEquipmentProcessTrackingList.length == 0) {
      message.error('请选择设备！')
      const formItem = document.querySelector(`[data-field="tables"]`);
      formItem.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
      return
    }
    submitting.value = true;

    const innerFormData = buildRequestData();

    console.log(innerFormData)
    // 构建最终提交数据结构
    const submitData = {
      processDefinitionId: KeyId.value,
      variables: {
        formData: JSON.stringify(innerFormData)
      }
    };
    console.log('submitData', submitData)
    const res = await enabledApi.start(submitData);

    if (res.success) {
      message.success('提交成功');
      router.push('/dynamic/enabled');
    } else {
      message.error('提交失败');
    }
  } catch (error) {
   message.error(error.message);
  } finally {
    submitting.value = false;
  }
};

const formData = ref({

  bussEquipmentProcessTrackingList: [] // 设备列表
});


// 审批记录相关方法
const showApprovalRecord = () => {
  // 处理审批记录的显示逻辑
  console.log('显示审批记录');
};
const datalistAll = ref({})
const initializeData = async () => {
  loading.value = true;
  try {
     const { data } = await enabledApi.getToDo({procInstanceId: procInstanceId.value.id});
    if (!data) {
      message.error('未找到相关数据');
      return;
    }
    const loginUser = computed(() => userStore.info ?? {});


    const isNewRecord = !data.bussWorksheet &&
      !data.bussTransferForm &&
      !data.bussEquipmentProcessTrackingList;

    if (isNewRecord) {
      // 新记录时只初始化申请相关信息
      bussTransferForm.value = {
        applyDate: dayjs().format('YYYY-MM-DD'),
        managementOrg: loginUser.value.organizationId,
        managementOrgStr: loginUser.value.organizationName,
        propertyOrg: null,
        propertyOrgStr: null,
        applyUser: loginUser.value.userId,
        applyUserStr: loginUser.value.realName,
        applyOrg: loginUser.value.organizationId,
        applyOrgStr: loginUser.value.organizationName,
      };
      datalistAll.value = data
    } else {
      // 有数据时，直接使用后端返回的数据
      bussWorksheet.value = data.bussWorksheet
      bussWorksheet.value.id = ""
        bussWorksheet.value.procInstanceId=""
        bussWorksheet.value.isResubmit = true
      formData.value = {
        bussEquipmentProcessTrackingList: data.bussEquipmentProcessTrackingList
      };

      bussTransferForm.value = data.bussTransferForm
      bussTransferForm.value.id= ""
      bussTransferForm.value.applyDate = getdate();  
    }

    getUseOrgList();
  } catch (error) {
    // message.error('获取数据失败：' + error.message);
  } finally {
    loading.value = false;
  }
};

const getdate = () => {
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth() + 1; // 月份从0开始，需要加1
  const day = currentDate.getDate();
  console.log(year + "-" + month + "-" + day);
  return `${year}-${month}-${day}`
}
// {managementStatus:'5',equCondition:'1',propertyOrg:userStore.info.organizationId}
const queryParam = ref()
const goTodeviceList = async (row) => {
  console.log('bussTransferForm.value',bussTransferForm.value);

  if (!bussTransferForm.value.propertyOrg) {
     message.error('请选择产权单位！')
     return
  }
  if (!bussTransferForm.value.useOrg) {
     message.error('请选择使用单位！')
     return
  }
  queryParam.value = { managementStatus: '5', equCondition: '1', propertyOrg:bussTransferForm.value.propertyOrg,managementOrg:bussTransferForm.value.managementOrg,useOrg:bussTransferForm.value.useOrg }
  current.value = row;
  showEdit.value = true;
};


const getList = (obj) => {
  if (obj) {
    // 为每条记录设置固定的管理状态值
    const updatedList = obj.map(item => ({
      ...item,
      activationManagerChangeStr: '内部租赁', // 固定值为3，对应"内部租赁"
      activationManagerChange:3
    }));

    formData.value.bussEquipmentProcessTrackingList = updatedList;
  }
}

const handleDelete = (res) => {
  if (res.uuId) {
    formData.value.bussEquipmentProcessTrackingList = formData.value.bussEquipmentProcessTrackingList.filter(item => item.uuId !== res.uuId);
  }
  if (res.equId) {
    formData.value.bussEquipmentProcessTrackingList = formData.value.bussEquipmentProcessTrackingList.filter(item => item.equId !== res.equId);
  }
}
const orgList = ref()
const equConditionOptions = ref([
  { value: 3, label: '内部租赁' }
]);
// 获取组织机构树
// const getOrgList = async () => {
//   try {
//     const res = await OrganizationApi.organizationTreeList();
//     orgList.value = res.data;
//   } catch (error) {
//     message.error('获取组织机构失败');
//   }
// };
// getOrgList()

const initEnumData = async () => {
  try {
    // 获取设备状态枚举
    const equConditionData = await EnumApi.getEnumList({ enumName: 'EquConditionEnum' });
    // equConditionOptions.value = equConditionData.data;

  } catch (error) {
    message.error('获取枚举数据失败');
  }
};

// 产权单位选项
const propertyOrgOptions = ref([]);
// 使用单位选项
const useOrgOptions = ref([]);

// 获取产权单位列表
const getPropertyOrgList = async () => {
  try {
    // 调用API获取产权单位列表，传入pId=-1
    const res = await EnumApi.getPropertyOrgList({workSheetType:'equipment_activation'});
    propertyOrgOptions.value = res.data
  } catch (error) {
    message.error('获取产权单位列表失败');
  }
};

// 获取使用单位列表
const getUseOrgList = async () => {
  try {
    if (!bussTransferForm.value.applyOrg) {
      console.warn('申请单位ID不存在，无法获取使用单位列表');
      return;
    }

    // 调用API获取使用单位列表，传入申请单位的id作为pId
    const res = await EnumApi.getUseOrgList({ pId: bussTransferForm.value.applyOrg,workSheetTypeAndStep:'equipment_activation_1_1',isLibrary:false,isVirtual:false });
    useOrgOptions.value = res.data
  } catch (error) {
    message.error('获取使用单位列表失败');
  }
};

// 处理产权单位变更
const handlePropertyOrgChange = (value) => {
  formData.value.bussEquipmentProcessTrackingList = [{
          code: '',
          uuId: SomeTools.guid()
        }]
  // 找到选中的产权单位
  const selectedOrg = propertyOrgOptions.value.find(item => item.value === value);
  if (selectedOrg) {
    // 更新产权单位名称
    formData.value.propertyOrgStr = selectedOrg.label;
  }
};

// 处理使用单位变更
const handleUseOrgChange = (value) => {
  // 找到选中的使用单位
  formData.value.bussEquipmentProcessTrackingList = [{
          code: '',
          uuId: SomeTools.guid()
        }]
  const selectedOrg = useOrgOptions.value.find(item => item.value === value);
  if (selectedOrg) {
    // 更新使用单位名称
    formData.value.useOrgStr = selectedOrg.label;
  }
};

import SomeTools from '@/utils/someTools.js'

// 添加新附件
const addAttachment = () => {
  if (!bussTransferForm.value.propertyOrg) {
     message.error('请选择产权单位！')
     return
  }
  if (!bussTransferForm.value.useOrg) {
     message.error('请选择使用单位！')
     return
  }
  formData.value.bussEquipmentProcessTrackingList.push({
    code: '',
    uuId: SomeTools.guid()
  });
};

const handleSearch = async (record) => {
  const uniqueSampleTypeIds = new Set(formData.value.bussEquipmentProcessTrackingList.map(item => item.code));
  const hasDuplicateSampleTypeId = formData.value.bussEquipmentProcessTrackingList.length !== uniqueSampleTypeIds.size;
  if(hasDuplicateSampleTypeId){
    record.code = ''
     message.error('请勿重复输入设备编号！')
     return
  }

  queryParam.value = {searchText:record.code, managementStatus: '5', equCondition: '1', propertyOrg:bussTransferForm.value.propertyOrg,managementOrg:bussTransferForm.value.managementOrg,useOrg:bussTransferForm.value.useOrg }
  try {
    if (record.code) {
      let obj = await UnitApi.getRegistered(queryParam.value)
      obj.equId = obj.id
      obj.activationManagerChangeStr = '内部租赁' // 固定值为3，对应"内部租赁"
      obj.activationManagerChange = 3 // 固定值为3
      delete obj.id
      if (record.uuId) {
        for (let i = 0; i < formData.value.bussEquipmentProcessTrackingList.length; i++) {
          if (formData.value.bussEquipmentProcessTrackingList[i].uuId == record.uuId) {
            formData.value.bussEquipmentProcessTrackingList[i] = obj
          }
        }
      }

      if (record.equId) {
        for (let i = 0; i < formData.value.bussEquipmentProcessTrackingList.length; i++) {
          if (formData.value.bussEquipmentProcessTrackingList[i].equId == record.equId) {
            formData.value.bussEquipmentProcessTrackingList[i] = obj
          }
        }
      }
    }

  } catch (e) {
    console.log(e);

  }

}


const procInstanceId = ref({
  id: ''
});
const route = useRoute();
onMounted(async () => {
  if (route.query?.procInsId) {
    procInstanceId.value.id = route.query.procInsId;
    sessionStorage.setItem('equipmentAcceptanceProcId', procInstanceId.value.id);
  } else if (sessionStorage.getItem('equipmentAcceptanceProcId')) {
    procInstanceId.value.id = sessionStorage.getItem('equipmentAcceptanceProcId');
  }
  if (!procInstanceId.value.id) {
    message.error('流程定义ID未找到，请联系管理员');
    return;
  }
  initializeData();
  getProcessDefinitionByKey();

  initEnumData();
  getPropertyOrgList();
  getUseOrgList();
});
</script>

<style lang="less" scoped>
.custom-table {
  margin-top: 16px;

  :deep(.ant-table) {

    // 提高固定列的层级
    .ant-table-fixed-left,
    .ant-table-fixed-right {
      background: #fff;
      z-index: 3; // 增加层级
    }

    .ant-table-cell:not(.ant-table-cell-fix-right-first) {
      white-space: nowrap !important; // 强制不换行
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      line-height: 1 !important;
      font-size: 14px !important;

      >span,
      >div {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }
    }

    // 大屏幕样式（默认）
    @media screen and (min-width: 1920px) {
      .ant-table-cell {
        padding: 20px 20px !important;
        height: 60px !important;
      }

      .ant-table-row {
        height: 60px !important;
      }
    }

    // 中等屏幕样式
    @media screen and (min-width: 1366px) and (max-width: 1919px) {
      .ant-table-cell {
        padding: 10px 20px !important;
        height: 40px !important;
      }

      .ant-table-row {
        height: 40px !important;
      }
    }

    // 小屏幕样式
    @media screen and (max-width: 1365px) {
      .ant-table-cell {
        padding: 4px 8px !important;
        height: 32px !important;
      }

      .ant-table-row {
        height: 32px !important;
      }
    }

    // 调整固定列单元格样式
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      z-index: 3 !important; // 增加层级
      background: #ECF4FE !important;
    }

    // 调整表头固定列样式
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right {
        z-index: 4 !important; // 确保表头在最上层
        background: #DAECFF !important;
      }
    }

    // 优化阴影效果
    .ant-table-fixed-right::before,
    .ant-table-fixed-left::before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      width: 10px;
      pointer-events: none;
      z-index: 2; // 阴影层级低于固定列
      transition: box-shadow .3s;
    }

    .ant-table-fixed-left::before {
      right: 0;
      box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
    }

    .ant-table-fixed-right::before {
      left: 0;
      box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
    }

    // 设置表格内容的层级
    .ant-table-content {
      z-index: 1;
    }

    // 确保滚动区域正确显示
    .ant-table-body {
      overflow-x: auto !important;
      overflow-y: auto !important;
    }

    // 固定列不换行
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right:not(.ant-table-cell-fix-right-first) {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .ant-table-row {
      height: 24px !important;
    }

    // 表头固定列不换行
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right:not(.ant-table-cell-fix-right-first) {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.approval-record-btn {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  z-index: 100;

  .bg-image {
    display: block;
    max-width: 100%;
    height: auto;
    object-fit: contain;
    background-color: transparent;
  }

  .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .icon {
      width: 15px;
      height: 15px;
      margin-bottom: 4px;
    }

    .text {
      writing-mode: vertical-lr;
      font-size: 14px;
      color: #333;
      letter-spacing: 2px;
    }
  }
}

.equipment-acceptance {
  width: 100%;
  max-width: 1920px; // 最大宽度限制
  height: calc(100vh - 100px);
  overflow: auto;
  margin: 0 auto; // 居中
  padding: 24px 9.06vw 24px 8.18vw; // 转换为vw: 174/1920*100=9.06, 157/1920*100=8.18
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);

  .form-title {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 24px;
  }

  .section {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;

      .blue-bar {
        width: 3px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
      }
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: clamp(16px, 2vw, 24px);
    margin-bottom: 24px;
    justify-content: space-between;

    .form-item {
      display: flex;
      flex-direction: column;
      min-width: 300px; // 最小宽度
      max-width: 300px; // 最大宽度

      .label {
        height: 32px;
        line-height: 32px;
        margin-bottom: 8px;
        color: #0C0B0B;

        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 4px;
        }
      }

      .value {
        width: 100%;
        height: 32px;
        color: #666;

        :deep(.ant-input),
        :deep(.ant-select),
        :deep(.ant-picker),
        :deep(.ant-input-number) {
          width: 100%;
          height: 32px;
        }

        :deep(.ant-input-number-input) {
          height: 16px;
          line-height: 16px;
        }

        :deep(.ant-select-selector) {
          height: 32px !important;

          .ant-select-selection-item {
            line-height: 32px;
          }
        }

        :deep(.ant-picker) {
          .ant-picker-input {
            height: 16px;
            line-height: 16px;

            input {
              height: 16px;
              line-height: 16px;
            }
          }
        }
      }
    }
  }

  .acceptance-no {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    max-width: 300px; // 与其他表单项保持一致的最大宽度

    :deep(.first-input) {
      width: 65% !important; // 验资前面的输入框占65%
    }

    :deep(.second-input) {
      width: 35% !important; // 号前面的输入框占35%
    }

    .separator {
      color: #666;
      white-space: nowrap;
    }
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .equipment-acceptance {
    padding: 24px 6vw;

    .form-grid {
      gap: 16px;
    }
  }
}

@media screen and (max-width: 1366px) {
  .equipment-acceptance {
    padding: 24px 4vw;

    .form-grid {
      grid-template-columns: repeat(2, minmax(300px, 1fr));
    }
  }
}

@media screen and (max-width: 1024px) {
  .equipment-acceptance {
    padding: 24px 2vw;

    .form-grid {
      grid-template-columns: repeat(1, 1fr);

      .form-item {
        max-width: none;
      }
    }
  }
}

.percentage-input {
  position: relative;
  display: flex;
  align-items: center;

  .ant-input {
    width: 100%;
  }

  .percentage-symbol {
    position: absolute;
    right: 12px;
    color: #666;
  }
}

.attachment-table {
  width: 89%;
  border: 1px solid #e8e8e8;
  border-radius: 2px;


  .table-header,
  .table-row,
  .add-row {
    // 添加add-row到统一高度设置中
    display: flex;
    padding: 12px 8px;

    border-bottom: 1px solid #e8e8e8;
    align-items: center;
    gap: 12px;
    min-height: 56px; // 统一设置最小高度
  }

  .table-header {
    font-weight: 500;
  }

  .table-row:last-child {
    border-bottom: none;
  }

  // 使用百分比和flex布局
  .col-serial {
    width: 5%; // 序号列较窄
    min-width: 40px;
  }

  .col-name {
    width: 25%; // 名称及型号需要较大空间
    min-width: 180px;
  }

  .col-unit {
    width: 10%; // 单位列较窄
    min-width: 80px;
  }

  .col-quantity {
    width: 10%; // 数量列较窄
    min-width: 80px;
  }

  .col-manufacturer {
    width: 20%; // 生产厂家需要适中空间
    min-width: 150px;
  }

  .col-serial-no {
    width: 20%; // 出厂编号需要适中空间
    min-width: 150px;
  }

  .col-action {
    width: 10%; // 操作列较窄
    min-width: 60px;
    text-align: center;

    .delete-btn {
      color: #FF4D4F;
      cursor: pointer;
    }
  }

  :deep(.ant-input),
  :deep(.ant-input-number) {
    width: 100%;
  }


  // 响应式调整
  @media screen and (max-width: 1366px) {
    .col-name {
      width: 22%; // 较小屏幕时稍微压缩名称列
    }

    .col-manufacturer,
    .col-serial-no {
      width: 18%; // 压缩这两列
    }
  }

  @media screen and (max-width: 1024px) {
    overflow-x: auto; // 当屏幕太小时允许横向滚动

    .table-header,
    .table-row {
      min-width: 900px; // 确保在小屏幕上内容不会过度压缩
    }
  }

  .add-row {
    justify-content: center; // 水平居中
    align-items: center; // 垂直居中
    cursor: pointer;
    border-bottom: none; // 最后一行不需要底部边框

    a {
      display: flex;
      align-items: center; // 图标和文字垂直居中
      color: #1890ff;

      .anticon {
        margin-right: 4px;
      }
    }

    &:hover {
      background: #f5f5f5;
    }
  }
}

.upload-section {
  .upload-header {
    display: flex;
    align-items: center;
    gap: 12px;

    .upload-button {
      display: inline-flex;
      align-items: center;
      padding: 4px 15px;
      background: #0085FF;
      border-radius: 2px;
      color: white;
      cursor: pointer;
      font-size: 14px;

      .anticon {
        margin-right: 4px;
      }
    }

    .upload-tip {
      color: #999;
      font-size: 12px;
    }
  }

  .file-list {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    //display: grid;
    //grid-template-columns: repeat(3, 0.3fr);
    //gap: 16px;

    .file-item {
      display: flex;
      align-items: center;
      padding: 8px;
      // background: #F7F9FC;
      border-radius: 2px;
      width: 32%;

      .file-link{
        word-break:break-all;
      }

      .file-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #2D5CF6;
      }

      .file-name {
        flex: 1;
        font-size: 14px;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-actions {
        margin-left: 8px;

        .delete-icon {
          color: #999;
          cursor: pointer;

          &:hover {
            color: #FF4B4B;
          }
        }

        .anticon-sync {
          color: #2D5CF6;
        }
      }
    }
  }
}

.tech-file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;

  .file-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      color: #2D5CF6;
    }

    .file-name {
      color: #333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .delete-icon {
    color: #999;
    cursor: pointer;

    &:hover {
      color: #ff4d4f;
    }
  }
}

.tech-upload-button {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  color: #666;
  font-size: 14px;

  .anticon {
    margin-right: 4px;
    font-size: 14px;
  }

  &:hover {
    color: #2D5CF6;
    border-color: #2D5CF6;
  }
}

.value {
  :deep(.ant-input-affix-wrapper) {
    .ant-input-suffix {
      color: #666;
    }
  }
}

.bottom-buttons {
  margin-top: 150px;
  margin-bottom: 89rpx;
  bottom: 180px; // 修改为距离底部80px
  left: 0;
  right: 0;
  padding: 0 32px; // 调整左右内边距
  text-align: right;
  z-index: 100;

  .save-btn {
    height: 32px;
    padding: 0 16px;
    border: 1px solid #DCDFE6;
    color: #606266;
    background: transparent !important;
    border-radius: 4px;

    &:hover {
      border-color: #409EFF;
      color: #409EFF;
    }
  }

  .submit-btn {
    height: 32px;
    padding: 0 16px;
    margin-left: 27px; // 调整按钮之间的间距
    background: #409EFF;
    border-radius: 4px;
    border: none;

    &:hover {
      background: #66B1FF;
    }
  }
}

.custom-empty {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  min-width: 0;
  min-height: 120px;
  padding-left: 500px;
  img {
    width: 196px;
    height: 180px;
    display: block;
  }
}
.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>
