<template>
  <div class="approval-record">
    <div class="timeline">
      <div v-for="(record, index) in records" :key="index" class="timeline-item">
        <div class="avatar" :class="record.status">
          <span class="avatar-text">{{ record.name.charAt(0) }}</span>
          <img 
            v-if="record.status === 'approved'" 
            src="@/assets/equipment/iconSuccess.png" 
            class="status-icon" 
            alt="已通过"
          />
        </div>
        <div class="content">
          <div class="header">
            <div class="title">
              <span class="name">{{ record.name }}</span>
              <span class="role">({{ record.role }})</span>
            </div>
            <span class="time">{{ record.time }}</span>
          </div>
          <div class="status-text" :class="record.status">{{ record.statusText }}</div>
          <div class="comment" v-if="record.comment">{{ record.comment }}</div>
        </div>
        <div 
          class="timeline-line" 
          v-if="index !== records.length - 1" 
          :class="{
            'dashed': record.status === 'pending' || record.status === 'rejected' || 
                     records.slice(0, index).some(r => r.status === 'pending' || r.status === 'rejected')
          }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// import { defineProps } from 'vue';

defineProps({
  records: {
    type: Array,
    default: () => []
  }
});
</script>

<style lang="less" scoped>
.approval-record {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  padding: 16px;
  box-sizing: border-box;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #E8E8E8;
    border-radius: 3px;
  }

  .timeline {
    position: relative;
  }

  .timeline-item {
    display: flex;
    position: relative;
    padding-bottom: 32px;

    &:last-child {
      padding-bottom: 0;
    }

    .avatar {
      width: 40px;  // 增大头像尺寸
      height: 40px;
      border-radius: 50%;
      background: #1890FF;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      flex-shrink: 0;
      margin-right: 12px;
      font-family: "HarmonyOS Sans SC";

      .avatar-text {
        color: #fff;
        font-size: 20px;  // 增大字体
        font-weight: 500;
      }

      .status-icon {
        position: absolute;
        right: -4px;
        bottom: -4px;
        width: 16px;  // 调整图标大小
        height: 16px;
      }

      &.rejected {
        background: #FF4D4F;
      }

      &.pending {
        background: #FAAD14;
      }
    }

    .content {
      flex: 1;
      min-width: 0;

      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .title {
          .name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
          }

          .role {
            font-size: 14px;
            color: #666;
            margin-left: 4px;
          }
        }

        .time {
          font-size: 12px;
          color: #999;
        }
      }

      .status-text {
        font-size: 14px;
        margin-bottom: 8px;
        color: #666;

        &.approved {
          color: #52C41A;
        }

        &.rejected {
          color: #FF4D4F;
        }

        &.pending {
          color: #FAAD14;
        }
      }

      .comment {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
        word-break: break-all;
      }
    }

    .timeline-line {
      position: absolute;
      left: 20px;  // 调整线的位置
      top: 40px;
      width: 2px;
      height: calc(100% - 40px);
      background: #1890FF;

      &.dashed {
        background: none;
        background-image: linear-gradient(#1890FF 40%, transparent 40%);
        background-size: 2px 6px;
        background-repeat: repeat-y;
      }
    }
  }
}
</style>



