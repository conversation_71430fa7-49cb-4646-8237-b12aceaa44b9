<template>

  <a-form ref="formRef" :model="detailData" :rules="rules" :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
    :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }">
    <a-row :gutter="16">
      <a-col :md="12" :sm="24" :xs="24" style="padding-right: 0px;">
        <div class="rows">
          <div class="rows_1">
            <span class="rows_1_1">
              产权单位
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{  detailData.propertyOrgStr }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              管理单位
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
                {{ detailData.managementOrgStr }}

            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              使用单位
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
             {{ detailData.useOrgStr }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">

            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">

            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">

            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">

            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">

            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">

            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">

            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">

            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">

            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">

            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">

            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">

            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">

            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">

            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">

            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">

            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">

            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">

            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">

            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">

            </span>
          </div>
        </div>
      </a-col>
      <a-col :md="12" :sm="24" :xs="24" style="padding-left: 0px;">
        <div class="rows2">
          <div class="rows2_2">
            <span class="rows2_2_2">
              管理状态
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ detailData.managementStatusStr }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              设备状态
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ detailData.equConditionStr }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              存放地点
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ detailData.storageLocationStr }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>

        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>
      </a-col>
    </a-row>
  </a-form>
</template>

<script setup>
import { defineComponent, reactive, toRefs, ref, watch } from 'vue';
import iconData from 'ele-admin-pro/es/ele-icon-picker/icons';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: '设备详情'
  }
});

const detailData = ref({});
watch(() => props.data, (val) => {
  console.log(val)
  if( val){
    if(val.type == "scrap"){
      detailData.value = val.bussScrapedEquipment;
    }else if(val.type == "disposal"){
      detailData.value = val.bussDisposaledEquipment;
    }else{
      detailData.value = val.bussRegisteredEquipment;
    }
  }
}, { immediate: true });
</script>

<style lang="less" scoped>
.rows {
  display: flex;
  height: 53px;
  align-items: center;
  border: 1px solid rgba(172, 180, 201, 0.2);

  .rows_1 {
    width: 300px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #F1F8FF;
    border-right: 1px solid rgba(172, 180, 201, 0.2);

    .rows_1_1 {
      margin-right: 16px;
    }
  }

  .rows_2 {
    width: 100%;

    .rows_2_2 {
      margin-left: 16px;
    }
  }
}


.rows2 {
  display: flex;
  height: 53px;
  align-items: center;
  border-top: 1px solid rgba(172, 180, 201, 0.2);
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);

  .rows2_2 {
    width: 300px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: rgba(241, 248, 255, 1);
    border-right: 1px solid rgba(172, 180, 201, 0.2);

    .rows2_2_2 {
      margin-right: 16px;
    }
  }

  .rows2_3 {
    width: 100%;
    
    .rows2_3_3 {
      margin-left: 16px;
    }
  }
}

.rows1 {
  border-top: 0;
  border-left: 1px solid rgba(172, 180, 201, 0.2);
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);
}

.rows22{
  border-top: 0;
  border-left: 0;
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);
}
</style>




