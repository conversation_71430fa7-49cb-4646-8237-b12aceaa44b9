<!-- 在籍设备台账 -->
<template>

  <div class="ele-body">
    <div class="reg-body">
      <!-- 顶部按钮组 -->
      <div class="header-tools">
        <div class="left">

          <a-button class="filter-button" style="display: flex;align-items: center;justify-content: space-between;"
            @click="isShowSearch = !isShowSearch">
            <i class="iconfont icon-search" style="margin-right: 6px;"></i>
            筛选
          </a-button>
          <div class="search-input">
            <a-input v-model:value="queryParams.searchText" placeholder="搜索" :style="{ width: '240px' }"
              @keyup.enter="handleSearch2" allow-clear style="margin-right: 10px;" @change="handleChange">
              <template #suffix>
                <search-outlined class="search-icon" @click="handleSearch2" />
              </template>
            </a-input>
            <a-tooltip title="搜索字段包含（设备编号,财务卡片编号, 存放地点,出厂编号,财务原值,净值,生产厂家）">
              <i class="iconfont icon-tooltip" style="margin-right: 6px;color: #0085FF; cursor: pointer;"></i>
            </a-tooltip>
          </div>
          <div>

          </div>
        </div>
        <div class="right">
          <a-button class="tool-button" style="display: flex;align-items: center;justify-content: space-between;"
            @click="handleExport" v-privilege="'equipmentform:registered:export'">
            <i class="iconfont icon-rongqi-copy" style="margin-right: 6px;"></i>
            导出
          </a-button>
          <a-button class="tool-button" style="display: flex;align-items: center;justify-content: space-between;"
            @click="goToEquipmentImport" v-privilege="'equipmentform:registered:import'">
            <i class="iconfont icon-import" style="margin-right: 6px;"></i>
            导入
          </a-button>
          <a-button class="tool-button colorBlue" @click="goToEquipmentAcceptance"
            v-privilege="'equipmentform:registered:create'">
            <template #icon>
              <DesktopOutlined />
            </template>
            设备入账
          </a-button>
          <a-button type="link" class="link-button" @click="goToApplicationRecord"
            v-privilege="'equipmentform:registered:record'">
            申请记录 >
          </a-button>
        </div>
      </div>

      <!-- 搜索工具栏 -->
      <div class="search-form" v-if="isShowSearch">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="设备类型">
              <a-tree-select v-model:value="sblxList" show-search
                :dropdown-style="{ maxHeight: '440px', overflow: 'auto' }" placeholder="请选择" allow-clear multiple
                :show-checked-strategy="SHOW_ALL" :tree-data="equipmentTypeList"
                :field-names="{ label: 'name', value: 'id' }" @select="selectSelect" @change="changeSelect"
                tree-node-filter-prop="name" max-tag-count="responsive" style="width: 100%;">
              </a-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item label="使用单位">
              <a-tree-select v-model:value="useOrg" show-search
                :dropdown-style="{ maxHeight: '440px', overflow: 'auto' }" placeholder="请选择" allow-clear multiple
                :show-checked-strategy="SHOW_ALL" :tree-data="orgList" :field-names="{ label: 'name', value: 'id' }"
                tree-node-filter-prop="name" max-tag-count="responsive" style="width: 100%;"
                :treeExpandedKeys="expandedKeys" @treeExpand="handleTreeExpand">
                <template #title="node">
                  <span @click="(e) => handleTitleClick(e, node)" style="display: inline-block; width: 100%;">
                    {{ node.name }}
                  </span>
                </template>
              </a-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="2" style="display: flex; justify-content: end;">
            <a-button style="width: 100%;" type="primary" class="search-button" @click="handleSearch1">查询</a-button>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="4">
            <a-form-item label="产权单位">
              <a-select v-model:value="queryParams.propertyOrg" placeholder="全部" style="width: 100%;">
                <a-select-option value="">全部</a-select-option>
                <a-select-option v-for="item in cqdwList" :key="item.label" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="管理单位">
              <a-select v-model:value="queryParams.managementOrg" placeholder="全部" style="width: 100%;">
                <a-select-option value="">全部</a-select-option>
                <a-select-option v-for="item in gldwList" :key="item.label" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>

          </a-col>
          <a-col :span="4">
            <a-form-item label="设备状态">
              <a-select v-model:value="queryParams.equCondition" placeholder="全部" style="width: 100%;">
                <a-select-option value="">全部</a-select-option>
                <a-select-option v-for="item in equConditionOptions" :key="item.label" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>

          </a-col>
          <a-col :span="5">
            <a-form-item label="管理状态">
              <a-select v-model:value="queryParams.managementStatus" placeholder="全部" style="width: 100%;">
                <a-select-option value="">全部</a-select-option>
                <a-select-option v-for="item in managementStatusOptions" :key="item.label" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="购置年度">
              <a-range-picker v-model:value="yearAll" picker="year" :placeholder='placeholders'
                :value-format="yearFormat" style="width: 100%;" />
            </a-form-item>
          </a-col>
          <a-col :span="2" style="display: flex; justify-content: end;">
            <a-button style="width: 100%;" class="reset-button" @click="handleReset">重置</a-button>
          </a-col>
        </a-row>
        <!-- <div class="search-row">
          <div class="search-item">
            <span class="label">设备类型:</span>
            <a-tree-select v-model:value="sblxList" show-search
              :dropdown-style="{ maxHeight: '440px', overflow: 'auto' }" placeholder="请选择" allow-clear multiple
              :show-checked-strategy="SHOW_ALL" :tree-data="equipmentTypeList"
              :field-names="{ label: 'name', value: 'id' }" @select="selectSelect" @change="changeSelect"
              tree-node-filter-prop="name" max-tag-count="responsive" style="width: 100%;">
            </a-tree-select>
          </div>
          <div class="search-item">
            <span class="label">使用单位:</span>
            <a-tree-select v-model:value="useOrg" show-search :dropdown-style="{ maxHeight: '440px', overflow: 'auto' }"
              placeholder="请选择" allow-clear multiple :show-checked-strategy="SHOW_ALL" :tree-data="orgList"
              :field-names="{ label: 'name', value: 'id' }" tree-node-filter-prop="name" max-tag-count="responsive"
              style="width: 100%;">
            </a-tree-select>
          </div>


          <a-button type="primary" class="search-button" @click="handleSearch1">查询</a-button>
        </div> -->
        <!-- <div class="search-row">
          <div class="search-item">
            <span class="label">产权单位:</span>
            <a-select v-model:value="queryParams.propertyOrg" placeholder="全部" style="width: 100%">
              <a-select-option value="">全部</a-select-option>
              <a-select-option v-for="item in cqdwList" :key="item.label" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
          <div class="search-item">
            <span class="label">设备状态:</span>
            <a-select v-model:value="queryParams.equCondition" placeholder="全部" style="width: 100%">
              <a-select-option value="">全部</a-select-option>
              <a-select-option v-for="item in equConditionOptions" :key="item.label" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
          <div class="search-item">
            <span class="label">管理状态:</span>
            <a-select v-model:value="queryParams.managementStatus" placeholder="全部">
              <a-select-option value="">全部</a-select-option>
              <a-select-option v-for="item in managementStatusOptions" :key="item.label" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
          <div class="search-item">
            <span class="label">购置年度:</span>
            <a-range-picker v-model:value="yearAll" picker="year" :placeholder='placeholders'
              :value-format="yearFormat" />
          </div>
          <a-button class="reset-button" @click="handleReset">重置</a-button>
        </div> -->
      </div>

      <!-- 数据表格 -->
      <!-- <a-table :columns="columns" :data-source="tableData" :pagination="false" :scroll="scroll" class="custom-table">
        <template #emptyText>
          <div class="custom-empty">
            <img src="@/assets/images/noData.png" />
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleView(record)">查看</a>

              <a-button type="link" :disabled="!canEdit(record)" @click="handleEdit(record)"
                style="padding: 0; margin: 0; height: auto;" v-privilege="'equipmentform:registered:edit'">
                编辑
              </a-button>
              <a-button type="link" @click="handleAmend(record)" style="padding: 0; margin: 0; height: auto;"
                v-privilege="'equipmentform:registered:amend'">
                修改
              </a-button>
            </a-space>
          </template>
        </template>
        <template #name="{ text }">
          <a>{{ text }}</a>
        </template>
      </a-table> -->


      <ResizableTable :columns="columns" :data-source="tableData" :scroll="{ x: 'max-content', y: 400 }"
        :save-column-width="true" storage-key="example-table-widths" @resizeColumn="onResizeColumn">
        <template #emptyText>
          <div class="custom-empty">
            <img src="@/assets/images/noData.png" />
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleView(record)">查看</a>
              <a-button type="link" :disabled="!canEdit(record)" @click="handleEdit(record)"
                style="padding: 0; margin: 0; height: auto;" v-privilege="'equipmentform:registered:edit'">
                编辑
              </a-button>
              <a-button type="link" @click="handleAmend(record)" style="padding: 0; margin: 0; height: auto;"
                v-privilege="'equipmentform:registered:amend'">
                修改
              </a-button>
            </a-space>
          </template>
        </template>
        <template #name="{ text }">
          <a>{{ text }}</a>
        </template>
      </ResizableTable>


      <!-- 表格底部统计信息 -->
      <div class="table-footer" :class="{ 'follow-page': isShowSearch }">
        <div class="total-info">
          设备数量合计: {{ totalCount }}，财务原值合计：￥{{ totalFinanceValue }}，净值合计：￥{{ totalNetValue }}
        </div>
        <a-pagination v-model:current="pagination.current" :total="pagination.total" style="z-index: 20;"
          :showLessItems="true" :showSizeChanger="true" @change="handlePageChange" :defaultPageSize="20"
          :pageSizeOptions="['20', '50', '100']" @showSizeChange="handleSizeChange" />
      </div>
    </div>
  </div>
  <a-modal v-model:visible="showHistory" title="历史记录" width="80%" @cancel="showHistory = false" :footer="false">
    <a-table :columns="historyColumns" :data-source="historyData">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a @click="historyDetail(record)">查看</a>
        </template>
      </template>
    </a-table>
  </a-modal>
  <detail-drawer v-model:visible="showDetail" :data="current" title="在籍设备卡片信息" @close="handleDrawerClose"
    @showHistoryDetail="handleShowHistory" />

</template>

<script setup>
import { ref, onMounted, nextTick, watch, h } from 'vue';
import { EnumApi } from '@/api/common/enum';
import { BasicInformationApi } from '@/api/buss/BasicInformationApi';
import { EquipmentAcceptanceApi } from '@/api/buss/EquipmentAcceptanceApi';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { UnitApi } from '@/api/common/UnitApi';
import DetailDrawer from '@/components/DetailDrawer/index.vue'
// import ResizableTable from '@/components/ResizableTable.vue'
const router = useRouter();

const yearAll = ref([])
const placeholders = ref(['请选择', '请选择'])
const sblxList = ref([])
const useOrg = ref([])
const scroll = ref({ x: 'max-content', y: 'calc(100vh - 287px)' })

const goToApplicationRecord = () => {
  router.push('/buss/regApplicationRecord');
};

const handleChange = (e) => {
  nextTick(() => {
    if (e.type === 'click') {
      handleSearch1()
    }
  })

}
// 枚举选项
const equConditionOptions = ref([]);
const managementStatusOptions = ref([]);
const yearFormat = 'YYYY';
// 是否显示详情弹窗
const showDetail = ref(false)
// 初始化枚举数据
const initEnumData = async () => {
  try {
    // 获取设备状态枚举
    const equConditionData = await EnumApi.getEnumList({ enumName: 'EquConditionEnum' });
    equConditionOptions.value = equConditionData.data;

    // 获取管理状态枚举
    const managementStatusData = await EnumApi.getEnumList({ enumName: 'ManagementStatusEnum' });
    managementStatusOptions.value = managementStatusData.data;
  } catch (error) {
    message.error('获取枚举数据失败');
  }
};

// 组织机构树数据
const orgList = ref([]);
const cqdwList = ref([])
// 获取组织机构树
const getOrgList = async () => {
  try {
    const res = await UnitApi.getUseOrgTree({ workSheetType: 'equipment_acceptance' });
    const res1 = await UnitApi.getPropertyOrgList();
    orgList.value = res[0].children;
    for (let i = 0; i < orgList.value.length; i++) {
      orgList.value[i].selectable = false
    }


    cqdwList.value = res1;
  } catch (error) {
    message.error('获取组织机构失败');
  }
};

// 设备类型树数据
const equipmentTypeList = ref([]);

// 获取设备类型树
const getEquipmentTypeTree = async () => {
  try {
    const res = await BasicInformationApi.getEquipmentTypeTree();
    equipmentTypeList.value = res.data[0].children;
  } catch (error) {
    message.error('获取设备类型失败');
  }
};

const gldwList = ref([])

// 获取设备类型树
const getManagementOrgList = async () => {
  try {
    const res = await EnumApi.getManagementOrgList({});
    gldwList.value = res.data;
  } catch (error) {
    message.error('获取管理单位失败');
  }
};

onMounted(() => {
  initEnumData();
  getOrgList();
  getEquipmentTypeTree(); // 添加到 onMounted 中
  getManagementOrgList();
  handleSearch();
});
// 列宽调整事件处理
const onResizeColumn = (width, column) => {
  console.log(`列 ${column.title} 宽度调整为: ${width}px`)
}

const handlePageChange = (current) => {

  pagination.value.current = current;
  queryParams.value.pageNo = current;
  handleSearch();
};

const handleSizeChange = (current, size) => {
  pagination.value.pageSize = size;
  pagination.value.current = 1; // 切换每页条数时重置为第一页
  queryParams.value.pageNo = 1;
  queryParams.value.pageSize = size;
  handleSearch();
};
// 查询参数
const queryParams = ref({
  searchText: null, //搜索内容
  useOrg: null,//使用单位
  propertyOrg: null,//产权单位
  equCondition: null,//设备状态
  managementStatus: null,//管理状态
  purchaseYearBegin: null,//购置年度开始
  purchaseYearEnd: null,//购置年度结束
  equType: null,//设备类别
  equSubType: null,//设备种类
  equModel: null,//规格型号
  equName: null,//设备名称
  managementOrg: null,//管理单位
  pageSize: 20,
  pageNo: 1
});

// 重置按钮处理函数
const handleReset = () => {
  yearAll.value = []
  sList.value = []
  sblxList.value = []
  useOrg.value = []
  queryParams.value = {
    searchText: null, //搜索内容
    useOrg: null,//使用单位
    propertyOrg: null,//产权单位
    equCondition: null,//设备状态
    managementStatus: null,//管理状态
    purchaseYearBegin: null,//购置年度开始
    purchaseYearEnd: null,//购置年度结束
    equType: null,//设备类别
    equSubType: null,//设备种类
    equModel: null,//规格型号
    equName: null,//设备名称
    managementOrg: null,//管理单位
    pageSize: 20,
    pageNo: 1
  };
  fetchTableData(queryParams.value);
};

const getArray = (selectData) => {
  let map = new Map()
  for (let item of selectData) {
    if (!map.has(item.value)) {
      map.set(item.value, item)
    }
  }
  const list = [...map.values()]

  return list
}

const handleUseUnitChange = (values) => {

  queryParams.useUnitIds = Array.isArray(values) ? values : [values]
};

const handlePropertyUnitChange = (value) => {
  queryParams.value.propertyUnit = value;
};

// 表格列定义
const columns = [
  // 左侧固定列
  {
    title: '序号',
    width: 70,
    fixed: 'left',
    key: 'index',
    resizable: true,
    customRender: ({ index }) => {
      // 根据当前页码和每页条数计算序号
      return ((pagination.value.current - 1) * pagination.value.pageSize) + index + 1;
    }
  },
  {
    title: '设备编号',
    dataIndex: 'code',
    key: 'code',
    width: 120,
    resizable: true,
    fixed: 'left',
    align: 'left',

    customHeaderCell: () => ({
      style: {
        minWidth: '120px',
        maxWidth: '120px'
      }
    }),
    customCell: () => ({
      style: {
        minWidth: '120px',
        maxWidth: '120px'
      }
    })
  },
  {
    title: '财务卡片编号', resizable: true, dataIndex: 'financialNumber', key: 'financialNumber', width: 130, fixed: 'left', ellipsis: true,
    customHeaderCell: () => ({
      style: {
        minWidth: '120px',
        maxWidth: '120px'
      }
    }),
    customCell: () => ({
      style: {
        minWidth: '120px',
        maxWidth: '120px'
      }
    })
  },
  {
    title: '设备名称', resizable: true, dataIndex: 'equNameStr',key: 'equNameStr', width: 120, fixed: 'left', ellipsis: true,
    customHeaderCell: () => ({
      style: {
        minWidth: '120px',
        maxWidth: '120px'
      }
    }),
    customCell: () => ({
      style: {
        minWidth: '120px',
        maxWidth: '120px'
      }
    })
  },
  {
    title: '规格型号', resizable: true,dataIndex: 'equModelStr', key: 'equModelStr',width: 120, fixed: 'left', ellipsis: true,
    customHeaderCell: () => ({
      style: {
        minWidth: '120px',
        maxWidth: '120px'
      }
    }),
    customCell: () => ({
      style: {
        minWidth: '120px',
        maxWidth: '120px'
      }
    }),
    customRender: ({ text, record, index, column }) => {
      return h('span', { title: text }, text);
    }
  },


  // 中间可滚动列
  {
    title: '型号备注', resizable: true,dataIndex: 'equModelInfo',  key: 'equModelInfo',ellipsis: true, width: 100, customHeaderCell: () => ({
      style: {
        minWidth: '100px',
        maxWidth: '100px'
      }
    }),
    customCell: () => ({
      style: {
        minWidth: '100px',
        maxWidth: '100px'
      }
    })
  },
  { title: '管理单位',resizable: true, dataIndex: 'managementOrgStr',key: 'managementOrgStr', width: 120 },
  { title: '使用单位', resizable: true,dataIndex: 'useOrgStr',key: 'useOrgStr', width: 120 },
  { title: '存放地点', resizable: true,dataIndex: 'storageLocationStr', key: 'storageLocationStr',width: 120 },
  { title: '财务原值',resizable: true, dataIndex: 'financialOriginalValue', key: 'financialOriginalValue',width: 120 },
  { title: '净值', resizable: true,dataIndex: 'netWorth', key: 'netWorth',width: 120 },

  { title: '管理状态',resizable: true, dataIndex: 'managementStatusStr',key: 'managementStatusStr', width: 100 },
  { title: '设备状态', resizable: true,dataIndex: 'equConditionStr', key: 'equConditionStr',width: 100 },

  { title: '生产厂家', resizable: true,dataIndex: 'manufacturer', key: 'manufacturer',width: 150 },
  { title: '出厂日期', resizable: true,dataIndex: 'productionDate',key: 'productionDate', width: 120 },
  { title: '出厂编号', resizable: true,dataIndex: 'factoryNumber',key: 'factoryNumber', width: 120 },
  { title: '设备类别', resizable: true,dataIndex: 'equTypeStr', key: 'equTypeStr',width: 120 },
  { title: '设备种类', resizable: true,dataIndex: 'equSubTypeStr', key: 'equSubTypeStr',width: 120 },
  { title: '设备性质', resizable: true,dataIndex: 'equNatureStr', key: 'equNatureStr',width: 120 },
  { title: '固定资产分类', resizable: true,dataIndex: 'fixedAssetsStr',key: 'fixedAssetsStr', width: 120 },
  // { title: '购置日期', dataIndex: 'purchaseDate', width: 120 },
  // { title: '购置年度', dataIndex: 'purchaseYear', width: 100 },

  { title: '产权单位', resizable: true,dataIndex: 'propertyOrgStr',key: 'propertyOrgStr', width: 120 },
  { title: '功率kw', resizable: true,dataIndex: 'power', key: 'power',width: 120 },
  { title: '设备型号编码', resizable: true,dataIndex: 'modelCode', key: 'modelCode',width: 120 },
  { title: '购置年度', resizable: true,dataIndex: 'purchaseYear',key: 'purchaseYear', width: 100 },

  // 右侧固定列
  { title: '操作', key: 'action', width: 120, fixed: 'right' }
];


// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 20, // 默认改为20
  total: 0,
  showTotal: (total) => `共 ${total} 条`
});

// 表格数据
const tableData = ref([{

}]);
const selectedRowKeys = ref([]);
const selectedRows = ref([]);
const totalCount = ref(0);
const totalFinanceValue = ref(0);
const totalNetValue = ref(0);
var isShowSearch = ref(false);


const searchText = ref('');

const sList = ref([])
const selectSelect = (value, node, extra) => {
  sList.value.push({
    value: value,
    level: node.level
  })
}

const handleTreeExpand = (node) => {
  expandedKeys.value = node
}

const expandedKeys = ref([])
const handleTitleClick = (e, node) => {
  if (node.children.length > 0) {
    const key = orgList.value.find(item => item.name === node.name).id;
    if (expandedKeys.value.includes(key)) {
      expandedKeys.value = expandedKeys.value.filter(k => k !== key);
    } else {
      expandedKeys.value = [...expandedKeys.value, key];
    }
  }
};

const arrList = ref([])
const changeSelect = (value, node, extra) => {
  arrList.value = []
  queryParams.value.equType = []
  queryParams.value.equSubType = []
  queryParams.value.equModel = []
  queryParams.value.equName = []
  nextTick(() => {
    for (let i = 0; i < value.length; i++) {
      for (let s = 0; s < sList.value.length; s++) {
        if (sList.value[s].value == value[i]) {
          arrList.value.push({
            value: value[i],
            level: sList.value[s].level
          })
        }
      }

    }
    arrList.value = getArray(arrList.value)

  })
}
const handleSearch1 = () => {
  // 重置分页到第一页
  pagination.value.current = 1;
  handleSearch()
};

const handleSearch2 = () => {
  // 重置分页到第一页
  pagination.value.current = 1;
  handleSearch()
};
// 查询方法
const handleSearch = () => {
  // 重置分页到第一页
  // pagination.value.current = 1;
  queryParams.value.pageNo = pagination.value.current;
  queryParams.value.pageSize = pagination.value.pageSize;
  if (yearAll.value && yearAll.value.length > 0) {
    queryParams.value.purchaseYearBegin = yearAll.value[0]
    queryParams.value.purchaseYearEnd = yearAll.value[1]
  } else {
    queryParams.value.purchaseYearBegin = null
    queryParams.value.purchaseYearEnd = null
  }

  if (useOrg.value.length > 0) {
    queryParams.value.useOrg = useOrg.value.join(',')
  } else {
    queryParams.value.useOrg = null
  }


  if (arrList.value.length > 0) {
    const arr = []
    const arr1 = []
    const arr2 = []
    const arr3 = []
    for (let i = 0; i < arrList.value.length; i++) {
      if (arrList.value[i].level == '1') {
        arr.push(arrList.value[i].value)
      }
      if (arrList.value[i].level == '2') {
        arr1.push(arrList.value[i].value)
      }
      if (arrList.value[i].level == '3') {
        arr2.push(arrList.value[i].value)
      }
      if (arrList.value[i].level == '4') {
        arr3.push(arrList.value[i].value)
      }
    }
    queryParams.value.equType = arr.join(',')
    queryParams.value.equSubType = arr1.join(',')
    queryParams.value.equName = arr2.join(',')
    queryParams.value.equModel = arr3.join(',')
  }

  const params = queryParams.value

  // 调用查询接口
  fetchTableData(params);
  //isShowSearch.value = false;
};
import { useUserStore } from '@/store/modules/user';

// 在 setup 中添加
const userStore = useUserStore();

// 判断是否可以编辑设备
const canEdit = (record) => {
  // 获取当前登录用户的组织ID
  const currentUserOrgId = userStore.info.organizationId;

  // 判断条件：
  // 1. 当前用户所属组织是否为设备的产权单位
  // 2. 设备的管理状态是否允许编辑

  // 检查产权单位是否匹配
  const isPropertyOrgMatch = record.propertyOrg === currentUserOrgId + "";
  // 检查设备管理状态是否允许编辑
  // 假设只有特定状态的设备可以编辑，例如：正常使用(1)、闲置(2)等
  // const editableStatuses = ['1', '2', '5']; // 根据实际业务需求调整可编辑的状态
  // const hasEditableStatus = editableStatuses.includes(record.managementStatus);

  // 返回是否可以编辑
  return isPropertyOrgMatch;
};
// 表格数据获取方法
const fetchTableData = async (params) => {
  try {


    const res = await EquipmentAcceptanceApi.getData(params);

    tableData.value = res.rows;

    pagination.value.total = res.totalRows;
    const res1 = await EquipmentAcceptanceApi.getOriginalValue(params)
    // 更新统计数据
    totalCount.value = res1.data.totalNum || 0;
    totalFinanceValue.value = formatNumber(res1.data.totalFinancialOriginalValue) || 0;
    totalNetValue.value = formatNumber(res1.data.totalNetWorth) || 0;
  } catch (error) {
    message.error('获取数据失败');
  }
};

const formatNumber = (num) => {
  if (!num && num !== 0) return '';
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};
//设备入账
const goToEquipmentAcceptance = () => {
  router.push('/buss/apply/equipmentAcceptance');
};

// 导出数据
const handleExport = () => {
  // 构建导出参数，与查询参数保持一致
  const exportParams = {
    ...queryParams.value
  };

  // 如果有搜索文本，添加到导出参数中
  if (searchText.value) {
    exportParams.searchText = searchText.value;
  }
  EquipmentAcceptanceApi.exportData(exportParams);
};


const goToEquipmentImport = () => {
  router.push('/buss/equipmentUpload');
};

const handleAmend = (record) => {

  router.push({
    path: '/buss/equAcceptanceAmend',
    query: {
      id: record.id,
      type: 'reg'
    }
  });
};

const handleEdit = (record) => {

  if (!canEdit(record)) {
    message.error('当前设备不允许编辑！');
    return;
  }
  router.push({
    path: '/buss/apply/equAcceptanceEdit',
    query: {
      id: record.id
    }
  });
};

const current = ref(null)
const handleDrawerClose = () => {
  showDetail.value = false;
  current.value = null;
};


const handleView = (record) => {
  //通过接口去获取
  EquipmentAcceptanceApi.getEquipmentFullInfo({ id: record.id }).then(res => {
    res.data.type = 'reg'
    current.value = res.data;
    current.value.type = 'reg'
    showDetail.value = true;
  })
};

const historyColumns = [
  { title: '序号', dataIndex: 'index', customRender: ({ index }) => index + 1 },
  { title: '设备编号', dataIndex: 'code', },

  { title: '产权单位', dataIndex: 'propertyOrgStr', },
  { title: '修改日期', dataIndex: 'applyStartDate' },
  { title: '操作', key: 'action', width: 100 }
];
const showHistory = ref(false);
const historyData = ref([]);
const handleShowHistory = (historyList) => {
  // 这里就能拿到子组件传来的 historyList

  historyData.value = historyList;
  showHistory.value = true;
  // 你可以在这里弹窗、赋值等
};


const historyDetail = (record) => {
  window.open("/history?id=" + record.transferFormId + "&historyCode=" + record.code, '_blank');
};

// 计算合计值
const calculateTotals = () => {
  totalCount.value = selectedRows.value.length;
  totalFinanceValue.value = selectedRows.value.reduce((sum, row) =>
    sum + (Number(row.financialOriginalValue) || 0), 0
  ).toFixed(2);
  totalNetValue.value = selectedRows.value.reduce((sum, row) =>
    sum + (Number(row.netWorth) || 0), 0
  ).toFixed(2);
};


watch(
  () => isShowSearch.value,
  (sum) => {
    if (sum) {
      scroll.value = { x: 'max-content', y: 'calc(100vh - 385px)' }
    } else {
      scroll.value = { x: 'max-content', y: 'calc(100vh - 270px)' }
    }
  }
);
</script>

<style lang="less" scoped>
.reg-body {
  height: 100%;
  overflow: auto;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  border-radius: 6px;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);
  padding: 16px 16px;
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;

  .header-tools {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;

    .left {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .filter-button {
        min-width: 80px;
        height: 32px;
        border-radius: 7px;
      }

      .search-input {
        width: clamp(280px, 20vw, 320px);
        display: flex;
        align-items: center;

        :deep(.ant-input) {
          width: 100%;
          height: 24px;

        }

        .search-icon {
          cursor: pointer;
        }
      }
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .tool-button {
        height: 32px;
        border-radius: 7px;

      }

      .colorBlue {
        color: #176DF4
      }

      .link-button {
        height: 32px;
        padding: 0 8px;
      }
    }
  }



  .search-form {

    padding: 0 16px; // 统一内边距
    border-radius: 8px; // 增加圆角


    .search-row {
      display: flex;
      flex-wrap: wrap;
      gap: 24px; // 增加间距
      margin-bottom: 12px; // 增加行间距

      &:last-child {
        margin-bottom: 0;
      }

      .search-item {
        display: flex;
        align-items: center;
        // min-width: 300px;
        flex: 1;

        .label {
          min-width: 80px;
          // margin-right: 12px; // 增加标签和输入框的间距
          color: #666;
          font-size: 14px;
        }

        :deep(.ant-select),
        :deep(.ant-input) {
          width: 64%;
          height: 32px;

          .ant-select-selector {
            background: #fff; // 确保选择器背景为白色
            border-radius: 4px;
          }
        }

        :deep(.ant-input) {
          background: #fff; // 确保输入框背景为白色
          border-radius: 4px;
        }
      }

      .search-button,
      .reset-button {
        height: 32px;
        min-width: 80px;
        margin-left: auto;
        border-radius: 4px; // 统一按钮圆角
      }

      .search-button {
        background: #1890ff; // 查询按钮使用主题蓝色
      }

      .reset-button {
        background: #fff; // 重置按钮使用白色背景
        border: 1px solid #d9d9d9;
      }
    }
  }

  .table-footer {
    position: fixed; // 默认固定定位
    bottom: 0px;
    background: #ECF5FE;
    width: calc(100% - 32px);
    max-width: 1888px;
    margin: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px 0px;
    border-radius: 8px;
    z-index: 10;

    .total-info {
      color: #666;
      font-size: 14px;
    }

    // 当筛选展开时的样式
    &.follow-page {
      transform: none;
      margin-top: 16px;
    }
  }

  // 表格容器样式
  .custom-table {
    overflow-x: auto !important;
    width: 100%;
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .reg-body {
    .search-form {
      .search-row {
        .search-item {
          width: 50%;
        }
      }
    }
  }
}

@media screen and (max-width: 1366px) {
  .reg-body {
    .search-form {
      .search-row {
        .search-item {
          min-width: calc(50% - 16px);
        }
      }
    }
  }
}

@media screen and (max-width: 1024px) {
  .reg-body {
    .header-tools {

      .left,
      .right {
        width: 100%;
        justify-content: space-between;
      }
    }

    .search-form {
      .search-row {
        .search-item {
          min-width: 100%;
        }
      }
    }

    .table-footer {
      flex-direction: column;
      text-align: center;

      .total-info {
        width: 100%;
      }
    }
  }
}

// 表格样式增强，确保在所有浏览器中都能正确显示滚动条
:deep(.ant-table) {

  // 表格布局设置
  table {
    table-layout: fixed !important; // 强制表格使用固定布局
    min-width: 100% !important;
    width: max-content !important; // 确保表格内容超出时显示滚动条
  }

  // 单元格样式
  .ant-table-cell {
    // max-width: 100% !important; // 限制单元格最大宽度
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    line-height: 1 !important;
    font-size: 14px !important;

    >span,
    >div {
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }
  }

  // 确保表格内容可以水平滚动
  .ant-table-content {
    overflow-x: auto !important;
    min-width: 100% !important;
    z-index: 0 !important;
  }

  // 确保滚动区域正确显示
  .ant-table-body {
    overflow-x: auto !important;
    overflow-y: auto !important;
    min-width: 100% !important;
  }

  // 兼容360浏览器
  .ant-table-container {
    overflow-x: auto !important;
  }

  // 大屏幕样式（默认）
  @media screen and (min-width: 1920px) {
    .ant-table-cell {
      padding: 20px 20px !important;
      height: 60px !important;
    }

    .ant-table-row {
      height: 60px !important;
    }
  }

  // 中等屏幕样式
  @media screen and (min-width: 1366px) and (max-width: 1919px) {
    .ant-table-cell {
      padding: 10px 20px !important;
      height: 40px !important;
    }

    .ant-table-row {
      height: 40px !important;
    }
  }

  // 小屏幕样式
  @media screen and (max-width: 1365px) {
    .ant-table-cell {
      padding: 4px 8px !important;
      height: 32px !important;
    }

    .ant-table-row {
      height: 32px !important;
    }
  }

  // 固定列样式
  .ant-table-fixed-left,
  .ant-table-fixed-right {
    background: #fff !important;
    box-shadow: none !important; // 移除原有阴影
    z-index: 3 !important; // 提高固定列的层级
  }

  // 隐藏特定列
  tr>.ant-table-cell-fix-left:nth-child(6) {
    display: none !important;
  }

  // 调整固定列单元格样式
  .ant-table-cell-fix-left,
  .ant-table-cell-fix-right {
    z-index: 3 !important; // 增加层级
    background: #ECF4FE !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  // 调整表头固定列样式
  .ant-table-thead {

    th.ant-table-cell-fix-left,
    th.ant-table-cell-fix-right {
      z-index: 4 !important; // 确保表头在最上层
      background: #DAECFF !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }

    .ant-table-cell-scrollbar {
      box-shadow: none;
    }
  }

  // 优化阴影效果
  .ant-table-fixed-right::before,
  .ant-table-fixed-left::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    bottom: 0 !important;
    width: 10px !important;
    pointer-events: none !important;
    z-index: 2 !important; // 阴影层级低于固定列
    transition: box-shadow .3s !important;
  }

  .ant-table-fixed-left::before {
    right: 0 !important;
    box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15) !important;
  }

  .ant-table-fixed-right::before {
    left: 0 !important;
    box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15) !important;
  }
}

// 修复行样式
:deep(.ant-row) {
  flex-wrap: nowrap !important;
}

:deep(.ant-form-item-label) {
  overflow: visible !important;
}

// 悬停样式


// 全局滚动条样式，确保在360浏览器中可见
// :deep(*::-webkit-scrollbar) {
//   width: 4px !important; // 垂直滚动条宽度
//   height: 4px !important; // 水平滚动条高度
// }

// :deep(*::-webkit-scrollbar-thumb) {
//   background: rgba(0, 0, 0, 0.3) !important; // 滚动条颜色
//   border-radius: 4px !important;
// }

// :deep(*::-webkit-scrollbar-thumb:hover) {
//   background: rgba(0, 0, 0, 0.5) !important; // 悬停时更深的颜色
// }

// :deep(*::-webkit-scrollbar-track) {
//   background: rgba(0, 0, 0, 0.05) !important; // 轻微可见的轨道
// }

// // 确保表格容器允许滚动 - 360浏览器兼容性修复
// .custom-table {
//   overflow-x: auto !important;
//   width: 100% !important;
// }

// // 确保表格内容可以水平滚动 - 360浏览器兼容性修复
// :deep(.ant-table-content) {
//   overflow-x: auto !important;
//   min-width: 100% !important;
// }

// // 确保滚动区域正确显示 - 360浏览器兼容性修复
// :deep(.ant-table-body) {
//   overflow-x: auto !important;
//   overflow-y: auto !important;
//   min-width: 100% !important;
// }

// // 兼容360浏览器
// :deep(.ant-table-container) {
//   overflow-x: auto !important;
// }

// .custom-table :deep(.ant-table-cell-fix-left),
// .custom-table :deep(.ant-table-cell-fix-right) {
//   background: inherit !important;
// }

// .custom-table :deep(.ant-table-cell-fix-left-last::after),
// .custom-table :deep(.ant-table-cell-fix-right-first::after) {
//   background-color: transparent !important;
// }



// .custom-table :deep(.ant-table-tbody) > tr:hover > td {
//   background: #FFFFFF !important;
// }

// .custom-table {
:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  background-color: #FFFFFF !important;
}

// }

// /deep/ .ant-select-tree-title{
//   display: flex;
// }</style>
