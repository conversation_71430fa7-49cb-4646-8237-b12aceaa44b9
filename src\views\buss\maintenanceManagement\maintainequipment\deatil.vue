<!-- 应用编辑弹窗 -->
<template>
  <div>
    <!-- 新增 -->
    <a-modal width="80%" :maskClosable="false" :visible="visible" :confirm-loading="loading" :forceRender="true"
      title="设备保养记录" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible" centered="true">
      <div>
        <a-table :columns="columns" :data-source="data" :pagination="false" class="custom-table">
          <template #emptyText>
            <div class="custom-empty">
              <img src="@/assets/images/noData.png" />
            </div>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'maintenanceRecords'">
              <a-tooltip :overlayClassName="['custom-tooltip']" placement="right">
                <template v-slot:title>
                  <a-textarea readonly :rows="12" style="width: 100%;" :bordered="false"
                    v-model:value="record.maintenanceRecords" />
                </template>
                <div class="comment">
                  {{ record.maintenanceRecords }}
                </div>

              </a-tooltip>
            </template>
            <template v-if="column.key === 'applyTitle'">
              <div style="color: #176DF4;cursor: pointer;" @click="handleClick(record)">{{ record.applyTitle }}</div>
            </template>
          </template>
        </a-table>
      </div>
      <template #footer>

      </template>
    </a-modal>
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { useUserStore } from '@/store/modules/user';
import { h } from 'vue'
import { EquipmentAcceptanceApi } from '@/api/buss/EquipmentAcceptanceApi';

export default {
  name: 'changeUser',
  components: {},
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Array
  },
  emits: ['done', 'update:visible', 'getList'],
  data() {
    return {
      autoSize: { minRows: 12, maxRows: 12 },
      columns: [
        {
          title: '保养结果记录',
          dataIndex: 'maintenanceRecords',
          key: 'maintenanceRecords',
          width: 200,
          customHeaderCell: () => ({
            style: {
              minWidth: '200px',
              maxWidth: '200px'
            }
          }),
          customCell: () => ({
            style: {
              minWidth: '200px',
              maxWidth: '200px'
            }
          })
        },
        {
          title: '工单截至日期',
          dataIndex: 'planApplyEndDate',
          key: 'planApplyEndDate',
          width: 150
        },
        {
          title: '实际完成日期',
          dataIndex: 'applyEndDate',
          key: 'applyEndDate',
          width: 150
        },
        {
          title: '技术员',
          dataIndex: 'maintenanceUserStr',
          key: 'maintenanceUserStr',
          width: 150
        },
        {
          title: '包机人',
          dataIndex: 'contractUser',
          key: 'contractUser',
          width: 150
        },
        {
          title: '保养附件',
          dataIndex: 'maintenanceFileList',
          key: 'maintenanceFileList',
          width: 200
        },
        {
          title: '关联工单',
          dataIndex: 'applyTitle',
          key: 'applyTitle',
          width: '20%'
        }
      ],
      tableData: [],
      processDefinitionId: ''
    };
  },
  // mounted() {
  //   this.init();
  // },
  // watch: {
  //   data() {
  //     this.init();
  //   }
  // },
  created() { },
  methods: {
    handleClick(record) {
      console.log('record', record);

      EquipmentAcceptanceApi.getProcessDefinitionByProcessInstanceId({ processInstanceId: record.procInstanceId }).then(res => {
        const queryParams1 = new URLSearchParams({
        id: record.procInstanceId,
        procInsId: record.procInstanceId,
        processDefinitionId: res.data,
        actId: null,
      }).toString();
      console.log(queryParams1);
      window.open(`/completed?${queryParams1}`, '_blank');
      })
    },


    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2021/4/7 11:00
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style lang="less" scoped>
.search-form {

  padding: 16px 16px; // 统一内边距
  border-radius: 8px; // 增加圆角

  .search-row1 {
    display: flex;
    width: 80%;
    justify-content: space-between;
  }

  .search-row {
    display: flex;
    flex-wrap: wrap;
    gap: 24px; // 增加间距
    margin-bottom: 12px; // 增加行间距
    justify-content: space-between;

    &:last-child {
      margin-bottom: 0;
    }

    .search-item {
      display: flex;
      align-items: center;
      // min-width: 300px;
      // flex: 1;

      .label {
        min-width: 80px;
        // margin-right: 12px; // 增加标签和输入框的间距
        color: #666;
        font-size: 14px;
      }

      :deep(.ant-select),
      :deep(.ant-input) {
        width: 64%;
        height: 32px;

        .ant-select-selector {
          background: #fff; // 确保选择器背景为白色
          border-radius: 4px;
        }
      }

      :deep(.ant-input) {
        background: #fff; // 确保输入框背景为白色
        border-radius: 4px;
      }
    }

    .search-button,
    .reset-button {
      height: 32px;
      min-width: 80px;
      margin-left: auto;
      border-radius: 4px; // 统一按钮圆角
    }

    .search-button {
      background: #1890ff; // 查询按钮使用主题蓝色
    }

    .reset-button {
      background: #fff; // 重置按钮使用白色背景
      border: 1px solid #d9d9d9;
    }
  }
}

.custom-table {
  margin-top: 16px;
  min-height: 400px;

  :deep(.ant-table) {

    // 提高固定列的层级
    .ant-table-fixed-left,
    .ant-table-fixed-right {
      background: #fff;
      z-index: 3; // 增加层级
    }

    .ant-table-cell {
      white-space: nowrap !important; // 强制不换行
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      line-height: 1 !important;
      font-size: 14px !important;

      >span,
      >div {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }
    }

    // 大屏幕样式（默认）
    @media screen and (min-width: 1920px) {
      thead .ant-table-cell {
        padding: 20px 20px !important;
        height: 60px !important;
      }

      .ant-table-row {
        height: 60px !important;
      }
    }

    // 中等屏幕样式
    @media screen and (min-width: 1366px) and (max-width: 1919px) {
      thead .ant-table-cell {
        padding: 10px 20px !important;
        height: 40px !important;
      }

      .ant-table-row {
        height: 40px !important;
      }
    }

    // 小屏幕样式
    @media screen and (max-width: 1365px) {
      thead .ant-table-cell {
        padding: 4px 8px !important;
        height: 32px !important;
      }

      .ant-table-row {
        height: 32px !important;
      }
    }

    // 调整固定列单元格样式
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      z-index: 3 !important; // 增加层级
      background: #ECF4FE !important;
    }

    // 调整表头固定列样式
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right {
        z-index: 4 !important; // 确保表头在最上层
        background: #DAECFF !important;
      }
    }

    // 优化阴影效果
    .ant-table-fixed-right::before,
    .ant-table-fixed-left::before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      width: 10px;
      pointer-events: none;
      z-index: 2; // 阴影层级低于固定列
      transition: box-shadow .3s;
    }

    .ant-table-fixed-left::before {
      right: 0;
      box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
    }

    .ant-table-fixed-right::before {
      left: 0;
      box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
    }

    // 设置表格内容的层级
    .ant-table-content {
      z-index: 1;
    }

    // 确保滚动区域正确显示
    .ant-table-body {
      overflow-x: auto !important;
      overflow-y: auto !important;
    }

    // 固定列不换行
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .ant-table-row {
      height: 24px !important;
    }

    // 表头固定列不换行
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.table-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}

:deep(.ant-table-tbody .ant-table-cell) {
  padding: 10px 16px !important;
}

.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}

::v-deep .custom-tooltip {
  /* 注意：如果使用scoped，需要加深度选择器 */
  width: 300px;
  max-height: 200px;
  overflow-y: auto;
}

// :deep(.custom-tooltip) {
//   background-color: #fff !important;
//   .ant-tooltip-content {
//     .ant-tooltip-inner {
//       width: 300px;
//       max-height: 200px;
//       overflow-y: auto;
//     }
//   }
// }</style>
<style lang="less">
.comment {
  /* 必须设置固定宽度或最大宽度 */
  width: 170px;
  /* 或使用 max-width: 100%; 适应父容器 */
  /* 关键属性组合 */
  white-space: nowrap;
  /* 禁止文本换行 */
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 显示省略号 */
}

.custom-tooltip {
  .ant-tooltip-content {

    /* 注意：如果使用scoped，需要加深度选择器 */
    .ant-tooltip-arrow {
      overflow: visible !important;
      z-index: 9999;

      .ant-tooltip-arrow-content {
        background-color: white;
        border-bottom: 1px solid #42A5FF;
        border-left: 1px solid #42A5FF;
      }

    }

    .ant-tooltip-inner {
      width: 250px;
      max-height: 300px;
      // overflow-y: auto;
      padding: 0 !important;
      // background-color: pink;
      // border: 1px solid #FF4D4F;
      background-image: url('@/assets/images/tooipbj.png');
      /* 背景图居中 */
      background-position: center center;
      /* 关键属性：覆盖整个元素，可能会裁剪图片 */
      background-size: cover;
      color: black;
      /* 背景图自适应 */
      background-repeat: no-repeat;
      border: 1px solid #42A5FF;

      textarea {
        border: 0;
        background-color: rgba(255, 255, 255, 0);
      }
    }
  }
}
</style>
