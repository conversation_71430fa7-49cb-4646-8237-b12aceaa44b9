<template>
  <div class="ele-body">
    <div class="reg-body">
      <!-- 顶部按钮组 -->
      <div class="header-tools">
        <div class="left">
          <a-button class="tool-button" style="color: #176DF4;" @click="openAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新建
          </a-button>
          <!-- <a-button class="tool-button" style="color: #176DF4;" @click="expandAll">
            展开全部
          </a-button>
          <a-button class="tool-button" style="color: #176DF4;" @click="foldAll">
            折叠全部
          </a-button> -->
          <a-button @click="expandAll">展开全部</a-button>
          <a-button @click="foldAll">折叠全部</a-button>
        </div>
      </div>


      <!-- 数据表格 -->
      <a-table :columns="columns" :data-source="orgList" :pagination="false" ref="table" :scroll="{ x: 'max-content' }"
        class="custom-table" style="width: 100%;" rowKey="id" :expanded-row-keys="expandedRowKeys"
        @expand="handleExpand">
        <template #emptyText>
          <div class="custom-empty">
            <img src="@/assets/images/noData.png"/>
            <!-- <p>抱歉，暂时还没有数据</p> -->
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'orgType'">
            <span v-if="record.orgType === 1">公司</span>
            <span v-else-if="record.orgType === 2">部门</span>
            <span v-else> 其他</span>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="openEdit(record, '1')">编辑</a>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
  </div>
  <!-- 编辑弹窗 -->
  <org-edit v-model:visible="showEdit" :isUpdate="updateOrg" :data="currentOrgInfo" @done="reload" :org-list="orgLists"
    :defaultKey="defaultKey" />
</template>
<script>
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { equipmentTypeConfigurationApi } from '@/api/basicInformationConfiguration/equipmentTypeConfigurationApi';
import OrgEdit from './org-edit.vue';

export default {
  name: 'Organization',
  components: {
    OrgEdit,
    PlusOutlined,
  },
  data() {
    return {
      columns: [
        {
          title: '设备类别',
          dataIndex: 'name',
          align: 'left'
        },
        // {
        //   title: '设备种类',
        //   dataIndex: 'orgCode',
        //   align: 'center',
        //   width:120
        // },
        // {
        //   title: '设备名称',
        //   dataIndex: 'orgType',
        //   key: 'orgType',
        //   align: 'center',
        //   width:120
        // },
        // {
        //   title: '规格型号',
        //   key: 'orgNatureStr',
        //   dataIndex: 'orgNatureStr',
        //   align: 'center',
        //   width:120
        // },
        {
          title: '保养周期',
          key: 'maintenanceCycleStr',
          dataIndex: 'maintenanceCycleStr',
          align: 'center',
          width: 120
        },
        // {
        //   title: '机构状态',
        //   key: 'statusFlag',
        //   dataIndex: 'statusFlag',
        //   align: 'center'
        // },
        // {
        //   title: '排序',
        //   dataIndex: 'orgSort',
        //   align: 'center',
        //   width:60
        // },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          align: 'center',
          width: 120
        }
      ],
      rowSelection: {
        checkStrictly: false,
        onChange: (selectedRowKeys, selectedRows) => {
          console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
        },
        onSelect: (record, selected, selectedRows) => {
          console.log(record, selected, selectedRows);
        },
        onSelectAll: (selected, selectedRows, changeRows) => {
          console.log(selected, selectedRows, changeRows);
        },
      },
      // 是否显示编辑弹窗
      showEdit: false,
      // 表格展开的行
      expandedRowKeys: [],
      // 全部机构数据
      orgList: [],
      orgLists: [],
      defaultKey: '',
    };
  },
  created() {
    this.datasource()
  },
  methods: {

    /**
     * 打开编辑弹窗
     *
     * <AUTHOR>
     * @date 2022/5/20 17:54
     */
    openAdd() {
      this.defaultKey = '1';
      this.currentOrgInfo = {};
      this.showEdit = true;
      this.updateOrg = false;
    },

    /**
     * 打开编辑弹窗
     *
     * <AUTHOR>
     * @date 2022/5/20 17:54
     */
    async openEdit(row, flag) {
      console.log('row', row);

      this.currentOrgInfo = { id: row.id }
      // this.currentOrgInfo = await UserApi.getOrgDetail({ orgId: row.nodeId });
      // console.log('this.currentOrgInfo',this.currentOrgInfo);
      this.defaultKey = flag;
      this.showEdit = true;
      this.updateOrg = true;
    },

    /**
     * 展开全部
     *
     * <AUTHOR>
     * @date 2022/5/20 17:54
     */
    expandAll() {
      const allKeys = [];
      const traverse = (nodes) => {
        nodes.forEach(node => {
          allKeys.push(node.id);
          if (node.children) {
            traverse(node.children);
          }
        });
      };
      traverse(this.orgList);
      this.expandedRowKeys = allKeys;
    },

    handleExpand(expanded, record) {
      console.log('expanded', expanded);
      console.log('record', record);
      if (expanded) {
        this.expandedRowKeys = [...this.expandedRowKeys, record.id];
      } else {
        this.expandedRowKeys = this.expandedRowKeys.filter(key => key !== record.id);
      }
    },

    /**
     * 折叠全部
     *
     * <AUTHOR>
     * @date 2022/5/20 17:54
     */
    foldAll() {
      this.expandedRowKeys = [];
    },

    /**
     * 展开的行变化
     *
     * <AUTHOR>
     * @date 2022/5/20 17:54
     */
    onExpandedRowsChange(expandedRows) {
      this.expandedRowKeys = expandedRows;
    },

    reload() {
      this.datasource()
    },

    /**
     * 获取表格数据
     *
     * <AUTHOR>
     * @date 2022/5/20 17:54
     */
    async datasource() {
      let arr = await equipmentTypeConfigurationApi.organizationTreeList();
      this.orgList = this.getForTree(arr.data[0].children)
      this.orgLists = arr.data
    },

    getForTree(data) {
      data.forEach((item, index) => {
        // 判断对象中是否有children
        if (item.children && item.children.length > 0) {
          // 对children项递归
          this.getForTree(item.children)
        } else {
          //  没有children直接赋值
          delete data[index].children
        }
      })
      return data
    }
  }
};
</script>
<style scoped>
/deep/ .ant-table-cell-fix-left,
.ant-table-cell-fix-right {
  z-index: 3 !important;
  background: #ECF4FE !important;
}

/deep/ .ant-table-thead>tr>th {
  z-index: 3 !important;
  background: #DBECFF !important;
}

/deep/ .ele-table-tool .ele-tool {
  display: none !important;
}
</style>
<style lang="less" scoped>
.reg-body {
  height: 100%;
  overflow: auto;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  border-radius: 6px;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);
  padding: 16px 16px;
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;

  .header-tools {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;

    .left {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .filter-button {
        min-width: 80px;
        height: 32px;
        border-radius: 7px;
      }

      .search-input {
        width: clamp(280px, 20vw, 320px);

        :deep(.ant-input) {
          width: 100%;
          height: 24px;

        }

        .search-icon {
          cursor: pointer;
        }
      }
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .tool-button {
        height: 32px;
        border-radius: 7px;
      }

      .link-button {
        height: 32px;
        padding: 0 8px;
      }
    }
  }

  .search-form {

    padding: 16px 16px; // 统一内边距
    border-radius: 8px; // 增加圆角


    .search-row {
      display: flex;
      flex-wrap: wrap;
      gap: 24px; // 增加间距
      margin-bottom: 12px; // 增加行间距

      &:last-child {
        margin-bottom: 0;
      }

      .search-item {
        display: flex;
        align-items: center;
        // min-width: 300px;
        flex: 1;

        .label {
          min-width: 80px;
          // margin-right: 12px; // 增加标签和输入框的间距
          color: #666;
          font-size: 14px;
        }

        :deep(.ant-select),
        :deep(.ant-input) {
          width: 64%;
          height: 32px;

          .ant-select-selector {
            background: #fff; // 确保选择器背景为白色
            border-radius: 4px;
          }
        }

        :deep(.ant-input) {
          background: #fff; // 确保输入框背景为白色
          border-radius: 4px;
        }
      }

      .search-button,
      .reset-button {
        height: 32px;
        min-width: 80px;
        margin-left: auto;
        border-radius: 4px; // 统一按钮圆角
      }

      .search-button {
        background: #1890ff; // 查询按钮使用主题蓝色
      }

      .reset-button {
        background: #fff; // 重置按钮使用白色背景
        border: 1px solid #d9d9d9;
      }
    }
  }

  .table-footer {
    position: fixed; // 默认固定定位
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 32px);
    max-width: 1888px;
    margin: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px 24px;
    border-radius: 8px;

    z-index: -100;

    .total-info {
      color: #666;
      font-size: 14px;
    }

    // 当筛选展开时的样式
    &.follow-page {
      position: static; // 改为静态定位
      transform: none;
      width: 100%;
      margin-top: 16px;
    }
  }


  .custom-table {
    margin-top: 16px;

    :deep(.ant-table) {

      // 提高固定列的层级
      .ant-table-fixed-left,
      .ant-table-fixed-right {
        background: #fff;
        z-index: 3; // 增加层级
      }

      .ant-table-cell {
        white-space: nowrap !important; // 强制不换行
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        line-height: 1 !important;
        font-size: 14px !important;

        >span,
        >div {
          white-space: nowrap !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
        }
      }

      // 大屏幕样式（默认）
      @media screen and (min-width: 1920px) {
        .ant-table-cell {
          padding: 20px 20px !important;
          height: 60px !important;
        }

        .ant-table-row {
          height: 60px !important;
        }
      }

      // 中等屏幕样式
      @media screen and (min-width: 1366px) and (max-width: 1919px) {
        .ant-table-cell {
          padding: 10px 20px !important;
          height: 40px !important;
        }

        .ant-table-row {
          height: 40px !important;
        }
      }

      // 小屏幕样式
      @media screen and (max-width: 1365px) {
        .ant-table-cell {
          padding: 4px 8px !important;
          height: 32px !important;
        }

        .ant-table-row {
          height: 32px !important;
        }
      }

      // 调整固定列单元格样式
      .ant-table-cell-fix-left,
      .ant-table-cell-fix-right {
        z-index: 3 !important; // 增加层级
        background: #ECF4FE !important;
      }

      tr>.ant-table-cell-fix-left:nth-child(6) {
        display: none !important;
      }

      // 调整表头固定列样式
      .ant-table-thead {

        th.ant-table-cell-fix-left,
        th.ant-table-cell-fix-right {
          z-index: 4 !important; // 确保表头在最上层
          background: #DAECFF !important;
        }
      }

      // 优化阴影效果
      .ant-table-fixed-right::before,
      .ant-table-fixed-left::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 10px;
        pointer-events: none;
        z-index: 2; // 阴影层级低于固定列
        transition: box-shadow .3s;
      }

      .ant-table-fixed-left::before {
        right: 0;
        box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
      }

      .ant-table-fixed-right::before {
        left: 0;
        box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
      }

      // 设置表格内容的层级
      .ant-table-content {
        z-index: 1;
      }

      // 确保滚动区域正确显示
      .ant-table-body {
        overflow-x: auto !important;
        overflow-y: auto !important;
      }

      // 固定列不换行
      .ant-table-cell-fix-left,
      .ant-table-cell-fix-right {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .ant-table-row {
        height: 24px !important;
      }

      // 表头固定列不换行
      .ant-table-thead {

        th.ant-table-cell-fix-left,
        th.ant-table-cell-fix-right {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .reg-body {
    .search-form {
      .search-row {
        .search-item {
          width: 50%;
          // min-width: calc(32.33% - 40px);
        }
      }
    }
  }
}

@media screen and (max-width: 1366px) {
  .reg-body {
    .search-form {
      .search-row {
        .search-item {
          min-width: calc(50% - 16px);
        }
      }
    }
  }
}

@media screen and (max-width: 1024px) {
  .reg-body {
    .header-tools {

      .left,
      .right {
        width: 100%;
        justify-content: space-between;
      }
    }

    .search-form {
      .search-row {
        .search-item {
          min-width: 100%;
        }
      }
    }

    .table-footer {
      flex-direction: column;
      text-align: center;

      .total-info {
        width: 100%;
      }
    }
  }
}

// 表格响应式
:deep(.ant-table) {
  .ant-table-content {
    overflow-x: auto;
  }

  @media screen and (max-width: 1024px) {
    .ant-table-cell {
      white-space: nowrap;
    }
  }
}

// 固定列样式
:deep(.ant-table) {
  .ant-table-body {
    overflow-x: auto;
    overflow-y: auto;
  }

  .ant-table-fixed-left,
  .ant-table-fixed-right {
    background: #fff;
    box-shadow: none; // 移除原有阴影
    z-index: 2; // 提高固定列的层级
  }

  // 确保表格内容正确显示
  .ant-table-content {
    z-index: 0;
  }

  // 修复固定列单元格层级
  .ant-table-cell-fix-left,
  .ant-table-cell-fix-right {
    z-index: 2 !important;
    background: #fff !important;
  }

  // 修复表头固定列层级
  .ant-table-thead th.ant-table-cell-fix-left,
  .ant-table-thead th.ant-table-cell-fix-right {
    z-index: 3 !important;
  }
}

:deep(.ant-tree-switcher) {
  position: relative;
}

:deep(.ant-tree-switcher_close::before) {
  content: "";
  position: absolute;
  right: -200px;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 999;
}

:deep(.ant-tree-switcher_open::before) {
  content: "";
  position: absolute;
  right: -200px;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 999;
}
.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>
