<template>
  <div class="ele-body">
    <div class="reg-body">
      <!-- 顶部按钮组 -->
      <div class="header-tools">
        <div class="left">

          <a-button class="filter-button" @click="isShowSearch = !isShowSearch">
            <template #icon><filter-outlined /></template>
            筛选
          </a-button>
          <div class="search-input">
            <a-input v-model:value="searchText" placeholder="支持设备编号/设备代码搜索" :style="{ width: '240px' }"
              @keyup.enter="handleSearch">
              <template #suffix>
                <search-outlined class="search-icon" @click="handleSearch" />
              </template>
            </a-input>
          </div>
        </div>
        <div class="right">
          <a-button
            class="tool-button"
            style="display: flex;align-items: center;justify-content: space-between;"
            @click="handleExport"
          >
            <i class="iconfont icon-import" style="margin-right: 6px;"></i>
            导出
          </a-button>
          <a-button class="tool-button" style="display: flex;align-items: center;justify-content: space-between;"
            @click="goToEquipmentImport"
          >
            <i class="iconfont icon-rongqi-copy" style="margin-right: 6px;"></i>
            导入
          </a-button>
          <a-button class="tool-button" @click="goToEquipmentAcceptance">
            <template #icon>
              <DesktopOutlined />
            </template>
            设备入账
          </a-button>
          <a-button type="link" class="link-button" @click="goToApplicationRecord">
            申请记录 >
          </a-button>
        </div>
      </div>

      <!-- 搜索工具栏 -->
      <div class="search-form" v-if="isShowSearch">
        <div class="search-row">
          <div class="search-item">
            <span class="label">设备类型:</span>
            <tree-cascade-select
              v-model:value="queryParams.equipmentType"
              :options="equipmentTypeList"
              :multiple="false"
              placeholder="请选择设备类型"
              style="width: 64%"
            />
          </div>
          <div class="search-item">
            <span class="label">使用单位:</span>
            <tree-cascade-select
              v-model:value="queryParams.useUnitIds"
              :options="orgList"
              @change="handleUseUnitChange"
              placeholder="请选择使用单位"
              style="width: 64%"
            />
          </div>
          <div class="search-item">
            <span class="label">产权单位:</span>
            <tree-cascade-select
              v-model:value="queryParams.propertyUnit"
              :options="orgList"
              @change="handlePropertyUnitChange"
              placeholder="请选择产权单位"
              style="width: 64%"
            />
          </div>

          <a-button type="primary" class="search-button" @click="handleSearch">查询</a-button>
        </div>
        <div class="search-row">
          <div class="search-item">
            <span class="label">设备状态:</span>
            <a-select v-model:value="queryParams.equipmentStatus" placeholder="全部">
              <a-select-option value="">全部</a-select-option>
              <a-select-option v-for="item in equConditionOptions" :key="item.label" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
          <div class="search-item">
            <span class="label">管理状态:</span>
            <a-select v-model:value="queryParams.managementStatus" placeholder="全部">
              <a-select-option value="">全部</a-select-option>
              <a-select-option v-for="item in managementStatusOptions" :key="item.label" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
          <div class="search-item">
            <span class="label">购置年度:</span>
            <a-date-picker
              v-model:value="queryParams.purchaseYear"
              picker="year"
              placeholder="请选择年份"
              :format="yearFormat"
              style="width: 64%"
            />
          </div>
          <a-button class="reset-button" @click="handleReset">重置</a-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="false"
        :scroll="{ x: 'max-content' }"
        :row-selection="rowSelection"
        :row-key="record => record.equId"
        class="custom-table"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleView(record)">查看</a>
              <a @click="handleEdit(record)">编辑</a>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 表格底部统计信息 -->
      <div class="table-footer" :class="{ 'follow-page': isShowSearch }">
        <div class="total-info">
          设备数量合计: {{ totalCount }}，财务原值合计：{{ totalFinanceValue }}元，净值合计：{{ totalNetValue }}万元
        </div>

        <a-pagination
          v-model:current="pagination.current"
          :total="pagination.total"
          :pageSize="10"
          :showLessItems="true"
          @change="handlePageChange"
        />
      </div>
    </div>
  </div>

  <detail-drawer
    v-model:visible="showDetail"
    :data="current"
    title="在籍设备信息"
    @close="handleDrawerClose"
  />
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { EnumApi } from '@/api/common/enum';
import { OrganizationApi } from '@/api/system/organization/OrganizationApi';
import { BasicInformationApi } from '@/api/buss/BasicInformationApi';
import {EquipmentAcceptanceApi} from '@/api/buss/EquipmentAcceptanceApi';
import { message } from 'ant-design-vue';
import {
  SearchOutlined,
  FilterOutlined,
  LogoutOutlined,
  LoginOutlined,
  DesktopOutlined
} from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import TreeCascadeSelect from '@/components/TreeCascadeSelect/index.vue';
import DetailDrawer from '@/components/DetailDrawer/index.vue'
const router = useRouter();

const goToApplicationRecord = () => {
  router.push('/buss/regApplicationRecord');
};

// 枚举选项
const equConditionOptions = ref([]);
const managementStatusOptions = ref([]);
const yearFormat = 'YYYY';
// 是否显示详情弹窗
const showDetail = ref(false)
// 初始化枚举数据
const initEnumData = async () => {
  try {
    // 获取设备状态枚举
    const equConditionData = await EnumApi.getEnumList({ enumName: 'EquConditionEnum' });
    equConditionOptions.value = equConditionData.data;

    // 获取管理状态枚举
    const managementStatusData = await EnumApi.getEnumList({ enumName: 'ManagementStatusEnum' });
    managementStatusOptions.value = managementStatusData.data;
  } catch (error) {
    message.error('获取枚举数据失败');
  }
};

// 组织机构树数据
const orgList = ref([]);

// 获取组织机构树
const getOrgList = async () => {
  try {
    const res = await OrganizationApi.organizationTreeList();
    orgList.value = res.data;
  } catch (error) {
    message.error('获取组织机构失败');
  }
};

// 设备类型树数据
const equipmentTypeList = ref([]);

// 获取设备类型树
const getEquipmentTypeTree = async () => {
  try {
    const res = await BasicInformationApi.getEquipmentTypeTree();
    equipmentTypeList.value = res.data;
  } catch (error) {
    message.error('获取设备类型失败');
  }
};

onMounted(() => {
  initEnumData();
  getOrgList();
  getEquipmentTypeTree(); // 添加到 onMounted 中
  handleSearch();
});

// 查询参数
const queryParams = ref({
  useUnitIds: [],
  propertyUnit: '',
  equipmentType: '',
  equipmentStatus: '',
  managementStatus: '',
  purchaseYear: null
});

// 重置按钮处理函数
const handleReset = () => {
  queryParams.value = {
    useUnitIds: [],
    propertyUnit: undefined,
    equipmentType: '',
    equipmentStatus: '',
    managementStatus: '',
    purchaseYear: null
  };
};

const handleUseUnitChange = (values) => {
  queryParams.value.useUnitIds = values;
};

const handlePropertyUnitChange = (value) => {
  queryParams.value.propertyUnit = value;
};

// 表格列定义
const columns = [
  // 左侧固定列
  {
    title: '序号',
    width: 60,
    fixed: 'left',
    customRender: ({ index }) => {
      // 根据当前页码和每页条数计算序号
      return ((pagination.value.current - 1) * pagination.value.pageSize) + index + 1;
    }
  },
  { title: '设备编号', dataIndex: 'code', width: 120, fixed: 'left' },
  { title: '设备名称', dataIndex: 'equNameStr', width: 120, fixed: 'left' },
  { title: '规格型号', dataIndex: 'equModelStr', width: 100, fixed: 'left' },
  { title: '型号备注', dataIndex: 'equModelInfoStr', width: 100, fixed: 'left' },

  // 中间可滚动列
  { title: '管理单位', dataIndex: 'managementOrgStr', width: 120 },
  { title: '使用单位', dataIndex: 'useOrgStr', width: 120 },
  { title: '存放地点', dataIndex: 'storageLocationStr', width: 120 },
  { title: '设备状态', dataIndex: 'equConditionStr', width: 100 },
  { title: '管理状态', dataIndex: 'managementStatusStr', width: 100 },
  { title: '设备类型', dataIndex: 'equType', width: 120 },
  { title: '设备分类', dataIndex: 'equClassification', width: 120 },
  { title: '设备种类', dataIndex: 'equSubTypeStr', width: 120 },
  { title: '设备性质', dataIndex: 'equNatureStr', width: 120 },
  { title: '生产厂家', dataIndex: 'manufacturer', width: 150 },
  { title: '出厂编号', dataIndex: 'factoryNumber', width: 120 },
  { title: '出厂日期', dataIndex: 'productionDate', width: 120 },
  { title: '购置日期', dataIndex: 'purchaseDate', width: 120 },
  { title: '购置年度', dataIndex: 'purchaseYear', width: 100 },
  { title: '财务原值', dataIndex: 'financialOriginalValue', width: 120 },
  { title: '净值', dataIndex: 'netWorth', width: 120 },
  { title: '产权单位', dataIndex: 'propertyOrgStr', width: 120 },
  { title: '设备型号编码', dataIndex: 'modelCode', width: 120 },
  { title: '备注', dataIndex: 'remark', width: 150 },
  { title: '创建时间', dataIndex: 'createTime', width: 150 },
  { title: '更新时间', dataIndex: 'updateTime', width: 150 },

  // 右侧固定列
  { title: '操作', key: 'action', width: 120, fixed: 'right' }
];


// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
});

// 表格数据
const tableData = ref([]);
const selectedRowKeys = ref([]);
const selectedRows = ref([]);
const totalCount = ref(0);
const totalFinanceValue = ref(0);
const totalNetValue = ref(0);
var isShowSearch = ref(false);
const searchText = ref('');


// 查询方法
const handleSearch = () => {
  // 重置分页到第一页
  pagination.current = 1;
  // 构建查询参数
  const params = {
    pageNo: pagination.value.current,
    pageSize: pagination.value.pageSize,
    ...queryParams.value
  };
  console.log(4444,params)
  // 如果有搜索文本，添加到查询参数中
  if (searchText.value) {
    params.searchText = searchText.value;
  }
  // 调用查询接口
  fetchTableData(params);
};

// 表格数据获取方法
const fetchTableData = async (params) => {
  try {
    console.log(3333,params)

    const res = await EquipmentAcceptanceApi.getData(params);

    tableData.value = res.rows;
    console.log(tableData.value)
    pagination.value.total = res.totalRows;

    // 更新统计数据
    totalCount.value = res.totalCount || 0;
    totalFinanceValue.value = res.totalFinanceValue || 0;
    totalNetValue.value = res.totalNetValue || 0;
  } catch (error) {
    message.error('获取数据失败');
  }
};

//设备入账
const goToEquipmentAcceptance = () => {
  router.push('/buss/apply/equipmentAcceptance');
};

// 导出数据
const handleExport = () => {
  // 构建导出参数，与查询参数保持一致
  const exportParams = {
    ...queryParams.value
  };

  // 如果有搜索文本，添加到导出参数中
  if (searchText.value) {
    exportParams.searchText = searchText.value;
  }

  // 直接调用导出，会打开新页面下载
  EquipmentAcceptanceApi.exportData(exportParams);
};


 const goToEquipmentImport = () => {
  router.push('/buss/equipmentUpload');
};

  const handleEdit = (record) => {
    router.push({
        path: '/buss/apply/equAcceptanceEdit',
        query: {
            id: record.id
        }
    });
};

const handlePageChange = (page) => {
  pagination.value.current = page;
  handleSearch(); // 重新获取数据
};

const current = ref(null)
const handleDrawerClose = () => {
  showDetail.value = false;
  current.value = null;
};


const handleView = (record) => {
  current.value = record;
  showDetail.value = true;
};




// 计算合计值
const calculateTotals = () => {
  totalCount.value = selectedRows.value.length;
  totalFinanceValue.value = selectedRows.value.reduce((sum, row) =>
    sum + (Number(row.financialOriginalValue) || 0), 0
  ).toFixed(2);
  totalNetValue.value = selectedRows.value.reduce((sum, row) =>
    sum + (Number(row.netWorth) || 0), 0
  ).toFixed(2);
};
</script>

<style lang="less" scoped>
.reg-body {
  height: 100%;
  overflow:auto;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  border-radius: 6px;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);
  padding: 16px 16px;
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;

  .header-tools {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;

    .left {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .filter-button {
        min-width: 80px;
        height: 32px;
        border-radius: 7px;
      }

      .search-input {
        width: clamp(280px, 20vw, 320px);

        :deep(.ant-input) {
          width: 100%;
          height: 24px;

        }

        .search-icon {
          cursor: pointer;
        }
      }
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .tool-button {
        height: 32px;
        border-radius: 7px;
      }

      .link-button {
        height: 32px;
        padding: 0 8px;
      }
    }
  }

  .search-form {

    padding: 16px 16px; // 统一内边距
    border-radius: 8px; // 增加圆角


    .search-row {
      display: flex;
      flex-wrap: wrap;
      gap: 24px; // 增加间距
      margin-bottom: 12px; // 增加行间距

      &:last-child {
        margin-bottom: 0;
      }

      .search-item {
        display: flex;
        align-items: center;
        min-width: 300px;
        flex: 1;

        .label {
          min-width: 80px;
          margin-right: 12px; // 增加标签和输入框的间距
          color: #666;
          font-size: 14px;
        }

        :deep(.ant-select),
        :deep(.ant-input) {
          width: 64%;
          height: 32px;

          .ant-select-selector {
            background: #fff; // 确保选择器背景为白色
            border-radius: 4px;
          }
        }

        :deep(.ant-input) {
          background: #fff; // 确保输入框背景为白色
          border-radius: 4px;
        }
      }

      .search-button,
      .reset-button {
        height: 32px;
        min-width: 80px;
        margin-left: auto;
        border-radius: 4px; // 统一按钮圆角
      }

      .search-button {
        background: #1890ff; // 查询按钮使用主题蓝色
      }

      .reset-button {
        background: #fff; // 重置按钮使用白色背景
        border: 1px solid #d9d9d9;
      }
    }
  }

  .table-footer {
    position: fixed; // 默认固定定位
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 32px);
    max-width: 1888px;
    margin: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px 24px;
    border-radius: 8px;

    z-index: -100;

    .total-info {
      color: #666;
      font-size: 14px;
    }

    // 当筛选展开时的样式
    &.follow-page {
      position: static; // 改为静态定位
      transform: none;
      width: 100%;
      margin-top: 16px;
    }
  }


  .custom-table {
    margin-top: 16px;

    :deep(.ant-table) {

      // 提高固定列的层级
      .ant-table-fixed-left,
      .ant-table-fixed-right {
        background: #fff;
        z-index: 3; // 增加层级
      }

      .ant-table-cell {
        white-space: nowrap !important; // 强制不换行
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        line-height: 1 !important;
        font-size: 14px !important;

        >span,
        >div {
          white-space: nowrap !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
        }
      }

      // 大屏幕样式（默认）
      @media screen and (min-width: 1920px) {
        .ant-table-cell {
          padding: 20px 20px !important;
          height: 60px !important;
        }

        .ant-table-row {
          height: 60px !important;
        }
      }

      // 中等屏幕样式
      @media screen and (min-width: 1366px) and (max-width: 1919px) {
        .ant-table-cell {
          padding: 10px 20px !important;
          height: 40px !important;
        }

        .ant-table-row {
          height: 40px !important;
        }
      }

      // 小屏幕样式
      @media screen and (max-width: 1365px) {
        .ant-table-cell {
          padding: 4px 8px !important;
          height: 32px !important;
        }

        .ant-table-row {
          height: 32px !important;
        }
      }

      // 调整固定列单元格样式
      .ant-table-cell-fix-left,
      .ant-table-cell-fix-right {
        z-index: 3 !important; // 增加层级
        background: #ECF4FE !important;
      }

      // 调整表头固定列样式
      .ant-table-thead {

        th.ant-table-cell-fix-left,
        th.ant-table-cell-fix-right {
          z-index: 4 !important; // 确保表头在最上层
          background: #DAECFF !important;
        }
      }

      // 优化阴影效果
      .ant-table-fixed-right::before,
      .ant-table-fixed-left::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 10px;
        pointer-events: none;
        z-index: 2; // 阴影层级低于固定列
        transition: box-shadow .3s;
      }

      .ant-table-fixed-left::before {
        right: 0;
        box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
      }

      .ant-table-fixed-right::before {
        left: 0;
        box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
      }

      // 设置表格内容的层级
      .ant-table-content {
        z-index: 1;
      }

      // 确保滚动区域正确显示
      .ant-table-body {
        overflow-x: auto !important;
        overflow-y: auto !important;
      }

      // 固定列不换行
      .ant-table-cell-fix-left,
      .ant-table-cell-fix-right {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .ant-table-row {
        height: 24px !important;
      }

      // 表头固定列不换行
      .ant-table-thead {

        th.ant-table-cell-fix-left,
        th.ant-table-cell-fix-right {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .reg-body {
    .search-form {
      .search-row {
        .search-item {
          min-width: calc(32.33% - 40px);
        }
      }
    }
  }
}

@media screen and (max-width: 1366px) {
  .reg-body {
    .search-form {
      .search-row {
        .search-item {
          min-width: calc(50% - 16px);
        }
      }
    }
  }
}

@media screen and (max-width: 1024px) {
  .reg-body {
    .header-tools {

      .left,
      .right {
        width: 100%;
        justify-content: space-between;
      }
    }

    .search-form {
      .search-row {
        .search-item {
          min-width: 100%;
        }
      }
    }

    .table-footer {
      flex-direction: column;
      text-align: center;

      .total-info {
        width: 100%;
      }
    }
  }
}

// 表格响应式
:deep(.ant-table) {
  .ant-table-content {
    overflow-x: auto;
  }

  @media screen and (max-width: 1024px) {
    .ant-table-cell {
      white-space: nowrap;
    }
  }
}

// 固定列样式
:deep(.ant-table) {
  .ant-table-body {
    overflow-x: auto;
    overflow-y: auto;
  }

  .ant-table-fixed-left,
  .ant-table-fixed-right {
    background: #fff;
    box-shadow: none; // 移除原有阴影
    z-index: 2; // 提高固定列的层级
  }

  // 确保表格内容正确显示
  .ant-table-content {
    z-index: 0;
  }

  // 修复固定列单元格层级
  .ant-table-cell-fix-left,
  .ant-table-cell-fix-right {
    z-index: 2 !important;
    background: #fff !important;
  }

  // 修复表头固定列层级
  .ant-table-thead th.ant-table-cell-fix-left,
  .ant-table-thead th.ant-table-cell-fix-right {
    z-index: 3 !important;
  }
}
.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>
