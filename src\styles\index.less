/** 全局样式 */
@import 'ant-design-vue/es/message/style/index.less';
@import 'ele-admin-pro/es/style/nprogress.less';
@import 'ele-admin-pro/es/style/display.less';
@import 'ele-admin-pro/es/style/common.less';
@import './transition/index.less';

// 主题
@import 'ele-admin-pro/es/style/themes/dynamic.less';

@import url(//at.alicdn.com/t/font_3190923_g95c1a9wwwo.css);

// 定义字体
// @font-face {
//   font-family: 'HarmonyOS Sans SC';
//   src: url('/fonts/HarmonyOS_Regular.woff2') format('woff2');
//   font-weight: normal;
//   font-style: normal;
//   font-display: swap;
// }

// @font-face {
//   font-family: 'HarmonyOS';
//   src: url('/public/fonts/HarmonyOS_Sans_SC_Regular.woff2') format('woff2');
//   font-weight: normal;
//   font-style: normal;
//   font-display: swap;
// }

@font-face {
  font-family: 'Alimama FangYuanTi VF';
  src: url('/public/fonts/AlimamaFangYuanTiVF-Thin.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

.header-title {
  font-family: 'Alimama FangYuanTi VF', sans-serif,
}

.ant-btn {
  border-radius: 4px !important;
}

/* 需要覆盖框架样式变量写最下面, 具体请到文档查看 */
/*搜索框与表格之间的间隙*/
.block-interval {
  margin-bottom: 10px;
}

.left-menu-height {
  height: calc(90vh - 12px);
  overflow: auto;
}

.table-height {
  min-height: calc(90vh - 108px);
}

// 搜索框内元素间隔
.search-bar-margin-left {
  margin-left: 1em;
}


.ant-menu {
  background: transparent !important;
}

.ele-body {
  background: transparent !important;
  /* 移除内容区域背景 */
  padding: 0px 16px !important;
  height: calc(100vh - 100px);
  overflow: auto;
}

.ele-body1 {
  background: transparent !important;
  /* 移除内容区域背景 */
  padding: 0px 16px !important;
  height: unset !important;
}

// 如果子菜单展开时也需要透明
.ant-menu-submenu-popup {
  background: #fff !important;
}

.ant-tabs-tab-active {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid #fff;
}

.ant-menu-submenu-selected {
  color: black !important;
}

.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: transparent !important;

  // &::before {
  //   content: '';
  //   position: absolute;
  //   left: 49px;  // 调整蓝点位置
  //   top: 50%;
  //   transform: translateY(-50%);
  //   width: 6px;
  //   height: 6px;
  //   border-radius: 50%;
  //   background: #1890ff;
  // }
}

.ant-menu-vertical .ant-menu-item::after,
.ant-menu-vertical-left .ant-menu-item::after,
.ant-menu-vertical-right .ant-menu-item::after,
.ant-menu-inline .ant-menu-item::after {
  border-right: transparent !important;
}

.ant-menu-vertical .ant-menu-submenu,
.ant-menu-vertical-left .ant-menu-submenu,
.ant-menu-vertical-right .ant-menu-submenu,
.ant-menu-inline .ant-menu-submenu {
  padding-bottom: 11.02px !important;
}


.ant-card {
  background: rgba(255, 255, 255, 0.4) !important;
  box-sizing: border-box !important;
  border: 1px solid #FFFFFF !important;
  backdrop-filter: blur(398px) !important;
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1) !important;
}

.ant-table {
  background: transparent !important;
}

.ant-table-cell-fix-left,
.ant-table-cell-fix-right {
  background: #ECF4FE !important;
}


// .ant-table-fixed-right {
//   border-left: 2px solid black !important;
// }

.ant-table-thead>tr>th {
  background: #DCECFF !important;
}

.block-interval {
  //display: none;
}


.ant-input:focus,
.ant-input-focused {
  border-color: var(--primary-color-hover);
  box-shadow: 0 0 0 1px var(--primary-color-outline) !important;
  border-right-width: 1px !important;
  outline: 0;
}

.ant-input-affix-wrapper-focused {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px #1890ff !important;

}

.ant-input-affix-wrapper {
  border-radius: 4px !important;
}


.ant-drawer-header {
  background: transparent !important;
}


.notice-item {}

.ant-input-number {
  width: 100% !important;
}

.ant-tabs {
  line-height: 0.5715 !important;
}

// 全局设置鸿蒙字体
body {
  font-family: "HarmonyOS Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" !important;
}

// 确保 ant-design-vue 组件也使用鸿蒙字体
:root {
  --ant-font-family: "HarmonyOS Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" !important;
}


// 全局滚动条样式
*::-webkit-scrollbar {
  width: 1px !important; // 垂直滚动条宽度
  height: 1px !important; // 水平滚动条高度
}

*::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1) !important; // 更淡的滚动条颜色
  border-radius: 2px !important; // 圆角

  &:hover {
    background: rgba(0, 0, 0, 0.2) !important; // 悬停时稍微深一点
  }
}

*::-webkit-scrollbar-track {
  background: transparent !important; // 轨道透明
}

// 针对火狐浏览器的滚动条样式
// * {
//   scrollbar-width: thin !important; // 细滚动条
//   scrollbar-color: rgba(255, 0, 0, 0.1) transparent !important; // 滚动条颜色和轨道颜色
// }

// 覆盖特定组件的滚动条样式
.ele-body,
.custom-menu,
.menu-container,
.ant-table-body,
.ant-modal-body,
.ant-drawer-body {
  &::-webkit-scrollbar {
    width: 1px !important;
    height: 1px !important;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1) !important;
    border-radius: 2px !important;
  }

  &::-webkit-scrollbar-track {
    background: transparent !important;
  }

  .ant-tabs-tab {
    font-size: 16px !important;
  }


}

.approvalFont {
  color: #606266;
}

.ant-tree {
  background: transparent !important;
  height: cal(100vh - 40px) !important;
  overflow-y: auto !important;
}

.ant-tabs-tab-active{
    border: none !important;
}

// 响应式布局变量
:root {
  // 基于视口高度的变量
  --vh-unit: 1vh; // 1% 视口高度
  --row-height-large: calc(3vh + 30px); // 大屏幕行高
  --row-height-medium: calc(2.5vh + 20px); // 中等屏幕行高
  --row-height-small: calc(2vh + 15px); // 小屏幕行高

  // 基于视口宽度的变量
  --cell-padding-h: calc(1vw + 5px); // 水平内边距
  --cell-padding-v: calc(0.5vh + 5px); // 垂直内边距

  // 容器尺寸
  --container-width: min(100%, 1920px);
  --container-padding: calc(1vw + 10px);
}

// 高度自适应类
.height-adaptive {
  height: calc(100vh - 120px); // 减去固定元素高度
  overflow-y: auto;
}

// 表格自适应样式
.table-adaptive {

  // 大屏幕样式（默认）- 1920*1200 及以上
  @media screen and (min-width: 1920px) and (min-height: 1080px) {
    .ant-table-cell {
      padding: var(--cell-padding-v) var(--cell-padding-h) !important;
      height: var(--row-height-large) !important;
    }

    .ant-table-row {
      height: var(--row-height-large) !important;
    }
  }

  // 1920*1080 专用样式
  @media screen and (min-width: 1920px) and (max-height: 1080px) {
    .ant-table-cell {
      padding: calc(0.8vh + 4px) var(--cell-padding-h) !important;
      height: calc(2.8vh + 25px) !important;
    }

    .ant-table-row {
      height: calc(2.8vh + 25px) !important;
    }
  }
}

.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border-color: var(--primary-color-hover);
  //box-shadow: 0 0 0 1px var(--primary-color-outline) !important;
  border-right-width: 1px !important;
  outline: 0;
}

.ant-picker-focused {
  border-color: var(--primary-color-hover);
  box-shadow: 0 0 0 1px var(--primary-color-outline) !important;
  border-right-width: 1px !important;
  outline: 0;
}

.ant-input-affix-wrapper-focused {
  /* border-color: #1890ff !important; */
  box-shadow: 0 0 0 1px var(--primary-color-outline) !important;
}

.ant-input-number-focused {
  /* border-color: #1890ff !important; */
  box-shadow: 0 0 0 1px var(--primary-color-outline) !important;
}

/* 旋转下拉箭头 */
.ant-select-arrow {
  transform: rotate(0deg);
  transition: transform 0.3s;
}

/* 当展开时恢复原始方向（可选） */
.ant-select-open .ant-select-arrow {
  transform: rotate(180deg);
}

.ant-select-open,ant-select-show-search{
  .ant-select-arrow{
  transform: rotate(0deg) !important;
}
}

.ant-table-tbody>tr>td {
  border-bottom: 1px solid #DAE4EF !important;
  transition: background 0.3s;
}

.ant-pagination-item-active {
  border-radius: 4px !important;
  background: #0085FF !important;
  border-width: 0px !important;

  a {
    color: #FFFFFF !important;
  }
}

.ant-pagination-item {
  border-radius: 4px !important;
}

.ant-pagination-item-link {
  border-radius: 4px !important;
}

//表格左侧阴影
.ant-table-ping-left:not(.ant-table-has-fix-left) .ant-table-container::before {
  box-shadow: inset 6px 0 8px -8px var(--shadow-color) !important;
}

//表格左侧阴影
.ant-table-ping-right:not(.ant-table-has-fix-right) .ant-table-container::after {
  box-shadow: inset -6px 0 8px -8px var(--shadow-color) !important;
}

.amap-info-content {
  background: transparent !important;
}

.amap-info-close {
  display: none !important;
}

.ant-table-tbody {
  .ant-empty-normal .ant-empty-image {
    display: none !important;
  }

  .ant-empty-description {
    display: none;
  }
}

.scale-wrapper {
  width: 1920px;
  height: 1080px;
  transform-origin: left top;
  position: absolute;
  left: 0;
  top: 0;
}

.custom-empty {
  width: 100%;
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;

  img {
    position: fixed;
  }
}


.custom-emptyLeft {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  min-width: 0;
  min-height: 120px;
  padding-left: 300px;

  img {
    width: 196px;
    height: 180px;
    display: block;
  }
}

.ant-picker-dropdown-range {
  padding: 0 !important;
}

.ant-picker-range-arrow {
  position: absolute;
  top: 1px;
  right: 1px;
  width: 10px;
  height: 10px;
  border: 5px solid #f0f0f0;
  border-color: #fff #fff transparent transparent;
  content: '';
  display: none !important;
}


//360代码优化

// 360浏览器滚动条兼容性修复
// 全局滚动条样式，确保在360浏览器中可见
.ele-body::-webkit-scrollbar,
.custom-menu::-webkit-scrollbar,
.menu-container::-webkit-scrollbar,
.ant-table-body::-webkit-scrollbar,
.ant-modal-body::-webkit-scrollbar,
.ant-drawer-body::-webkit-scrollbar {
  width: 0.45rem !important; // 垂直滚动条宽度
  height: 0.45rem !important; // 水平滚动条高度
}

/deep/ *::-webkit-scrollbar {
  width: 0.25rem !important;
  height: 0.25rem !important;
}

*::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3) !important; // 滚动条颜色
  border-radius: 4px !important;
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5) !important; // 悬停时更深的颜色
}

*::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05) !important; // 轻微可见的轨道
}

// 表格滚动条兼容性修复
.ant-table-content {
  overflow-x: auto !important;
  min-width: 100% !important;
}

.ant-table-body {
  overflow-x: auto !important;
  overflow-y: auto !important;
  min-width: 100% !important;
}

.ant-table-container {
  overflow-x: auto !important;
}

// 确保表格容器允许滚动
.custom-table {
  overflow-x: auto !important;
  width: 100% !important;
}

.ant-table-cell-scrollbar {
  box-shadow: 0px !important;
}


// .ant-table-tbody > tr.ant-table-row:hover > td),
// :deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
//   background-color: #FFFFFF !important;
// }




