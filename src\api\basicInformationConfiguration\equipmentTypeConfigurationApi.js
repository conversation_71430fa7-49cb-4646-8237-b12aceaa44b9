import Request from '@/utils/request-util';

/**
 * 机构管理的api
 *
 * <AUTHOR>
 * @date 2021/4/8 11:43
 */
export class equipmentTypeConfigurationApi {
  /**
   * 获取组织机构界面
   *
   * <AUTHOR>
   * @date 2022/5/20 17:18
   */
  static organizationTreeList(params) {
    return Request.get('/apiBus/equipmentClassificationCode/tree', params);
  }

  /**
   * 公司树列表
   *
   * <AUTHOR>
   * @date 2022/5/20 17:18
   */
  static companyTreeList(params) {
    return Request.get('/hrOrganization/companyTree', params);
  }

  /**
   * 树
   *
   * <AUTHOR>
   * @date 2021/4/8 11:43
   */
  static tree(params) {
    return Request.get('/hrOrganization/roleBindOrgScopeAntdv', params);
  }

  /**
   * 新增
   *
   * <AUTHOR>
   * @date 2021/4/8 11:43
   */
  static add(params) {
    return Request.post('/apiBus/equipmentClassificationCode/add', params);
  }

  /**
   * 删除
   *
   * <AUTHOR>
   * @date 2021/4/8 11:45
   */
  static del(params) {
    return Request.post('/hrOrganization/delete', params);
  }

  /**
   * 批量删除
   *
   * <AUTHOR>
   * @date 2021/4/8 11:45
   */
  static batchDel(params) {
    return Request.post('/hrOrganization/batchDelete', params);
  }

  /**
   * 修改
   *
   * <AUTHOR>
   * @date 2021/4/8 11:45
   */
  static edit(params) {
    return Request.post('/apiBus/equipmentClassificationCode/edit', params);
  }

  /**
   * 查看详情
   *
   * <AUTHOR>
   * @date 2021/4/8 11:46
   */
  static detail(params) {
    return Request.get('/apiBus/equipmentClassificationCode/detail', params);
  }

  /**
   * 更新状态
   *
   * <AUTHOR>
   * @date 2021/4/8 13:41
   */
  static updateStatus(params) {
    return Request.post('/hrOrganization/updateStatus', params);
  }

  /**
   * 获取最近选择的条件
   *
   * <AUTHOR>
   * @date 2021/4/8 13:41
   */
   static selector(params) {
    return Request.get('/sysUser/selectorAll', params);
  }
}
