/**
    使用方法
    <new-input v-model:value="record.demandPlanRemark" :placeholder="placeholder" :maxlength="maxlength" :autoSize="autoSize"/>

    参数说明：
    1. record.demandPlanRemark 输入框的值
    2. placeholder 输入框的占位符
    3. maxlength 输入框的最大长度
    4. autoSize 文本域的自动高度
    示例：
    const placeholder = '请输入'
    const maxlength =  30
    const autoSize = { minRows: 1, maxRows: 4 }
*/



<template>
  <div class="switchable-input">
    <!-- 将 v-model:value 绑定到本地变量 currentValue -->
    <a-input :disabled="disabled" v-model:value="currentValue" :placeholder="placeholder" :class="{ 'hidden': isTextarea }"
      @click="activateTextarea" :maxlength="maxlength"/>
    <a-textarea :disabled="disabled" ref="textareaRef" v-model:value="currentValue" :placeholder="placeholder"
      :auto-size="autoSize" :class="{ 'hidden': !isTextarea }"  @blur="deactivateTextarea" :maxlength="maxlength"/>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';  // 引入 watch

const props = defineProps({
  value: {
    type: String,
  },
  placeholder:{
    type: String,
    default: '请输入',
  },
  maxlength:{
    type: Number,
  },
  autoSize:{
    type: Object,
    default: { minRows: 1, maxRows: 4 },
  },
  disabled:{
    type: Boolean,
    default: false,
  },
});

// 定义本地响应式变量，初始值为 props.value
const currentValue = ref(props.value);
// 定义更新事件
const emit = defineEmits(['update:value']);

// 监听 prop 变化，同步到本地变量
watch(() => props.value, (newVal) => {
  currentValue.value = newVal;
});

// 监听本地变量变化，通知父组件更新
watch(currentValue, (newVal) => {
  emit('update:value', newVal);
});

// ... 以下为原有代码（无需修改）
const isTextarea = ref(false);
const textareaRef = ref(null);

const activateTextarea = () => {
  isTextarea.value = true;
  setTimeout(() => {
    textareaRef.value?.focus();
  }, 0);
};

const deactivateTextarea = () => {
  isTextarea.value = false;
};
</script>

<style scoped>
.switchable-input {
  position: relative;
  width: 150px;
}

.hidden {
  position: absolute;
  opacity: 0;
  pointer-events: none;
  display: none;
}

.a-input,
.ant-input-textarea {
  transition: all 0.3s ease;
}
</style>
