// 页面内按钮过滤
import store from '@/store/index';
import { useUserStore } from '@/store/modules/user';

export default {
  mounted: function (el, binding) {
    let useUser = useUserStore()
    // console.log('useUserStore', useUser);
    // console.log('useUserStore', useUserStore().authorities);
    // console.log('useUserStore().roles',useUserStore().roles);

    //如果是超级管理员，则拥有所有按钮的权限
    if (useUserStore().roles.includes("superAdmin")) {
      return true;
    }
    //如果非超级管理员，则需要判断用户的权限列表中是否包含该权限，
    //authorities，在用户登录成功后进入index.vue时进行了获取赋值，binding.value为 v-privilege后面跟的按钮编码
    if (!useUser.authorities.includes(binding.value)) {
      el.parentNode.removeChild(el);
    }
  }
};
