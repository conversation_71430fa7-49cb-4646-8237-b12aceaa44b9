<template>
  <div class="acceptance-form">
    <h1 class="title">设备入账验收转资单</h1>
    
    <!-- 申请信息部分 -->
    <div class="section">
      <div class="section-header">
        <div class="blue-bar"></div>
        <span>申请信息</span>
      </div>
      
      <div class="info-grid">
        <div class="info-item">
          <span class="label">产权单位</span>
          <span class="value">机电处</span>
        </div>
    
        <div class="info-item">
          <span class="label">申请单位</span>
          <span class="value">中铁第三建设（集团）有限责任公司机电处</span>
        </div>
      </div>

      <div class="business-title">
        <span class="required">*</span>
        <span class="label">业务标题</span>
        <a-input class="input" placeholder="请输入业务标题" maxlength="16"/>
      </div>
    </div>

    <!-- 设备信息部分 -->
    <div class="section">
      <div class="section-header">
        <div class="blue-bar"></div>
        <span>设备信息</span>
      </div>

      <div class="steps">
        <div class="step">
          <div class="step-number">1</div>
          <div class="step-content">
            <div class="step-title">上传文件</div>
            <div class="step-desc">通过Excel文件导入设备数据</div>
          </div>
        </div>
        <div class="step">
          <div class="step-number">2</div>
          <div class="step-content">
            <div class="step-title">匹配表头</div>
            <div class="step-desc">将表格中的表头与系统内的字段匹配</div>
          </div>
        </div>
        <div class="step">
          <div class="step-number">3</div>
          <div class="step-content">
            <div class="step-title">导入数据</div>
            <div class="step-desc">将文件中的设备数据导入系统</div>
          </div>
        </div>
      </div>

      <div class="download-hint">
        <div class="left">
          <check-circle-outlined class="check-icon" />
          <span>下载导入模板，根据模板快速录入内容</span>
        </div>
        <a-button type="link" class="download-btn">
          <download-outlined />
          立即下载
        </a-button>
      </div>

      <div class="upload-area">
        <div class="upload-content">
          <folder-outlined class="folder-icon" />
          <div class="upload-text">选择文件</div>
          <div class="upload-desc">下载模板并按要求填写，单次上传不超过5MB的XLSX文件</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { CheckCircleOutlined, DownloadOutlined, FolderOutlined } from '@ant-design/icons-vue';
</script>

<style lang="less" scoped>
.acceptance-form {
  padding: 24px;
  background: #f5f7fa;

  .title {
    font-size: 20px;
    color: #1e2329;
    text-align: center;
    margin-bottom: 24px;
  }

  .section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 24px;

      .blue-bar {
        width: 4px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
      }

      span {
        font-size: 16px;
        color: #1e2329;
      }
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-bottom: 20px;

    .info-item {
      display: flex;
      align-items: center;

      .label {
        width: 80px;
        color: #666;
      }

      .value {
        color: #333;
      }
    }
  }

  .business-title {
    display: flex;
    align-items: center;

    .required {
      color: #ff4d4f;
      margin-right: 4px;
    }

    .label {
      width: 80px;
      color: #666;
    }

    .input {
      width: 400px;
    }
  }

  .steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;

    .step {
      display: flex;
      align-items: flex-start;

      .step-number {
        width: 24px;
        height: 24px;
        background: #1890ff;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
      }

      .step-content {
        .step-title {
          font-size: 14px;
          color: #333;
          margin-bottom: 4px;
        }

        .step-desc {
          font-size: 12px;
          color: #999;
        }
      }
    }
  }

  .download-hint {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    padding: 12px 16px;
    border-radius: 4px;
    margin-bottom: 16px;

    .left {
      display: flex;
      align-items: center;

      .check-icon {
        color: #52c41a;
        margin-right: 8px;
      }
    }

    .download-btn {
      color: #1890ff;
      padding: 0;

      .anticon {
        margin-right: 4px;
      }
    }
  }

  .upload-area {
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    
    .upload-content {
      padding: 32px;
      text-align: center;
      cursor: pointer;

      &:hover {
        border-color: #1890ff;
      }

      .folder-icon {
        font-size: 32px;
        color: #1890ff;
        margin-bottom: 16px;
      }

      .upload-text {
        font-size: 16px;
        color: #333;
        margin-bottom: 8px;
      }

      .upload-desc {
        font-size: 12px;
        color: #999;
      }
    }
  }
}
</style>