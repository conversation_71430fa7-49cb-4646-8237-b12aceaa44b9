<template>
  <div class="ele-body1">
    <div class="equipment-acceptance">
      <div class="status-icon" v-if="bussWorksheet && bussWorksheet.status !== undefined">
        <img :src="getStatusImage(bussWorksheet.status)" :alt="getStatusText(bussWorksheet.status)"
          class="status-image" />
      </div>

      <div class="form-title">设备出库单</div>

      <!-- 申请信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          申请信息
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">申请人</div>
            <div class="approvalFont">{{ formData.applyUserStr }}</div>
          </div>
          <div class="form-item">
            <div class="label">申请单位</div>
            <div class="approvalFont">{{ formData.applyOrgStr }}</div>
          </div>
          <div class="form-item">
            <div class="label">申请日期</div>
            <div class="approvalFont">{{ formData.applyDate }}</div>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">产权单位</div>
            <div class="approvalFont">{{ formData.propertyOrgStr }}</div>
          </div>
          <div class="form-item">
            <div class="label">业务标题</div>
            <div class="approvalFont">{{ formData.applyTitle }}</div>
          </div>
          <div class="form-item">
            <div class="label">单据确认日期</div>
            <div class="approvalFont">{{ formData.confirmDate }}</div>
          </div>
        </div>
      </div>

      <!-- 出库信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          出库信息
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">发货单位</div>
            <div class="approvalFont">{{ formData.sendOrgStr }}</div>
          </div>
          <div class="form-item">
            <div class="label">使用单位</div>
            <div class="approvalFont">{{ formData.sendUseOrgStr }}</div>
          </div>
          <div class="form-item">
            <div class="label">管理单位</div>
            <div class="approvalFont">{{ formData.sendManagementOrgStr }}</div>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">收货单位</div>
            <div class="approvalFont">{{ formData.receiveOrgStr }}</div>
          </div>
          <div class="form-item">
            <div class="label">使用单位</div>
            <div class="approvalFont">{{ formData.receiveUseOrgStr }}</div>
          </div>
          <div class="form-item">
            <div class="label">管理单位</div>
            <div class="approvalFont">{{ formData.receiveManagementOrgStr }}</div>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">发货人</div>
            <div class="approvalFont">{{ formData.sendUserStr }}</div>
          </div>
          <div class="form-item">
            <div class="label">提货人/验收人</div>
            <div class="approvalFont">{{ formData.receiveUserStr }}</div>
          </div>
          <div class="form-item">
            <div class="label ">预计提货日期</div>
            <div class="approvalFont">{{ formData.expectedDeliveryDate }}</div>
          </div>

        </div>
        <div class="form-grid">
          <div class="form-item">
            <div class="label">预计出库日期</div>
            <div class="approvalFont">{{ formData.expectedOutboundDate }}</div>
          </div>
        </div>
      </div>

      <!-- 设备信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          设备明细
        </div>
        <a-table   v-if="!loading" :columns="columns" :data-source="bussEquipmentProcessTrackingList" bordered :pagination="false"
          :scroll="{ x: 'max-content' }">
          <template #emptyText>
            <div class="custom-empty">
              <img src="@/assets/images/noData.png" />
              <!-- <p>抱歉，暂时还没有数据</p> -->
            </div>
          </template>
          <template #bodyCell="{ column, index, record }">
          </template>
        </a-table>
      </div>

      <!-- 附件 -->
      <div class="section">

        <div class="upload-section">
          <div class="file-list">
            <template v-if="formData.fileList && formData.fileList.length > 0">
              <div v-for="(file, index) in formData.fileList" :key="file.fileId" class="file-item">
                <div class="file-icon">
                  <img src="/public/icon/icon-wrapper.svg" v-if="file.fileOriginName.endsWith('.xls') || file.fileOriginName.endsWith('.xlsx')" />
                  <img src="/public/icon/word.svg" v-else-if="file.fileOriginName.endsWith('.doc') || file.fileOriginName.endsWith('.docx')" />
                  <img src="/public/icon/pdf.svg" v-else-if="file.fileOriginName.endsWith('.pdf')" />
                  <img src="/public/icon/mp4.svg" v-else-if="file.fileOriginName.endsWith('.mov') || file.fileOriginName.endsWith('.mp4')" />
                  <img src="/public/icon/ppt.svg" v-else-if="file.fileOriginName.endsWith('.ppt') || file.fileOriginName.endsWith('.pptx')" />
                  <img src="/public/icon/jpg.svg" v-else-if="file.fileOriginName.endsWith('.png') || file.fileOriginName.endsWith('.jpg') || file.fileOriginName.endsWith('.jpeg') || file.fileOriginName.endsWith('.heif')" />
                  <img src="/public/icon/reader.svg" v-else />
                </div>
                <a :href="`${file.fileUrl}?filename=${file.fileOriginName}`"  target="_blank" class="file-link">
                  {{ file.fileOriginName }}
                </a>
                <sync-outlined v-if="file.status === 'loading'" class="loading-icon" spin />
              </div>
            </template>
            <!-- <div v-else class="no-files">暂无附件</div> -->
          </div>
        </div>
      </div>

      <!-- 审批说明 -->
      <div class="bottom-buttons">
        <a-button v-if="bussWorksheet?.isRevoke" type="primary" @click="handleCancel" :loading="submitting"
          class="submit-btn">
          撤回申请
        </a-button>
        <a-button v-if="bussWorksheet?.status === -1 || bussWorksheet?.status === 100" type="primary" @click="handleTwice"
          :loading="submitting" class="submit-btn">
          再次申请
        </a-button>
      </div>
    </div>

  </div>
</template>

<script setup>

import { ref, onMounted, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { warehouseApi } from '@/api/dynamic/warehouseApi';
import { FileUploadUrl } from '@/api/system/operation/FileApi';
import { getToken } from '@/utils/token-util';
import { API_BASE_PREFIX } from '@/config/setting';
import { useRouter } from 'vue-router';
import { EquipmentAcceptanceApi } from '@/api/buss/EquipmentAcceptanceApi';
import { equipmentScrappingApi } from '@/api/buss/equipmentScrappingApi';
import { UnitApi } from '@/api/common/UnitApi'
const router = useRouter();

// 引入状态图片
import cancelImg from '@/assets/equipment/quxiao.png';
import passedImg from '@/assets/equipment/tongguo.png';
import rejectedImg from '@/assets/equipment/jujue.png';

// 获取状态图片
const getStatusImage = (status) => {
  switch (status) {
    case -1: // 已取消
      return cancelImg;
    case 99: // 已通过
      return passedImg;
    case 100: // 未通过
      return rejectedImg;
    default:
      return '';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case -1:
      return '已取消';
    case 99:
      return '已通过';
    case 100:
      return '未通过';
    default:
      return '';
  }
};

const loading = ref(false);
const saving = ref(false);
const submitting = ref(false);

// 业务标题和关联投资计划的独立字段
const businessTitle = ref('');
const investmentPlan = ref('');
const isTransfer = ref('1'); // 添加是否转入设备字段，默认为'1'（否）

// 上传相关配置
const uploadUrl = `${API_BASE_PREFIX}${FileUploadUrl}?secretFlag=0`;
const headers = {
  Authorization: getToken()
};

const route = useRouter();
// 定义路由参数
const processDefinitionId = ref('');
const actId = ref('');
const taskId = ref('');
const procInsId = ref('');
const worksheetId = ref('');
const props = defineProps({
  formData: {
    type: Object,
    default: () => ({})
  }
});
const options = ref([])
//初始化枚举数据
const getManagementOrg = async (id) => {
  try {
    // 获取管理单位
    const gldwlist = await UnitApi.getUserListByOrg(id);
    options.value = gldwlist;
    console.log('gldwlist-----', gldwlist)
  } catch (error) {
    // message.error('获取枚举数据失败');
  }
};
const handleOrgChange = async (value) => {
  console.log(`selected ${value}`);
};
const handleBlur = () => {
  console.log('blur');
};
const handleFocus = () => {
  console.log('focus');
};
const filterOption = (input, option) => {
  console.log('input', input);
  console.log('option', option);
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
const bussWorksheet = ref()
const alldata = ref()
const initializeData = async (id) => {
  loading.value = true;
  try {
    const { data } = await warehouseApi.getToDo({ procInstanceId: id });
    console.log('获取的数据：', data);
    if (data.bussWorksheet) {
      bussWorksheet.value = data.bussWorksheet;
    }
    // 处理业务标题和关联投资计划
    if (data.bussTransferForm) {
      formData.value = data.bussTransferForm
      datalist.value = data.bussTransferForm
      console.log('formData.value.sendOrg', formData.value.sendOrg);
      getManagementOrg(formData.value.sendOrg)
      // businessTitle.value = data.bussTransferForm.applyTitle || '';
      // investmentPlan.value = data.bussTransferForm.investmentPlan || '';
      // isTransfer.value = data.bussTransferForm.isTransfer || '1'; // 使用'1'作为默认值
    }

    // 处理设备基本信息
    if (data.bussEquipmentProcessTrackingList?.length > 0) {
      bussEquipmentProcessTrackingList.value = data.bussEquipmentProcessTrackingList;
    }
    alldata.value = {
      bussTransferForm: data.bussTransferForm,
      bussWorksheet: data.bussWorksheet,
      bussEquipmentProcessTrackingList: data.bussEquipmentProcessTrackingList,
    }
    console.log('alldata.value', alldata.value)

  } catch (error) {
    message.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

const KeyId = ref()

const getProcessDefinitionByKey = async () => {
  try {

    const id = await equipmentScrappingApi.getProcessDefinitionByKey({ key: 'equipment_outbound' });
    KeyId.value = id.data.id
  } catch (error) {

  }
}
const datalist = ref({
  handledUserStr: '张散'
})
const bussEquipmentProcessTrackingList = ref()
getProcessDefinitionByKey()
// if (id) {
//   initializeData(id);
// } else {
//   message.error('未找到审批单ID');
// }

// 文件列表相关
const fileList = ref([]);
const fileViewList = ref([]);


// 处理普通附件上传
const handleChange = (info) => {
  console.log('当前上传的文件:', info.file);
  console.log('所有文件列表:', info.fileList);

  // 保持原始fileList与上传组件同步
  fileList.value = info.fileList;

  // 更新显示用的文件列表
  fileViewList.value = info.fileList
    .filter(file => file.status === 'done')  // 只保留上传完成的文件
    .map(file => ({
      fileId: file.response.data.fileId, fileUrl: file.response.data.fileUrl,
      fileName: file.name,
      uid: file.uid,
      status: file.status
    }));

  // 处理单个文件的状态提示
  if (info.file.status === 'error') {
    message.error('上传出错：' + info.file.error.message);
  }
};


// 删除普通附件
const handleRemove = (file) => {
  // 从显示列表中删除
  const viewIndex = fileViewList.value.findIndex(f => f.uid === file.uid);
  if (viewIndex > -1) {
    fileViewList.value.splice(viewIndex, 1);
  }

  // 从上传列表中删除
  const fileIndex = fileViewList.value.findIndex(f => f.uid === file.uid);
  if (fileIndex > -1) {
    fileViewList.value.splice(fileIndex, 1);
  }
};


// 验证表单数据
const validateForm = () => {
  // 按顺序定义需要验证的字段
  const fieldsToValidate = [
    { field: 'expectedOutboundDate', label: '预计出库日期', type: 'date' },
  ];

  // 依次验证每个字段
  for (const { field, label, type } of fieldsToValidate) {
    if (!formData.value[field]) {
      message.error(`请${type === 'select' ? '选择' : type === 'date' ? '选择' : '输入'}${label}`);

      const formItem = document.querySelector(`[data-field="${field}"]`);
      if (formItem) {
        // 滚动到可视区域，并确保元素在视图中间
        formItem.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
        const tableBody = document.querySelector('.ant-table-body');
        if (tableBody) {
          // 方式一：直接滚动到最右边
          tableBody.scrollTo({
            left: tableBody.scrollWidth,  // scrollWidth 是内容的总宽度
            behavior: 'smooth'
          });
        }
        // 使用 nextTick 确保 DOM 更新后再执行点击操作
        nextTick(() => {
          setTimeout(() => {
            switch (type) {
              case 'input': {
                const input = formItem.querySelector('input');
                input?.focus();
                break;
              }
              case 'select': {
                const select = formItem.querySelector('.ant-select-selector');
                select?.click();
                break;
              }
              case 'date': {
                const datePicker = formItem.querySelector('.date-picker');
                if (datePicker) {
                  // 先聚焦
                  const input = datePicker.querySelector('input');
                  input?.focus();
                  // 然后触发点击以打开日期选择面板
                  setTimeout(() => {
                    datePicker.click();
                  }, 100);
                }
                break;
              }
            }
          }, 500); // 等待滚动完成后再聚焦
        });
      }
      return false;
    }
  }
  return true;
};

const comment = ref()
// 提交方法
const handleSubmit = async (isApprove) => {
  try {
    // 执行验证
    if (!validateForm()) {
      return; // 验证不通过直接返回,不执行提交
    }

    submitting.value = true;

    // 构建内部formData结构
    // 构建内部formData结构
    const innerFormData = {
      formDatas: {
        approve: isApprove.toString(), // 转换为字符串的 "true" 或 "false"
        bussWorksheet: {
          id: worksheetId.value
        },
        bussTransferForm: {
          receiveUser: formData.value.receiveUser
        }
      }
    };
    // 构建最终提交数据结构
    const submitData = {
      processDefinitionId: processDefinitionId.value,
      taskId: taskId.value,
      variables: {
        formData: JSON.stringify(innerFormData)
      },
      comment: comment.value
    };

    const res = await warehouseApi.submit(submitData);

    if (res.success) {
      message.success('提交成功');
      router.push('/dynamic/warehouse');
    } else {
      message.error(res.message || '提交失败');
    }
  } catch (error) {
    message.error('提交失败：' + error.message);
  } finally {
    submitting.value = false;
  }
};

const formData = ref({
  id: null,
  applyUser: null, // 申请人ID
  applyUserStr: null, // 申请人
  applyOrg: null, // 申请单位ID
  applyOrgStr: null, // 申请单位
  applyDate: null, // 申请日期
  applyTitle: null,//业务标题
  propertyOrg: null, // 产权单位ID
  propertyOrgStr: null, // 产权单位
  sendOrg: null, // 发货单位ID
  sendOrgStr: null, // 发货单位
  sendUseOrg: null,// 发货使用单位ID
  sendUseOrgStr: null,//发货使用单位
  receiveOrg: null,//收货单位
  receiveOrgStr: null,
  receiveUseOrg: null,//收货使用单位
  receiveUseOrgStr: null,
  sendUser: null,//收货人
  sendUserStr: null,
  receiveUser: null,
  receiveUserStr: null,
  expectedDeliveryDate: null,
  expectedOutboundDate: null,
  sendManagementOrg: null,
  sendManagementOrgStr: null,
  receiveManagementOrg: null,
  receiveManagementOrgStr: null,
  bussEquipmentProcessTrackingList: []

});

// 附件列表数据
const attachments = ref([
  {
    name: '',
    unit: '',
    quantity: 1,
    manufacturer: '',
    serialNo: ''
  }
]);

const columns = [
  {
    title: '序号',
    width: 80,
    customRender: ({ text, record, index }) => {
      return `${index + 1}`;
    }
  },
  { title: '设备编号', dataIndex: 'code', width: 120, },
  { title: '财务卡片编号', dataIndex: 'financialNumber', width: 200, },
  { title: '设备名称', dataIndex: 'equNameStr', width: 200, },
  { title: '规格型号', dataIndex: 'equModelStr', width: 140, },
  { title: '型号备注', dataIndex: 'equModelInfo', width: 250, },
  { title: '管理单位', dataIndex: 'managementOrgStr', width: 200 },
  { title: '存放地点', dataIndex: 'storageLocationStr', width: 250, },
  { title: '使用单位', dataIndex: 'useOrgStr', width: 200, },
  { title: '财务原值', dataIndex: 'financialOriginalValue', width: 120 },
  { title: '净值', dataIndex: 'netWorth', width: 120 },
  { title: '管理状态', dataIndex: 'managementStatusStr', width: 100 },
  { title: '设备状态', dataIndex: 'equConditionStr', width: 100 },
  { title: '生产厂家', dataIndex: 'manufacturer', width: 150 },
  { title: '出厂日期', dataIndex: 'productionDate', width: 120 },
  { title: '出厂编号', dataIndex: 'factoryNumber', width: 120 },
  { title: '设备类别', dataIndex: 'equTypeStr', width: 200 },
  { title: '设备种类', dataIndex: 'equSubTypeStr', width: 200 },
  { title: '设备性质', dataIndex: 'equNatureStr', width: 120 },
  { title: '固定资产分类', dataIndex: 'fixedAssets', width: 160 },
  { title: '产权单位', dataIndex: 'propertyOrgStr', width: 120 },
  { title: '功率kw', dataIndex: 'power', width: 100 },
  { title: '设备型号编码', dataIndex: 'modelCode', width: 160 },
  { title: '购置年度', dataIndex: 'purchaseYear', width: 100 },
  { title: '出库信息备注', dataIndex: 'outboundRemark', width: 250, fixed: 'right' },

]



const handleTwice = async () => {
  route.push({
    path: '/buss/applyTwice/warehouseTwice',
    query: {
      procInsId: bussWorksheet.value.procInstanceId,
      type: bussWorksheet.value.type
    }
  });
}
// 审批记录相关方法
const showApprovalRecord = () => {
  // 处理审批记录的显示逻辑
  console.log('显示审批记录');
};

const handleCancel = async () => {
  try {
    submitting.value = true;

    // 构建最终提交数据结构
    const submitData = {
      id: bussWorksheet.value.procInstanceId,
      key: bussWorksheet.value.type
    };


    const res = await warehouseApi.end(submitData);

    if (res.success) {
      message.success('撤回成功');
      window.close()
    } else {
      message.error('撤回失败');
    }
  } catch (error) {
    message.error('撤回失败');
  } finally {
    submitting.value = false;
  }
};
onMounted(() => {
  // const route = useRouter();
  processDefinitionId.value = props.formData.processDefinitionId;
  actId.value = props.formData.actId;
  taskId.value = props.formData.taskId;
  procInsId.value = props.formData.procInsId;
  worksheetId.value = props.formData.worksheetId;
  if (procInsId.value) {
    initializeData(procInsId.value);
  } else {
    message.error('未找到审批单ID');
  }
});
</script>

<style lang="less" scoped>
.approval-record-btn {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  z-index: 100;

  .bg-image {
    display: block;
    max-width: 100%;
    height: auto;
    object-fit: contain;
    background-color: transparent;
  }

  .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .icon {
      width: 15px;
      height: 15px;
      margin-bottom: 4px;
    }

    .text {
      writing-mode: vertical-lr;
      font-size: 14px;
      color: #333;
      letter-spacing: 2px;
    }
  }
}

.equipment-acceptance {
  width: 100%;
  max-width: 1920px; // 最大宽度限制
  height: calc(100vh - 40px);
  overflow: auto;
  margin: 0 auto; // 居中
  padding: 24px 9.06vw 24px 8.18vw; // 转换为vw: 174/1920*100=9.06, 157/1920*100=8.18
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);

  .form-title {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 24px;
  }

  .section {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;

      .blue-bar {
        width: 3px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
      }
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: clamp(16px, 2vw, 24px);
    margin-bottom: 24px;
    justify-content: space-between;

    .form-item {
      display: flex;
      flex-direction: column;
      min-width: 300px; // 最小宽度
      max-width: 300px; // 最大宽度

      .label {
        height: 32px;
        line-height: 32px;
        margin-bottom: 8px;
        color: #0C0B0B;

        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 4px;
        }
      }

      .value {
        width: 100%;
        height: 32px;
        background: rgba(255, 255, 255, 0.6);
        box-sizing: border-box;
        /* -line-列表 */
        border: 0.5px solid rgba(30, 41, 64, 0.08);

        :deep(.ant-input),
        :deep(.ant-select),
        :deep(.ant-picker),
        :deep(.ant-input-number) {
          width: 100%;
          height: 32px;
        }

        :deep(.ant-input-number-input) {
          height: 16px;
          line-height: 16px;
        }

        :deep(.ant-select-selector) {
          height: 32px !important;

          .ant-select-selection-item {
            line-height: 32px;
          }
        }

        :deep(.ant-picker) {
          .ant-picker-input {
            height: 16px;
            line-height: 16px;

            input {
              height: 16px;
              line-height: 16px;
            }
          }
        }
      }
    }
  }

  .acceptance-no {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    max-width: 300px; // 与其他表单项保持一致的最大宽度

    :deep(.first-input) {
      width: 65% !important; // 验资前面的输入框占65%
    }

    :deep(.second-input) {
      width: 35% !important; // 号前面的输入框占35%
    }

    .separator {
      color: #666;
      white-space: nowrap;
    }
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .equipment-acceptance {
    padding: 24px 6vw;

    .form-grid {
      gap: 16px;
    }
  }
}

@media screen and (max-width: 1366px) {
  .equipment-acceptance {
    padding: 24px 4vw;

    .form-grid {
      grid-template-columns: repeat(2, minmax(300px, 1fr));
    }
  }
}

@media screen and (max-width: 1024px) {
  .equipment-acceptance {
    padding: 24px 2vw;

    .form-grid {
      grid-template-columns: repeat(1, 1fr);

      .form-item {
        max-width: none;
      }
    }
  }
}

.percentage-input {
  position: relative;
  display: flex;
  align-items: center;

  .ant-input {
    width: 100%;
  }

  .percentage-symbol {
    position: absolute;
    right: 12px;
    color: #666;
  }
}

.section {
  .a-table {
    margin-top: 16px;
  }
}

.upload-section {
  .upload-header {
    display: flex;
    align-items: center;
    gap: 12px;

    .upload-button {
      display: inline-flex;
      align-items: center;
      padding: 4px 15px;
      background: #0085FF;
      border-radius: 2px;
      color: white;
      cursor: pointer;
      font-size: 14px;

      .anticon {
        margin-right: 4px;
      }
    }

    .upload-tip {
      color: #999;
      font-size: 12px;
    }
  }

  .file-list {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    //display: grid;
    //grid-template-columns: repeat(3, 0.3fr);
    //gap: 16px;

    .file-item {
      display: flex;
      align-items: center;
      padding: 8px;
      // background: #F7F9FC;
      border-radius: 2px;
      width: 32%;

      .file-link {
        word-break: break-all;
      }

      .file-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #2D5CF6;
      }

      .file-name {
        flex: 1;
        font-size: 14px;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-actions {
        margin-left: 8px;

        .delete-icon {
          color: #999;
          cursor: pointer;

          &:hover {
            color: #FF4B4B;
          }
        }

        .anticon-sync {
          color: #2D5CF6;
        }
      }
    }
  }
}

.tech-file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;

  .file-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      color: #2D5CF6;
    }

    .file-name {
      color: #333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .delete-icon {
    color: #999;
    cursor: pointer;

    &:hover {
      color: #ff4d4f;
    }
  }
}

.tech-upload-button {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  color: #666;
  font-size: 14px;

  .anticon {
    margin-right: 4px;
    font-size: 14px;
  }

  &:hover {
    color: #2D5CF6;
    border-color: #2D5CF6;
  }
}

.value {
  :deep(.ant-input-affix-wrapper) {
    .ant-input-suffix {
      color: #666;
    }
  }
}

.bottom-buttons {
  margin-top: 150px;
  margin-bottom: 89rpx;
  bottom: 180px; // 修改为距离底部80px
  left: 0;
  right: 0;
  padding: 0 32px; // 调整左右内边距
  text-align: right;
  z-index: 100;

  .reject-btn {
    height: 32px;
    padding: 0 16px;
    border: 1px solid #f30d05;
    color: #f30d05;
    background: transparent !important;
    border-radius: 4px;

    &:hover {
      border-color: #409EFF;
      color: #409EFF;
    }
  }

  .submit-btn {
    height: 32px;
    padding: 0 16px;
    margin-left: 27px; // 调整按钮之间的间距
    background: #409EFF;
    border-radius: 4px;
    border: none;

    &:hover {
      background: #66B1FF;
    }
  }
}

.equipment-info-table {
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  border: 0.5px solid rgba(30, 41, 64, 0.08);
  ; // 加深整体边框颜色

  .info-row {
    display: flex;

    &:last-child {
      border-bottom: none;
    }

    .info-cell {
      flex: 1;
      display: flex;
      min-height: 44px;
      border-bottom: 0.5px solid #d9d9d9; // 加深底部边框颜色

      .label {
        width: 120px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        background: #E2F0FF;
        color: #333;
        font-size: 14px;
        padding: 0 16px;
        box-sizing: border-box;
        border-bottom: none; // 移除label的单独底边
      }

      .value {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 0 16px;
        background: #fff;
        color: #333;
        font-size: 14px;
        border-bottom: none; // 移除value的单独底边
      }

      border-right: 1px solid #d9d9d9; // 加深右侧边框颜色

      &:last-child {
        border-right: none;
      }
    }

    &:last-child {
      .info-cell {
        border-bottom: none;
      }
    }
  }

  // 技术资料样式
  .tech-files {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    .preview-btn,
    .view-btn {
      display: inline-flex;
      align-items: center;
      color: #1890FF;
      cursor: pointer;
      margin-right: 16px;

      .anticon {
        margin-right: 4px;
      }

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.section {
  :deep(.ant-table-thead) {
    >tr>th {
      background: #DBECFF !important;
      box-sizing: border-box;
      border: 0.5px solid rgba(30, 41, 64, 0.08);
      padding: 10px 20px !important;

      // 重置 hover 状态的背景色
      &:hover {
        background: #E2F0FF !important;
      }
    }
  }

  // 确保表格边框样式一致
  :deep(.ant-table) {
    border: 0.5px solid rgba(30, 41, 64, 0.08);
  }

  :deep(.ant-table-cell) {
    border: 0.5px solid rgba(30, 41, 64, 0.08) !important;
  }
}

.approval-description {
  padding: 16px;
  background: transparent;

  .ant-textarea {
    width: 100%;
    border: 1px solid #E2F0FF;
    border-radius: 4px;
    box-sizing: border-box;

    &:hover,
    &:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

/* 其他样式保持不变 */

.status-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.status-image {
  width: 146px;
  height: auto;
}

/* 确保表单标题有足够的右边距，避免被状态图标遮挡 */
.form-title {
  padding-right: 140px;
}
.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>
