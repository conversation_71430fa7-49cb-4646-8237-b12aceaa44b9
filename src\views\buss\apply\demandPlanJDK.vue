<template>
  <div class="ele-body">
    <div class="equipment-acceptance">

      <div class="form-title">设备需求计划单</div>

      <!-- 申请信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          申请信息
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">申请人</div>
            <div class="approvalFont">{{ bussTransferForm.applyUserStr }}</div>
          </div>
          <div class="form-item">
            <div class="label">申请日期</div>
            <div class="approvalFont">{{ bussTransferForm.applyDate }}</div>
          </div>
          <div class="form-item">
            <div class="label">申请单位</div>
            <div class="approvalFont">{{ bussTransferForm.applyOrgStr }}</div>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item" data-field="businessTitle">
            <div class="label required">业务标题</div>
            <a-input v-model:value="businessTitle" placeholder="请输入业务标题" maxlength="16" />
          </div>

        </div>


      </div>



      <!-- 主要附件 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          主要附件
        </div>

        <div class="attachment-table">
          <div class="table-header">
            <div class="col-serial">序号</div>
            <div class="col-name">设备名称</div>
            <div class="col-unit">规格型号</div>
            <div class="col-quantity">数量</div>
            <div class="col-manufacturer">使用单位</div>
            <div class="col-serial-no">存放地点</div>
            <div class="col-serial-no">计划使用日期</div>
            <div class="col-serial-no">备注</div>
            <div class="col-action">操作</div>
          </div>

          <div v-for="(item, index) in attachments" :key="index" class="table-row">
            <div class="col-serial">{{ index + 1 }}</div>
            <div class="col-name required">
              <a-input v-model:value="item.equNameStr" placeholder="请输入设备名称" maxlength="16" />
            </div>
            <div class="col-unit required">
              <a-input v-model:value="item.equModelStr" placeholder="请输入规格型号" maxlength="16" />
            </div>
            <div class="col-quantity required">
              <a-input-number v-model:value="item.num" :min="1" placeholder="请输入数量" :precision="0" />
            </div>
            <div class="col-manufacturer required">
              <!-- <a-input v-model:value="item.useOrgStr" placeholder="请输入使用单位" /> -->
              <a-select v-model:value="item.useOrg" placeholder="请选择使用单位" style="width: 100%;"
                @change="(value) => handleUseOrgChange(value, item, index)">
                <a-select-option v-for="item in useOrgOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
            <div class="col-serial-no required">
              <a-input v-model:value="item.storageLocationStr" placeholder="请输入存放地点" maxlength="30" />
            </div>
            <div class="col-serial-no required">
              <a-date-picker v-model:value="item.planUseDate" value-format="YYYY-MM-DD" />
            </div>
            <div class="col-serial-no ">
              <a-input v-model:value="item.demandPlanRemark" placeholder="请输入备注" maxlength="30" />
            </div>
            <div class="col-action required">
              <a class="delete-btn" @click="removeAttachment(index)"> <i class="iconfont icon-delete" style="margin-right: 6px;"></i></a>
            </div>
          </div>

          <div class="add-row" style="text-align: center;">
            <a @click="addAttachment">
              <plus-outlined />
              新增一行
            </a>
          </div>
        </div>
      </div>

      <!-- 附件 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          附件
        </div>

        <div class="upload-section">
          <div class="upload-header">
            <a-upload v-model:file-list="fileList" :multiple="true" :action="uploadUrl" :headers="headers"
              :showUploadList="false" @change="handleChange" :maxCount="10" :beforeUpload="beforeFileUpload">
              <div class="upload-button">
                <upload-outlined />
                <span>点击上传</span>
                <a-spin v-if="uploading" size="small" style="margin-left: 8px;" />
              </div>
            </a-upload>
            <span class="upload-tip">最大1GB/个，支持图片、视频、文档等常见文件格式(zip、rar等压缩文件除外)</span>
          </div>

          <div class="file-list">

            <div v-for="(file, index) in fileViewList" :key="file.uid" class="file-item">
              <div class="file-icon">
                <file-excel-outlined v-if="file.fileName.endsWith('.xls') || file.fileName.endsWith('.xlsx')" />
                <file-word-outlined v-if="file.fileName.endsWith('.doc') || file.fileName.endsWith('.docx')" />
                <file-pdf-outlined v-if="file.fileName.endsWith('.pdf')" />
                <video-camera-outlined v-if="file.fileName.endsWith('.mov') || file.fileName.endsWith('.mp4')" />
                <file-outlined v-else />
              </div>
              <a :href="`${file.fileUrl}?filename=${file.fileName}`"  target="_blank" class="file-link">
                  {{ file.fileName }}
                </a>
              <div class="file-actions">
                <sync-outlined v-if="file.status === 'uploading'" spin />
                <close-outlined v-else class="delete-icon" @click="handleRemove(file)" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 底部按钮 -->
      <div class="bottom-buttons">
        <a-button @click="handleSave" :loading="saving" class="save-btn">保存</a-button>
        <a-button type="primary" @click="handleSubmit" :loading="submitting" class="submit-btn">提交</a-button>
      </div>
    </div>

  </div>
</template>

<script setup>

import { computed, ref, onMounted, nextTick, watch, onBeforeUnmount  } from 'vue';
import { message } from 'ant-design-vue';
import { EquipmentAcceptanceApi } from '@/api/buss/EquipmentAcceptanceApi';
import { BasicInformationApi } from '@/api/buss/BasicInformationApi';
import { FileUploadUrl } from '@/api/system/operation/FileApi';
import { getToken } from '@/utils/token-util';
import { EnumApi } from '@/api/common/enum';
import { API_BASE_PREFIX } from '@/config/setting';
import { useUserStore } from '@/store/modules/user';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
import { DemandPlanApi } from '@/api/buss/DemandPlanApi';

const userStore = useUserStore();
const loading = ref(false);
const saving = ref(false);
const submitting = ref(false);
const uploading = ref(false);
const router = useRouter();
const processDefinitionId = ref(''); // 流程定义ID

// 业务标题和关联投资计划的独立字段
const businessTitle = ref('');
const yearFormat = 'YYYY';
// 上传相关配置
const uploadUrl = `${API_BASE_PREFIX}${FileUploadUrl}?secretFlag=0`;
const headers = {
  Authorization: getToken()
};

// 文件列表相关
const fileList = ref([]);
const fileViewList = ref([]);
const techFileList = ref([]); // 技术资料上传组件的文件列表
const techFileViewList = ref([]); // 技术资料显示列表


// 上传前验证文件
const beforeFileUpload = (file) => {
  // 检查文件数量限制
  if (fileList.value.length >= 10) {
    message.error('最多只能上传10个文件！');
    return false;
  }

  // // 检查文件大小（5MB）
  const isLessThan5M = file.size / 1024 / 1024 < 5;
  if (!isLessThan5M) {
    message.error('文件大小不能超过5MB！');
    return false;
  }

  return true;
};

// 验证表单数据
const validateForm = () => {
  // 按顺序定义需要验证的字段
  // 首先验证业务标题
  if (!businessTitle.value) {
    message.error('请输入业务标题');
    const formItem = document.querySelector('[data-field="businessTitle"]');
    if (formItem) {
      formItem.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
      nextTick(() => {
        setTimeout(() => {
          const input = formItem.querySelector('input');
          input?.focus();
        }, 500);
      });
    }
    return false;
  }
  const fieldsToValidate = [

  ];

  // 依次验证每个字段
  for (const { field, label, type } of fieldsToValidate) {
    if (!attachments.value[field]) {
      message.error(`请${type === 'select' ? '选择' : type === 'date' ? '选择' : '输入'}${label}`);

      const formItem = document.querySelector(`[data-field="${field}"]`);
      if (formItem) {
        // 滚动到可视区域，并确保元素在视图中间
        formItem.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // 使用 nextTick 确保 DOM 更新后再执行点击操作
        nextTick(() => {
          setTimeout(() => {
            switch (type) {
              case 'input':
              case 'number': {
                const input = formItem.querySelector('input');
                input?.focus();
                break;
              }
              case 'select': {
                const select = formItem.querySelector('.ant-select-selector');
                select?.click();
                break;
              }
              case 'date': {
                const datePicker = formItem.querySelector('.date-picker');
                if (datePicker) {
                  // 先聚焦
                  const input = datePicker.querySelector('input');
                  input?.focus();
                  // 然后触发点击以打开日期选择面板
                  setTimeout(() => {
                    datePicker.click();
                  }, 100);
                }
                break;
              }
            }
          }, 500); // 等待滚动完成后再聚焦
        });
      }
      return false;
    }
  }

  return true;
};


// 在验证函数中添加验证逻辑
const validateAttachments = () => {
  for (const attachment of attachments.value) {
    if (!attachment.equNameStr) {
      message.error('请输入设备名称');
      return false;
    }
    if (!attachment.equModelStr) {
      message.error('请输入规格型号');
      return false;
    }

    //根据上面页面配置的字段些这个验证
    if (!attachment.num) {
      message.error('请输入数量');
      return false;
    }

    if (!attachment.useOrgStr) {
      message.error('请选择使用单位');
      return false;
    }

    if (!attachment.storageLocationStr) {
      message.error('请选择存放地点');
      return false;
    }

    if (!attachment.planUseDate) {
      message.error('请选择计划使用日期');
      return false;
    }


  }
  return true;
};



// 构建保存数据的方法
const buildSaveRequestData = () => {

  const requestData = {

    bussWorksheet: {
      ...bussWorksheet.value,
      name: businessTitle.value
    },
    bussTransferForm: {
      ...bussTransferForm.value,
      applyTitle: businessTitle.value,
      fileList: fileViewList.value,
    },
    bussEquipmentProcessTrackingList: attachments.value.map(item => ({
      equNameStr: item.equNameStr,
      equModelStr: item.equModelStr,
      num: item.num,
      useOrg: item.useOrg,
      useOrgStr: item.useOrgStr,
      storageLocationStr: item.storageLocationStr,
      planUseDate: item.planUseDate,
      demandPlanRemark: item.demandPlanRemark
    }))
  };

  return requestData;
};
// 构建提交数据的方法
const buildRequestData = () => {
  const requestData = {
    formDatas: {
      bussWorksheet: {
        ...bussWorksheet.value
      },
      bussTransferForm: {
        ...bussTransferForm.value,
        applyTitle: businessTitle.value,
        fileList: fileViewList.value,
      },
      bussEquipmentProcessTrackingList: attachments.value.map(item => ({
        equNameStr: item.equNameStr,
        equModelStr: item.equModelStr,
        num: item.num,
        useOrg: item.useOrg,
        useOrgStr: item.useOrgStr,
        storageLocationStr: item.storageLocationStr,
        planUseDate: item.planUseDate,
        demandPlanRemark: item.demandPlanRemark
      }))
    }
  };

  return requestData;
};




// 保存方法
const handleSave = async () => {
  try {
    saving.value = true;

    const params = buildSaveRequestData();
    const res = await DemandPlanApi.saveJDK(params);

    if (res.success) {
      message.success('保存成功');
      // 从返回值中完全覆盖表单数据
      if (res.data) {
        // 完全覆盖bussTransferForm数据
        if (res.data.bussTransferForm) {
          bussTransferForm.value = res.data.bussTransferForm;
        }

        // 完全覆盖bussWorksheet数据
        if (res.data.bussWorksheet) {
          bussWorksheet.value = res.data.bussWorksheet;
          businessTitle.value = res.data.bussWorksheet.name;
        }

      }
    } else {
      message.error('保存失败');
    }
  } catch (error) {
    message.error(error.message.split(",")[0]);
  } finally {
    saving.value = false;
  }
};

// 提交方法
const handleSubmit = async () => {
  try {
    // 执行验证
    if (!validateAttachments()) {
      return;
    }

    submitting.value = true;

    const innerFormData = buildRequestData();
    const submitData = {
      processDefinitionId: processDefinitionId.value,
      variables: {
        formData: JSON.stringify(innerFormData)
      }
    };
    if (autoSaveTimer) {
      clearInterval(autoSaveTimer);
      autoSaveTimer = null;
    }

    const res = await DemandPlanApi.submit(submitData);

    if (res.success) {
      message.success('提交成功');
      router.push('/buss/demandListJDK');
    } else {
      message.error('提交失败');
      autoSave()
    }
  } catch (error) {
    message.error(error.message);
    autoSave()
  } finally {
    submitting.value = false;
  }
};
const bussTransferForm = ref({
  applyUser: '', // 申请人ID
  applyUserStr: '', // 申请人姓名
  applyOrg: '', // 申请单位ID
  applyOrgStr: '', // 申请单位名称
  applyDate: '', // 申请日期
  applyTitle: '', // 申请标题
});
const bussWorksheet = ref({
  name: ''
});


// 附件列表数据
const attachments = ref([

]);

// 添加新附件
const addAttachment = () => {
  attachments.value.push({
    equNameStr: '',
    equModelStr: '',
    num: 1,
    useOrg: '',
    useOrgStr: '',
    storageLocationStr: '',
    planUseDate: '',
    demandPlanRemark: ''
  });
};

// 删除附件
const removeAttachment = (index) => {
  attachments.value.splice(index, 1);
};

// 处理普通附件上传
// 处理普通附件上传
const handleChange = (info) => {
  console.log('当前上传的文件:', info.file);
  console.log('所有文件列表:', info.fileList);

  // 更新上传状态
  uploading.value = info.file.status === 'uploading';

  // 保持原始fileList与上传组件同步
  fileList.value = info.fileList;

  // 更新显示用的文件列表
  if (info.file.status === 'done') {
    if (fileViewList.value.length > 0) {
      let fileShowList = info.fileList
        .filter(file => file.status === 'done') // 只保留上传完成的文件
        .map(file => ({
          fileId: file.response.data.fileId,
          fileUrl: file.response.data.fileUrl,
          fileName: file.name,
          uid: file.uid,
          status: file.status
        }))
        .filter(newFile =>
          !fileViewList.value.some(existingFile =>
            existingFile.uid === newFile.uid || // 通过 uid 判断重复
            existingFile.fileId === newFile.fileId // 或通过 fileId 判断重复
          )
        );

      // 合并并更新列表
      fileViewList.value = [...fileViewList.value, ...fileShowList];

    } else {
      fileViewList.value = info.fileList
        .filter(file => file.status === 'done')  // 只保留上传完成的文件
        .map(file => ({
          fileId: file.response.data.fileId, fileUrl: file.response.data.fileUrl,
          fileName: file.name,
          uid: file.uid,
          status: file.status
        }));
    }
    // 上传成功提示
    message.success(`${info.file.name} 上传成功`);
  } else if (info.file.status === 'error') {
    // 处理单个文件的状态提示
    message.error(`${info.file.name} 上传失败`);
  }
};

// 处理技术资料上传
const handleTechFileChange = (info) => {
  // 保持原始列表与上传组件同步
  techFileList.value = info.fileList;

  if (info.file.status === 'done' && info.file.response) {
    const response = info.file.response;
    if (response.success) {
      // 更新显示列表
      techFileViewList.value = [{
        fileId: response.data.fileId,
        fileName: info.file.name,
        uid: info.file.uid,
        status: 'done'
      }];

      // 更新formData中的techFile用于显示
      formData.value.techFile = {
        fileId: response.data.fileId,
        fileName: info.file.name
      };
    } else {
      message.error('技术资料上传失败');
    }
  } else if (info.file.status === 'error') {
    message.error('上传出错：' + info.file.error.message);
  }
};

// 删除技术资料
const removeTechFile = () => {
  formData.value.techFile = null;
  techFileList.value = [];
  techFileViewList.value = [];
};

// 删除普通附件
const handleRemove = (file) => {
  // 从显示列表中删除
  const viewIndex = fileViewList.value.findIndex(f => f.uid === file.uid);
  if (viewIndex > -1) {
    fileViewList.value.splice(viewIndex, 1);
  }

  // 从上传列表中删除
  const fileIndex = fileList.value.findIndex(f => f.uid === file.uid);
  if (fileIndex > -1) {
    fileList.value.splice(fileIndex, 1);
  }
};

// 审批记录相关方法
const showApprovalRecord = () => {
  // 处理审批记录的显示逻辑
  console.log('显示审批记录');
};
let autoSaveTimer = null;
const autoSave = () => {
  //1分钟调用handlesave方法一次
  console.log('开启自动保存');
  if (autoSaveTimer) clearInterval(autoSaveTimer);
  autoSaveTimer = setInterval(() => {
    if (bussTransferForm.value.applyUser && bussTransferForm.value.applyOrg) {
      handleSave1();
    }
  }, 60000);
}

const handleSave1 = async () => {
  try {
    saving.value = true;
    submitting.value = true;

    const params = buildSaveRequestData();
    const res = await DemandPlanApi.saveJDK(params);

    if (res.success) {

    } else {
      message.error('保存失败');
    }
  } catch (error) {
    message.error(error.message.split(",")[0]);
  } finally {
    saving.value = false;
    submitting.value = false;
  }
};

const initializeData = async () => {
  loading.value = true;

  try {
    const { data } = await DemandPlanApi.getNewBuiltJDK();

    const isNewRecord = !data.bussWorksheet &&
      !data.bussTransferForm &&
      !data.bussEquipmentProcessTrackingList;

    if (isNewRecord) {
      // 新记录时初始化申请信息
      bussTransferForm.value = {
        ...bussTransferForm.value,
        applyUser: loginUser.value.userId,
        applyUserStr: loginUser.value.realName,
        applyOrg: loginUser.value.organizationId,
        applyOrgStr: loginUser.value.organizationName,
        applyDate: dayjs().format('YYYY-MM-DD')
      };

    } else {
      // 加载已有数据时
      if (data.bussTransferForm) {
        bussTransferForm.value = {
          ...bussTransferForm.value,
          ...data.bussTransferForm
        };

      }
      if (!bussTransferForm.value.applyUser) {
        bussTransferForm.value.applyUser = loginUser.value.userId,
          bussTransferForm.value.applyUserStr = loginUser.value.realName,
          bussTransferForm.value.applyOrg = loginUser.value.organizationId,
          bussTransferForm.value.applyOrgStr = loginUser.value.organizationName,
          bussTransferForm.value.applyDate = dayjs().format('YYYY-MM-DD')
      }
      // 加载已有数据时
      if (data.bussWorksheet) {
        bussWorksheet.value = data.bussWorksheet
        businessTitle.value = data.bussWorksheet.name
      }

      // 加载设备数据
      // 加载设备数据
      if (data.bussEquipmentProcessTrackingList?.length > 0) {
        const equipmentData = data.bussEquipmentProcessTrackingList;
        attachments.value = equipmentData
      }

      if (data.bussTransferForm.fileList?.length > 0) {
        fileViewList.value = data.bussTransferForm.fileList.map(file => ({
          fileId: file.fileId,
          fileName: file.fileOriginName,
          fileUrl: file.fileUrl,
          uid: file.fileId // 使用fileId作为uid
        }));
      }
    }
    autoSave()
  } catch (error) {
    // message.error('获取数据失败：' + error.message);
  } finally {
    loading.value = false;
  }
};

const initProcessDefinitionId = async () => {
  try {
    const { data } = await EnumApi.getProcessDefinitionByKey({
      key: 'demand_plan_jdk'
    });
    processDefinitionId.value = data.id;
  } catch (error) {
    message.error('获取流程定义ID失败');
  }
};


const loginUser = computed(() => userStore.info ?? {});
onBeforeUnmount(() => {
  if (autoSaveTimer) {
    clearInterval(autoSaveTimer);
    autoSaveTimer = null;
  }
});
onMounted(() => {
  initializeData();
  initProcessDefinitionId();

  getUserOrgOptions();
  watch(
    () => router.fullPath,
    () => {
      if (autoSaveTimer) {
        clearInterval(autoSaveTimer);
        autoSaveTimer = null;
      }
    }
  );
});

const getUserOrgOptions = async () => {
  try {
    console.log(loginUser.value.organizationId)
    const res = await EnumApi.getUseOrgList({ pId: loginUser.value.organizationId });
    console.log(res);
    useOrgOptions.value = res.data;
  } catch (error) {
    message.error('获取使用单位列表失败');
  }
};


// 在 setup 中添加
const equTypeOptions = ref([]);      // 设备类别选项
const equSubTypeOptions = ref([]);   // 设备种类选项
const equNameOptions = ref([]);      // 设备名称选项
const equModelOptions = ref([]);     // 规格型号选项
const equNatureOptions = ref([]);    // 设备性质选项
const equSourceOptions = ref([]);    // 设备来源选项
const equImportanceOptions = ref([]); // 设备重要性选项
const managementStatusOptions = ref([]); // 管理状态选项
const equConditionOptions = ref([]); // 设备状态选项
const depreciationMethodOptions = ref([]); // 折旧方式选项

// 获取设备类别（第一级）
const getEquTypeOptions = async () => {
  try {

    const res = await BasicInformationApi.getEquData();
    equTypeOptions.value = res.data;
  } catch (error) {
    message.error('获取设备类别失败');
  }
};

// 设备类别改变
const handleEquTypeChange = async (value) => {
  formData.value.equSubType = null;
  formData.value.equName = null;
  formData.value.equModel = null;
  equSubTypeOptions.value = [];
  equNameOptions.value = [];
  equModelOptions.value = [];

  if (value) {
    try {
      const res = await BasicInformationApi.getEquData({ pId: value });
      equSubTypeOptions.value = res.data;
    } catch (error) {
      message.error('获取设备种类失败');
    }
  }
};

// 设备种类改变
const handleEquSubTypeChange = async (value) => {
  formData.value.equName = null;
  formData.value.equModel = null;
  equNameOptions.value = [];
  equModelOptions.value = [];

  if (value) {
    try {
      const res = await BasicInformationApi.getEquData({ pId: value });
      equNameOptions.value = res.data;
    } catch (error) {
      message.error('获取设备名称失败');
    }
  }
};

// 设备名称改变
const handleEquNameChange = async (value) => {
  formData.value.equModel = null;
  equModelOptions.value = [];

  if (value) {
    try {
      // 获取规格型号选项
      const res = await BasicInformationApi.getEquData({ pId: value });
      equModelOptions.value = res.data;

      // 获取保养周期
      const { data } = await EquipmentAcceptanceApi.getEquipmentByCode({ id: value });
      if (data) {
        formData.value.serviceIntervalTypeStr = data.maintenanceCycleStr;
      }
    } catch (error) {
      message.error('获取规格型号失败');
    }
  }
};

// 规格型号改变
const handleEquModelChange = async (value) => {
  if (value) {
    try {
      // 获取设备型号编码
      const { data } = await EquipmentAcceptanceApi.getEquipmentByCode({ id: value });
      if (data) {
        formData.value.modelCode = data.code;
      }
    } catch (error) {
      message.error('获取设备型号编码失败');
    }
  }
};

// 初始化枚举数据
const initEnumData = async () => {
  try {
    // 获取设备性质枚举
    const equNatureData = await EnumApi.getEnumList({ enumName: 'EquNatureEnum' });
    equNatureOptions.value = equNatureData.data;

    // 获取设备来源枚举
    const equSourceData = await EnumApi.getEnumList({ enumName: 'EquSourceEnum' });
    equSourceOptions.value = equSourceData.data;

    // 获取设备重要性枚举
    const equImportanceData = await EnumApi.getEnumList({ enumName: 'EquImportanceEnum' });
    equImportanceOptions.value = equImportanceData.data;

    // 获取管理状态枚举
    const managementStatusData = await EnumApi.getEnumList({ enumName: 'ManagementStatusEnum' });
    managementStatusOptions.value = managementStatusData.data;

    // 获取设备状态枚举
    const equConditionData = await EnumApi.getEnumList({ enumName: 'EquConditionEnum' });
    equConditionOptions.value = equConditionData.data;

    // 获取折旧方式枚举
    const depreciationMethodData = await EnumApi.getEnumList({ enumName: 'DepreciationMethodEnum' });
    depreciationMethodOptions.value = depreciationMethodData.data;



    // 设置默认值
    formData.value.managementStatus = 1;
    formData.value.equCondition = 1;
  } catch (error) {
    message.error('获取枚举数据失败');
  }
};

// 转入设备编号
const transferEquipmentCode = ref('');

// 查询转入设备信息
const searchTransferEquipment = async () => {
  if (!transferEquipmentCode.value) {
    message.warning('请输入转入设备编号');
    return;
  }

  try {
    // 调用查询接口
    const { data } = await EquipmentAcceptanceApi.getEquipmentByCode(transferEquipmentCode.value);

    if (data) {
      // 查询到设备，更新表单数据
      formData.value = {
        ...formData.value,
        code: data.code,
        equType: data.equType,
        equSubType: data.equSubType,
        equName: data.equName,
        equModel: data.equModel,
        equModelInfo: data.equModelInfo,
        equNature: data.equNature,
        purchaseYear: data.purchaseYear,
        equSource: data.equSource,
        unit: data.unit,
        num: data.num,
        equContractPriceTax: data.equContractPriceTax,
        manufacturer: data.manufacturer,
        factoryNumber: data.factoryNumber,
        productionDate: data.productionDate,
        contractNumber: data.contractNumber,
        // ... 其他需要复制的字段
      };

      // 触发相关联的数据加载
      if (data.equType) {
        await handleEquTypeChange(data.equType);
      }
      if (data.equSubType) {
        await handleEquSubTypeChange(data.equSubType);
      }
      if (data.equName) {
        await handleEquNameChange(data.equName);
      }

      message.success('设备信息加载成功');
    } else {
      message.warning('未找到对应的设备信息');
    }
  } catch (error) {
    message.error('查询设备信息失败');
  }
};



// 选项数据
const useOrgOptions = ref([]);
const storageLocationOptions = ref([]);


// 处理使用单位变化
const handleUseOrgChange = (value, item, index) => {
  console.log(value)
  // 设置使用单位ID
  item.useOrg = value;

  // 查找选中的使用单位对象
  const selectedOrg = useOrgOptions.value.find(org => org.value === value);

  // 设置使用单位名称
  if (selectedOrg) {
    item.useOrgStr = selectedOrg.label;
  } else {
    item.useOrgStr = '';
  }

  console.log(`行 ${index + 1} 使用单位已更新:`, item.useOrg, item.useOrgStr);
};

// 处理存放地点变化
const handleStorageLocationChange = (value) => {
  formData.value.storageLocationStr = value;  // 保存数字 ID
};

</script>

<style lang="less" scoped>
.approval-record-btn {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  z-index: 100;

  .bg-image {
    display: block;
    max-width: 100%;
    height: auto;
    object-fit: contain;
    background-color: transparent;
  }

  .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .icon {
      width: 15px;
      height: 15px;
      margin-bottom: 4px;
    }

    .text {
      writing-mode: vertical-lr;
      font-size: 14px;
      color: #333;
      letter-spacing: 2px;
    }
  }
}


.equipment-acceptance {
  width: 100%;
  max-width: 1920px; // 最大宽度限制
  height: calc(100vh - 100px);
  overflow: auto;
  margin: 0 auto; // 居中
  padding: 24px 9.06vw 24px 8.18vw; // 转换为vw: 174/1920*100=9.06, 157/1920*100=8.18
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);

  .form-title {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 24px;
  }

  .section {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;

      .blue-bar {
        width: 3px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
      }
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: clamp(16px, 2vw, 24px);
    margin-bottom: 24px;
    justify-content: space-between;

    .form-item {
      display: flex;
      flex-direction: column;
      min-width: 300px; // 最小宽度
      max-width: 300px; // 最大宽度

      .label {
        height: 32px;
        line-height: 32px;
        margin-bottom: 8px;
        color: #0C0B0B;

        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 4px;
        }
      }

      .value {
        width: 100%;
        height: 32px;
        color: #666;

        :deep(.ant-input),
        :deep(.ant-select),
        :deep(.ant-picker),
        :deep(.ant-input-number) {
          width: 100%;
          height: 32px;
        }

        :deep(.ant-input-number-input) {
          height: 16px;
          line-height: 16px;
        }

        :deep(.ant-select-selector) {
          height: 32px !important;

          .ant-select-selection-item {
            line-height: 32px;
          }
        }

        :deep(.ant-picker) {
          .ant-picker-input {
            height: 16px;
            line-height: 16px;

            input {
              height: 16px;
              line-height: 16px;
            }
          }
        }
      }
    }
  }

  .acceptance-no {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    max-width: 300px; // 与其他表单项保持一致的最大宽度

    :deep(.first-input) {
      width: 65% !important; // 验资前面的输入框占65%
    }

    :deep(.second-input) {
      width: 35% !important; // 号前面的输入框占35%
    }

    .separator {
      color: #666;
      white-space: nowrap;
    }
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .equipment-acceptance {
    padding: 24px 6vw;

    .form-grid {
      gap: 16px;
    }
  }
}

@media screen and (max-width: 1366px) {
  .equipment-acceptance {
    padding: 24px 4vw;

    .form-grid {
      grid-template-columns: repeat(2, minmax(300px, 1fr));
    }
  }
}

@media screen and (max-width: 1024px) {
  .equipment-acceptance {
    padding: 24px 2vw;

    .form-grid {
      grid-template-columns: repeat(1, 1fr);

      .form-item {
        max-width: none;
      }
    }
  }
}

.percentage-input {
  position: relative;
  display: flex;
  align-items: center;

  .ant-input {
    width: 100%;
  }

  .percentage-symbol {
    position: absolute;
    right: 12px;
    color: #666;
  }
}

.attachment-table {
  width: 89%;
  border: 1px solid #e8e8e8;
  border-radius: 2px;


  .table-header,
  .table-row,
  .add-row {
    // 添加add-row到统一高度设置中
    display: flex;
    padding: 12px 8px;

    border-bottom: 1px solid #e8e8e8;
    align-items: center;
    gap: 12px;
    min-height: 56px; // 统一设置最小高度
  }

  .table-header {
    font-weight: 500;
    text-align: center;
  }

  .table-row:last-child {
    border-bottom: none;
  }

  // 使用百分比和flex布局
  .col-serial {
    width: 5%; // 序号列较窄
    min-width: 40px;
  }

  .col-name {
    width: 25%; // 名称及型号需要较大空间
    min-width: 180px;
  }

  .col-unit {
    width: 10%; // 单位列较窄
    min-width: 80px;
  }

  .col-quantity {
    width: 10%; // 数量列较窄
    min-width: 80px;
  }

  .col-manufacturer {
    width: 20%; // 生产厂家需要适中空间
    min-width: 150px;
  }

  .col-serial-no {
    width: 20%; // 出厂编号需要适中空间
    min-width: 150px;
  }

  .col-action {
    width: 10%; // 操作列较窄
    min-width: 60px;
    text-align: center;

    .delete-btn {
      color: #FF4D4F;
      cursor: pointer;
    }
  }

  :deep(.ant-input),
  :deep(.ant-input-number) {
    width: 100%;
  }

  // 响应式调整
  @media screen and (max-width: 1366px) {
    .col-name {
      width: 22%; // 较小屏幕时稍微压缩名称列
    }

    .col-manufacturer,
    .col-serial-no {
      width: 18%; // 压缩这两列
    }
  }

  @media screen and (max-width: 1024px) {
    overflow-x: auto; // 当屏幕太小时允许横向滚动

    .table-header,
    .table-row {
      min-width: 900px; // 确保在小屏幕上内容不会过度压缩
    }
  }

  .add-row {
    justify-content: center; // 水平居中
    align-items: center; // 垂直居中
    cursor: pointer;
    border-bottom: none; // 最后一行不需要底部边框

    a {
      display: flex;
      align-items: center; // 图标和文字垂直居中
      color: #1890ff;

      .anticon {
        margin-right: 4px;
      }
    }

    &:hover {
      background: #f5f5f5;
    }
  }
}

.upload-section {
  .upload-header {
    display: flex;
    align-items: center;
    gap: 12px;

    .upload-button {
      display: inline-flex;
      align-items: center;
      padding: 4px 15px;
      background: #0085FF;
      border-radius: 2px;
      color: white;
      cursor: pointer;
      font-size: 14px;

      .anticon {
        margin-right: 4px;
      }
    }

    .upload-tip {
      color: #999;
      font-size: 12px;
    }
  }

  .file-list {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    //display: grid;
    //grid-template-columns: repeat(3, 0.3fr);
    //gap: 16px;

    .file-item {
      display: flex;
      align-items: center;
      padding: 8px;
      // background: #F7F9FC;
      border-radius: 2px;
      width: 32%;

      .file-link{
        word-break:break-all;
      }

      .file-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #2D5CF6;
      }

      .file-name {
        flex: 1;
        font-size: 14px;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-actions {
        margin-left: 8px;

        .delete-icon {
          color: #999;
          cursor: pointer;

          &:hover {
            color: #FF4B4B;
          }
        }

        .anticon-sync {
          color: #2D5CF6;
        }
      }
    }
  }
}

.tech-file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;

  .file-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      color: #2D5CF6;
    }

    .file-name {
      color: #333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .delete-icon {
    color: #999;
    cursor: pointer;

    &:hover {
      color: #ff4d4f;
    }
  }
}

.tech-upload-button {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  color: #666;
  font-size: 14px;

  .anticon {
    margin-right: 4px;
    font-size: 14px;
  }

  &:hover {
    color: #2D5CF6;
    border-color: #2D5CF6;
  }
}

.value {
  :deep(.ant-input-affix-wrapper) {
    .ant-input-suffix {
      color: #666;
    }
  }
}

.bottom-buttons {
  margin-top: 80px;
  margin-bottom: 89rpx;
  bottom: 180px; // 修改为距离底部80px
  left: 0;
  right: 0;
  padding: 0 32px; // 调整左右内边距
  text-align: right;
  z-index: 100;

  .save-btn {
    height: 32px;
    padding: 0 16px;
    border: 1px solid #DCDFE6;
    color: #606266;
    background: transparent !important;
    border-radius: 4px;

    &:hover {
      border-color: #409EFF;
      color: #409EFF;
    }
  }

  .submit-btn {
    height: 32px;
    padding: 0 16px;
    margin-left: 27px; // 调整按钮之间的间距
    background: #409EFF;
    border-radius: 4px;
    border: none;

    &:hover {
      background: #66B1FF;
    }
  }
}
</style>
