<template>
  <div class="todo-container">
    <div v-if="globalLoading" class="global-loading">
      <div class="loading-mask"></div>
      <div class="loading-content">
        <span>正在加载必要组件，请耐心等待...</span>
        <a-progress :percent="loadingProgress" status="active" :stroke-width="16" class="big-progress" />
      </div>
    </div>
    <!-- 左侧组件区域 -->
    <div class="left-component">
      <!-- <div v-if="loading" class="loading-state"> -->
        <!-- <span>Loading componentdone...</span> -->
      <!-- </div> -->
      <!-- <div v-else-if="error" class="error-state">
        {{ error }}
      </div>
      <div v-else-if="!comp" class="error-state"> -->
        <!-- 找不到组件: {{ comp }}
        <br>
        可用组件列表: {{ Object.keys(resultComps) }} -->
      <!-- </div> -->
      <component  :is="comp" :value="componentProps" :jsonData="jsonData" :taskId="taskId" :procInsId="procInsId"
        :processDefinitionId="processDefinitionId" :actId="actId" :formData="formData" ref="process_f"
        @mounted="handleComponentMounted"   @child-loaded="handleChildLoaded">
      </component>
    </div>

    <!-- 右侧审批记录区域 -->
    <div style="padding: 13px 20px 6px 4px;">
      <div class="right-panel" v-show="showRightPanel">
        <div class="right-panel-header">
          <div class="panel-title">
            <img src="@/assets/equipment/yinzhang.png" alt="" class="icon" />
            审批记录
          </div>
        </div>
        <div class="approval-record">
          <div class="timeline">
            <div v-for="(record, index) in approvalRecords" :key="'record-' + index + '-' + updateTrigger"
              class="timeline-item">

              <div :class="record.status === 10055 ? 'avatar1' : 'avatar'">
                <span class="avatar-text">{{ record.userInitial }}</span>
                <img v-if="record.status == 1002 || record.status == 1004" src="@/assets/equipment/shenpisuccess.png"
                  class="status-icon" alt="已通过" />
                <img v-if="record.status === 1003" src="@/assets/equipment/icon-check.png" class="status-icon"
                  alt="已通过" />
                <img v-if="record.status === 10055" src="@/assets/equipment/weidao.png" class="status-icon" alt="已通过" />
              </div>
              <div class="content">
                <div class="header">
                  <div class="title">
                    <span class="name" :style="record.status === 10055 ? 'color:#ACB4C9' : ''">{{
                      record.assigneeName.split(".")[0] }}</span>
                    <span class="role" v-if="record.assigneeName"
                      :style="record.status === 10055 ? 'color:#ACB4C9' : ''">({{
                        record.userName }})</span>
                  </div>
                  <span class="time" :style="record.status === 10055 ? 'color:#ACB4C9' : ''">
                  {{ (record.time || record.endTime).split(" ")[0] }}</span>
                </div>

                <div class="status-text" :class="record.status" :style="record.status === 10055 ? 'color:#ACB4C9' : ''">
                  {{ record.statusText }}</div>
                    <a-tooltip v-if="record.comment.length>3" :title="record.comment" placement="right">
                      <div class="comment" v-if="record.comment && index !== 0">{{ record.comment }}</div>
                    </a-tooltip>
                      <div v-else>
                        <div class="comment" v-if="record.comment && index !== 0">{{ record.comment }}</div>
                      </div>
              </div>
              <div class="timeline-line" v-if="index !== approvalRecords.length - 1"></div>
            </div>
          </div>
        </div>
      </div>
    </div>


    <!-- 展开按钮 -->
    <div class="expand-button-container" v-if="!showRightPanel">
      <button class="expand-button" @click="showRightPanel = true">
        <HistoryOutlined />
        <span>审批记录</span>
      </button>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, markRaw, watch } from 'vue'
//import resultComps from './components'
import { RegisteredEquipmentApi } from '@/api/workflow/RegisteredEquipmentApi';
import ApprovalRecord from '../components/ApprovalRecord.vue';
import { CloseOutlined, HistoryOutlined } from '@ant-design/icons-vue';
// 修改为按需加载单个组件
const loadSingleComponent = async (componentName) => {
  try {
    // 使用Vite的import.meta.glob动态导入
    const modules = import.meta.glob('@/views/buss/done/*.vue');
    const targetPath = Object.keys(modules).find(path =>
      path.endsWith(`/${componentName}.vue`)
    );

    if (!targetPath) {
      console.error(`找不到组件: ${componentName}`);
      return null;
    }

    // 导入找到的组件
    const module = await modules[targetPath]();

    if (module.default) {
      // 使用 markRaw 防止组件被转换为响应式对象
      return markRaw(module.default);
    } else {
      console.error(`组件加载失败: ${componentName} - module.default 不存在`);
      return null;
    }
  } catch (error) {
    console.error(`加载组件失败: ${componentName}`, error);
    return null;
  }
};
import { useRouter } from 'vue-router';


export default defineComponent({
  name: 'FormProcessTask',
  components: {
    ApprovalRecord,
    CloseOutlined,
    HistoryOutlined
  },
  data() {
    return {
      globalLoading: false,
      loadingProgress: 0,
      jsonData: {},
      buttonList: {},
      recordData: {},
      newData: {},
      showFlag: false,
      period: "",
      loading: true,
      error: null,
      //resultComps,
      comp: null,
      procInsId: "",
      processDefinitionId: "",
      actId: "",
      taskId: "",
      componentProps: {
        isDone: 0,
        // 可以添加其他需要传递的数据
      },
      formData: {
        isDone: 0,
        taskId: '',
        procInsId: '',
        processDefinitionId: '',
        actId: '',
        // 可以添加更多需要传递的数据
      },
      showRightPanel: true, // 控制右侧面板显示
      approvalRecords: [

      ],
      fileName: ""
    }
  },
  async created() {
  //   this.globalLoading = true;
  // this.loadingProgress = 0;
  // const duration = 3000; // 3秒
  // const start = Date.now();

  // // 启动进度条动画
  // const progressInterval = setInterval(() => {
  //   const elapsed = Date.now() - start;
  //   this.loadingProgress = Math.min(100, Math.round((elapsed / duration) * 100));
  //   if (this.loadingProgress >= 100) {
  //     clearInterval(progressInterval);
  //   }
  // }, 30);

  // 并行加载数据
  await this.initializeData();

  // // 计算已用时间
  // const elapsed = Date.now() - start;
  // if (elapsed < duration) {
  //   // 不足3秒则补足
  //   await new Promise(resolve => setTimeout(resolve, duration - elapsed));
  // }

  // // 关闭loading
  // this.globalLoading = false;
  // this.loadingProgress = 100;

  },
  methods: {
    handleChildLoaded() {
      if (name === 'equUploadApproval') {
    globalLoading.value = true
    loading.value = true
  } else {
    globalLoading.value = false
    loading.value = false
  }
},
    getAvatarClass(status) {
      switch (status) {
        case 'completed': return 'avatar-completed';
        case 'rejected': return 'avatar-rejected';
        case 'waiting': return 'avatar-waiting';
        default: return '';
      }
    },
    async waitForComponents() {
      this.globalLoading = true;
      this.loadingProgress = 0;
      const duration = 3000; // 3秒
      const start = Date.now();
      const progressInterval = setInterval(() => {
        const elapsed = Date.now() - start;
        this.loadingProgress = Math.min(100, Math.round((elapsed / duration) * 100));
        if (this.loadingProgress >= 100) {
          clearInterval(progressInterval);
        }
      }, 30);

      // 等待3秒
      await new Promise(resolve => setTimeout(resolve, duration));
      this.globalLoading = false;
      this.loadingProgress = 100;
    },
    async initializeData() {
      try {
        // 从路由获取参数
        const route = this.$route
        this.processDefinitionId = route.query.processDefinitionId
        this.actId = route.query.actId
        this.taskId = route.query.taskId
        this.procInsId = route.query.procInsId
        this.worksheetId = route.query.worksheetId
        this.fileName = route.query.fileName;
        var componentName = "";
        if (!this.fileName) {
          const res = await RegisteredEquipmentApi.taskFormData({
            processDefinitionId: this.processDefinitionId,
            actId: this.actId
          })
          this.jsonData = JSON.parse(res.data)
          // 从后台返回的数据中获取组件名称
          componentName = this.jsonData.list[0].options.defaultValue;
        }else{
          componentName = this.fileName;
        }
        // 获取表单数据


        // 只加载需要的组件
        const componentModule = await loadSingleComponent(componentName);
        console.log(88, componentModule)
        if (!componentModule) {
          throw new Error(`无法加载组件: ${componentName}`);
        }

        // 设置组件
        this.comp = componentModule;
        // 更新要传递给子组件的数据
        this.formData = {
          isDone: 0,
          taskId: this.taskId,
          procInsId: this.procInsId,
          processDefinitionId: this.processDefinitionId,
          actId: this.actId,
          formData: this.jsonData,
          worksheetId: this.worksheetId
        }

        const [flowableRes, historyRes] = await Promise.all([
          RegisteredEquipmentApi.getFlowAbleMap({ id: this.procInsId }),
          RegisteredEquipmentApi.getCommentHistory({ id: this.procInsId })
        ])

        console.log('流程图数据1:', flowableRes.data)
        console.log('审批记录数据1:', historyRes.data)
        //  // 安全处理流程图数据
        let processedElements = []
        let processedFlows = []

        if (flowableRes.data && flowableRes.data.elements) {
          processedElements = flowableRes.data.elements.length > 2
            ? flowableRes.data.elements.slice(1, -1)
            : [...flowableRes.data.elements]
        }

        if (flowableRes.data && flowableRes.data.flows) {
          processedFlows = flowableRes.data.flows.length > 2
            ? flowableRes.data.flows.slice(1, -1)
            : [...flowableRes.data.flows]
        }

        // // 合并数据 - 确保所有属性都有默认值
        this.flowableData = {
          elements: processedElements || [],
          flows: processedFlows || [],
          comments: (historyRes.data || [])
        }

        // 处理审批记录数据 - 确保数据安全

        // 创建新的记录数组
        var newRecords = []

        for (var i = 0; i < processedElements.length; i++) {
          console.log(11)
          var data = processedElements[i];
          var newRecord = {};
          if (historyRes.data[i]) {
            newRecord.userInitial = historyRes.data[i].assigneeName ? historyRes.data[i].assigneeName.charAt(0) : '?',
              newRecord.userName = historyRes.data[i].assigneeName || '未知用户',
              newRecord.time = historyRes.data[i].time || '',
              newRecord.status = historyRes.data[i].approveStatus || 10055,
              newRecord.statusText = historyRes.data[i].approveStatusStr || '待处理',
              newRecord.comment = historyRes.data[i].comment || '',
              newRecord.assigneeName = historyRes.data[i].name || '',
              newRecord.endTime = historyRes.data[i].endTime || '',
              newRecord.isCheck = true
          } else {
            newRecord = {
              userInitial: data.assigneeName ? data.assigneeName.charAt(0) : '?',
              userName: data.assigneeName || '未知用户',
              time: data.time || '',
              status: 10055,
              statusText: data.approveStatusStr || '待处理',
              comment: data.comment || '',
              assigneeName: data.name || '',
              endTime: data.endTime || '',
              isCheck: false
            }
          }



          newRecords.push(newRecord)

        }
        // const newRecords = flowableRes.data.map(item => ({
        //   userInitial: item.assigneeName ? item.assigneeName.charAt(0) : '?',
        //   userName: item.assigneeName || '未知用户',
        //   time: item.time || '',
        //   status: item.approveStatus || 'waiting',
        //   statusText: item.approveStatusStr || '待处理',
        //   comment: item.comment || '',
        //   assigneeName: item.name || '',
        //   endTime: item.endTime || ''
        // }))

        console.log('处理后的审批记录:', newRecords)

        // 直接更新数组，不使用复杂的异步处理
        this.approvalRecords = newRecords
        this.updateTrigger++

        // console.log(this.formData)
        // console.log(4444,componentName === 'equUploadApproval')
        this.loading = false
          //this.globalLoading = false;
      } catch (err) {
        console.error('初始化数据错误:', err)
        this.error = err.message
        this.loading = false
      }
    },
    handleComponentMounted() {
      console.log('动态组件已挂载，props:', {
        componentProps: this.componentProps,
        jsonData: this.jsonData,
        taskId: this.taskId,
        procInsId: this.procInsId,
        processDefinitionId: this.processDefinitionId,
        actId: this.actId
      })
    }
  }
})
</script>

<style lang="less" scoped>
.todo-container {
  display: flex;
  position: relative;
  height: 100%;
  width: 100%;
  min-height: 100vh;

  background-image: url(/src/assets/equipment/back.jpg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.left-component {
  flex: 1;
  overflow: auto;
  padding: 12px 2px 10px 6px;
  padding-right: 2px;
}

.right-panel {
  width: 300px;
  background-color: rgba(255, 255, 255, 0.8);
  background: linear-gradient(180deg, rgba(235, 245, 255, 0.8) 0%, rgba(235, 245, 255, 0.4) 100%);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: calc(100vh - 40px);
}

.global-loading {
  position: fixed;
  top: 0; left: 0; width: 100vw; height: 100vh;
  z-index: 1000;
  pointer-events: all;
}

.loading-mask {
  position: absolute;
  top: 0; left: 0; width: 100vw; height: 100vh;
  background: rgba(255,255,255,0.5); /* 半透明白色 */
  z-index: 1;
}

.loading-content {
  position: absolute;
  top: 50%; left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2; /* 一定要比mask高 */
  width: 400px;
  padding: 40px 0;
  font-size: 18px;

  text-align: center;
}
.loading-state,
.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.approval-record {
  padding: 15px;
  height: 95%;
  overflow: auto;
}

.timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 60px;
  position: relative;
}

.avatar1 {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #ACB4C9 !important;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
  position: relative;
  flex-shrink: 0;
}

.avatar1.waiting {
  background-color: #ACB4C9;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #1890ff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
  position: relative;
  flex-shrink: 0;
}

.avatar.waiting {
  background-color: #1890ff;
}

.avatar.rejected {
  background-color: #ff4d4f;
}

.avatar-text {
  color: white;
  font-weight: bold;
}

.status-icon {
  position: absolute;
  bottom: -6px;
  right: -5px;
  width: 18px;
  height: 18px;
}

.content {
  flex: 1;
}

.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.title {
  font-weight: bold;
}

.role {
  color: #666;
  margin-left: 5px;
}

.time {
  color: #999;
  font-size: 12px;
}

.status-text {
  color: #1890ff;
  margin-bottom: 5px;
  font-weight: 500;
}

.status-text.waiting {
  color: #1890ff;
}

.status-text.rejected {
  color: #ff4d4f;
}

.comment {
  background-color: rgba(255, 255, 255, 0.6);
  padding:4px 8px;
  border-radius: 4px;
  font-size: 13px;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* 限制显示的行数为 3 行 */
  -webkit-box-orient: vertical;
  word-wrap: break-word; /* 允许长单词换行到下一行 */
  word-break: break-all; /* 强制换行 */
}

.timeline-line {
  position: absolute;
  left: 20px;
  top: 40px;
  bottom: -60px;
  width: 2px;
  background-color: #1890ff;
}

.timeline-line.dashed {
  background: repeating-linear-gradient(to bottom,
      #1890ff 0px,
      #1890ff 4px,
      transparent 4px,
      transparent 8px);
}

.expand-button-container {
  position: fixed;
  right: 20px;
  bottom: 20px;
}

.expand-button {
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.expand-button span {
  margin-left: 5px;
}

.right-panel-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
}

.panel-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;

  .icon {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    color: #1890ff;
  }
}
</style>
