import Request from '@/utils/request-util';

/**
 * 设备验收API
 *
 * <AUTHOR> @date 2024/01/09
 */
export class registeredApi {
  /**
   * 获取新建数据
   *
   * <AUTHOR> @date 2024/01/09
   */
  static getData(params) {
    return Request.get('/analysis/reportRegisteredReal/getReport', params);
  }



  static getChildrenList(params) {
    return Request.get('/analysis/reportRegisteredReal/getReportDataList', params);
  }


  static downLoad(params) {
    return Request.downLoad('/api/analysis/reportRegisteredReal/export', params);
  }

  static downLoad1(params) {
      return Request.downLoad('/api/analysis/analysisReportSheet/downLoadExcel', params);
    }

    static downLoad2(params) {
      return Request.downLoad('/api/analysis/analysisReport/exportEquipment', params);
    }

  static findPage(params) {
    return Request.get('/analysis/analysisReportSheet/findPage', params);
  }

  static reportRegisteredMonth(params) {
    return Request.get('/analysis/reportRegisteredMonth/getReport',params);
  }

static findEquipmentPage(params) {
    return Request.get('/analysis/analysisReport/findEquipmentPage', params);
  }

  static getReportManagementOrg() {
    return Request.get('/analysis/reportRegisteredReal/getReportPropertyOrg');
  }

}
