<!-- 顶栏消息通知 -->
<template>
  <a-dropdown v-model:visible="visible" placement="bottom" :trigger="['hover']" :overlay-style="{padding: '-50px 10px' }" class="xxtz">
    <div @click="lookMore" style="display: inline-block;">
    <a-badge :count="unreadNum" class="ele-notice-trigger" :offset="[6, 4]">
      <bell-outlined style="padding: 8px 0" />
    </a-badge>
    </div>
    <template #overlay class="xxtz">
      <div class="ant-dropdown-menu ele-notice-pop">
        <div @click.stop="">

              <div class="notice-container">
                <div class="all-container" :style="{
                backgroundColor: props.backgroundColor,
                color: props.textColor,
                borderColor: props.borderColor
              }">
                <div class="notice-header" @click="lookMore">
                  <div style="font-size: 16px;font-family: 'Noto Sans SC', sans-serif;">消息通知</div>
                  <div >
                    <!-- <img src="@/assets/equipment/into.png" alt="" style="width: 13px;height: 13px;"> -->
                  </div>
                </div>
                <div style="border-top: 2px solid #f0f0f0;" :style="{borderColor: props.borderColor}"></div>
                <div class="notice-list" v-if="notice.length>0">

                  <div v-for="item in notice" :key="item.messageId" class="notice-item1" @click="noticeDetail(item)" :style="{borderBottom: props.borderBottomColor}">

                    <div class="notice-content">
                      <div class="notice-title-row">
                        <div class="notice-title">
                          {{ item.messageTitle }}
                          <span class="unread-dot" v-if="!item.readFlag"></span>
                        </div>
                        <div class="notice-time">{{ formatTimeAgo(item.messageSendTime) }}</div>
                      </div>
                      <div class="notice-desc">{{ item.messageContent }}</div>
                    </div>
                  </div>
                </div>

                <div class="notice-footer" v-if="notice.length>0" @click="lookMore" v-show="props.isShowFooter">
                  <span>查看全部消息</span>
                </div>

                <div class="notice-list1" v-else style="display: flex; align-items: center;justify-content: center;">
                  <img src="@/assets/images/noData2.png" style="width: 50%;" alt="" />
                </div>
              </div>
            </div>
        </div>
      </div>
    </template>
  </a-dropdown>

  <!--消息详情对话框-->
  <a-modal v-model:visible="noticeDetailShow" :title="noticeDetailObject.messageTitle" @ok="closeDetail" :footer="null"
    :maskClosable="false">
    <p>{{ noticeDetailObject.messageContent }}</p>
  </a-modal>
</template>

<script setup>
import { computed, ref, onMounted, onBeforeUnmount } from 'vue';
import { NoticeApi } from '@/api/system/notice/NoticeApi';
import { useNoticeStore } from '@/store/modules/notice';
import { useRouter } from 'vue-router';

let noticeStore = useNoticeStore();
let router = useRouter();

// 是否显示消息详情对话框
const noticeDetailShow = ref(false);

// 通知详情的内容
const noticeDetailObject = ref({
  messageTitle: '',
  messageContent: ''
});

// 添加 props 定义，接收背景颜色参数
const props = defineProps({
  backgroundColor: {
    type: String,
    default: '#ffffff' // 默认白色背景
  },
  textColor: {
    type: String,
    default: '#333333' // 默认文本颜色
  },
  borderColor: {
    type: String,
    default: '#f0f0f0' // 默认边框颜色
  },
  isShowFooter:{
    type: Boolean,
    default: true
  },
  borderBottomColor: {
    type: String,
    default: '2px solid rgba(255, 255, 255, 100)' // 默认边框颜色
  }
});

// 是否显示
const visible = ref(false);
// 选项卡选中
const active = ref('notice');
// 通知数据
const notice = computed(() => noticeStore.$state.unReadNoticeList);

// 通知标题
const noticeTitle = computed(() => {
  return '';
});

// 未读数量
const unreadNum = computed(() => {
  return notice.value.length;
});

// 定时器引用
let messageTimer = null;

/* 查询数据 */
const query = async () => {
  try {
    // 获取用户所有的未读消息
    let noticeList = await NoticeApi.getUnReadMessages();
    noticeStore.setNotice(noticeList);
  } catch (error) {
    console.error('获取未读消息失败:', error);
  }
};

// 启动定时器，每2分钟请求一次消息
const startMessageTimer = () => {
  // 先清除可能存在的定时器
  stopMessageTimer();

  // 设置新的定时器，120000毫秒 = 2分钟
  messageTimer = setInterval(() => {
    query();
  }, 120000);
};

// 停止定时器
const stopMessageTimer = () => {
  if (messageTimer) {
    clearInterval(messageTimer);
    messageTimer = null;
  }
};

// 组件挂载时启动定时器
onMounted(() => {
  // 立即执行一次查询
  query();
  // 启动定时器
  startMessageTimer();
});

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  stopMessageTimer();
});

/* 清空通知 */
const clearNotice = () => {
  noticeStore.setNotice([]);

  // 调用接口，全部标记为已读
  NoticeApi.messageSetRead();
};

/* 查看详情 */
const noticeDetail = messageObject => {
  // noticeDetailObject.value.messageTitle = messageObject.messageTitle;
  // noticeDetailObject.value.messageContent = messageObject.messageContent;
  // noticeDetailShow.value = true;

  // 更新消息为已读状态
  let param = { messageIdList: [messageObject.messageId] };
  NoticeApi.batchUpdateReadFlag(param);

  // 移除数组中已读消息
  noticeStore.removeMessage(messageObject.messageId);
  visible.value = false; // 处理谷歌、360浏览器打开新的页面后展开消息不消失问题
  window.open(messageObject.messageUrl, '_blank');
};

/* 清空通知 */
const closeDetail = () => {
  noticeDetailShow.value = false;
};

/* 查看更多通知 */
const lookMore = () => {
  if(!props.isShowFooter){
    return;
  }
  visible.value = false;
  router.push('/notice/mynotice');

};

// 查询未读消息
query();

// 获取通知图标样式
const getNoticeIconClass = (type) => {
  switch(type) {
    case 'system': return 'system';
    case 'project': return 'project';
    case 'maintenance': return 'maintenance';
    default: return 'system';
  }
};

const formatTimeAgo = (messageSendTime) => {
  const now = new Date().getTime();
  const sendTime = new Date(messageSendTime).getTime();
  const diff = now - sendTime;

  // 转换为分钟
  const minutes = Math.floor(diff / (1000 * 60));
  // 转换为小时
  const hours = Math.floor(diff / (1000 * 60 * 60));
  // 转换为天
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 60) {
    return `${minutes}分钟前`;
  } else if (hours < 24) {
    return `${hours}小时前`;
  } else {
    return `${days}天前`;
  }
};
</script>

<script>
import { BellOutlined, NotificationFilled } from '@ant-design/icons-vue';

export default {
  name: 'HeaderNotice',
  components: {
    BellOutlined,
    NotificationFilled
  }
};
</script>

<style scoped lang="less">
.ant-dropdown{
  top: 35px !important;
}
.ele-notice-trigger.ant-badge {
  color: inherit;
  margin-left: 114px;
}

.ele-notice-pop {
  &.ant-dropdown-menu {
    padding: 0;
    width: 336px;
    max-width: 100%;
    margin-top: 11px;
    background-color: transparent !important;
    box-shadow: unset !important;
  }

  // 内容
  .ant-list-item {
    padding-left: 24px;
    padding-right: 24px;
    transition: none;  // 移除过渡效果
    cursor: pointer;

    &:hover {
      background: none;  // 移除悬停背景色
    }
  }

  .ant-tag {
    margin: 0;
  }

  // 操作按钮
  .ele-notice-actions {
    border-top: 1px solid hsla(0, 0%, 60%, 0.15);

    &>.ele-cell-content {
      line-height: 46px;
      text-align: center;
      cursor: pointer;
      color: inherit;

      &:hover {
        background: none;  // 移除悬停背景色
      }
    }
  }

  .ant-tabs-nav {
    margin-bottom: 0;
  }
}

.notice-container {
  width: 100%;
  // background: #ffffff;
  padding: 3px 3px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 5px;
}

.all-container {
  width: 100%;
  // background: #F1F8FF;
  border-radius: 5px;
}

.notice-header {
  padding: 5px 15px;
  //border-top: 1px solid #f0f0f0;
  font-weight: 400;
  display: flex;
  border-radius: 13px;
  justify-content: space-between;
  align-items: center;
  font-family: 'Noto Sans SC', sans-serif;
}

.notice-list {
  max-height: 400px;
  overflow-y: auto;
}

.notice-list1{
  min-height: 400px;
  overflow-y: auto;
}

.notice-item1 {
  padding: 12px 20px;
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  //border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  &:hover {
    // background: #f5f5f5;
  }
}

.notice-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;

  &.system {
    background: #e6f7ff;
    color: #1890ff;
  }

  &.project {
    background: #fff7e6;
    color: #fa8c16;
  }

  &.maintenance {
    background: #f6ffed;
    color: #52c41a;
  }
}

.notice-content {
  flex: 1;
}

.notice-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;

}

.notice-title {
  font-weight: 500;
  position: relative;
  padding-right: 12px;
    width: 80%;
  .unread-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #ff4d4f;
    display: inline-block;
    //position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}

.notice-time {
  font-size: 12px;
  color: #999;
}

.notice-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}

.notice-footer {
  padding: 12px 20px;
  // border-top: 1px solid #f0f0f0;
  text-align: center;
  color: #1890ff;
  cursor: pointer;

  &:hover {
    background: #f5f5f5;
  }
}


</style>




