<template>
  <div class="ele-body">
    <div class="equipment-acceptance">

      <div class="form-title">设备保养工单</div>

      <!-- 申请信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          工单信息
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">工单编号</div>
            <div class="value">{{ formData.applyTitle }}</div>
          </div>
          <div class="form-item">
            <div class="label">使用单位</div>
            <div class="value">{{ formData.useOrgStr }}</div>
          </div>

          <div class="form-item">
            <div class="label">技术员</div>
            <div class="value">
              {{ formData.technicianUserStr }}
            </div>
          </div>

        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">工单创建日期</div>
            <div class="value">
              {{ formData.applyStartDate }}
            </div>
          </div>
          <div class="form-item">
            <div class="label">工单截止日期</div>
            <div class="value">
              {{ formData.planApplyEndDate }}
            </div>
          </div>
          <div class="form-item">
            <div class="label">实际完成日期</div>
            <div class="value">
              {{ formData.applyEndDate }}
            </div>
          </div>
        </div>
      </div>


      <!-- 设备信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          设备明细
        </div>

        <div data-field="tables">
          <a-table :columns="columns" :data-source="formData.bussEquipmentProcessTrackingList" bordered
            :pagination="false" :scroll="{ x: 1000 }" class="custom-table" :style="{ zIndex: 1 }">
            <template #emptyText>
              <div class="custom-empty">
                <img src="@/assets/images/noData.png" />
              </div>
            </template>
            <template #bodyCell="{ column, index, record }">
              <template v-if="column.dataIndex === 'code'">
                <span :style="record.maintenanceIsCancel ? 'text-decoration:line-through;color:#999' : ''">{{
                  record.code
                  }}</span>
                  <!-- <a-range-picker v-model:value="record.maintenanceIsCancel" picker="date"
                :value-format="yearFormat" style="width: 100%" /> -->
              </template>
              <template v-if="column.dataIndex === 'equNameStr'">
                <span :style="record.maintenanceIsCancel ? 'text-decoration:line-through;color:#999' : ''">{{
                  record.equNameStr }}</span>
              </template>
              <template v-if="column.dataIndex === 'equModelStr'">
                <span :style="record.maintenanceIsCancel ? 'text-decoration:line-through;color:#999' : ''">{{
                  record.equModelStr }}</span>
              </template>
              <template v-if="column.dataIndex === 'contractUser'">
                <div class="form-item" style="margin-bottom: 0;">
                  <a-input :disabled="record.maintenanceIsCancel" v-model:value="record.contractUser"
                    placeholder="请输入包机人" :style="{ width: '100%' }">
                  </a-input>
                </div>
              </template>
              <template v-if="column.dataIndex === 'maintenanceRecords'">
                <div class="form-item" style="margin-bottom: 0;">
                  <new-input :disabled="record.maintenanceIsCancel" v-model:value="record.maintenanceRecords"
                    :placeholder="'请输入保养结果记录'" :maxlength="500" />
                </div>
              </template>
              <template v-if="column.dataIndex === 'maintenanceFileList'">
                <div class="form-item" style="margin-bottom: 0;" >
                  <InputUploader :backgroundColor="record.maintenanceIsCancel ? '#F5F5F5' : '#fff'"
                    :read-only="record.maintenanceIsCancel" :max-count="10" button-text="上传文件"
                    :accept-types="['.jpg', '.jpeg', '.png', '.mp4', '.mov', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', '.pdf']"
                    :initial-files="record.maintenanceFileList" :show-border="true"
                    @file-uploaded="(file) => onTechFileUploaded(file, record)"
                    @file-removed="(file) => onTechFileRemoved(file, record)"
                    @upload-status-change="onUploadStatusChange" />
                </div>
              </template>
            </template>
          </a-table>
        </div>
      </div>


      <!-- 底部按钮 -->
      <div class="bottom-buttons">
        <a-button @click="handleSave" :loading="saving" class="save-btn">保存</a-button>
        <a-button type="primary" @click="handleSubmit" :loading="submitting" class="submit-btn">提交</a-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'DynamicStatusChangeCreate',
}
</script>
<script setup>

import { ref, onMounted, nextTick, computed, watch, onBeforeUnmount, h } from 'vue';
import { message } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import { maintenanceorderApi } from '@/api/maintenanceManagement/maintenanceorderApi';
import newInput from '@/components/ipttext/newInput.vue'
import InputUploader from '@/components/InputUploader/index.vue';
import { useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();
const formData = ref({
  applyTitle: '',
  useOrg: '',
  useOrgStr: '',
  technicianUser: '',
  technicianUserStr: '',
  applyStartDate: '',
  planApplyEndDate: '',
  applyEndDate: '',
  bussEquipmentProcessTrackingList: []
})
const processDefinitionId = ref('');
const actId = ref('');
const taskId = ref('');
const procInsId = ref('');
const worksheetId = ref('');
const bussWorksheet = ref({
  name: '',
  id: ""
});

const saving = ref(false);
const submitting = ref(false);

const isShowbutton = ref(true);

const onTechFileUploaded = (file, record) => {
  console.log('技术文件上传成功:', file, record);
  if (!record.maintenanceFileList) {
    techFileList.value = [];
  }
  record.maintenanceFileList.push(file);
  console.log('技术文件列表:', record.maintenanceFileList);
  isShowbutton.value = false;
};

const onTechFileRemoved = (file, record) => {
  console.log('技术文件已删除:', file);
  record.maintenanceFileList = record.maintenanceFileList.filter(f => f.uid !== file.uid);
  isShowbutton.value = true;
};

// 在 script 部分添加上传状态变量
const isUploading = ref(false);

// 添加上传状态变更处理函数
const onUploadStatusChange = (status) => {
  isUploading.value = status;
};

const columns = ref([
  {
    title: '序号',
    width: 80,
    customRender: ({ text, record, index }) => {
      return `${index + 1}`;
    }
  },
  { title: '设备编号', dataIndex: 'code', width: 120 },
  { title: '设备名称', dataIndex: 'equNameStr', width: 140 },
  { title: '规格型号', dataIndex: 'equModelStr', width: 140 },
  { title: '包机人', dataIndex: 'contractUser', width: 140 },
  { title: '保养结果记录', dataIndex: 'maintenanceRecords', width: 140 },
  { title: '保养附件', dataIndex: 'maintenanceFileList', width: 200 },
])

const initializeData = async (id) => {
  try {
    const { data } = await maintenanceorderApi.getToDo({ procInstanceId: id });
    console.log('获取的数据：', data);
    // // 保存工作单信息，包含状态
    if (data.bussWorksheet) {
      bussWorksheet.value = data.bussWorksheet;
    }
    // // 处理业务标题和关联投资计划
    if (data.bussTransferForm) {
      formData.value = data.bussTransferForm
      formData.value.applyEndDate = data.bussTransferForm.applyEndDate || '--------';
    }

    // // 处理设备基本信息
    if (data.bussEquipmentProcessTrackingList?.length > 0) {
      formData.value.bussEquipmentProcessTrackingList = data.bussEquipmentProcessTrackingList;
      // formData.value.bussEquipmentProcessTrackingList[3].maintenanceIsCancel = true;
    }
    autoSave();
  } catch (error) {
    message.error('获取数据失败');
  }
};

// 构建保存数据的方法
const buildRequestDataSave = () => {
  const requestData = {
    bussWorksheet: {
      id: bussWorksheet.value.id
    },
    bussEquipmentProcessTrackingList: formData.value.bussEquipmentProcessTrackingList,
  };
  console.log('提交的数据：', requestData);
  return requestData;
};

let autoSaveTimer = null;
const autoSave = () => {
  //1分钟调用handlesave方法一次
  console.log('开启自动保存');
  if (autoSaveTimer) clearInterval(autoSaveTimer);
  autoSaveTimer = setInterval(() => {
    if (formData.value.applyUser && formData.value.applyOrg) {
      handleSave1();
    }
  }, 60000);
}

const handleSave1 = async () => {
  try {
    saving.value = true;
    submitting.value = true;

    const params = buildRequestDataSave();
    const res = await maintenanceorderApi.save(params);

    if (res.success) {

    } else {
      message.error('保存失败');
    }
  } catch (error) {
    message.error(error.message.split(",")[0]);
  } finally {
    saving.value = false;
    submitting.value = false;
  }
};

// 保存方法
const handleSave = async () => {
  try {
    saving.value = true;
    let params = {};
    params = buildRequestDataSave()

    console.log('params--', params)
    let res = await maintenanceorderApi.save(params);
    if (res.success) {
      if (res.data.bussTransferForm) {
        formData.value = res.data.bussTransferForm;
      }

      if (res.data.bussEquipmentProcessTrackingList) {
        formData.value.bussEquipmentProcessTrackingList = res.data.bussEquipmentProcessTrackingList;
      }

      // 完全覆盖bussWorksheet数据
      if (res.data.bussWorksheet) {
        bussWorksheet.value = res.data.bussWorksheet;
      }
      message.success('保存成功');
      console.log('res.data', res.data)
      //
    } else {
      message.error('保存失败');
    }
  } catch (error) {
    // message.error('保存失败：' + error.message);
  } finally {
    saving.value = false;
  }
};

const handleSubmit = async () => {
  try {
    submitting.value = true;
    // 构建最终提交数据结构
    const lastColumn = document.querySelector('.ant-table-thead .ant-table-cell:nth-last-child(2)');
    const array = formData.value.bussEquipmentProcessTrackingList;
    for (let i = 0; i < array.length; i++) {
      if (array[i].maintenanceIsCancel == false && !array[i].contractUser) {
        lastColumn.scrollIntoView({ behavior: "smooth", inline: 'center' });
        message.error('第' + (i + 1) + '行包机人不能为空')
        return
      }

      if (array[i].maintenanceIsCancel == false && !array[i].maintenanceRecords) {
        lastColumn.scrollIntoView({ behavior: "smooth", inline: 'center' });
        message.error('第' + (i + 1) + '行保养结果记录不能为空')
        return
      }

      if (array[i].maintenanceIsCancel == false) {
        if (!array[i].maintenanceFileList || array[i].maintenanceFileList.length == 0) {
          lastColumn.scrollIntoView({ behavior: "smooth", inline: 'center' });
          message.error('第' + (i + 1) + '行保养附件不能为空')
          return
        }

      }

    }
    const innerFormData = {
      formDatas: {
        bussWorksheet: {
          id: bussWorksheet.value.id
        },
        bussEquipmentProcessTrackingList: formData.value.bussEquipmentProcessTrackingList,
      }
    };
    const submitData = {
      taskId: taskId.value,
      variables: {
        formData: JSON.stringify(innerFormData)
      },
      comment: '已保养'
    };
    console.log('submitData', submitData);
    if (autoSaveTimer) {
      clearInterval(autoSaveTimer);
      autoSaveTimer = null;
    }
    const res = await maintenanceorderApi.submit(submitData);

    if (res.success) {
      message.success('提交成功');
      router.push('/buss/maintenanceorder');
    } else {
      message.error('提交失败');
      autoSave()
    }
  } catch (error) {
    message.error('提交失败');
    autoSave()
  } finally {
    submitting.value = false;
  }
};

onBeforeUnmount(() => {
  if (autoSaveTimer) {
    clearInterval(autoSaveTimer);
    autoSaveTimer = null;
  }
});

onMounted(() => {
  processDefinitionId.value = route.query.processDefinitionId;
  actId.value = route.query.actId;
  taskId.value = route.query.taskId;
  procInsId.value = route.query.procInsId;
  worksheetId.value = route.query.worksheetId;
  if (procInsId.value) {
    initializeData(procInsId.value);
  } else {
    message.error('未找到审批单ID');
  }

  watch(
    () => router.fullPath,
    () => {
      if (autoSaveTimer) {
        clearInterval(autoSaveTimer);
        autoSaveTimer = null;
      }
    }
  );
});


</script>

<style lang="less" scoped>
.custom-table {
  margin-top: 16px;

  :deep(.ant-table) {

    // 提高固定列的层级
    .ant-table-fixed-left,
    .ant-table-fixed-right {
      background: #fff;
      z-index: 3; // 增加层级
    }

    // .ant-table-cell:not(.ant-table-cell-fix-right-first) {
    //   white-space: nowrap !important; // 强制不换行
    //   overflow: hidden !important;
    //   text-overflow: ellipsis !important;
    //   line-height: 1 !important;
    //   font-size: 14px !important;

    //   >span,
    //   >div {
    //     white-space: nowrap !important;
    //     overflow: hidden !important;
    //     text-overflow: ellipsis !important;
    //   }
    // }

    // 大屏幕样式（默认）
    @media screen and (min-width: 1920px) {
      .ant-table-cell {
        padding: 20px 20px !important;
        height: 60px !important;
      }

      .ant-table-row {
        height: 60px !important;
      }
    }

    // 中等屏幕样式
    @media screen and (min-width: 1366px) and (max-width: 1919px) {
      .ant-table-cell {
        padding: 10px 20px !important;
        height: 40px !important;
      }

      .ant-table-row {
        height: 40px !important;
      }
    }

    // 小屏幕样式
    @media screen and (max-width: 1365px) {
      .ant-table-cell {
        padding: 4px 8px !important;
        height: 32px !important;
      }

      .ant-table-row {
        height: 32px !important;
      }
    }

    // 调整固定列单元格样式
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      z-index: 3 !important; // 增加层级
      background: #ECF4FE !important;
    }

    // 调整表头固定列样式
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right {
        z-index: 4 !important; // 确保表头在最上层
        background: #DAECFF !important;
      }
    }

    // 优化阴影效果
    .ant-table-fixed-right::before,
    .ant-table-fixed-left::before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      width: 10px;
      pointer-events: none;
      z-index: 2; // 阴影层级低于固定列
      transition: box-shadow .3s;
    }

    .ant-table-fixed-left::before {
      right: 0;
      box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
    }

    .ant-table-fixed-right::before {
      left: 0;
      box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
    }

    // 设置表格内容的层级
    .ant-table-content {
      z-index: 1;
    }

    // 确保滚动区域正确显示
    .ant-table-body {
      overflow-x: auto !important;
      overflow-y: auto !important;
    }

    // 固定列不换行
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right:not(.ant-table-cell-fix-right-first) {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .ant-table-row {
      height: 24px !important;
    }

    // 表头固定列不换行
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right:not(.ant-table-cell-fix-right-first) {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.approval-record-btn {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  z-index: 100;

  .bg-image {
    display: block;
    max-width: 100%;
    height: auto;
    object-fit: contain;
    background-color: transparent;
  }

  .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .icon {
      width: 15px;
      height: 15px;
      margin-bottom: 4px;
    }

    .text {
      writing-mode: vertical-lr;
      font-size: 14px;
      color: #333;
      letter-spacing: 2px;
    }
  }
}

.equipment-acceptance {
  width: 100%;
  max-width: 1920px; // 最大宽度限制
  height: calc(100vh - 100px);
  overflow: auto;
  margin: 0 auto; // 居中
  padding: 24px 9.06vw 24px 8.18vw; // 转换为vw: 174/1920*100=9.06, 157/1920*100=8.18
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);

  .form-title {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 24px;
  }

  .section {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;

      .blue-bar {
        width: 3px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
      }
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: clamp(16px, 2vw, 24px);
    margin-bottom: 24px;
    justify-content: space-between;

    .form-item {
      display: flex;
      flex-direction: column;
      min-width: 300px; // 最小宽度
      max-width: 300px; // 最大宽度

      .label {
        height: 32px;
        line-height: 32px;
        margin-bottom: 8px;
        color: #0C0B0B;

        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 4px;
        }
      }

      .value {
        width: 100%;
        height: 32px;
        color: #666;

        :deep(.ant-input),
        :deep(.ant-select),
        :deep(.ant-picker),
        :deep(.ant-input-number) {
          width: 100%;
          height: 32px;
        }

        :deep(.ant-input-number-input) {
          height: 16px;
          line-height: 16px;
        }

        :deep(.ant-select-selector) {
          height: 32px !important;

          .ant-select-selection-item {
            line-height: 32px;
          }
        }

        :deep(.ant-picker) {
          .ant-picker-input {
            height: 16px;
            line-height: 16px;

            input {
              height: 16px;
              line-height: 16px;
            }
          }
        }
      }
    }
  }

  .acceptance-no {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    max-width: 300px; // 与其他表单项保持一致的最大宽度

    :deep(.first-input) {
      width: 65% !important; // 验资前面的输入框占65%
    }

    :deep(.second-input) {
      width: 35% !important; // 号前面的输入框占35%
    }

    .separator {
      color: #666;
      white-space: nowrap;
    }
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .equipment-acceptance {
    padding: 24px 6vw;

    .form-grid {
      gap: 16px;
    }
  }
}

@media screen and (max-width: 1366px) {
  .equipment-acceptance {
    padding: 24px 4vw;

    .form-grid {
      grid-template-columns: repeat(2, minmax(300px, 1fr));
    }
  }
}

@media screen and (max-width: 1024px) {
  .equipment-acceptance {
    padding: 24px 2vw;

    .form-grid {
      grid-template-columns: repeat(1, 1fr);

      .form-item {
        max-width: none;
      }
    }
  }
}

.percentage-input {
  position: relative;
  display: flex;
  align-items: center;

  .ant-input {
    width: 100%;
  }

  .percentage-symbol {
    position: absolute;
    right: 12px;
    color: #666;
  }
}

.attachment-table {
  width: 89%;
  border: 1px solid #e8e8e8;
  border-radius: 2px;


  .table-header,
  .table-row,
  .add-row {
    // 添加add-row到统一高度设置中
    display: flex;
    padding: 12px 8px;

    border-bottom: 1px solid #e8e8e8;
    align-items: center;
    gap: 12px;
    min-height: 56px; // 统一设置最小高度
  }

  .table-header {
    font-weight: 500;
  }

  .table-row:last-child {
    border-bottom: none;
  }

  // 使用百分比和flex布局
  .col-serial {
    width: 5%; // 序号列较窄
    min-width: 40px;
  }

  .col-name {
    width: 25%; // 名称及型号需要较大空间
    min-width: 180px;
  }

  .col-unit {
    width: 10%; // 单位列较窄
    min-width: 80px;
  }

  .col-quantity {
    width: 10%; // 数量列较窄
    min-width: 80px;
  }

  .col-manufacturer {
    width: 20%; // 生产厂家需要适中空间
    min-width: 150px;
  }

  .col-serial-no {
    width: 20%; // 出厂编号需要适中空间
    min-width: 150px;
  }

  .col-action {
    width: 10%; // 操作列较窄
    min-width: 60px;
    text-align: center;

    .delete-btn {
      color: #FF4D4F;
      cursor: pointer;
    }
  }

  :deep(.ant-input),
  :deep(.ant-input-number) {
    width: 100%;
  }


  // 响应式调整
  @media screen and (max-width: 1366px) {
    .col-name {
      width: 22%; // 较小屏幕时稍微压缩名称列
    }

    .col-manufacturer,
    .col-serial-no {
      width: 18%; // 压缩这两列
    }
  }

  @media screen and (max-width: 1024px) {
    overflow-x: auto; // 当屏幕太小时允许横向滚动

    .table-header,
    .table-row {
      min-width: 900px; // 确保在小屏幕上内容不会过度压缩
    }
  }

  .add-row {
    justify-content: center; // 水平居中
    align-items: center; // 垂直居中
    cursor: pointer;
    border-bottom: none; // 最后一行不需要底部边框

    a {
      display: flex;
      align-items: center; // 图标和文字垂直居中
      color: #1890ff;

      .anticon {
        margin-right: 4px;
      }
    }

    &:hover {
      background: #f5f5f5;
    }
  }
}

.upload-section {
  .upload-header {
    display: flex;
    align-items: center;
    gap: 12px;

    .upload-button {
      display: inline-flex;
      align-items: center;
      padding: 4px 15px;
      background: #0085FF;
      border-radius: 2px;
      color: white;
      cursor: pointer;
      font-size: 14px;

      .anticon {
        margin-right: 4px;
      }
    }

    .upload-tip {
      color: #999;
      font-size: 12px;
    }
  }

  .file-list {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    //display: grid;
    //grid-template-columns: repeat(3, 0.3fr);
    //gap: 16px;

    .file-item {
      display: flex;
      align-items: center;
      padding: 8px;
      // background: #F7F9FC;
      border-radius: 2px;
      width: 32%;

      .file-link {
        word-break: break-all;
      }

      .file-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #2D5CF6;
      }

      .file-name {
        flex: 1;
        font-size: 14px;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-actions {
        margin-left: 8px;

        .delete-icon {
          color: #999;
          cursor: pointer;

          &:hover {
            color: #FF4B4B;
          }
        }

        .anticon-sync {
          color: #2D5CF6;
        }
      }
    }
  }
}

.tech-file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;

  .file-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      color: #2D5CF6;
    }

    .file-name {
      color: #333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .delete-icon {
    color: #999;
    cursor: pointer;

    &:hover {
      color: #ff4d4f;
    }
  }
}

.tech-upload-button {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  color: #666;
  font-size: 14px;

  .anticon {
    margin-right: 4px;
    font-size: 14px;
  }

  &:hover {
    color: #2D5CF6;
    border-color: #2D5CF6;
  }
}

.value {
  :deep(.ant-input-affix-wrapper) {
    .ant-input-suffix {
      color: #666;
    }
  }
}

.bottom-buttons {
  margin-top: 150px;
  margin-bottom: 89rpx;
  bottom: 180px; // 修改为距离底部80px
  left: 0;
  right: 0;
  padding: 0 32px; // 调整左右内边距
  text-align: right;
  z-index: 100;

  .save-btn {
    height: 32px;
    padding: 0 16px;
    border: 1px solid #DCDFE6;
    color: #606266;
    background: transparent !important;
    border-radius: 4px;

    &:hover {
      border-color: #409EFF;
      color: #409EFF;
    }
  }

  .submit-btn {
    height: 32px;
    padding: 0 16px;
    margin-left: 27px; // 调整按钮之间的间距
    background: #409EFF;
    border-radius: 4px;
    border: none;

    &:hover {
      background: #66B1FF;
    }
  }
}

.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>
