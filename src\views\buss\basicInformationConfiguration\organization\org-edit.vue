<!-- 组织机构编辑弹窗 -->
<template>
  <div>
    <!-- 编辑 -->
    <common-drawer
    :width="800"
    :visible="visible"
      :title="activeKey=='1'?'编辑机构':'设置审批人'"
      @close="updateVisible(false)"
      v-if="isUpdate"
      :isShowTab="false"
      :activeKey="activeKey"
      :tabList="tabList"
      @tabChange="tabChange"
    >
      <template #extra>
        <div style="height: 32px" v-if="activeKey=='1'">
          <a-button type="primary" @click="save" :loading="loading">确定</a-button>
          <a-button @click="updateVisible(false)" :loading="loading" style="margin-left: 20px;">取消</a-button>
        </div>
      </template>
      <!-- 基本信息 -->
      <a-form ref="formRef"  v-if="activeKey == '1'" :model="form" :rules="rules" :label-col="{ md: { span: 4 }, sm: { span: 24 } }"
        :wrapper-col="{ md: { span: 20 }, sm: { span: 24 } }">
        <a-form-item label="上级机构:" name="orgParentId">
          <a-tree-select :disabled="form.orgParentId === '-1'" v-model:value="form.orgParentId" style="width: 100%"
            :tree-data="orgList" :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" placeholder="请选择上级机构"
            :fieldNames="{ children: 'children', label: 'title', key: 'id', value: 'id' }" allow-clear
            @select="selectOrg" />
        </a-form-item>
        <a-form-item label="性质:" name="orgNature">
          <a-select v-model:value="form.orgNature" placeholder="请选择性质" style="width: 100%" allow-clear
            :disabled="disabled">
            <a-select-option v-for="item in xzList" :key="item.label" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="机构名称:" name="orgName">
          <a-input v-model:value="form.orgName" placeholder="请输入机构名称" allow-clear />
        </a-form-item>
        <a-form-item v-if="address" label="地址:" name="address">
          <a-cascader v-model:value="form.address" :options="cityOptions" placeholder="请选择地址"
            :fieldNames="{ label: 'name', value: 'id', children: 'children' }" />
          <!-- <city-tree-select v-model:value="form.address" placeholder="请选择城市" :options="cityOptions" :multiple="false"></city-tree-select> -->
        </a-form-item>
        <a-form-item label="机构编码:" name="orgCode">
          <a-input v-model:value="form.orgCode" placeholder="请输入机构编码" allow-clear />
        </a-form-item>
        <a-form-item label="排序号:" name="orgSort">
          <a-input-number v-model:value="form.orgSort" placeholder="请输入排序号" :min="0" class="ele-fluid" />
        </a-form-item>
      </a-form>
      <!-- 设置审批人 -->
      <set-approver v-else ref="setApproverRef" :data="data"></set-approver>
    </common-drawer>

    <!-- 新增 -->
    <a-modal :width="800" :visible="visible" :confirm-loading="loading" :forceRender="true" :maskClosable="false"
      title="新建机构" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible" @ok="save" v-else
      @close="updateVisible(false)">
      <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ md: { span: 4 }, sm: { span: 24 } }"
        :wrapper-col="{ md: { span: 20 }, sm: { span: 24 } }">
        <a-form-item label="上级机构:" name="orgParentId">
          <a-tree-select :disabled="form.orgParentId === '-1'" v-model:value="form.orgParentId" style="width: 100%"
            :tree-data="orgList" :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" placeholder="请选择上级机构"
            :fieldNames="{ children: 'children', label: 'title', key: 'id', value: 'id' }" allow-clear
            @select="selectOrg" />
        </a-form-item>
        <a-form-item label="性质:" name="orgNature">
          <a-select v-model:value="form.orgNature" placeholder="请选择性质" style="width: 100%" allow-clear
            :disabled="disabled">
            <a-select-option v-for="item in xzList" :key="item.label" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="机构名称:" name="orgName">
          <a-input v-model:value="form.orgName" placeholder="请输入机构名称" allow-clear />
        </a-form-item>
        <a-form-item v-if="address" label="地址:" name="address">
          <a-cascader v-model:value="form.address" :options="cityOptions" placeholder="请选择地址"
            :fieldNames="{ children: 'children', label: 'name', value: 'id' }" />
          <!-- <a-input v-model:value="form.address" placeholder="请输入机构名称" allow-clear /> -->
        </a-form-item>
        <a-form-item label="机构编码:" name="orgCode">
          <div class="flex flex-row">
            <div class="fixed-code" title="前缀固定码">
              <div v-if="codePrefixs.length > 0">
                <span>
                  {{ codePrefixs[codePrefixs.length-1].fcode }}
                  <!--
                  <template v-for="(item, index) in codePrefixs" :key="'fixed-code_'+index">
                    {{ item.fcode }}
                  </template>
                  -->
                </span>
              </div>
            </div>
            <a-input v-model:value="form.orgCode" placeholder="请输入机构编码" allow-clear class="flex-1"/>
          </div>
        </a-form-item>
        <a-form-item label="排序号:" name="orgSort">
          <a-input-number v-model:value="form.orgSort" placeholder="请输入排序号" :min="0" class="ele-fluid" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { UserApi } from '@/api/system/user/UserApi';
import { EnumApi } from '@/api/common/enum';
import { OrganizationApi } from '@/api/basicInformationConfiguration/organizationApi';
import CityTreeSelect from '@/components/CityTreeSelect/index.vue';
import SetApprover from './set-approver.vue';
import { nextTick } from 'vue';

export default {
  name: 'OrgEdit',
  components: { CityTreeSelect,SetApprover },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 接收上级传过来的组织机构信息
    data: Object,
    // 组织机构列表
    orgList: Array,
    // 是否是编辑界面
    isUpdate: Boolean,
    // 默认选中
    defaultKey: String,
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      // 表单数据
      form: {
        orgId: null,
        orgParentId: null,
        address: [],
        orgNature: null,
        orgName: null,
        orgCode: null,
        // orgType: null,
        orgSort: null,
      },
      // 表单验证规则
      /*
      rules: {
        orgParentId: [{ required: true, message: '请选择上级机构', type: 'string', trigger: 'blur' }],
        address: [{ required: true, message: '请选择地址', type: 'array', trigger: 'blur' }],
        orgNature: [{ required: true, message: '请选择性质', type: 'number', trigger: 'change' }],
        orgName: [{ required: true, message: '请输入组织机构名称', type: 'string', trigger: 'blur' }],
        orgCode: [{ required: true, message: '请输入组织机构编码', type: 'string', trigger: 'blur' }],
        // orgType: [{ required: true, message: '请选择机构类型', type: 'number', trigger: 'blur' }],
        orgSort: [{ required: true, message: '请输入组织机构排序', type: 'number', trigger: 'blur' }],
      },*/
      // 提交状态
      loading: false,
      xzList: [],
      address: false,
      lxList: [],
      cityOptions: [],
      disabled: false,
      // 提交状态
      loading: false,
      activeKey: '1',
      tabList: [
        {
          key: '1',
          name: '基本信息'
        },
        {
          key: '2',
          name: '设置审批人'
        }
      ],
      codePrefixs:[], // 机构代码前缀
      //codeRules : {"1":2,"2":3,"3":2,"4":4} // 规则 默认都是4位，带有虚拟外租单位的，使用上级机构代码
    };
  },
  // mounted() {
  //   this.init();
  // },
  computed: {
    rules: function(){  // 改为计算属性
        let re = {
          orgParentId: [{ required: true, message: '请选择上级机构', type: 'string', trigger: 'blur' }],
          address: [{ required: true, message: '请选择地址', type: 'array', trigger: 'blur' }],
          orgNature: [{ required: true, message: '请选择性质', type: 'number', trigger: 'change' }],
          orgName: [{ required: true, message: '请输入组织机构名称', type: 'string', trigger: 'blur' }],
          orgCode: [{ required: true, message: '请输入组织机构编码，数值类型', type: 'string', trigger: 'blur',pattern: /^[0-9]+$/ }],
          // orgType: [{ required: true, message: '请选择机构类型', type: 'number', trigger: 'blur' }],
          orgSort: [{ required: true, message: '请输入组织机构排序', type: 'number', trigger: 'blur' }],
        }
        if(!this.isUpdate)
        {
          re.orgCode = [{ required: true, message: '请输入组织机构编码，数值类型，长度为4位', type: 'string', trigger: 'blur',len:4,pattern: /^[0-9]+$/ }];
        }
        return re;
      }
  },
  watch: {
    // 编辑时监听data是否变化
    data() {
      this.init();
      this.initEnumData();
      // 20250704 王文胜
      this.resetForm();
    }
  },
  methods: {

    resetForm(){ // 清空表单
      this.codePrefixs = [];
      if(this.$refs.formRef)
      this.$refs.formRef.clearValidate();
    },
    // tab切换
    tabChange(key) {
      this.activeKey = key;
      nextTick(() => {
        if (key == '1') {
        //  this.init()
        } else {
          this.$refs.setApproverRef.getListData();
        }
      });
    },
    selectOrg(value, node, exea) {
      this.codePrefixs = []; // 清空前缀
      if (node.orgNature == 3) {
        this.form.orgNature = this.xzList[1].value
        this.disabled = true
        this.address = false
        if (node.level >= 1) {
          this.address = true
        }
      } else {
        this.form.orgNature = null
        this.disabled = false
        this.address = false
        if (node.level == 0) {
          this.form.orgNature = this.xzList[0].value
          this.disabled = true
        }
        if (node.level >= 1) {
          this.address = true
        }
      }
      // 设置前缀 20250704 王文胜
      this.parseCodePrefix(value,node,{id:-1000,level:-1,children:this.orgList},this.codePrefixs);
    },
    parseCodePrefix(value,node,root,ret) /// 解析编码前缀 20250704 王文胜
    {
      if(root.level>0 && root.id==value)
      {
        if(root.orgNature!=3) // 虚拟外租单位使用父级
          ret.push({id:root.id,level:root.level,name:root.orgName,code:root.orgCode,fcode:this.completeCode(root.level,root.orgCode)});
        return true;
      }
      if(root.children && root.children.length>0)
        for(let i=0;i<root.children.length;i++)
        {
          if(this.parseCodePrefix(value,node,root.children[i],ret))
          {
            if(root.level>0 && root.orgNature!=3 ) // 虚拟外租单位使用父级
              ret.unshift({id:root.id,level:root.level,name:root.orgName,code:root.orgCode,fcode:this.completeCode(root.level,root.orgCode)});
            return true;
          }
        }
      return false;
    },
    completeCode(level,value){  /// 补全代码 20250704 王文胜
      let len = 4; // 默认4位长度
      //len = this.codeRules[level]||4;
      value = value+"";
      let offset = len - value.length;
      for(let i=0;i<offset;i++)
      {
        value = "0"+value;
      }
      return value;
    },
    async initEnumData() {
      let arr = await EnumApi.getEnumList({ enumName: 'OrgNatureEnum' });
      let arr1 = await EnumApi.getEnumList({ enumName: 'OrgTypeEnum' })
      let arr2 = await EnumApi.getAddressTree('')
      this.xzList = arr.data
      this.lxList = arr1.data
      this.cityOptions = arr2.data[0].children
    },
    // 初始化数据
    async init() {
      this.address = false
      if (this.data.orgId) {
        this.tabChange(this.defaultKey);
        let res = await UserApi.getOrgDetail(this.data);
        this.form = res
        if (this.form.orgNature == 3) {
          this.disabled = true
          this.address = false
          if (this.form.orgLevel >= 2) {
            this.address = true
          }
        } else {
          this.disabled = false
          this.address = false
          if (this.form.orgLevel == 0) {
            this.disabled = true
          }
          if (this.form.orgLevel >= 2) {
            this.address = true
          }
        }
        this.form.address = []
        console.log('this.form', this.form);
        if (this.form.addrNation) {
          this.form.address.push(this.form.addrNation)
          if (this.form.addrProvince) {
            this.form.address.push(this.form.addrProvince)
            if (this.form.addrCity) {
              this.form.address.push(this.form.addrCity)
              if (this.form.addrCounty) {
                this.form.address.push(this.form.addrCounty)
              }
            }
          }
        }
        // this.form.address = [res.addrNation]
      } else {
        this.address = false
        this.form = {}
      }
    },
    save() {
      // 校验表单
      this.form.addrNation = ''
      console.log('this.form', this.form);
      let obj = {
        ...this.form,
      }
      if (this.form.address && this.form.address.length > 0) {
        obj.addrNation = this.form.address[0]
        obj.addrProvince = this.form.address[1]
        obj.addrCity = this.form.address[2]
        obj.addrCounty = this.form.address[3]
      }

      this.$refs.formRef.validate().then(async valid => {
        if (valid) {
          // 修改加载框为正在加载
          this.loading = true;
          let result;
          // 执行编辑或修改用户方法
          if (this.isUpdate) {
            result = OrganizationApi.edit(obj);
          } else {
            /// 处理前缀数据
            let code = "";
            if(this.codePrefixs.length > 0)
            {
              code += this.codePrefixs[this.codePrefixs.length-1].fcode;
            }
            code += obj.orgCode;
            // 合并数据
            let formData = Object.assign({}, obj,{orgCode:code});
            result = OrganizationApi.add(formData);
          }
          result.then(res => {
            // 移除加载框
            this.loading = false;
            // 提示添加成功
            message.success(res.message);
            // 如果是新增，则form表单置空
            if (!this.isUpdate) {
              this.form = {};
            }
            // 关闭弹框，通过控制visible的值，传递给父组件
            this.updateVisible(false);
            this.$emit('done');
          })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    },

    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2021/4/7 11:00
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>
<style scoped>
.ant-modal-content {
  background-color: #F4F9FF;
}

.ant-modal-header {
  background-color: #F4F9FF;
}
.flex
{
  display: flex;
}
.flex-row
{
  flex-direction: row;
}
.flex-1
{
  flex:1;
}
.fixed-code > div
{
  margin-right: 6px;
  display: inline-block;
}
.fixed-code span
{
  letter-spacing: 2px;
  display: inline-block;
  border:1px solid #aaa;
  text-align: center;
  margin:0px 2px;
  padding: 3px 5px;
  border-radius: 3px;
  font-size: 16px;
  background-color: #eee;
}
</style>
