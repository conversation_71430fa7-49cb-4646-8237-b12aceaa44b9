import Request from '@/utils/request-util';

/**
 * 机构管理的api
 */
export class aintenancecycleApi {
  /**
   * 列表
   */
  static getList(params) {
    return Request.get('/apiBus/maintenanceType/page', params);
  }

  /**
   * 新增
   */
  static add(params) {
    return Request.post('/apiBus/maintenanceType/add', params);
  }

  /**
   * 修改
   */
  static edit(params) {
    return Request.post('/apiBus/maintenanceType/edit', params);
  }


  /**
   * 删除
   */
  static delete(id) {
    return Request.post('/apiBus/maintenanceType/delete?id='+id);
  }


  /**
   * 详情
   */
  static detail(id) {
    return Request.get('/apiBus/maintenanceType/detail?id='+id);
  }

  /**
   * 下拉列表
   */
  static getSelectList() {
    return Request.get('/apiBus/maintenanceType/getList');
  }
}
