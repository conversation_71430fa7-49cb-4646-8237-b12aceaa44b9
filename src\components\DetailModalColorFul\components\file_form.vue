<template>
  <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
    :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }">
    <div class="file-list-container">
      <template v-if="formData.fileList && formData.fileList.length > 0">
        <div class="file-grid">
          <div v-for="(file, index) in formData.fileList" :key="file.fileId" class="file-item">
            <div class="file-content">
              <div class="file-icon">
               <img src="/public/icon/icon-wrapper.svg" v-if="file.fileOriginName.endsWith('.xls') || file.fileOriginName.endsWith('.xlsx')" />
                  <img src="/public/icon/word.svg" v-else-if="file.fileOriginName.endsWith('.doc') || file.fileOriginName.endsWith('.docx')" />
                  <img src="/public/icon/pdf.svg" v-else-if="file.fileOriginName.endsWith('.pdf')" />
                  <img src="/public/icon/mp4.svg" v-else-if="file.fileOriginName.endsWith('.mov') || file.fileOriginName.endsWith('.mp4')" />
                  <img src="/public/icon/ppt.svg" v-else-if="file.fileOriginName.endsWith('.ppt') || file.fileOriginName.endsWith('.pptx')" />
                  <img src="/public/icon/jpg.svg" v-else-if="file.fileOriginName.endsWith('.png') || file.fileOriginName.endsWith('.jpg') || file.fileOriginName.endsWith('.jpeg') || file.fileOriginName.endsWith('.heif')" />
                  <img src="/public/icon/reader.svg" v-else />
              </div>
              <div class="file-name">{{ file.fileOriginName }}</div>
            </div>
            <a :href="`${file.fileUrl}?filename=${file.fileOriginName}`" target="_blank" class="preview-link">预览</a>
          </div>
        </div>
      </template>
       <div v-show="!detailData.fileList?.length" class="no-files">
        <img src="@/assets/images/noData.png" alt="">
      </div>
    </div>
  </a-form>
</template>

<script setup>
import { defineComponent, reactive, toRefs, ref, watch } from 'vue';
import iconData from 'ele-admin-pro/es/ele-icon-picker/icons';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: '设备详情'
  }
});
const detailData = ref({});
const formData = ref({});
watch(() => props.data, (val) => {
  console.log(val)
  if(val){
    if(val.type == "scrap"){
      formData.value = val.bussScrapedEquipment;
    }else if(val.type == "disposal"){
      formData.value = val.bussDisposaledEquipment;
    }else{
      formData.value = val.bussRegisteredEquipment;
    }
  }

}, { immediate: true });
</script>

<style lang="less" scoped>
.file-list-container {
  padding: 16px;
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.file-content {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0; // 确保flex子项可以正确缩小
}

.file-icon {
  margin-right: 12px;
  font-size: 20px;
  flex-shrink: 0; // 防止图标缩小

  .icon-excel {
    color: #1D6F42; // Excel绿色
  }

  .icon-word {
    color: #2B579A; // Word蓝色
  }

  .icon-pdf {
    color: #F40F02; // PDF红色
  }

  .icon-video {
    color: #FF7700; // 视频橙色
  }

  .icon-default {
    color: #8C8C8C; // 默认灰色
  }
}

.file-name {
  font-size: 14px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.preview-link {
  color: #1890ff;
  font-size: 14px;
  text-decoration: none;
  margin-left: 12px;
  flex-shrink: 0; // 防止"预览"文字缩小

  &:hover {
    text-decoration: underline;
  }
}

.a-form{
      height: 483px;
}

.no-files {
  text-align: center;
  padding: 24px;
  color: #999;
  font-style: italic;
}
</style>


