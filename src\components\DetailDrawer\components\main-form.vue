<template>
  <a-form ref="formRef" :model="detailData.bussEquipmentAccessoryList" :rules="rules" :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
    :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }">
    <a-row :gutter="16">
      <a-col :md="24" :sm="24" :xs="24" style="padding-right: 0px;">
        <a-table :columns="columns" :data-source="detailData.bussEquipmentAccessoryList" bordered :pagination="false">
          <template #emptyText>
              <div class="custom-empty">
                <img src="@/assets/images/noData.png" />
                <!-- <p>抱歉，暂时还没有数据</p> -->
              </div>
            </template>
          <!-- <template #bodyCell="{ column, text }">
            <template v-if="column.dataIndex === 'name'">
              <a>{{ text }}</a>
            </template>
          </template> -->
          <!-- <template #title>Header</template>
          <template #footer>Footer</template> -->
        </a-table>
      </a-col>
    </a-row>
  </a-form>
</template>

      <script setup>
      import { defineComponent, reactive, toRefs, ref, watch } from 'vue';
      import iconData from 'ele-admin-pro/es/ele-icon-picker/icons';
      const props = defineProps({
        visible: {
          type: Boolean,
          default: false
        },
        data: {
          type: Object,
          default: () => ({})
        },
        title: {
          type: String,
          default: '设备详情'
        }
      });
      const columns = [
        {
          title: '序号',
          dataIndex: 'xh',
          width: 80,
          customRender: ({ index }) => index + 1
        },
        {
          title: '名称及型号',
          dataIndex: 'nameModel',

          width: 120,
        },
        {
          title: '单位',
          dataIndex: 'unit',
        },
        {
          title: '数量',
          dataIndex: 'num',
          width: 80,
        },
        {
          title: '生产厂家',
          dataIndex: 'manufacturer',
        },
        {
          title: '出场编号',
          dataIndex: 'factoryNumber',
          width: 160,
        },
      ]

      const detailData = ref({});
      watch(() => props.data, (val) => {
        console.log(val)
        if( val){
          if(val.type == "scrap"){
            detailData.value = val.bussScrapedEquipment;
          }else if(val.type == "disposal"){
            detailData.value = val.bussDisposaledEquipment;
          }else{
            detailData.value = val.bussRegisteredEquipment;
          }
          //detailData.value = val.detailData;
          console.log(detailData.value)
        }

      }, { immediate: true });
      </script>

<style lang="less" scoped>
.rows {
  display: flex;
  height: 53px;
  align-items: center;
  border: 1px solid rgba(172, 180, 201, 0.2);

  .rows_1 {
    width: 145px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #F1F8FF;
    border-right: 1px solid rgba(172, 180, 201, 0.2);

    .rows_1_1 {
      margin-right: 16px;
    }
  }

  .rows_2 {
    .rows_2_2 {
      margin-left: 16px;
    }
  }
}


.rows2 {
  display: flex;
  height: 53px;
  align-items: center;
  border-top: 1px solid rgba(172, 180, 201, 0.2);
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);

  .rows2_2 {
    width: 145px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: rgba(241, 248, 255, 1);
    border-right: 1px solid rgba(172, 180, 201, 0.2);

    .rows2_2_2 {
      margin-right: 16px;
    }
  }

  .rows2_3 {
    .rows2_3_3 {
      margin-left: 16px;
    }
  }
}

.rows1 {
  border-top: 0;
  border-left: 1px solid rgba(172, 180, 201, 0.2);
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);
}

.rows22{
  border-top: 0;
  border-left: 0;
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);
}

:deep(.ant-table-thead > tr > th) {
  padding: 10px 16px !important;
}

:deep(.ant-table-tbody .ant-table-cell) {
  padding: 10px 16px !important;
}
.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>
