<template>
  <div class="ele-body">
    <div  class="notice-wrapper" style="display: flex; align-items: center;justify-content: center;">
       <img src="@/assets/images/noData3.png" style="width: 30%;"  alt="">
    </div>
  </div>
</template>

<script>
</script>

<style lang="less" scoped>
.notice-wrapper {
  height: calc(100vh - 100px);
  overflow: auto;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  border-radius: 6px;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);
  padding: 20px;
}

.left-nav {
  font-family: HarmonyOS Sans SC;
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
  letter-spacing: normal;
  color: #1A1A1A;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 0 16px;

  .back-btn {
    font-size: 16px;
    color: #333;
    padding: 0;

    .anticon {
      margin-right: 8px;
    }
  }

  .right-nav {
    .ant-btn-link {
      color: #1890ff;
      font-size: 14px;
    }
  }
}

.notice-content {
  padding: 10px 16px;
}

.notice-list1 {
  .notice-item {
    margin-bottom: 16px;
    border-radius: 10px;
    border: 1px solid #FFFFFF;
    background: rgba(255, 255, 255, 1);
    box-sizing: border-box;
    backdrop-filter: blur(398px);
    box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);
    padding: 6px 26px;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .notice-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .notice-title {
        display: flex;
        align-items: center;
        gap: 8px;
        max-width: 70%; // 限制标题宽度，留出时间显示空间

        .title-text {

          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
          /* 小标题 */
          font-family: HarmonyOS Sans SC;
          font-size: 16px;
          font-weight: 600;
          line-height: 22px;
          letter-spacing: normal;
          /* 字体/选中强调 */
          color: #303133;
        }

        .unread-badge {
          color: #ff4d4f;
          font-size: 12px;
          flex-shrink: 0; // 防止红点被压缩
        }
      }

      .notice-time {
        color: #666;
        font-size: 14px;
        flex-shrink: 0; // 防止时间被压缩
      }
    }

    .notice-body {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .notice-desc {
        color: #333;
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 50%; // 内容最多占50%宽度
      }
    }

    .notice-footer {
      margin-top: 0; // 移除上边距，因为不再需要

      .detail-btn {
        color: #1890ff;
        padding: 0;
        font-size: 14px;

        &:hover {
          color: #40a9ff;
        }
      }
    }
  }


  .notice-item2 {
    margin-bottom: 16px;
    border-radius: 10px;
    border: 1px solid #FFFFFF;
    background: rgba(250, 252, 255, 0.4);
    box-sizing: border-box;
    backdrop-filter: blur(398px);
    box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);
    padding: 6px 26px;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .notice-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .notice-title {
        display: flex;
        align-items: center;
        gap: 8px;
        max-width: 70%; // 限制标题宽度，留出时间显示空间

        .title-text {

          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
          /* 小标题 */
          font-family: HarmonyOS Sans SC;
          font-size: 16px;
          font-weight: 600;
          line-height: 22px;
          letter-spacing: normal;
          /* 字体/选中强调 */
          color: #303133;
        }

        .unread-badge {
          color: #ff4d4f;
          font-size: 12px;
          flex-shrink: 0; // 防止红点被压缩
        }
      }

      .notice-time {
        color: #666;
        font-size: 14px;
        flex-shrink: 0; // 防止时间被压缩
      }
    }

    .notice-body {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .notice-desc {
        color: #333;
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 50%; // 内容最多占50%宽度
      }
    }

    .notice-footer {
      margin-top: 0; // 移除上边距，因为不再需要

      .detail-btn {
        color: #1890ff;
        padding: 0;
        font-size: 14px;

        &:hover {
          color: #40a9ff;
        }
      }
    }
  }
}

// 响应式处理
@media screen and (max-width: 768px) {
  .notice-wrapper {
    padding: 16px;
  }

  .notice-item {
    padding: 16px !important;
  }
}
</style>
