<template>
  <div class="ele-body">
    <div class="reg-body">
      <!-- 顶部按钮组 -->
      <div class="header-tools">
        <div class="left">

          <a-button class="filter-button" style="display: flex;align-items: center;justify-content: space-between;"
            @click="isShowSearch = !isShowSearch">
            <!-- <template #icon><filter-outlined /></template> -->
            <i class="iconfont icon-search" style="margin-right: 6px;"></i>
            筛选
          </a-button>
          <div class="search-input">
            <a-input v-model:value="queryParams.searchText" placeholder="搜索" :style="{ width: '240px' }"
              @keyup.enter="handleSearch" allow-clear style="margin-right: 10px;" @change="handleChange">
              <template #suffix>
                <search-outlined class="search-icon" @click="handleSearch" />
              </template>
            </a-input>
            <a-tooltip title="搜索字段包含（设备编号,财务卡片编号, 存放地点,出厂编号,财务原值,净值,生产厂家）">
              <i class="iconfont icon-tooltip" style="margin-right: 6px;color: #0085FF; cursor: pointer;"></i>
            </a-tooltip>
          </div>
        </div>
        <div class="right">
          <!-- <a-button class="tool-button" style="display: flex;align-items: center;justify-content: space-between;">
            <i class="iconfont icon-import" style="margin-right: 6px;"></i>
            导出
          </a-button> -->
          <a-button class="tool-button" style="display: flex;align-items: center;justify-content: space-between;"
            @click="handleExport" v-privilege="'equipmentform:scrap:export'">
            <i class="iconfont icon-rongqi-copy" style="margin-right: 6px;"></i>
            导出
          </a-button>
          <a-button class="tool-button"
            style="color: #176DF4;display: flex;align-items: center;justify-content: space-between;"
            @click="goToEquipmentScrapping" v-privilege="'equipmentform:scrap:create'">
            <i class="iconfont icon-sbbf" style="margin-right: 6px; margin-top: 3px;"></i>
            设备报废
          </a-button>
          <!-- <a-button class="tool-button" style="color: #176DF4;">
            <template #icon>
              <DesktopOutlined />
            </template>
            设备入账
          </a-button> -->
          <a-button type="link" class="link-button" @click="goToApplicationRecord"
            v-privilege="'equipmentform:scrap:record'">
            申请记录 >
          </a-button>
        </div>
      </div>

      <!-- 搜索工具栏 -->
      <div class="search-form" v-if="isShowSearch">
        <a-row :gutter="16">
          <a-col :span="10">
            <a-form-item label="设备类型">
              <a-tree-select v-model:value="sblxList" show-search
                :dropdown-style="{ maxHeight: '440px', overflow: 'auto' }" placeholder="请选择" allow-clear multiple
                :show-checked-strategy="SHOW_ALL" :tree-data="equipmentTypeList"
                :field-names="{ label: 'name', value: 'id' }" @select="selectSelect" @change="changeSelect"
                tree-node-filter-prop="name" max-tag-count="responsive" style="width: 100%;">
              </a-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="使用单位">
              <a-tree-select v-model:value="useOrg" show-search
                :dropdown-style="{ maxHeight: '440px', overflow: 'auto' }" placeholder="请选择" allow-clear multiple
                :show-checked-strategy="SHOW_ALL" :tree-data="orgList" :field-names="{ label: 'name', value: 'id' }"
                tree-node-filter-prop="name" max-tag-count="responsive" style="width: 100%;"
                :treeExpandedKeys="expandedKeys" @treeExpand="handleTreeExpand">
                <template #title="node">
                  <span @click="(e) => handleTitleClick(e, node)" style="display: inline-block; width: 100%;">
                    {{ node.name }}
                  </span>
                </template>
              </a-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="2" style="display: flex; justify-content: end;">
            <a-button style="width: 100%;" type="primary" class="search-button" @click="handleSearch">查询</a-button>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="5">
            <a-form-item label="产权单位">
              <a-select v-model:value="queryParams.propertyOrg" placeholder="全部" style="width: 100%">
                <a-select-option value="">全部</a-select-option>
                <a-select-option v-for="item in cqdwList" :key="item.label" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="管理单位">
              <a-select v-model:value="queryParams.managementOrg" placeholder="全部" style="width: 100%">
                <a-select-option value="">全部</a-select-option>
                <a-select-option v-for="item in gldwList" :key="item.label" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="购置年度">
              <a-range-picker v-model:value="yearAll" picker="year" :placeholder='placeholders'
                :value-format="yearFormat" style="width: 100%;" />
            </a-form-item>
          </a-col>
          <a-col :span="7">
            <a-form-item label="报废日期">
              <a-range-picker v-model:value="scrapAll" picker="Date" :placeholder='placeholders'
                :value-format="yearFormats" style="width: 100%;" dropdownClassName="custom-datepicker-dropdown" />
            </a-form-item>
          </a-col>
          <a-col :span="2" style="display: flex; justify-content: end;">
            <a-button style="width: 100%;" class="reset-button" @click="handleReset">重置</a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 数据表格 -->
      <a-table :columns="columns" :data-source="tableData" @change="handleTableChange" :pagination="false"
        :scroll="scroll" class="custom-table" @resizeColumn="handleResizeColumn">
        <template #emptyText>
          <div class="custom-empty">
            <img src="@/assets/images/noData.png" />
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'equModelStr'">
      <span title="{{ record.equModelStr }}">{{ record.equModelStr }}</span>
    </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleView(record)">查看</a>
              <a-button type="link" @click="handleAmend(record)" style="padding: 0; margin: 0; height: auto;"
                v-privilege="'equipmentform:scrap:amend'">
                修改
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 表格底部统计信息 -->
      <div class="table-footer" :class="{ 'follow-page': isShowSearch }">
        <div class="total-info">
          设备数量合计: {{ totalCount }}
          <!-- ，财务原值合计：{{ totalFinanceValue }}元，净值合计：{{ totalNetValue }}万元 -->
        </div>
        <a-pagination style="z-index: 10;" v-model:current="pagination.current" :total="pagination.total"
          :showLessItems="true" :showSizeChanger="true" @change="handleTableChange" :defaultPageSize="20"
          :pageSizeOptions="['20', '50', '100']" @showSizeChange="handleSizeChange" />
      </div>
    </div>
  </div>
  <!-- handleTableChange -->
  <!-- 查看弹窗 -->
  <!-- <scrapEquipment-edit v-model:visible="showEdit" :data="current" @done="reload" v-if="showEdit" /> -->
  <!-- <detail-drawer v-model:visible="showDetail" :data="current" title="报废设备信息" @close="handleDrawerClose" /> -->
  <detail-drawer v-model:visible="showDetail" :data="current" title="报废设备信息" @close="handleDrawerClose" />
</template>

<script setup>
import { ref, onMounted, watch, nextTick ,h} from 'vue';
import {
  SearchOutlined,
  FilterOutlined,
  LogoutOutlined,
  LoginOutlined,
  DesktopOutlined
} from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import { scrapEquipmentApi } from '@/api/buss/scrapEquipmentApi';
import { BasicInformationApi } from '@/api/buss/BasicInformationApi';
import { OrganizationApi } from '@/api/system/organization/OrganizationApi';
import TreeCascadeSelect from '@/components/TreeCascadeSelect/index.vue';
import { message } from 'ant-design-vue';
import { EnumApi } from '@/api/common/enum';
import { UnitApi } from '@/api/common/UnitApi';
import DetailDrawer from '@/components/DetailDrawer/index.vue'
import { TreeSelect } from 'ant-design-vue';


const yearAll = ref([])
const scrapAll = ref([])
const placeholders = ref(['请选择', '请选择'])
const sblxList = ref([])
const useOrg = ref([])
const scroll = ref({ x: 'max-content', y: 'calc(100vh - 270px)' })
// const expandedKeys = ref([]);
// 查询参数
const queryParams = ref({
  searchText: null, //搜索内容
  useOrg: null,//使用单位
  propertyOrg: null,//产权单位
  // equCondition: null,//设备状态
  // managementStatus: null,//管理状态
  purchaseYearBegin: null,//购置年度开始
  purchaseYearEnd: null,//购置年度结束
  scrapDateBegin: null,//报废日期开始
  scrapDateEnd: null,//报废日期结束
  equType: null,//设备类别
  equSubType: null,//设备种类
  equModel: null,//规格型号
  equName: null,//设备名称
  pageSize: 20,
  pageNo: 1
});
const SHOW_ALL = TreeSelect.SHOW_ALL;
// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0
});

const handleChange = (e) => {
  console.log('e', e.type);
  console.log('e', e.type);
  nextTick(() => {
    if (e.type === 'click') {
      handleSearch()
    }
  })
}

const handleResizeColumn = (w, col) => {
  console.log(w, col);
  col.width = w;
  col.xyz = 111
};

const sList = ref([])
const handleAmend = (record) => {
  console.log('record', record);
  router.push({
    path: '/buss/equAcceptanceAmend',
    query: {
      id: record.id,
      type: 'scrap'
    }
  });
};
const selectSelect = (value, node, extra) => {
  sList.value.push({
    value: value,
    level: node.level
  })
}

const arrList = ref([])
const changeSelect = (value, node, extra) => {
  arrList.value = []
  queryParams.value.equType = []
  queryParams.value.equSubType = []
  queryParams.value.equModel = []
  queryParams.value.equName = []
  console.log('value.value', value);
  console.log('sList.value', sList.value);
  nextTick(() => {
    for (let i = 0; i < value.length; i++) {
      for (let s = 0; s < sList.value.length; s++) {
        if (sList.value[s].value == value[i]) {
          arrList.value.push({
            value: value[i],
            level: sList.value[s].level
          })
        }
      }

    }
    arrList.value = getArray(arrList.value)
    console.log('arrList.value', arrList.value);

  })
}
// 查询方法
const handleSearch = () => {
  // 重置分页到第一页
  pagination.value.current = 1;
  queryParams.value.pageNo = pagination.value.current;
  queryParams.value.pageSize = pagination.value.pageSize;

  if (yearAll.value && yearAll.value.length > 0) {
    queryParams.value.purchaseYearBegin = yearAll.value[0]
    queryParams.value.purchaseYearEnd = yearAll.value[1]
  } else {
    queryParams.value.purchaseYearBegin = null
    queryParams.value.purchaseYearEnd = null
  }

  if (scrapAll.value && scrapAll.value.length > 0) {
    queryParams.value.scrapDateBegin = scrapAll.value[0]
    queryParams.value.scrapDateEnd = scrapAll.value[1]
  } else {
    queryParams.value.scrapDateBegin = null
    queryParams.value.scrapDateEnd = null
  }
  console.log('useOrg.value.join(', ')', useOrg.value.join(','));

  queryParams.value.useOrg = useOrg.value.join(',')
  const arr = []
  const arr1 = []
  const arr2 = []
  const arr3 = []
  for (let i = 0; i < arrList.value.length; i++) {
    if (arrList.value[i].level == '1') {
      arr.push(arrList.value[i].value)
    }
    if (arrList.value[i].level == '2') {
      arr1.push(arrList.value[i].value)
    }
    if (arrList.value[i].level == '3') {
      arr2.push(arrList.value[i].value)
    }
    if (arrList.value[i].level == '4') {
      arr3.push(arrList.value[i].value)
    }
  }
  queryParams.value.equType = arr.join(',')
  queryParams.value.equSubType = arr1.join(',')
  queryParams.value.equName = arr2.join(',')
  queryParams.value.equModel = arr3.join(',')
  // console.log('newArr', [...map.values()]);
  // console.log('yearAll', yearAll.value)
  console.log('queryParams.value', queryParams.value);
  // 调用查询接口
  datasource(queryParams.value);
};
// 重置按钮处理函数
const handleReset = () => {
  queryParams.value = {
    searchText: null, //搜索内容
    useOrg: null,//使用单位
    propertyOrg: null,//产权单位
    // equCondition: null,//设备状态
    // managementStatus: null,//管理状态
    purchaseYearBegin: null,//购置年度开始
    purchaseYearEnd: null,//购置年度结束
    scrapDateBegin: null,//报废日期开始
    scrapDateEnd: null,//报废日期结束
    equType: null,//设备类别
    equSubType: null,//设备种类
    equModel: null,//规格型号
    equName: null,//设备名称
    pageSize: 20,
    pageNo: 1
  };
  yearAll.value = []
  sList.value = []
  sblxList.value = []
  useOrg.value = []
  datasource(queryParams.value);
};

const selectUse = (value, node, extra) => {
  console.log('extra', extra)
}

// // 切换节点展开状态
// const toggleExpand = (key) => {
//   // console.log('key',key);

//   // if (expandedKeys.value.includes(key)) {
//   //   expandedKeys.value = expandedKeys.value.filter(k => k !== key);
//   // } else {
//   //   expandedKeys.value = [...expandedKeys.value, key];
//   // }
//   // console.log('expandedKeys.value',expandedKeys.value);
// };

// // 处理展开事件（可选）
// const handleExpand = (expandedKeys) => {
//   console.log('expandedKeys', expandedKeys);
//   expandedKeys.value = expandedKeys;
// };

const getArray = (selectData) => {
  let map = new Map()
  for (let item of selectData) {
    if (!map.has(item.value)) {
      map.set(item.value, item)
    }
  }
  const list = [...map.values()]
  console.log(list, '数组去重后')
  return list
}
// 当前编辑数据
const current = ref(null)
// 是否显示编辑弹窗
const showEdit = ref(false)
// 是否显示详情弹窗
const showDetail = ref(false)
// 表格列定义
const columns = [
  // 左侧固定列
  {
    title: '序号', width: 80, fixed: 'left',
    customRender: ({ text, record, index }) => {
      return `${(pagination.value.current - 1) * pagination.value.pageSize + index + 1}`;
    }
  },
  {
    title: '设备编号', dataIndex: 'code', width: 120, fixed: 'left', ellipsis: true,resizable: true,
    customHeaderCell: () => ({
      style: {
        minWidth: '120px',
        maxWidth: '120px'
      }
    }),
    customCell: () => ({
      style: {
        minWidth: '120px',
        maxWidth: '120px'
      }
    })
  },
  {
    title: '财务卡片编号', dataIndex: 'financialNumber', width: 130, fixed: 'left', ellipsis: true,
    customHeaderCell: () => ({
      style: {
        minWidth: '130px',
        maxWidth: '130px'
      }
    }),
    customCell: () => ({
      style: {
        minWidth: '130px',
        maxWidth: '130px'
      }
    })
  },
  {
    title: '设备名称', dataIndex: 'equNameStr', width: 100, fixed: 'left', ellipsis: true,
    customHeaderCell: () => ({
      style: {
        minWidth: '100px',
        maxWidth: '100px'
      }
    }),
    customCell: () => ({
      style: {
        minWidth: '100px',
        maxWidth: '100px'
      }
    })
  },
  {
    title: '规格型号', dataIndex: 'equModelStr', width: 100, fixed: 'left', ellipsis: true,
    customHeaderCell: () => ({
      style: {
        minWidth: '100px',
        maxWidth: '100px'
      }
    }),
    customCell: () => ({
      style: {
        minWidth: '100px',
        maxWidth: '100px'
      }
    }),customRender: ({text,record,index,column})=>{
      return h('span', { title:text }, text);
    }
  },
  // {
  //   width: 0, fixed: 'left', ellipsis: true
  // },
  // {
  //   title: '型号备注', dataIndex: 'equModelInfo', width: 100, fixed: 'left',
  // },
  {
    title: "型号备注",
    dataIndex: "equModelInfo",
    width: 100,
  },

  // 中间可滚动列
  { title: '管理单位', dataIndex: 'managementOrgStr', width: 120 },
  { title: '存放地点', dataIndex: 'storageLocationStr', width: 120 },
  { title: '设备类别', dataIndex: 'equTypeStr', width: 120 },
  { title: '购置年度', dataIndex: 'purchaseYear', width: 120 },
  { title: '财务原值', dataIndex: 'financialOriginalValue', width: 120 },
  { title: '净值', dataIndex: 'netWorth', width: 120 },
  { title: '生产厂家', dataIndex: 'manufacturer', width: 150 },
  { title: '出厂日期', dataIndex: 'productionDate', width: 120 },
  { title: '出厂编号', dataIndex: 'factoryNumber', width: 120 },
  { title: '报废批文号', dataIndex: 'scrapApprovalNumber', width: 120 },
  { title: '报废日期', dataIndex: 'scrapDate', width: 100 },
  { title: '报废原因', dataIndex: 'scrapRemark', width: 100 },
  { title: '设备种类', dataIndex: 'equSubTypeStr', width: 120 },
  { title: '设备性质', dataIndex: 'equNatureStr', width: 120 },
  { title: '固定资产分类', dataIndex: 'fixedAssets', width: 120 },
  { title: '产权单位', dataIndex: 'propertyOrgStr', width: 120 },
  { title: '功率kw', dataIndex: 'power', width: 100 },
  { title: '设备型号编码', dataIndex: 'modelCode', width: 120 },
  // 右侧固定列
  { title: '操作', key: 'action',width: 120, fixed: 'right' }
];

// 表格数据
const tableData = ref([]);
const selectedRowKeys = ref([]);
const totalCount = ref(0);
const totalFinanceValue = ref(0);
const totalNetValue = ref(0);
var isShowSearch = ref(false);

const datasource = async (queryParam) => {
  queryParam.pageNo = pagination.value.current
  queryParam.pageSize = pagination.value.pageSize
  const dataList = await scrapEquipmentApi.getData(queryParam);
  const res1 = await scrapEquipmentApi.getOriginalValue(queryParam)
  tableData.value = dataList.data.rows
  // 更新统计数据
  totalCount.value = res1.data.totalNum || 0;
  totalFinanceValue.value = res1.data.totalFinancialOriginalValue || 0;
  totalNetValue.value = res1.data.totalNetWorth || 0;
  pagination.value.total = dataList.data.totalRows;
}
datasource(queryParams.value)
// 设置表格数据
// tableData.value = mockTableData;

// 方法定义...

const handleTableChange = (current) => {
  pagination.value.current = current
  queryParams.value.pageNo = current
  queryParams.value.pageSize = pagination.value.pageSize
  // queryParams.value.keyword = this.queryParams.searchText
  datasource(queryParams.value);
};
const handleSizeChange = (current, size) => {
  pagination.value.pageSize = size;
  pagination.value.current = 1; // 切换每页条数时重置为第一页
  queryParams.value.pageNo = 1;
  queryParams.value.pageSize = size;
  handleSearch();
};


const handleView = (record) => {
  //通过接口去获取
  scrapEquipmentApi.getEquipmentFullInfo({ id: record.id }).then(res => {
    res.data.type = 'scrap'
    current.value = res.data;
    current.value.type = 'scrap'
    showDetail.value = true;
  })
};

// 监听 showDetail 的变化，当抽屉关闭时重置数据
watch(showDetail, (val) => {
  if (!val) {
    current.value = null;
  }
});
const router = useRouter();
const goToApplicationRecord = () => {
  router.push('/buss/scrapApplication');
};

const goToEquipmentScrapping = () => {
  router.push('/buss/equipmentScrapping');
};


// 设备类型树数据
const equipmentTypeList = ref([]);

// 获取设备类型树
const getEquipmentTypeTree = async () => {
  try {
    const res = await BasicInformationApi.getEquipmentTypeTree();
    equipmentTypeList.value = res.data[0].children;
  } catch (error) {
    message.error('获取设备类型失败');
  }
};

// 枚举选项
// const equConditionOptions = ref([]);
const managementStatusOptions = ref([]);
const yearFormat = 'YYYY';
const yearFormats = 'YYYY-MM-DD';

// 组织机构树数据
const orgList = ref([]);
const cqdwList = ref([])
// 获取组织机构树
const getOrgList = async () => {
  try {
    const res = await UnitApi.getUseOrgTree({});
    const res1 = await UnitApi.getPropertyOrgList();
    orgList.value = res[0].children;
    for (let i = 0; i < orgList.value.length; i++) {
      orgList.value[i].selectable = false
      orgList.value[i].key = orgList.value[i].value
    }
    console.log('orgList.value', orgList.value);

    cqdwList.value = res1;
  } catch (error) {
    message.error('获取组织机构失败');
  }
};

const expandedKeys = ref([])
const handleTreeExpand = (node) => {
  expandedKeys.value = node
}
const handleTitleClick = (e, node) => {
  console.log('expandedKeys', expandedKeys.value);
  console.log('node', node);
  if (node.children.length > 0) {
    const key = orgList.value.find(item => item.name === node.name).id;
    if (expandedKeys.value.includes(key)) {
      expandedKeys.value = expandedKeys.value.filter(k => k !== key);
    } else {
      expandedKeys.value = [...expandedKeys.value, key];
    }
  }

};


// 导出数据
const handleExport = () => {
  // 构建导出参数，与查询参数保持一致
  const exportParams = {
    ...queryParams.value
  };

  // 如果有搜索文本，添加到导出参数中
  // if (searchText.value) {
  //   exportParams.searchText = searchText.value;
  // }

  // 直接调用导出，会打开新页面下载
  scrapEquipmentApi.exportData(exportParams);
};

const gldwList = ref([])

// 获取设备类型树
const getManagementOrgList = async () => {
  try {
    const res = await EnumApi.getManagementOrgList({});
    gldwList.value = res.data;
  } catch (error) {
    message.error('获取管理单位失败');
  }
};

getManagementOrgList()

// initEnumData();
getOrgList();
getEquipmentTypeTree(); // 添加到 onMounted 中
// onMounted(() => {

// });

const handleDrawerClose = () => {
  current.value = null;
  showDetail.value = false;
};

watch(
  () => isShowSearch.value,
  (sum) => {
    if (sum) {
      scroll.value = { x: 'max-content', y: 'calc(100vh - 385px)' }
    } else {
      scroll.value = { x: 'max-content', y: 'calc(100vh - 270px)' }
    }
  }
);
</script>

<style lang="less" scoped>
.reg-body {
  height: 100%;
  overflow: auto;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  border-radius: 6px;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);
  padding: 16px 16px;
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;

  .header-tools {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;

    .left {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .filter-button {
        min-width: 80px;
        height: 32px;
        border-radius: 7px;
      }

      .search-input {
        width: clamp(280px, 20vw, 320px);
        display: flex;
        align-items: center;

        :deep(.ant-input) {
          width: 100%;
          height: 24px;

        }

        .search-icon {
          cursor: pointer;
        }
      }
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .tool-button {
        height: 32px;
        border-radius: 7px;

      }

      .colorBlue {
        color: #176DF4
      }

      .link-button {
        height: 32px;
        padding: 0 8px;
      }
    }
  }



  .search-form {

    padding: 0 16px; // 统一内边距
    border-radius: 8px; // 增加圆角


    .search-row {
      display: flex;
      flex-wrap: wrap;
      gap: 24px; // 增加间距
      margin-bottom: 12px; // 增加行间距

      &:last-child {
        margin-bottom: 0;
      }

      .search-item {
        display: flex;
        align-items: center;
        // min-width: 300px;
        flex: 1;

        .label {
          min-width: 80px;
          // margin-right: 12px; // 增加标签和输入框的间距
          color: #666;
          font-size: 14px;
        }

        :deep(.ant-select),
        :deep(.ant-input) {
          width: 64%;
          height: 32px;

          .ant-select-selector {
            background: #fff; // 确保选择器背景为白色
            border-radius: 4px;
          }
        }

        :deep(.ant-input) {
          background: #fff; // 确保输入框背景为白色
          border-radius: 4px;
        }
      }

      .search-button,
      .reset-button {
        height: 32px;
        min-width: 80px;
        margin-left: auto;
        border-radius: 4px; // 统一按钮圆角
      }

      .search-button {
        background: #1890ff; // 查询按钮使用主题蓝色
      }

      .reset-button {
        background: #fff; // 重置按钮使用白色背景
        border: 1px solid #d9d9d9;
      }
    }
  }

  .table-footer {
    position: fixed; // 默认固定定位
    bottom: 0px;
    background: #ECF5FE;
    width: calc(100% - 32px);
    max-width: 1888px;
    margin: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px 24px;
    border-radius: 8px;
    z-index: 10;

    .total-info {
      color: #666;
      font-size: 14px;
    }

    // 当筛选展开时的样式
    &.follow-page {
      transform: none;
      margin-top: 16px;
    }
  }

  // 表格容器样式
  .custom-table {
    overflow-x: auto !important;
    width: 100%;
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .reg-body {
    .search-form {
      .search-row {
        .search-item {
          width: 50%;
        }
      }
    }
  }
}

@media screen and (max-width: 1366px) {
  .reg-body {
    .search-form {
      .search-row {
        .search-item {
          min-width: calc(50% - 16px);
        }
      }
    }
  }
}

@media screen and (max-width: 1024px) {
  .reg-body {
    .header-tools {
      .left,
      .right {
        width: 100%;
        justify-content: space-between;
      }
    }

    .search-form {
      .search-row {
        .search-item {
          min-width: 100%;
        }
      }
    }

    .table-footer {
      flex-direction: column;
      text-align: center;

      .total-info {
        width: 100%;
      }
    }
  }
}

// 表格样式增强，确保在所有浏览器中都能正确显示滚动条
:deep(.ant-table) {
  // 表格布局设置
  table {
    table-layout: fixed !important; // 强制表格使用固定布局
    min-width: 100% !important;
    width: max-content !important; // 确保表格内容超出时显示滚动条
  }

  // 单元格样式
  .ant-table-cell {
    // max-width: 100% !important; // 限制单元格最大宽度
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    line-height: 1 !important;
    font-size: 14px !important;

    >span,
    >div {
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }
  }

  // 确保表格内容可以水平滚动
  .ant-table-content {
    overflow-x: auto !important;
    min-width: 100% !important;
    z-index: 0 !important;
  }

  // 确保滚动区域正确显示
  .ant-table-body {
    overflow-x: auto !important;
    overflow-y: auto !important;
    min-width: 100% !important;
  }

  // 兼容360浏览器
  .ant-table-container {
    overflow-x: auto !important;
  }

  // 大屏幕样式（默认）
  @media screen and (min-width: 1920px) {
    .ant-table-cell {
      padding: 20px 20px !important;
      height: 60px !important;
    }

    .ant-table-row {
      height: 60px !important;
    }
  }

  // 中等屏幕样式
  @media screen and (min-width: 1366px) and (max-width: 1919px) {
    .ant-table-cell {
      padding: 10px 20px !important;
      height: 40px !important;
    }

    .ant-table-row {
      height: 40px !important;
    }
  }

  // 小屏幕样式
  @media screen and (max-width: 1365px) {
    .ant-table-cell {
      padding: 4px 8px !important;
      height: 32px !important;
    }

    .ant-table-row {
      height: 32px !important;
    }
  }

  // 固定列样式
  .ant-table-fixed-left,
  .ant-table-fixed-right {
    background: #fff !important;
    box-shadow: none !important; // 移除原有阴影
    z-index: 3 !important; // 提高固定列的层级
  }

  // 隐藏特定列
  tr>.ant-table-cell-fix-left:nth-child(6) {
    display: none !important;
  }

  // 调整固定列单元格样式
  .ant-table-cell-fix-left,
  .ant-table-cell-fix-right {
    z-index: 3 !important; // 增加层级
    background: #ECF4FE !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  // 调整表头固定列样式
  .ant-table-thead {
    th.ant-table-cell-fix-left,
    th.ant-table-cell-fix-right {
      z-index: 4 !important; // 确保表头在最上层
      background: #DAECFF !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }

    .ant-table-cell-scrollbar {
      box-shadow: none;
    }
  }

  // 优化阴影效果
  .ant-table-fixed-right::before,
  .ant-table-fixed-left::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    bottom: 0 !important;
    width: 10px !important;
    pointer-events: none !important;
    z-index: 2 !important; // 阴影层级低于固定列
    transition: box-shadow .3s !important;
  }

  .ant-table-fixed-left::before {
    right: 0 !important;
    box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15) !important;
  }

  .ant-table-fixed-right::before {
    left: 0 !important;
    box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15) !important;
  }
}

// 修复行样式
:deep(.ant-row) {
  flex-wrap: nowrap !important;
}

:deep(.ant-form-item-label) {
  overflow: visible !important;
}

// 悬停样式
// :deep(.ant-table-tbody > tr.ant-table-row:hover > td),
// :deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
//   background-color: #f0f7ff !important; // 使用自定义悬停背景色
// }

// 全局滚动条样式，确保在360浏览器中可见
// :deep(*::-webkit-scrollbar) {
//   width: 4px !important; // 垂直滚动条宽度
//   height: 4px !important; // 水平滚动条高度
// }

// :deep(*::-webkit-scrollbar-thumb) {
//   background: rgba(0, 0, 0, 0.3) !important; // 滚动条颜色
//   border-radius: 4px !important;
// }

// :deep(*::-webkit-scrollbar-thumb:hover) {
//   background: rgba(0, 0, 0, 0.5) !important; // 悬停时更深的颜色
// }

// :deep(*::-webkit-scrollbar-track) {
//   background: rgba(0, 0, 0, 0.05) !important; // 轻微可见的轨道
// }

// // 确保表格容器允许滚动 - 360浏览器兼容性修复
// .custom-table {
//   overflow-x: auto !important;
//   width: 100% !important;
// }

// // 确保表格内容可以水平滚动 - 360浏览器兼容性修复
// :deep(.ant-table-content) {
//   overflow-x: auto !important;
//   min-width: 100% !important;
// }

// // 确保滚动区域正确显示 - 360浏览器兼容性修复
// :deep(.ant-table-body) {
//   overflow-x: auto !important;
//   overflow-y: auto !important;
//   min-width: 100% !important;
// }

// // 兼容360浏览器
// :deep(.ant-table-container) {
//   overflow-x: auto !important;
// }
.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>




