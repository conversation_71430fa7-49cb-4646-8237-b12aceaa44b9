import Request from '@/utils/request-util';

/**
 * 表单管理api
 *
 * <AUTHOR>
 * @date 2021/7/5 21:14
 */
export class leasedOutApi {
  /**
   * 分页获取列表
   *
   * <AUTHOR>
   * @date 2022/5/8 20:36
   */
  static todoTaskPage(params) {
    return Request.getAndLoadData('/flowableTodoTask/page', params);
  }

  /**
   * 分页获取列表
   *
   * <AUTHOR>
   * @date 2022/5/8 20:36
   */
  static doneTaskPage(params) {
    return Request.getAndLoadData('/flowableDoneTask/page', params);
  }

  /**
     * 分页获取列表
     *
     * <AUTHOR>
     * @date 2022/5/8 20:36
     */
  static findPage(params) {
    return Request.getAndLoadData('/flowableInstance/my', params);
  }

  static getNewBuilt() {
    return Request.get('/apiBus/leaseReturnTransferFormEquipment/newBuilt');
  }

  static async saveDraft(params) {
    return await Request.post('/apiBus/leaseReturnTransferFormEquipment/saveDraft', params);
  }

  static getToDo(params) {
    return Request.get('/apiBus/leaseReturnTransferFormEquipment/getWorksheetInfo', params);
  }

  static start(params) {
    return Request.post('/flowableHandleTask/start', params);
  }

  static submit(params) {
    return Request.post('/flowableHandleTask/submit', params);
  }

  static end(params) {
            return Request.post('/flowableInstance/end', params);
          }
}
