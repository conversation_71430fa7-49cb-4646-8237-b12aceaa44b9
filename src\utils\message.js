import { message as antMessage } from 'ant-design-vue';

// 统一的消息配置
const MESSAGE_DURATION = 4; // 统一设置为4秒

// 封装消息方法，确保统一的配置
export const message = {
  success: (content, duration = MESSAGE_DURATION) => {
    return antMessage.success(content, duration);
  },
  error: (content, duration = MESSAGE_DURATION) => {
    return antMessage.error(content, duration);
  },
  warning: (content, duration = MESSAGE_DURATION) => {
    return antMessage.warning(content, duration);
  },
  info: (content, duration = MESSAGE_DURATION) => {
    return antMessage.info(content, duration);
  },
  loading: (content, duration = MESSAGE_DURATION) => {
    return antMessage.loading(content, duration);
  }
};

// 导出原始message对象，以便需要使用其他API时使用
export const originalMessage = antMessage;

// 默认导出封装后的message
export default message;