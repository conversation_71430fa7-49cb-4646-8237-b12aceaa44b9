<template>
  <common-drawer
    v-model:visible="visible"
    width="70%"
    :title="title"
    :isShowTab="true"
    :activeKey="activeKey"
    :tabList="tabList"
    @tabChange="tabChange"
    :maskClosable="true"
    :mask="true"
    :closable="false"
    @close="onClose"
  >
   <!-- 右上角历史记录按钮 -->
   <template #extra>
    <div
        v-if="detailData.type === 'reg' && detailData.historyList && detailData.historyList.length > 0"
        @click.stop
        class="history-link"
        @click="showHistory"
        style="margin-right: 8px;"
      >
        <span style="color: #1890ff; cursor: pointer;">历史记录 &gt;</span>
      </div>
      <slot name="extra"></slot>
    </template>
    <!-- 20250708 王文胜修改 -->
    <div style="background-color: #fff;margin:-25px -24px 0px -24px;padding:38px 24px;">
      <!-- 设备信息 -->
      <device-form v-if="activeKey === '1'" :data="detailData" :visible="visible"/>
      <!-- 使用情况 -->
      <usage-form v-if="activeKey === '2'" :data="detailData" :visible="visible"/>
      <!-- 财务信息 -->
      <finance-form v-if="activeKey === '3'" :data="detailData" :visible="visible"/>
      <!-- 租赁费用 -->
      <lease-form v-if="activeKey === '4'" :data="detailData" :visible="visible"/>
      <!-- 报废信息 -->
      <scrap-form v-if="activeKey === '5' && detailData?.type!=='reg'" :data="detailData" :visible="visible"/>
      <!-- 处置信息 -->
      <disposal-form v-if="activeKey === '8' && detailData?.type==='disposal'" :data="detailData" :visible="visible"/>
      <!-- 主要附件 -->
      <main-form v-if="activeKey === '6'" :data="detailData" :visible="visible"/>
      <!-- 动态信息 -->
      <dynamic-form v-if="activeKey === '7'" :data="detailData" :visible="visible"/>

      <!-- 相关附件 -->
      <file-form v-if="activeKey === '9'" :data="detailData" :visible="visible"/>
    </div>
  </common-drawer>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import DeviceForm from './components/device-form.vue';
import UsageForm from './components/usage-form.vue';
import FinanceForm from './components/finance-form.vue';
import LeaseForm from './components/lease-form.vue';
import MainForm from './components/main-form.vue';
import DynamicForm from './components/dynamic-form.vue';
import ScrapForm from './components/scrap-form.vue';
import DisposalForm from './components/disposal-form.vue';
import FileForm from './components/file-form.vue';
import { data } from 'jquery';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: '设备详情'
  },
});
const showHistory = () => {
  //历史记录只有一条是弹出浏览器
  if (detailData.value.historyList && detailData.value.historyList.length === 1) {
    const historyItem = detailData.value.historyList[0];
    window.open("/history?id="+historyItem.transferFormId+"&historyCode="+historyItem.code, '_blank');
  } else {
    // 如果有多条历史记录，就是弹出一个弹出框.
    // 这里可以使用一个事件来通知父组件显示历史记录
    emit('showHistoryDetail', detailData.value.historyList);
    //当前这个抽屉关闭
    emit('update:visible', false);

  }
};
const emit = defineEmits(['update:visible', 'close']);

const activeKey = ref('1');
const detailData = ref({
  type:"",
  code:"",
  fileList:[]
});

let tabList = ref([]);

watch(() => props.data, (val) => {
  if (val) {
    detailData.value = val;

    // 根据类型设置不同的标签页
    const type = val?.type || "";

    if (type === 'reg') {
      tabList.value = [
        { key: '1', name: '设备信息' },
        { key: '2', name: '使用情况' },
        { key: '3', name: '财务信息' },
        { key: '4', name: '租赁费用' },
        { key: '6', name: '主要附件' },
        { key: '7', name: '动态信息' },
        { key: '9', name: '相关附件' }
      ];
    } else if (type === 'scrap') {
      tabList.value = [
        { key: '1', name: '设备信息' },
        { key: '2', name: '使用情况' },
        { key: '3', name: '财务信息' },
        { key: '4', name: '租赁费用' },
        { key: '5', name: '报废信息' },
        { key: '6', name: '主要附件' },
        { key: '7', name: '动态信息' },
        { key: '9', name: '相关附件' }
      ];
    } else if (type === 'disposal') {
      // 安全地检查 scrapedEquipmentTransferForm 是否存在
      if (val.scrapedEquipmentTransferForm) {
        tabList.value = [
          { key: '1', name: '设备信息' },
          { key: '2', name: '使用情况' },
          { key: '3', name: '财务信息' },
          { key: '4', name: '租赁费用' },
          { key: '5', name: '报废信息' },
          { key: '6', name: '主要附件' },
          { key: '7', name: '动态信息' },
          { key: '8', name: '处置信息' },
          { key: '9', name: '相关附件' }
        ];
      } else {
        tabList.value = [
          { key: '1', name: '设备信息' },
          { key: '2', name: '使用情况' },
          { key: '3', name: '财务信息' },
          { key: '4', name: '租赁费用' },
          { key: '6', name: '主要附件' },
          { key: '7', name: '动态信息' },
          { key: '8', name: '处置信息' },
          { key: '9', name: '相关附件' }
        ];
      }
    }
  }
}, { immediate: true });

const handleVisibleChange = (val) => {
  emit('update:visible', val);
};

const tabChange = (key) => {
  console.log(666666666,key)
  activeKey.value = key;
};

const onClose = () => {
  // 先设置 visible 为 false，触发关闭动画
  emit('update:visible', false);

  // 使用 setTimeout 延迟重置数据，等待抽屉关闭动画完成
  setTimeout(() => {
    // 在关闭动画完成后再重置 detailData
    activeKey.value = '1';
    detailData.value = { type: "", code: "", fileList: [] };
    emit('close');
  }, 300); // 300ms 通常足够抽屉关闭动画完成
};

// 确保 visible 变化时也处理 detailData
watch(() => props.visible, (val) => {
  if (!val) {
    // 当抽屉关闭时，延迟重置 detailData
    setTimeout(() => {
      activeKey.value = '1';
      detailData.value = { type: "", code: "", fileList: [] };
    }, 300);
  }
});

</script>
<style scoped>
.history-link {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.history-link:hover span {
  text-decoration: underline;
}

.detail-modal {
  /* top: 20px; */
  height: 500px;
}
</style>













