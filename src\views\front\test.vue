<template>
  <a-table 
    :columns="columns" 
    :data-source="tableData" 
    :row-selection="rowSelection" 
    :loading="loading"
    :expandable="expandableConfig"
    @expand="handleExpandNode"
  />
  <a-button @click="handleExport">导出</a-button>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { AnalysisApi } from '@/api/analysis';
import R from 'raphael';

const columns = [
  {
    title: '名称',
    dataIndex: 'typeName',
    key: 'name',
     width: '30%',
  },
  {
    title: 'Address',
    dataIndex: 'address',
   
    key: 'address',
    children: [] // This will be populated from API response
  },
];

interface DataItem {
  key: number;
  name: string;
  age: number;
  address: string;
  children?: DataItem[];
}

export default defineComponent({
  setup() {
    const tableData = ref<DataItem[]>([]);
    const loading = ref<boolean>(false);
    const dynamicColumns = ref(columns);

    // Row selection configuration
    const rowSelection = {
      onChange: (selectedRowKeys: (string | number)[], selectedRows: DataItem[]) => {
        console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
      },
      onSelect: (record: DataItem, selected: boolean, selectedRows: DataItem[]) => {
        console.log(record, selected, selectedRows);
      },
      onSelectAll: (selected: boolean, selectedRows: DataItem[], changeRows: DataItem[]) => {
        console.log(selected, selectedRows, changeRows);
      },
    };

        // 处理节点展开的方法
    const handleExpandNode = async (expanded: boolean, record: any) => {
      console.log('Expand event:', expanded, record);
      
      // 只在展开父节点且没有子节点数据时请求
      if (expanded && record.hasChildren && (!record.children || record.children.length === 0)) {
        loading.value = true;
        try {
          console.log('Fetching children for:', record.key);
          // 调用API获取子节点数据
          const response = await AnalysisApi.getChildrenList({ parentClassificationCode:record.typeId,pLevel:record.level });
          
          if (response.data) {
            // 将获取到的子节点数据添加到当前记录
            record.children = response.data.map((item: any, index: number) => ({
            key: index,
            ...item,
            // 明确标记哪些是父节点，需要展开时加载子节点
            hasChildren: item.children.length==0?true:true

            }));
            
            // 强制更新表格数据
            tableData.value = [...tableData.value];
          }
          console.log('Children loaded:', response.data);
        } catch (error) {
          console.error('Failed to load children:', error);
        } finally {
          loading.value = false;
        }
      }
    };
    
    // 表格展开配置
    const expandableConfig = {
      // 展开/收起时的回调
      onExpand: handleExpandNode,
      // 指示节点是否可以展开
      rowExpandable: (record) => record.hasChildren === true
    };

    // Fetch data from API
    const fetchData = async () => {
      loading.value = true;
      try {
        const response = await AnalysisApi.getData({});
        
        // Update the address column's children with reportTitle data
        const addressColumnIndex = dynamicColumns.value.findIndex(col => col.key === 'address');
        if (addressColumnIndex !== -1 && response.data.reportTitle) {
          dynamicColumns.value[addressColumnIndex].children = response.data.reportTitle.map((title: any, dataIndex: number) => ({
            title: title.title,
            dataIndex: title.dataIndex,
            key: title.dataIndex,
            width: 120,
          }));
        }
        
        // Update table data with reportData
        if (response.data.reportData) {
          tableData.value = response.data.reportData.map((item: any, index: number) => ({
            key: index,
            ...item,
            // 明确标记哪些是父节点，需要展开时加载子节点
            hasChildren: item.children.length==0?true:true
          }));
        }
        
        console.log('Data loaded successfully:', response.data);
      } catch (error) {
        console.error('Failed to fetch data:', error);
      } finally {
        loading.value = false;
      }
    };
    
    const handleExport = async () => {
      try {
        await AnalysisApi.downLoad({});
      } catch (error) {
        console.error('Failed to export data:', error);
      }
    };
    
    // Load data when component mounts
    onMounted(() => {
      fetchData();
    });

    return {
      tableData,
      columns: dynamicColumns,
      rowSelection,
      loading,
      expandableConfig,
      handleExpandNode,
      handleExport,
    };
  },
});
// .custom-table {
//   :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
//     background-color: #FFFFFF !important;
//   }
// }
</script>

