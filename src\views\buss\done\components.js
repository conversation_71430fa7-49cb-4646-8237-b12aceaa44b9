// 使用异步方式加载组件
const modules = import.meta.glob('@/views/buss/done/*.vue')
const resultComps = {}

// 立即加载所有组件
Promise.all(
  Object.entries(modules).map(async ([path, importFn]) => {
    try {
      const module = await importFn()
      // 只获取文件名，不要包含路径
      const fileName = path.split('/').pop().replace('.vue', '')
      if (module.default) {
        resultComps[fileName] = module.default
        console.log(`成功注册组件: ${fileName}`)
      } else {
        console.warn(`组件注册失败 - ${fileName}: module.default 不存在`)
      }
    } catch (error) {
      console.error(`加载组件失败 - ${path}:`, error)
    }
  })
).then(() => {
  console.log('所有组件加载完成:', Object.keys(resultComps))
})

export default resultComps

