<template>
  <div class="login-wrapper" ref="loginPage">
    <!-- 视频背景 -->
    <video autoplay muted loop class="video-background">
      <source src="@/assets/login.mp4" type="video/mp4">
    </video>

    <!-- Logo区域 -->
    <div class="logo-area">
      <img src="@/assets/top.png" alt="logo" class="logo-img" />
      <!-- <span class="system-name">中煤矿建机电装备智能管控系统</span> -->
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 登录表单 -->
      <div class="login-form-container">
        <div class="login-form">
          <h2 class="title">欢迎登录</h2>
          
          <a-form layout="vertical" @keyup.enter="submit">
            <a-form-item>
              <a-input 
                v-model:value="form.account" 
                placeholder="请输入账号"
                class="custom-input"
              />
            </a-form-item>
            
            <a-form-item>
              <a-input-password 
                v-model:value="form.password" 
                placeholder="请输入密码"
                class="custom-input"
              />
            </a-form-item>

            <a-form-item>
              <a-checkbox v-model:checked="form.rememberMe">记住我</a-checkbox>
            </a-form-item>

            <a-button 
              type="primary" 
              block 
              :loading="loading" 
              @click="submit"
              class="login-button"
            >
              登录
            </a-button>
          </a-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { computed, onMounted, reactive, ref, unref } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { useRoute, useRouter } from 'vue-router';
  import { Form, message } from 'ant-design-vue';
  import { LockOutlined, QqOutlined, SafetyCertificateOutlined, UserOutlined, WechatOutlined } from '@ant-design/icons-vue';
  import I18nIcon from '@/layout/components/i18n-icon.vue';
  import { getToken, setToken } from '@/utils/token-util';
  import { cleanPageTabs, goHomeRoute } from '@/utils/page-tab-util';
  import { LoginApi } from '@/api/login/LoginApi';
  import { BACKEND_HOST, CAPTCHA_FLAG, SAAS_FLAG, SESSION_KEY_MENU_TYPE, SSO_CLIENT_ID, SSO_FLAG, IS_NEED_RSA } from '@/config/setting';
  import { SysTenantApi } from '@/api/tenant/SysTenantApi';
  import { useSystemStore } from '@/store/modules/system';
  import { SsoUtil } from '@/utils/sso-util';
  import { RsaEncry } from '@/utils/util';
  
  const useForm = Form.useForm;
  
  const { currentRoute } = useRouter();
  const { query } = useRoute();
  const { t } = useI18n();
  
  // 先清空缓存的前后台标识
  sessionStorage.removeItem(SESSION_KEY_MENU_TYPE);
  
  // 获取url传参的相关单点参数
  const ssoClientId = query?.clientId;
  const ssoCallback = query?.ssoCallback;
  
  // guns添加的自定义配置
  // 是否开启多租户
  const saasFlag = ref(SAAS_FLAG);
  const saasList = ref([]);
  // 主题信息
  let themeInfo = ref({
    gunsMgrLoginBackgroundImg: '',
    gunsMgrFooterText: '',
    gunsMgrBeiUrl: '',
    gunsMgrBeiNo: ''
  });
  // 登录页面引用
  const loginPage = ref(null);
  // 验证码校验
  const captchaFlag = ref(CAPTCHA_FLAG);
  
  // 登录框方向, 0 居中, 1 居右, 2 居左
  const direction = ref(0);
  // 加载状态
  const loading = ref(false);
  // 表单数据
  const form = reactive({
    account: '',
    password: '',
    verKey: '',
    verCode: '',
    rememberMe: false
  });
  // 验证码 base64 数据
  const captcha = ref('');
  // 验证码key标识
  const verKey = ref('');
  
  // 表单验证规则
  let ruleLists = ref({
    account: [
      {
        required: true,
        message: t('login.username'),
        type: 'string',
        trigger: 'blur'
      }
    ],
    password: [
      {
        required: true,
        message: t('login.password'),
        type: 'string',
        trigger: 'blur'
      }
    ]
  });
  // 如果开启了验证码开关，则同时需要校验验证码是否填写
  if (captchaFlag.value) {
    ruleLists.value.verCode = [
      {
        required: true,
        message: t('login.code'),
        type: 'string',
        trigger: 'blur'
      }
    ];
  }
  const { clearValidate, validate, validateInfos } = useForm(
    form,
    computed(() => ruleLists.value)
  );
  
  // 从store获取主题数据
  let systemStore = useSystemStore();
  
  /* 页面加载完成 */
  onMounted(async () => {
    // 加载租户列表
    if (saasFlag.value) {
      saasList.value = await SysTenantApi.dropDownList();
    }
    let result = await systemStore.loadThemeInfo();
    themeInfo.value = result;
    // 动态设置登录页面的背景
    loginPage.value.style.setProperty('--customBackground', `url(${result.gunsMgrLoginBackgroundImg})`);
  });
  
  /* 跳转到首页 */
  const goHome = () => {
    const { query } = unref(currentRoute);
    // 如果有from参数，则跳转到from页面
    if (query.from) {
      goHomeRoute(query.from);
      return;
    }else if (query.redirect) {
      // 如果有redirect参数，则跳转到redirect页面
      goHomeRoute(query.redirect);
      return;
    }else{
      //跳转到map
      goHomeRoute('/map');
      return;
    }
    //goHomeRoute(query.from);
  };
  
  /* 提交 */
  const submit = () => {
    if (SSO_FLAG && SAAS_FLAG) {
      message.warn('租户开关和单点开关不能同时开启');
      return;
    }
    validate().then(() => {
      loading.value = true;
      let formData = JSON.parse(JSON.stringify(form));
      // 是否需要加密
      if (IS_NEED_RSA) {
        // rsa加密密码
        formData.password = RsaEncry(formData.password);
      }
      LoginApi.login(formData)
        .then(response => {
          // 没开启单点登录
          if (!SSO_FLAG) {
            message.success('登录成功');
            if (response?.data?.loginUser?.menuType) {
              localStorage.setItem('menuType', response?.data?.loginUser?.menuType);
            }
            setToken(response?.data?.token, form.rememberMe);
            cleanPageTabs();
            goHome();
          } else {
            // 开启了单点登录
            SsoUtil.activateByLoginCode(ssoClientId ?? SSO_CLIENT_ID, ssoCallback ?? '', response?.data?.ssoLoginCode);
          }
        })
        .finally(() => {
          loading.value = false;
        });
    });
  };
  
  /* 获取图形验证码 */
  const changeCaptcha = () => {
    // 这里演示的验证码是后端返回base64格式的形式, 如果后端地址直接是图片请参考忘记密码页面
    LoginApi.getCaptcha()
      .then(response => {
        captcha.value = response.data.verImage;
        verKey.value = response.data.verKey;
        form.verKey = response.data.verKey;
        clearValidate();
      })
      .catch(e => {
        message.error(e.message);
      });
  };
  
  /* oauth2第三方登录 */
  const oauth2Login = type => {
    if (type === 'qq') {
      window.location.href = BACKEND_HOST + '/oauth2/detection/qq';
    } else if (type === 'wechat') {
      window.location.href = BACKEND_HOST + '/oauth2/detection/wechat';
    } else if (type === 'gitee') {
      window.location.href = BACKEND_HOST + '/oauth2/detection/gitee';
    }
  };
  
  if (getToken()) {
    goHome();
  } else {
    changeCaptcha();
  }
  </script>
  
  <style lang="less" scoped>
  .login-wrapper {
    min-height: 100vh;
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  // 视频背景
  .video-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
  }

  // Logo区域样式
  .logo-area {
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 2;
    position: absolute;
    top: 0;
    left: 0;

    .logo-img {
      height: 40px;
      width: auto;
    }

    .system-name {
      color: #fff;
      font-size: 20px;
      font-weight: bold;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }
  }

  // 主要内容区域
  .main-content {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 90px;
    z-index: 2;
  }

  // 登录表单容器
  .login-form-container {
    width: 420px;
    padding: 40px 50px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .login-form {
    .title {
      font-size: 32px;
      font-weight: bold;
      color: #000;
      margin-bottom: 48px;
      text-align: left;
    }

    :deep(.custom-input) {
      height: 48px;
      border-radius: 4px;
      border: 1px solid #e8e8e8;
    }

    :deep(.ant-form-item) {
      margin-bottom: 32px;
    }

    :deep(.ant-checkbox-wrapper) {
      color: #666;
      font-size: 16px;
    }

    .login-button {
      height: 48px;
      font-size: 18px;
      border-radius: 4px;
      background: #1890FF;
      border: none;
      margin-top: 16px;
      
      &:hover {
        background: #40A9FF;
      }
    }
  }

  // 响应式处理
  @media screen and (max-width: 768px) {
    .main-content {
      padding: 0 20px;
      justify-content: center;
    }

    .login-form-container {
      width: 100%;
      max-width: 380px;
    }

    .logo-area {
      padding: 16px;
    }
  }
  </style>
  
