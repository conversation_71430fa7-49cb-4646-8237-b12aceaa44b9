<template>
  <div class="ele-body1">
    <div class="equipment-acceptance">
   
      <div class="status-icon" v-if="bussWorksheet && bussWorksheet.status !== undefined">
        <img :src="getStatusImage(bussWorksheet.status)" :alt="getStatusText(bussWorksheet.status)"
          class="status-image" />
      </div>
      <div class="form-title">设备入账验收转资单</div>

      <!-- 申请信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          申请信息
        </div>

        <div class="form-grid">

          <div class="form-item">
            <div class="label">申请单号</div>
            <div class="approvalFont">{{ datalist.worksheetId }}</div>
          </div>
          <div class="form-item">
            <div class="label">申请人</div>
            <div class="approvalFont">{{ datalist.applyUserStr }}</div>
          </div>
          <div class="form-item">
            <div class="label">申请日期</div>
            <div class="approvalFont">{{ datalist.applyDate }}</div>
          </div>

        </div>
        <div class="form-grid">
          <div class="form-item">
            <div class="label">申请单位</div>
            <div class="approvalFont">{{ datalist.applyOrgStr }}</div>
          </div>
          <div class="form-item">
            <div class="label">业务标题</div>
            <div class="approvalFont">{{ datalist.applyTitle }}</div>
          </div>

        </div>
      </div>

      <!-- 设备信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          设备明细
        </div>
        <a-table :columns="columns" :data-source="bussEquipmentProcessTrackingList" bordered :pagination="false"
          :scroll="{ x: 'max-content', y: '490px' }" class="custom-table">
          <template #emptyText>
            <div class="custom-empty">
              <img src="@/assets/images/noData.png" />
              <!-- <p>抱歉，暂时还没有数据</p> -->
            </div>
          </template>
          <template #bodyCell="{ column, index, record }">
          </template>
        </a-table>
      </div>

      <div v-if="fixedTypeStr == 'todo'" class="approval-btns">
        <div class="section">
          <div class="section-title">
            <div class="blue-bar"></div>
            审批说明
          </div>

          <div class="approval-description">
            <a-textarea v-model:value="comment" :rows="4" placeholder="请输入审批说明" :maxlength="500" />
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="bottom-buttons">
          <a-button @click="handleReject" :loading="saving" danger style="background: none;">拒绝</a-button>
          <a-button type="primary" @click="handleApprove" :loading="submitting" class="submit-btn">同意</a-button>
        </div>

      </div>
      <div v-if="fixedTypeStr == 'done'" class="approval-btns">

      </div>

      <div v-if="fixedTypeStr == 'completed'" class="approval-btns">
        <div class="bottom-buttons">
          <a-button v-if="bussWorksheet?.isRevoke" type="primary" @click="handleCancel" :loading="submitting"
            class="submit-btn">
            撤回申请
          </a-button>
          <!-- <a-button v-if="bussWorksheet?.status === 100" type="primary"
          @click="handleTwice" :loading="submitting" class="submit-btn">
          再次申请
        </a-button> -->
        </div>
      </div>
      <!-- 审批说明 -->

    </div>

  </div>
</template>

<script setup>

import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import { scrapApplicationApi } from '@/api/buss/scrapApplicationApi';
import { FileUploadUrl } from '@/api/system/operation/FileApi';
import { getToken } from '@/utils/token-util';
import { API_BASE_PREFIX } from '@/config/setting';
import { useRouter, useRoute } from 'vue-router';
import { EquipmentAcceptanceApi } from '@/api/buss/EquipmentAcceptanceApi';
import { EnumApi } from '@/api/common/enum';
import { RegisteredEquipmentApi } from '@/api/workflow/RegisteredEquipmentApi';

// 1. 首先定义 props
const props = defineProps({
  formData: {
    type: Object,
    default: () => ({})
  }
});
// 引入状态图片
import cancelImg from '@/assets/equipment/quxiao.png';
import passedImg from '@/assets/equipment/tongguo.png';
import rejectedImg from '@/assets/equipment/jujue.png';

// 获取状态图片
const getStatusImage = (status) => {
  switch (status) {
    case -1: // 已取消
      return cancelImg;
    case 99: // 已通过
      return passedImg;
    case 100: // 未通过
      return rejectedImg;
    default:
      return '';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case -1:
      return '已取消';
    case 99:
      return '已通过';
    case 100:
      return '未通过';
    default:
      return '';
  }
};
// 2. 定义响应式变量
const loading = ref(false);
const KeyId = ref();
const datalist = ref({});
const bussEquipmentProcessTrackingList = ref([]);
const emit = defineEmits(['set-global-loading'])
// 3. 添加打印检查
console.log('Setup running, props:', props);
const bussWorksheet = ref();
const initializeData = async (id) => {
  loading.value = true;
  try {
    const { data } = await RegisteredEquipmentApi.getToDo({ procInstanceId: id });
    //这里传给父页面loading消失
    emit('set-global-loading', true)
    bussWorksheet.value = data.bussWorksheet;
    //这里传给父页面loading消失
    console.log('获取的数据：', data);
    // 处理业务标题和关联投资计划
    if (data.bussTransferForm) {
      datalist.value = data.bussTransferForm;
    }

    // 处理设备基本信息
    if (data.bussEquipmentProcessTrackingList?.length > 0) {
      bussEquipmentProcessTrackingList.value = data.bussEquipmentProcessTrackingList;
    }

  } catch (error) {
    message.error('获取数据失败：' + error.message);
  } finally {
    loading.value = false;
  }
};
const approvalChannel = ref(null);
const route = useRouter();
const fixedTypeStr = ref("");
// 4. 修改 onMounted
onMounted(async () => {

  approvalChannel.value = new BroadcastChannel('/dynamic/regApplicationRecord');
  console.log('Component mounted');
  console.log('Props in mounted:', props);
  console.log('FormData in mounted:', props.formData);



  // 先处理 props 数据
  if (props.formData) {
    processDefinitionId.value = props.formData.processDefinitionId;
    actId.value = props.formData.actId;
    taskId.value = props.formData.taskId;
    procInsId.value = props.formData.procInsId;
    worksheetId.value = props.formData.worksheetId;
    console.log('处理后的数据:', {
      processDefinitionId: processDefinitionId.value,
      actId: actId.value,
      taskId: taskId.value,
      procInsId: procInsId.value
    });

    // 如果有 procInsId，则初始化数据
    if (procInsId.value) {
      await initializeData(procInsId.value);
    }
  }

  // 获取路由参数

  const { fixedType } = route.currentRoute.value.meta
  console.log(fixedType);
  fixedTypeStr.value = fixedType;
  // const id = route.query.id;
  // console.log('Route query id:', id);

  // // 如果有路由参数 id，也初始化数据
  // if (id) {
  //   await initializeData(id);
  // }
});

onUnmounted(() => {
  // 关闭广播频道，避免内存泄漏
  approvalChannel.value?.close();
});

const getProcessDefinitionByKey = async () => {
  try {

    const id = await EnumApi.getProcessDefinitionByKey({ key: 'equipment_acceptance_batch' });
    KeyId.value = id.data.id

  } catch (error) {

  }
}
getProcessDefinitionByKey()


// 文件列表相关
const fileList = ref([]);
const fileViewList = ref([]);
const techFileList = ref([]); // 技术资料上传组件的文件列表
const techFileViewList = ref([]); // 技术资料显示列表


const comment = ref(''); // 审批意见
const approve = ref(true); // 审批结果，默认同意
const submitting = ref(false);
// 提交方法
const handleSubmit = async (isApprove) => {
  try {
    submitting.value = true;
    if (comment.value == null || comment.value == '') {
      if (isApprove) {
        comment.value = '同意'
      } else {
        comment.value = '拒绝'
      }
    }
    // 构建内部formData结构
    const innerFormData = {
      formDatas: {
        approve: isApprove.toString(), // 转换为字符串的 "true" 或 "false"
        bussWorksheet: {
          id: worksheetId.value
        }
      }
    };

    // 构建最终提交数据结构
    const submitData = {
      processDefinitionId: processDefinitionId.value,
      taskId: taskId.value,
      variables: {
        formData: JSON.stringify(innerFormData)
      },
      comment: comment.value
    };

    const res = await EquipmentAcceptanceApi.approval(submitData);

    if (res.success) {
      message.success('审批成功');
      // 发送审批完成消息
      approvalChannel.value.postMessage({
        type: 'APPROVAL_COMPLETED',
        procInsId: procInsId.value,
        timestamp: Date.now()
      });
      nextTick(() => {
        window.close();
      })
    } else {
      message.error('审批失败');
    }
  } catch (error) {
    message.error(error.message.split(",")[0]);
  } finally {
    submitting.value = false;
  }
};

// 同意按钮的处理方法
const handleApprove = () => {
  handleSubmit(true);
};

// 拒绝按钮的处理方法
const handleReject = () => {
  handleSubmit(false);
};


//撤回方法
const handleCancel = async () => {
  try {
    submitting.value = true;

    console.log(7777,bussWorksheet.value.procInstanceId)
    // 构建最终提交数据结构
    const submitData = {
      id:bussWorksheet.value.procInstanceId,
      key:bussWorksheet.value.type
    };


    const res = await EquipmentAcceptanceApi.end(submitData);

    if (res.success) {
      message.success('撤回成功');
      window.close()
    } else {
      message.error('撤回失败');
    }
  } catch (error) {
    message.error('撤回失败');
  } finally {
    submitting.value = false;
  }
};

const formData = ref({
  id: null,
  baseCode: '', // 基础编码
  code: '', // 编码
  equType: null, // 设备类别
  equSubType: null, // 设备种类
  equName: null, // 设备名称
  equModel: null, // 规格型号
  equModelNote: '', // 型号备注
  equNature: null, // 设备性质
  purchaseYear: '', // 购置年度
  equSource: null, // 设备来源
  unit: '', // 单位
  num: 1, // 数量
  equContractPriceTax: null, // 设备合同价（含税）
  manufacturer: '', // 生产厂家
  factoryNumber: '', // 出厂编号
  productionDate: null, // 出厂日期
  contractNumber: '', // 合同编号
  acceptanceNumber: null, // 验收单号
  acceptanceChar: '', // 验字
  acceptanceDate: null, // 验收日期
  power: '', // 功率
  importance: null, // 设备重要性
  modelCode: '', // 设备型号编码
  serviceInterval: '', // 保养周期
  propertyOrg: null, // 产权单位
  managementStatus: null, // 管理状态
  managementOrg: null, // 管理单位
  equCondition: null, // 设备状态
  useOrg: null, // 使用单位
  storageLocationStr: null, // 存放地点
  financialNumber: '', // 财务卡片编号
  financialOrg: '', // 财务组织
  financialOriginalValue: null, // 财务原值
  fixedAssets: '', // 固定资产分类
  sourceOfFunds: '', // 资金来源
  deferredIncome: '', // 递延收益
  alreadyAccruedMonths: '', // 已计提折旧月份
  depreciationMethod: null, // 折旧方式
  depreciationPeriod: '', // 折旧年限
  depreciationMonth: '', // 折旧月份
  residualRate: null, // 残值率
  netSalvage: null, // 预计净残值
  historicalOriginalValue: null, // 历史原值
  depreciationAmount: null, // 累计折旧额
  currentMonthDepreciationAmount: null, // 当月折旧额
  netWorth: null, // 净值
  taxRate: 13, // 税率
  approvalDescription: '', // 审批说明
});

// 附件列表数据
const attachments = ref([
  {
    name: '',
    unit: '',
    quantity: 1,
    manufacturer: '',
    serialNo: ''
  }
]);

// const columns = [
//   // 左侧固定列
//   {
//     title: '序号',
//     width: 80,
//     fixed: 'left',
//     customRender: ({ index }) => index + 1
//   },
//   {
//     title: '设备编号',
//     dataIndex: 'code',
//     width: 150,
//     fixed: 'left',
//     ellipsis: true,
//   customHeaderCell: () => ({
//       style: {
//         minWidth: '150px',
//         maxWidth: '150px'
//       }
//     }),
//     customCell: () => ({
//       style: {
//         minWidth: '100px',
//         maxWidth: '100px'
//       }
//     })
//   },
//   { title: '财务卡片编号', dataIndex: 'financialNumber', width: 130, ellipsis: true,
//   customHeaderCell: () => ({
//       style: {
//         minWidth: '100px',
//         maxWidth: '100px'
//       }
//     }),
//     customCell: () => ({
//       style: {
//         minWidth: '100px',
//         maxWidth: '100px'
//       }
//     }) },
//   { title: '设备名称', dataIndex: 'equNameStr', width: 100, ellipsis: true,
//   customHeaderCell: () => ({
//       style: {
//         minWidth: '100px',
//         maxWidth: '100px'
//       }
//     }),
//     customCell: () => ({
//       style: {
//         minWidth: '100px',
//         maxWidth: '100px'
//       }
//     }) },
//   { title: '规格型号', dataIndex: 'equModelStr', width: 100,ellipsis: true,
//   customHeaderCell: () => ({
//       style: {
//         minWidth: '100px',
//         maxWidth: '100px'
//       }
//     }),
//     customCell: () => ({
//       style: {
//         minWidth: '100px',
//         maxWidth: '100px'
//       }
//     }) },


//   // 中间可滚动列
//   { title: '型号备注', dataIndex: 'equModelInfo', width: 100},
//   { title: '管理单位', dataIndex: 'managementOrgStr', width: 120 },
//   { title: '使用单位', dataIndex: 'useOrgStr', width: 120 },
//   { title: '存放地点', dataIndex: 'storageLocationStr', width: 120 },
//   { title: '财务原值', dataIndex: 'financialOriginalValue', width: 120 },
//   { title: '净值', dataIndex: 'netWorth', width: 120 },

//   { title: '管理状态', dataIndex: 'managementStatusStr', width: 100 },
//   { title: '设备状态', dataIndex: 'equConditionStr', width: 100 },

//   { title: '生产厂家', dataIndex: 'manufacturer', width: 150 },
//   { title: '出厂日期', dataIndex: 'productionDate', width: 120 },
//   { title: '出厂编号', dataIndex: 'factoryNumber', width: 120 },
//   { title: '设备类别', dataIndex: 'equTypeStr', width: 120 },
//   { title: '设备种类', dataIndex: 'equSubTypeStr', width: 120 },
//   { title: '设备性质', dataIndex: 'equNatureStr', width: 120 },
//   { title: '固定资产分类', dataIndex: 'fixedAssetsStr', width: 140 },
//   // { title: '购置日期', dataIndex: 'purchaseDate', width: 120 },
//   // { title: '购置年度', dataIndex: 'purchaseYear', width: 100 },

//   { title: '产权单位', dataIndex: 'propertyOrgStr', width: 120 },
//   { title: '功率kw', dataIndex: 'power', width: 120 },
//   { title: '设备型号编码', dataIndex: 'modelCode', width: 140 },
//   { title: '购置年度', dataIndex: 'purchaseYear', width: 100 },
// ];

const columns = [
  // 左侧固定列
  {
    title: '序号',
    width: 80,
    fixed: 'left',
    customRender: ({ index }) => index + 1
  },
  {
    title: '设备编号',
    dataIndex: 'code',
    width: 150,
    fixed: 'left',
    ellipsis: true,
  },
  { title: '设备类别', dataIndex: 'equTypeStr', width: 150 },
  { title: '设备种类', dataIndex: 'equSubTypeStr', width: 150 },
  { title: '设备名称', dataIndex: 'equNameStr', width: 150, ellipsis: true },
  { title: '规格型号', dataIndex: 'equModelStr', width: 150, ellipsis: true },
  { title: '型号备注', dataIndex: 'equModelInfo', width: 150 },
  { title: '设备性质', dataIndex: 'equNatureStr', width: 150 },
  { title: '购置年度', dataIndex: 'purchaseYear', width: 150 },
  { title: '设备来源', dataIndex: 'equSourceStr', width: 150 },
  { title: '单位', dataIndex: 'unit', width: 150 },
  { title: '设备合同价(含税)', dataIndex: 'equContractPriceTax', width: 150 },
  { title: '生产厂家', dataIndex: 'manufacturer', width: 160 },
  { title: '出厂编号', dataIndex: 'factoryNumber', width: 150 },
  { title: '出厂日期', dataIndex: 'productionDate', width: 150 },
  { title: '合同编号', dataIndex: 'contractNumber', width: 150 },
  { title: '验收单号', dataIndex: 'acceptanceNumber', width: 150 },
  { title: '验收日期', dataIndex: 'acceptanceDate', width: 150 },
  { title: '功率', dataIndex: 'power', width: 150 },
  { title: '设备重要性', dataIndex: 'importanceStr', width: 150 },
  { title: '设备型号编码', dataIndex: 'modelCode', width: 140 },
  { title: '使用单位', dataIndex: 'useOrgStr', width: 150 },
  { title: '使用单位代码', dataIndex: 'useOrgCode', width: 150 },
  { title: '存放地点', dataIndex: 'storageLocationStr', width: 150 },

  { title: '折旧方式', dataIndex: 'depreciationMethodStr', width: 150 },
  { title: '折旧年限', dataIndex: 'depreciationPeriod', width: 150 },
  { title: '历史原值', dataIndex: 'historicalOriginalValue', width: 150 },

  { title: '财务原值', dataIndex: 'financialOriginalValue', width: 150 },
  { title: '净值', dataIndex: 'netWorth', width: 150 },
  { title: '税率', dataIndex: 'taxRate', width: 150 },
  { title: '残值率', dataIndex: 'residualRate', width: 150 },
  { title: '财务组织', dataIndex: 'financialOrg', width: 150 },
  { title: '财务卡片编号', dataIndex: 'financialNumber', width: 150, ellipsis: true },
  { title: '固定资产分类', dataIndex: 'fixedAssetsStr', width: 150 },
];

// 添加新附件
const addAttachment = () => {
  attachments.value.push({
    name: '',
    unit: '',
    quantity: 1,
    manufacturer: '',
    serialNo: ''
  });
};

// 删除附件
const removeAttachment = (index) => {
  attachments.value.splice(index, 1);
};

// 处理普通附件上传
const handleChange = (info) => {
  console.log('当前上传的文件:', info.file);
  console.log('所有文件列表:', info.fileList);

  // 保持原始fileList与上传组件同步
  fileList.value = info.fileList;

  // 更新显示用的文件列表
  fileViewList.value = info.fileList
    .filter(file => file.status === 'done')  // 只保留上传完成的文件
    .map(file => ({
      fileId: file.response.data.fileId, fileUrl: file.response.data.fileUrl,
      fileName: file.name,
      uid: file.uid,
      status: file.status
    }));

  // 处理单个文件的状态提示
  if (info.file.status === 'error') {
    message.error('上传出错：' + info.file.error.message);
  }
};

// 处理技术资料上传
const handleTechFileChange = (info) => {
  // 保持原始列表与上传组件同步
  techFileList.value = info.fileList;

  if (info.file.status === 'done' && info.file.response) {
    const response = info.file.response;
    if (response.success) {
      // 更新显示列表
      techFileViewList.value = [{
        fileId: response.data.fileId,
        fileName: info.file.name,
        uid: info.file.uid,
        status: 'done'
      }];

      // 更新formData中的techFile用于显示
      formData.value.techFile = {
        fileId: response.data.fileId,
        fileName: info.file.name
      };
    } else {
      message.error('技术资料上传失败');
    }
  } else if (info.file.status === 'error') {
    message.error('上传出错：' + info.file.error.message);
  }
};



// 审批记录相关方法
const showApprovalRecord = () => {
  // 处理审批记录的显示逻辑
  console.log('显示审批记录');
};


// 定义路由参数
const processDefinitionId = ref('');
const actId = ref('');
const taskId = ref('');
const procInsId = ref('');
const worksheetId = ref('');


</script>

<style lang="less" scoped>
.approval-record-btn {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  z-index: 100;

  .bg-image {
    display: block;
    max-width: 100%;
    height: auto;
    object-fit: contain;
    background-color: transparent;
  }

  .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .icon {
      width: 15px;
      height: 15px;
      margin-bottom: 4px;
    }

    .text {
      writing-mode: vertical-lr;
      font-size: 14px;
      color: #333;
      letter-spacing: 2px;
    }
  }
}

.equipment-acceptance {
  width: 100%;
  max-width: 1920px; // 最大宽度限制
  height: calc(100vh - 40px);
  overflow: auto;
  margin: 0 auto; // 居中
  padding: 24px 9.06vw 24px 8.18vw; // 转换为vw: 174/1920*100=9.06, 157/1920*100=8.18
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);

  .form-title {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 24px;
  }

  .section {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;

      .blue-bar {
        width: 3px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
      }
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: clamp(16px, 2vw, 24px);
    margin-bottom: 24px;
    justify-content: space-between;

    .form-item {
      display: flex;
      flex-direction: column;
      min-width: 300px; // 最小宽度
      max-width: 300px; // 最大宽度

      .label {
        height: 32px;
        line-height: 32px;
        margin-bottom: 8px;
        color: #0C0B0B;

        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 4px;
        }
      }

      .value {
        width: 100%;
        height: 32px;
        background: rgba(255, 255, 255, 0.6);
        box-sizing: border-box;
        /* -line-列表 */
        border: 0.5px solid rgba(30, 41, 64, 0.08);

        :deep(.ant-input),
        :deep(.ant-select),
        :deep(.ant-picker),
        :deep(.ant-input-number) {
          width: 100%;
          height: 32px;
        }

        :deep(.ant-input-number-input) {
          height: 16px;
          line-height: 16px;
        }

        :deep(.ant-select-selector) {
          height: 32px !important;

          .ant-select-selection-item {
            line-height: 32px;
          }
        }

        :deep(.ant-picker) {
          .ant-picker-input {
            height: 16px;
            line-height: 16px;

            input {
              height: 16px;
              line-height: 16px;
            }
          }
        }
      }
    }
  }

  .acceptance-no {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    max-width: 300px; // 与其他表单项保持一致的最大宽度

    :deep(.first-input) {
      width: 65% !important; // 验资前面的输入框占65%
    }

    :deep(.second-input) {
      width: 35% !important; // 号前面的输入框占35%
    }

    .separator {
      color: #666;
      white-space: nowrap;
    }
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .equipment-acceptance {
    padding: 24px 6vw;

    .form-grid {
      gap: 16px;
    }
  }
}

@media screen and (max-width: 1366px) {
  .equipment-acceptance {
    padding: 24px 4vw;

    .form-grid {
      grid-template-columns: repeat(2, minmax(300px, 1fr));
    }
  }
}

@media screen and (max-width: 1024px) {
  .equipment-acceptance {
    padding: 24px 2vw;

    .form-grid {
      grid-template-columns: repeat(1, 1fr);

      .form-item {
        max-width: none;
      }
    }
  }
}

.percentage-input {
  position: relative;
  display: flex;
  align-items: center;

  .ant-input {
    width: 100%;
  }

  .percentage-symbol {
    position: absolute;
    right: 12px;
    color: #666;
  }
}

.section {
  .a-table {
    margin-top: 16px;
  }
}

.upload-section {
  .upload-header {
    display: flex;
    align-items: center;
    gap: 12px;

    .upload-button {
      display: inline-flex;
      align-items: center;
      padding: 4px 15px;
      background: #0085FF;
      border-radius: 2px;
      color: white;
      cursor: pointer;
      font-size: 14px;

      .anticon {
        margin-right: 4px;
      }
    }

    .upload-tip {
      color: #999;
      font-size: 12px;
    }
  }

  .file-list {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    //display: grid;
    //grid-template-columns: repeat(3, 0.3fr);
    //gap: 16px;

    .file-item {
      display: flex;
      align-items: center;
      padding: 8px;
      // background: #F7F9FC;
      border-radius: 2px;
      width: 32%;

      .file-link {
        word-break: break-all;
      }

      .file-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #2D5CF6;
      }

      .file-name {
        flex: 1;
        font-size: 14px;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-actions {
        margin-left: 8px;

        .delete-icon {
          color: #999;
          cursor: pointer;

          &:hover {
            color: #FF4B4B;
          }
        }

        .anticon-sync {
          color: #2D5CF6;
        }
      }
    }
  }
}

.tech-file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;

  .file-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      color: #2D5CF6;
    }

    .file-name {
      color: #333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .delete-icon {
    color: #999;
    cursor: pointer;

    &:hover {
      color: #ff4d4f;
    }
  }
}

.tech-upload-button {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  color: #666;
  font-size: 14px;

  .anticon {
    margin-right: 4px;
    font-size: 14px;
  }

  &:hover {
    color: #2D5CF6;
    border-color: #2D5CF6;
  }
}

.value {
  :deep(.ant-input-affix-wrapper) {
    .ant-input-suffix {
      color: #666;
    }
  }
}

.bottom-buttons {
  margin-top: 50px;
  margin-bottom: 89rpx;
  bottom: 180px; // 修改为距离底部80px
  left: 0;
  right: 0;
  padding: 0 32px; // 调整左右内边距
  text-align: right;
  z-index: 100;

  .reject-btn {
    height: 32px;
    padding: 0 16px;
    border: 1px solid #f30d05;
    color: #f30d05;
    background: transparent !important;
    border-radius: 4px;

    &:hover {
      border-color: #409EFF;
      color: #409EFF;
    }
  }

  .submit-btn {
    height: 32px;
    padding: 0 16px;
    margin-left: 27px; // 调整按钮之间的间距
    background: #409EFF;
    border-radius: 4px;
    border: none;

    &:hover {
      background: #66B1FF;
    }
  }
}

.equipment-info-table {
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  border: 0.5px solid rgba(30, 41, 64, 0.08);
  ; // 加深整体边框颜色

  .info-row {
    display: flex;

    &:last-child {
      border-bottom: none;
    }

    .info-cell {
      flex: 1;
      display: flex;
      min-height: 44px;
      border-bottom: 0.5px solid #d9d9d9; // 加深底部边框颜色

      .label {
        width: 140px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        background: #E2F0FF;
        color: #333;
        font-size: 14px;
        padding: 0 16px;
        box-sizing: border-box;
        border-bottom: none; // 移除label的单独底边
      }

      .value {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 0 16px;
        background: #fff;
        color: #333;
        font-size: 14px;
        border-bottom: none; // 移除value的单独底边
      }

      border-right: 1px solid #d9d9d9; // 加深右侧边框颜色

      &:last-child {
        border-right: none;
      }
    }

    &:last-child {
      .info-cell {
        border-bottom: none;
      }
    }
  }

  // 技术资料样式
  .tech-files {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    .preview-btn,
    .view-btn {
      display: inline-flex;
      align-items: center;
      color: #1890FF;
      cursor: pointer;
      margin-right: 16px;

      .anticon {
        margin-right: 4px;
      }

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.section {
  :deep(.ant-table-thead) {
    >tr>th {
      background: #E2F0FF;
      box-sizing: border-box;
      border: 0.5px solid rgba(30, 41, 64, 0.08);

      // 重置 hover 状态的背景色
      &:hover {
        background: #E2F0FF !important;
      }
    }
  }

  // 确保表格边框样式一致
  :deep(.ant-table) {
    border: 0.5px solid rgba(30, 41, 64, 0.08);
  }

  :deep(.ant-table-cell) {
    border: 0.5px solid rgba(30, 41, 64, 0.08) !important;
  }
}

.approval-description {
  padding: 16px 0;
  background: transparent;

  .ant-textarea {
    width: 100%;
    border: 1px solid #E2F0FF;
    border-radius: 4px;
    box-sizing: border-box;

    &:hover,
    &:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }

  :deep(.ant-table) {

    // 表格布局设置
    table {
      table-layout: fixed !important; // 强制表格使用固定布局
      min-width: 100% !important;
      width: max-content !important; // 确保表格内容超出时显示滚动条
    }

    // 单元格样式
    .ant-table-cell {
      // max-width: 100% !important; // 限制单元格最大宽度
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
      line-height: 1 !important;
      font-size: 14px !important;

      >span,
      >div {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }
    }

    // 确保表格内容可以水平滚动
    .ant-table-content {
      overflow-x: auto !important;
      min-width: 100% !important;
      z-index: 0 !important;
    }

    // 确保滚动区域正确显示
    .ant-table-body {
      overflow-x: auto !important;
      overflow-y: auto !important;
      min-width: 100% !important;
    }

    // 兼容360浏览器
    .ant-table-container {
      overflow-x: auto !important;
    }

    // 大屏幕样式（默认）
    @media screen and (min-width: 1920px) {
      .ant-table-cell {
        padding: 20px 20px !important;
        height: 60px !important;
      }

      .ant-table-row {
        height: 60px !important;
      }
    }

    // 中等屏幕样式
    @media screen and (min-width: 1366px) and (max-width: 1919px) {
      .ant-table-cell {
        padding: 10px 20px !important;
        height: 40px !important;
      }

      .ant-table-row {
        height: 40px !important;
      }
    }

    // 小屏幕样式
    @media screen and (max-width: 1365px) {
      .ant-table-cell {
        padding: 4px 8px !important;
        height: 32px !important;
      }

      .ant-table-row {
        height: 32px !important;
      }
    }

    // 固定列样式
    .ant-table-fixed-left,
    .ant-table-fixed-right {
      background: #fff !important;
      box-shadow: none !important; // 移除原有阴影
      z-index: 3 !important; // 提高固定列的层级
    }

    // 隐藏特定列
    tr>.ant-table-cell-fix-left:nth-child(6) {
      display: none !important;
    }

    // 调整固定列单元格样式
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      z-index: 3 !important; // 增加层级
      background: #ECF4FE !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }

    // 调整表头固定列样式
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right {
        z-index: 4 !important; // 确保表头在最上层
        background: #DAECFF !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }

      .ant-table-cell-scrollbar {
        box-shadow: none;
      }
    }

    // 优化阴影效果
    .ant-table-fixed-right::before,
    .ant-table-fixed-left::before {
      content: '' !important;
      position: absolute !important;
      top: 0 !important;
      bottom: 0 !important;
      width: 10px !important;
      pointer-events: none !important;
      z-index: 2 !important; // 阴影层级低于固定列
      transition: box-shadow .3s !important;
    }

    .ant-table-fixed-left::before {
      right: 0 !important;
      box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15) !important;
    }

    .ant-table-fixed-right::before {
      left: 0 !important;
      box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15) !important;
    }
  }

}


.status-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.status-image {
  width: 146px;
  height: auto;
}
</style>
