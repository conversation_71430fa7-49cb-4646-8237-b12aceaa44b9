import Request from '@/utils/request-util';

/**
 * 设备验收API
 *
 * <AUTHOR> @date 2024/01/09
 */
export class scrapEquipmentApi {
  /**
   * 报废获取新建数据
   *
   * <AUTHOR> @date 2024/01/09
   */
  static getData(params) {
    return Request.get('/apiBus/scrapedEquipment/page', params);
  }

  static getOriginalValue(params) {
    return Request.get('/apiBus/scrapedEquipment/getOriginalValue', params);
  }

  static getOriginalValue1(params) {
    return Request.get('/apiBus/disposaledEquipment/getOriginalValue', params);
  }

  /**
   * 处置获取新建数据
   *
   * <AUTHOR> @date 2024/01/09
   */
  static getDisposaled(params) {
    return Request.get('/apiBus/disposaledEquipment/page', params);
  }

  /**
   * 导出设备数据
   *
   * @param {Object} params - 查询参数，与getData接口使用相同的参数
   */
  static exportData(params) {
    return Request.downLoad('/api/apiBus/scrapedEquipment/export', params);
  }

  /**
  * 导出设备数据
  *
  * @param {Object} params - 查询参数，与getData接口使用相同的参数
  */
  static exportData1(params) {
    return Request.downLoad('/api/apiBus/disposaledEquipment/export', params);
  }

  static getEquipmentFullInfo(params) {
    return Request.get('/apiBus/scrapedEquipment/getEquipmentFullInfo', params);
  }

  static getDisposaledEquipmentFullInfo(params) {
    return Request.get('/apiBus/disposaledEquipment/getEquipmentFullInfo', params);
  }
}
