<template>
  <div class="ele-body">
    <!-- 搜索表单 -->
    <div class="block-interval">
      <a-card :bordered="false">
        <a-form layout="inline" :model="where">
          <a-row>
            <a-form-item label="表单名称">
              <a-input v-model:value.trim="where.formResourceName" placeholder="请输入表单名称" allow-clear/>
            </a-form-item>
            <a-form-item label="唯一编码">
              <a-input v-model:value.trim="where.formResourceCode" placeholder="请输入唯一编码" allow-clear/>
            </a-form-item>
            <a-form-item class="ele-text-center">
              <a-space>
                <a-button type="primary" @click="reload">查询</a-button>
                <a-button @click="reset">重置</a-button>
              </a-space>
            </a-form-item>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 表格 -->
    <div>
      <a-card :bordered="false">
        <ele-pro-table
          ref="table"
          row-key="formResourceId"
          :datasource="datasource"
          :columns="columns"
          :where="where"
          v-model:selection="selection"
          :scroll="{ x: 'max-content' }"
        >
          <!-- table上边工具栏 -->
          <template #toolbar>
            <a-space>
              <a-button type="primary" @click="openEdit()">
                <template #icon>
                  <plus-outlined/>
                </template>
                <span>新建</span>
              </a-button>
            </a-space>
          </template>

          <template #bodyCell="{ column, record, text }">
            <!-- 表单分类 -->
            <template v-if="column.key === 'category'">
              {{ getCategoryName(text) }}
            </template>

            <!-- table操作栏按钮 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="openPreview(record)">预览</a>
                <a-divider type="vertical"/>
                <a @click="openDesign(record)">设计</a>
                <a-divider type="vertical"/>
                <a-dropdown>
                  <a class="ant-dropdown-link" @click.prevent>
                    更多
                    <DownOutlined/>
                  </a>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item>
                        <a @click="openEdit(record)">编辑</a>
                      </a-menu-item>
                      <a-menu-item>
                        <a @click="remove(record)">删除</a>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </ele-pro-table>
      </a-card>
    </div>

    <!-- 编辑弹窗 -->
    <simple-form-edit v-model:visible="showEdit" :data="current" @done="reload"/>
    <!-- 预览弹窗 -->
    <simple-form-preview v-model:visible="showPreview" :data="current" @done="reload"/>
    <!-- 设计弹窗 -->
    <simple-form-design v-model:visible="showDesign" :data="current" @done="reload"/>
  </div>
</template>

<script>
import {message} from 'ant-design-vue';
import {DownOutlined, PlusOutlined} from '@ant-design/icons-vue';
import SimpleFormEdit from '@/views/workflow/form/simple-form-edit.vue';
import SimpleFormPreview from '@/views/workflow/form/simple-form-preview.vue';
import SimpleFormDesign from '@/views/workflow/form/simple-form-design.vue';
import {CategoryApi} from '@/api/workflow/CategoryApi';
import {FormResourceApi} from '@/api/workflow/FormResourceApi';

export default {
  name: 'FormIndex',
  components: {
    SimpleFormEdit,
    SimpleFormPreview,
    PlusOutlined,
    DownOutlined,
    SimpleFormDesign
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          key: 'index',
          width: 48,
          align: 'center',
          fixed: 'left',
          hideInSetting: true,
          customRender: ({index}) => this.$refs.table.tableIndex + index
        },
        {
          title: '表单名称',
          dataIndex: 'formResourceName'
        },
        {
          title: '唯一编码',
          dataIndex: 'formResourceCode'
        },
        {
          title: '表单分类',
          dataIndex: 'category',
          key: 'category'
        },
        {
          title: '备注',
          dataIndex: 'remark'
        },
        {
          title: '操作',
          key: 'action',
          align: 'center'
        }
      ],
      // 表格搜索条件
      where: {},
      // 表格选中数据
      selection: [],
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showEdit: false,
      // 是否显示预览弹窗
      showPreview: false,
      // 是否显示设计弹窗
      showDesign: false,
      // 表单分类
      flowableCategoryListData: []
    };
  },
  mounted() {
    // 获取分类列表
    CategoryApi.categoryList().then(data => {
      this.flowableCategoryListData = data;
    });
  },
  methods: {
    /**
     * 搜索按钮
     *
     * <AUTHOR>
     * @date 2021/4/2 17:03
     */
    reload() {
      this.selection = [];
      this.$refs.table.reload({page: 1});
    },

    /**
     * 重置搜索
     *
     * <AUTHOR>
     * @date 2021/4/2 17:03
     */
    reset() {
      this.where.formResourceName = '';
      this.where.formResourceCode = '';
      this.reload();
    },

    /**
     * 删除单个
     *
     * <AUTHOR>
     * @date 2021/4/2 17:03
     */
    async remove(row) {
      const result = await FormResourceApi.del({formResourceId: row.formResourceId});
      message.success(result.message);
      this.reload();
    },

    /**
     * 打开编辑弹窗
     *
     * <AUTHOR>
     * @date 2021/4/2 17:03
     */
    openEdit(row) {
      this.current = row;
      this.showEdit = true;
    },
    /**
     * 打开预览弹窗
     *
     * <AUTHOR>
     * @date 2021/7/15 10:48
     */
    openPreview(row) {
      this.current = row;
      this.showPreview = true;
    },
    /**
     * 打开设计弹窗
     *
     * <AUTHOR>
     * @date 2021/7/15 11:09
     */
    openDesign(row) {
      this.current = row;
      this.showDesign = true;
    },
    /**
     * 通过分类code获取分类名称
     *
     * <AUTHOR>
     * @date 2021/7/1 18:43
     */
    getCategoryName(text) {
      const values = this.flowableCategoryListData.filter(item => item.categoryCode === text);
      if (values.length > 0) {
        return values[0].categoryName;
      }
    },

    /**
     * 打开表单预览
     *
     * <AUTHOR>
     * @date 2021/7/1 18:43

     openPreview(record) {
      console.log(record);
    },
     */
    /**
     * 打开表单设计界面
     *
     * <AUTHOR>
     * @date 2021/7/1 18:43

     async openDesign(record) {
      // 获取表单设计的详情
      let promise = await FormResourceApi.formResourceDetail({ formResourceId: record.formResourceId });
      console.log(promise);
    },
     */
    /**
     * 打开表单设计界面
     *
     * <AUTHOR>
     * @date 2021/7/1 18:43
     */
    async saveDesign(record) {
      // 获取表单设计的详情
      let promise = await FormResourceApi.formResourceDetail({formResourceId: record.formResourceId});
      console.log(promise);
    },

    /**
     * 获取表格数据
     *
     * <AUTHOR>
     * @date 2022/5/8 15:18
     */
    datasource({page, limit, where, orders}) {
      return FormResourceApi.findPage({...where, ...orders, pageNo: page, pageSize: limit});
    }
  }
};
</script>

<style scoped>
/*搜索框与表格之间的间隙*/
.block-interval {
  margin-bottom: 10px;
}
</style>
