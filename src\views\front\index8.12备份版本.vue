<template>
    <div class="scale-outer">

        <div class="dashboard-container" style="position: relative;">
            <!-- 顶部标题栏 -->
            <div class="header-bar">
                <div class="logo-title">
                    <img src="@/assets/equipment/map/maplogin.svg" alt="logo" class="logo-img" />

                </div>
                <div class="nav-tabs">
                    <div class="tab" :class="{ active: activeTab === 'home' }" @click="activeTab = 'home'">首页</div>
                    <div class="tab" :class="{ active: activeTab === 'dashboard' }" @click="activeTab = 'dashboard'">
                        数据驾驶舱
                    </div>
                    <div class="tab" :class="{ active: activeTab === 'equipment' }" @click="activeTab = 'equipment'">
                        设备联网
                    </div>
                </div>
                <div class="user-controls">
                    <div style="display: flex;align-items: center;">
                        <a-button type="primary" class="login-button" @click="gotoBack">
                            <img src="@/assets/equipment/map/fhsy.svg" />
                            <span class="homes">进入后台</span>
                        </a-button>
                        <div class="notification">
                            <!-- <bell-outlined />
                    <span class="badge">2</span> -->
                            <header-notice background-color="#141E4E" text-color="#F5F6F8" border-color="#141E4E"
                                :isShowFooter="false" border-bottom-color="2px solid rgba(255, 255, 255, 0.1)" />
                        </div>
                    </div>

                    <a-dropdown placement="bottomRight" :overlay-style="{ minWidth: '120px' }">
                        <div class="user-info">
                            <a-avatar :size="32" :src="loginUser.avatar" />
                            <span>{{ loginUser.realName }}</span>
                            <down-outlined class="down-icon" />
                        </div>
                        <template #overlay>
                            <a-menu @click="onUserDropClick">
                                <a-menu-item key="logout">
                                    <div class="ele-cell">
                                        <logout-outlined />
                                        <div class="ele-cell-content">退出登录</div>
                                    </div>
                                </a-menu-item>
                            </a-menu>
                        </template>
                    </a-dropdown>
                </div>
            </div>

            <!-- 主体内容区 -->
            <div class="dashboard-content">
                <!-- 左侧悬浮面板 -->
                <div class="left-panel" :class="{ 'panel-hidden': leftPanelHidden }">
                    <div class="panel-content">
                        <!-- 设备状态卡片 -->
                        <div class="device-status-card">
                            <div class="card-header">
                                <span class="title__eltit">
                                    联网设备状态
                                </span>
                            </div>
                            <div class="card-content">
                                <div class="device-status-grid">
                                    <div class="one_contant">
                                        <div class="status-item">
                                            <div class="status-icon hexagon">
                                                <img src="@/assets/equipment/map/a.svg" alt="运行中" />
                                            </div>
                                            <div class="status-info">
                                                <div class="status-label">运行中</div>
                                                <div class="status-count">
                                                    <span class="count-number running">8600</span>
                                                    <span class="count-unit">(台)</span>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="status-item">
                                            <div class="status-icon hexagon">
                                                <img src="@/assets/equipment/map/b.svg" alt="维修中" />
                                            </div>
                                            <div class="status-info">
                                                <div class="status-label">维修中</div>
                                                <div class="status-count">
                                                    <span class="count-number running">92</span>
                                                    <span class="count-unit">(台)</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                    <div class="one_contant">

                                        <div class="status-item">
                                            <div class="status-icon hexagon">
                                                <img src="@/assets/equipment/map/c.svg" alt="停用" />
                                            </div>
                                            <div class="status-info">
                                                <div class="status-label">停用</div>
                                                <div class="status-count">
                                                    <span class="count-number running">214</span>
                                                    <span class="count-unit">(台)</span>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="status-item">
                                            <div class="status-icon hexagon">
                                                <img src="@/assets/equipment/map/d.svg" alt="保养中" />
                                            </div>
                                            <div class="status-info">
                                                <div class="status-label">保养中</div>
                                                <div class="status-count">
                                                    <span class="count-number running">81</span>
                                                    <span class="count-unit">(台)</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>



                        <!-- 设备预警情况 -->
                        <div class="device-warning-card">
                            <div class="warning-sections">
                                <!-- 设备联网情况 -->
                                <div class="warning-section warning-section-margin">
                                    <div class="section-icon">
                                        <div class="icon-circle network">
                                            <img src="@/assets/equipment/map/zuozuo.png" alt="联网" />
                                        </div>
                                    </div>
                                    <div class="section-title">设备联网情况</div>
                                    <div class="section-stats">
                                        <div class="stat-item">
                                            <div class="stat-label">联网</div>
                                            <div class="stat-value online">1091</div>
                                        </div>

                                        <!-- 添加竖线分隔符 -->
                                        <div class="divider-vertical">
                                            <img src="@/assets/equipment/map/shu.png" alt="分隔线" />
                                        </div>

                                        <div class="stat-item">
                                            <div class="stat-label">离线</div>
                                            <div class="stat-value offline">105</div>
                                        </div>
                                    </div>

                                    <!-- 添加横线 -->
                                    <div class="divider-horizontal">
                                        <img src="@/assets/equipment/map/heng.png" alt="横线" />
                                    </div>
                                </div>

                                <!-- 设备报警情况 -->
                                <div class="warning-section">
                                    <div class="section-icon">
                                        <div class="icon-circle alarm">
                                            <img src="@/assets/equipment/map/zuoyou.png" alt="报警" />
                                        </div>
                                    </div>
                                    <div class="section-title">设备报警情况</div>
                                    <div class="section-stats">
                                        <div class="stat-item">
                                            <div class="stat-label">
                                                <span style="width: 100%;">
                                                    已处理
                                                </span>
                                            </div>
                                            <div class="stat-value processed">13</div>
                                        </div>

                                        <!-- 添加竖线分隔符 -->
                                        <div class="divider-vertical">
                                            <img src="@/assets/equipment/map/shu.png" alt="分隔线" />
                                        </div>

                                        <div class="stat-item">
                                            <div class="stat-label">
                                                <!-- <span class="online-dot">
                                                    <img src="@/assets/equipment/map/zuozuo4.png" alt="联网" />
                                                </span> -->
                                                <span style="width: 100%;">
                                                    未处理
                                                </span>
                                            </div>
                                            <div class="stat-value unprocessed">14</div>
                                        </div>
                                    </div>

                                    <!-- 添加横线 -->
                                    <div class="divider-horizontal">
                                        <img src="@/assets/equipment/map/heng.png" alt="横线" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 设备调拨次数分布 -->
                        <div class="device-allocation-card">
                            <div class="card-header">
                                <span class="title__eltit">
                                    设备调拨台数
                                </span>
                            </div>
                            <div class="card-content">
                                <div class="allocation-layout">
                                    <!-- 柱状图区域 -->
                                    <div class="chart-container">
                                        <div class="bar-chart" ref="allocationChartRef"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 中间地图区域 -->
                <div class="map-container">
                    <!-- 顶部悬浮统计栏 -->
                    <div class="map-floating-stats">
                        <div class="map-top-controls">
                            <div style="display: flex;align-items: center;">
                                <!-- <div class="map-region-selector">
                                    <span>安徽</span>
                                    <down-outlined />
                                </div> -->
                                <div class="selectbj">
                                    <a-cascader v-model:value="address" :options="cityOptions" placeholder="请选择地址"
                                        :fieldNames="{ children: 'children', label: 'name', value: 'id' }"
                                        change-on-select expandTrigger="hover" popupClassName="cityOpt"
                                        class="selectbj1" :allowClear="false" @change="cascaderChange" />
                                </div>
                                <div class="map-world-map-toggle" @click="toggleMapType('world')" v-if="!isWorldMap">
                                    <sync-outlined />
                                    <span>切换至世界地图</span>
                                </div>

                                <div class="map-world-map-toggle" @click="toggleMapType('china')" v-if="isWorldMap">
                                    <sync-outlined />
                                    <span>切换至中国地图</span>
                                </div>
                            </div>
                            <div class="map-controls-right">
                                <!-- <div class="map-fullscreen-toggle" @click="toggleFullscreenMap">
                                <fullscreen-outlined v-if="!mapFullscreen" />
                                <fullscreen-exit-outlined v-else />
                                <span>{{ mapFullscreen ? '退出全屏' : '全屏' }}</span>
                            </div> -->
                            </div>
                        </div>
                        <div class="map-stats-bar">
                            <div>
                            </div>
                            <div class="map-stat-item">
                                <!-- <div class="map-stat-icon">
                                        <img src="@/assets/equipment/map/dengji.png" alt="设备台数" />
                                    </div> -->
                                <div class="map-stat-content map-stat-content1">
                                    <div class="map-stat-label">设备台数</div>
                                    <div class="map-stat-value">{{ statsData[0].value }}</div>
                                </div>
                            </div>
                            <div class="map-stat-item">
                                <!-- <div class="map-stat-icon">
                                        <img src="@/assets/equipment/map/zaiku.png" alt="一级设备" />
                                    </div> -->
                                <div class="map-stat-content map-stat-content1">
                                    <div class="map-stat-label">一级设备</div>
                                    <div class="map-stat-value">{{ statsData[1].value }}</div>
                                </div>
                            </div>
                            <div class="map-stat-item">
                                <!-- <div class="map-stat-icon">
                                        <img src="@/assets/equipment/map/zaiku.png" alt="销售设备" />
                                    </div> -->
                                <div class="map-stat-content map-stat-content1">
                                    <div class="map-stat-label">二级设备</div>
                                    <div class="map-stat-value">{{ statsData[2].value }}
                                    </div>
                                </div>
                            </div>
                            <div class="map-stat-item">
                                <!-- <div class="map-stat-icon">
                                        <img src="@/assets/equipment/map/daiguan.png" alt="代管设备" />
                                    </div> -->
                                <div class="map-stat-content map-stat-content1">
                                    <div class="map-stat-label">租用设备</div>
                                    <div class="map-stat-value">{{ statsData[3].value }}</div>
                                </div>
                            </div>
                            <div class="map-stat-item">
                                <!-- <div class="map-stat-icon">
                                        <img src="@/assets/equipment/map/weixiu.png" alt="维修设备" />
                                    </div> -->
                                <div class="map-stat-content">
                                    <div class="map-stat-label">在用设备</div>
                                    <div class="map-stat-value">{{ statsData[4].value }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 中国地图 -->
                    <div id="main" class="map-chart"
                        style="width: 100%; height: 100vh; position: absolute; top: 0; left: 0px; z-index: 1;"
                        v-if="!isWorldMap && !isShowMap"></div>
                    <div id="world-map" class="map-chart"
                        style="width: 100%; height: 100vh; position: absolute; top: -80px; left: -30px; z-index: 1;"
                        v-if="isWorldMap && !isShowMap"></div>
                    <div id="amap-container"
                        style="width: 100%; height: 100vh; position: absolute; top: 0; left: 0px; z-index: 1;"
                        v-if="isShowMap"></div>



                    <!-- 新增国、省、市切换按钮 -->
                    <div class="map-level-buttons" v-show="!isWorldMap">
                        <div class="map-level-button" :class="{ 'active': currentLevel === 'china' }"
                            @click="switchToCountry">国
                        </div>
                        <div class="map-level-button" :class="{ 'active': currentLevel === 'province' }"
                            @click="switchToProvince">省
                        </div>
                        <!-- <div class="map-level-button" :class="{ 'active': currentLevel === 'city' }"
                            @click="switchToCity">市</div> -->
                    </div>
                </div>

                <!-- 右侧悬浮面板 -->
                <div class="right-panel" :class="{ 'panel-hidden': rightPanelHidden }">

                    <div class="panel-content">
                        <!-- 设备成本总览 -->
                        <div class="device-cost-card">
                            <div class="card-header">
                                <span class="title__eltit">
                                    使用费用统计
                                </span>

                            </div>
                            <div class="card-content">
                                <div class="device-status-grid">
                                    <!-- 成本概览 -->
                                    <div class="cost-overview">
                                        <!-- 购买成本 -->
                                        <div class="cost-item">
                                            <div class="cost-icon">
                                                <img src="@/assets/equipment/map/youshang1.svg" alt="购买成本" />
                                            </div>
                                            <div class="cost-info">
                                                <div class="cost-label">租赁费</div>
                                                <div class="cost-value">
                                                    <span class="value-number">8600</span>
                                                    <span class="value-unit">(台)</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 维护成本 -->
                                        <div class="cost-item">
                                            <div class="cost-icon">
                                                <img src="@/assets/equipment/map/youshang2.svg" alt="维护成本" />
                                            </div>
                                            <div class="cost-info">
                                                <div class="cost-label">维修费用</div>
                                                <div class="cost-value">
                                                    <span class="value-number">2.86</span>
                                                    <span class="value-unit">(万)</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 分隔线 -->
                                    <div class="divider-dashed"></div>

                                    <!-- 成本明细 -->
                                    <div class="cost-details">
                                        <!-- 左侧明细 -->
                                        <div class="detail-column">
                                            <div class="detail-item">
                                                <div class="detail-label">价格</div>
                                                <div class="detail-bar-container">
                                                    <div class="detail-bar" style="width: 62%"></div>
                                                </div>
                                                <div class="detail-percentage">62%</div>
                                            </div>
                                            <div class="detail-item">
                                                <div class="detail-label">税费</div>
                                                <div class="detail-bar-container">
                                                    <div class="detail-bar" style="width: 43%"></div>
                                                </div>
                                                <div class="detail-percentage">43%</div>
                                            </div>
                                            <div class="detail-item">
                                                <div class="detail-label">运输费</div>
                                                <div class="detail-bar-container">
                                                    <div class="detail-bar" style="width: 37%"></div>
                                                </div>
                                                <div class="detail-percentage">37%</div>
                                            </div>
                                            <div class="detail-item">
                                                <div class="detail-label">安装费</div>
                                                <div class="detail-bar-container">
                                                    <div class="detail-bar" style="width: 22%"></div>
                                                </div>
                                                <div class="detail-percentage">22%</div>
                                            </div>
                                        </div>

                                        <!-- 右侧明细 -->
                                        <div class="detail-column">
                                            <div class="detail-item">
                                                <div class="detail-label">人力</div>
                                                <div class="detail-bar-container">
                                                    <div class="detail-bar" style="width: 62%"></div>
                                                </div>
                                                <div class="detail-percentage">62%</div>
                                            </div>
                                            <div class="detail-item">
                                                <div class="detail-label">物料</div>
                                                <div class="detail-bar-container">
                                                    <div class="detail-bar" style="width: 28%"></div>
                                                </div>
                                                <div class="detail-percentage">28%</div>
                                            </div>
                                            <div class="detail-item">
                                                <div class="detail-label">其他</div>
                                                <div class="detail-bar-container">
                                                    <div class="detail-bar" style="width: 14%"></div>
                                                </div>
                                                <div class="detail-percentage">14%</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 设备资产概况 -->
                        <div class="device-asset-card">
                            <div>
                                <div class="card-header">
                                    <span class="title__eltit">
                                        设备资产概况
                                    </span>

                                </div>
                            </div>
                            <div class="card-content">
                                <div class="device-status-grid">
                                    <div class="asset-overview">
                                        <!-- 左侧数据 -->
                                        <div class="asset-left">
                                            <div class="asset-item top">
                                                <div class="asset-label">设备总资产</div>
                                                <div class="asset-value blue">5680<span class="unit">万</span></div>
                                            </div>

                                            <div class="asset-item bottom">
                                                <div class="asset-label">设备折旧额</div>
                                                <div class="asset-value cyan">230<span class="unit">万</span></div>
                                            </div>
                                        </div>

                                        <!-- 中央区域 -->
                                        <div class="asset-center">
                                            <!-- 中央图形 -->
                                            <div class="center-graphic">
                                                <!-- 外圈动画 -->
                                                <div class="circle-outer"></div>
                                                <!-- 内圈动画 -->
                                                <div class="circle-inner"></div>
                                                <!-- 中央图片 -->
                                                <img src="@/assets/equipment/map/youzhong.png" alt="中央图标"
                                                    class="center-image" />
                                            </div>
                                        </div>

                                        <!-- 右侧数据 -->
                                        <div class="asset-right">
                                            <div class="asset-item top">
                                                <div class="asset-label">设备残值</div>
                                                <div class="asset-value cyan">5200<span class="unit">万</span></div>
                                            </div>
                                            <div class="asset-item bottom">
                                                <div class="asset-label">平均使用寿命</div>
                                                <div class="asset-value blue">18.6<span class="unit">年</span></div>
                                            </div>
                                        </div>


                                    </div>
                                </div>

                            </div>
                        </div>

                        <!-- 设备故障统计 -->
                        <div class="device-fault-card">
                            <div class="card-header">
                                <div>
                                    <span class="title__eltit">
                                        设备故障统计
                                    </span>

                                </div>

                            </div>
                            <div class="card-content">
                                <div class="device-status-grid">
                                    <div class="fault-overview">
                                        <!-- 总故障次数 -->
                                        <div class="fault-stat-item">
                                            <div class="fault-icon">
                                                <!-- 图片暂缺，预留位置 -->
                                                <img src="@/assets/equipment/map/youxia1.png" alt="联网" />
                                            </div>
                                            <div class="fault-info">
                                                <div class="fault-label">总故障次数</div>
                                                <div class="fault-value">
                                                    <span class="value-number cyan">1134</span>
                                                    <span class="value-unit">(次)</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 故障率 -->
                                        <div class="fault-stat-item">
                                            <div class="fault-icon">
                                                <!-- 图片暂缺，预留位置 -->
                                                <img src="@/assets/equipment/map/youxia2.png" alt="联网" />
                                            </div>
                                            <div class="fault-info">
                                                <div class="fault-label">故障率</div>
                                                <div class="fault-value">
                                                    <span class="value-number cyan">24.23%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                    <!-- 故障类型次数分布 -->
                                    <div class="fault-distribution">
                                        <div class="distribution-title">故障类型次数分布</div>
                                        <div class="distribution-chart">
                                            <img src="@/assets/equipment/map/youxia3.png" alt="故障分布图"
                                                class="fault-distribution-chart" />
                                        </div>

                                        <!-- 故障类型标签 -->
                                        <div class="fault-types">
                                            <div class="fault-type-item">液压系统故障</div>
                                            <div class="fault-type-item">电气系统故障</div>
                                            <div class="fault-type-item">机械系统故障</div>
                                            <div class="fault-type-item">过滤系统故障</div>
                                            <div class="fault-type-item">传输系统故障</div>
                                            <div class="fault-type-item">其他故障</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="wer" class="wer">
                <div class="titles">
                    {{ titles }}
                </div>
                <div style="display: flex;justify-content: space-between;">
                    <div class="main cursor-pointer" @click="openOneLevelEqu(1, oneLevelEquNum)">
                        <div class="one">{{ oneLevelEquNum }}</div>
                        <div class="two">一级设备</div>
                    </div>
                      <div class="equipment-divider"></div>
                    <div class="main cursor-pointer" @click="openOneLevelEqu(2, twoLevelEquNum)">
                        <div class="one">{{ twoLevelEquNum }}</div>
                        <div class="two">二级设备</div>
                    </div>
                </div>

            </div>

            <div id="cluster-detail-panel" ref="clusterDetailPanel" v-show="showClusterPanel"
                class="cluster-detail-panel"
                :style="{ left: clusterPanelPosition.x + 'px', top: clusterPanelPosition.y + 'px' }">

                <div class="cluster-content">
                    <div v-for="(point, index) in clusterPointsData" :key="index" class="cluster-item"
                        @mouseover="handleClusterItemHover($event, true)"
                        @mouseout="handleClusterItemHover($event, false)">
                        <div class="cluster-name" style="text-align: left;">
                            {{ point.orgShortName || point.orgName || '未命名点位' }}
                        </div>
                        <div class="cluster-equipment">
                            <div class="equipment-item"
                                @click="handleEquipmentClick(point.orgId, point.orgShortName || point.orgName, 1, point.oneLevelEquNum)"
                                @mouseover="handleEquipmentHover($event, true)"
                                @mouseout="handleEquipmentHover($event, false)">
                                一级设备 <span class="equipment-count">{{ point.oneLevelEquNum || 0 }}</span>
                            </div>
                            <div class="equipment-divider1"></div>
                            <div class="equipment-item"
                                @click="handleEquipmentClick(point.orgId, point.orgShortName || point.orgName, 2, point.twoLevelEquNum)"
                                @mouseover="handleEquipmentHover($event, true)"
                                @mouseout="handleEquipmentHover($event, false)">
                                二级设备 <span class="equipment-count">{{ point.twoLevelEquNum || 0 }}</span>
                            </div>
                            <div class="equipment-divider1"></div>
                        </div>
                    </div>
                </div>
            </div>

            <a-modal v-model:visible="equipmentModalVisible" :title="equipmentModalTitle" width="90%" :footer="null"
                :destroyOnClose="true" class="equipment-modal">

                <a-spin :spinning="equipmentLoading">
                    <a-table :dataSource="equipmentList" :pagination="false" :scroll="{ y: '500px' }"
                        class="equipment-table">
                        <a-table-column key="index" title="序号" width="60px" align="center">
                            <template #default="{ index }">
                                {{ index + 1 }}
                            </template>
                        </a-table-column>
                        <a-table-column key="equipmentNo" title="设备编号" dataIndex="code" width="120px" align="center" />
                        <a-table-column key="propertyUnit" title="产权单位" dataIndex="propertyOrgStr" width="120px"
                            align="center" />
                        <a-table-column key="equipmentCategory" title="设备类别" dataIndex="equTypeStr" width="150px"
                            align="center" />
                        <a-table-column key="equipmentName" title="设备名称" dataIndex="equNameStr" width="120px"
                            align="center" />
                        <a-table-column key="specificationModel" title="规格型号" dataIndex="equModelStr" width="120px"
                            align="center" />
                        <a-table-column key="modelRemarks" title="型号备注" dataIndex="equModelInfo" width="120px"
                            align="center" />
                        <a-table-column key="purchaseYear" title="购置年度" dataIndex="purchaseYear" width="120px"
                            align="center" />
                        <a-table-column key="equipmentProperty" title="设备性质" dataIndex="equNatureStr" width="120px"
                            align="center" />
                        <a-table-column key="power" title="功率kw" dataIndex="power" width="120px" align="center" />
                        <a-table-column key="action" title="操作" width="100px" align="center">
                            <template #default="{ record }">
                                <a class="view-link" @click="viewEquipmentDetail(record)">查看</a>
                            </template>
                        </a-table-column>
                    </a-table>
                </a-spin>

                <detail-modal-color-ful v-model:visible="showDetail" :data="current" width="90%" title="在籍设备卡片信息"
                    @close="handleDrawerClose" class="detail-modal" />
            </a-modal>
        </div>

    </div>
</template>
<style>
.wer {
    position: absolute;
    display: none;
    width: 200px;
    height: 8.375rem;
    padding: 10px;
    z-index: 1000;
    /* background-color: #fff; */
    background-image: url('@/assets/equipment/map/mapblock.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.titles {
    width: 100%;
    font-size: 17px;
    font-weight: 500;
    letter-spacing: 0.04em;
    /* background-color: #011B62; */
}

.cursor-pointer {
    cursor: pointer;
}

.main {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    padding-top: 20px;
}

.one {
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 0em;
}

.two {
    font-size: 14px;
    font-weight: 300;
    letter-spacing: 0em;
    padding-top: 10px;
}

.equipment-modal .ant-modal-content {
    background-color: #0A1E42;
    background-image: url('@/assets/equipment/map/modal-bg.png');
    background-repeat: no-repeat;
    background-size: cover;
    border: 1px solid #1E3A6E;
    border-radius: 4px;
}

.equipment-modal .ant-modal-header {
    background-color: transparent;
    border-bottom: 1px solid #1E3A6E;
    padding: 16px 24px;
    position: relative;
}

.equipment-modal .ant-modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #0085FF, #00C6FF);
}

.equipment-modal .ant-modal-title {
    color: #fff;
    font-size: 18px;
    font-weight: 500;
}

.equipment-modal .ant-modal-close {
    color: #fff;
}

.equipment-modal .ant-modal-close:hover {
    color: #4B9EFF;
}

.equipment-modal .ant-modal-body {
    padding: 0;
    background-color: transparent;
}

.equipment-modal .equipment-table {
    background-color: transparent;
    padding: 0 16px 16px;
}

.equipment-modal .equipment-table .ant-table {
    background-color: transparent;
    color: #fff;
}

.equipment-modal .equipment-table .ant-table-thead>tr>th {
    background-color: rgba(20, 41, 108, 0.56) !important;
    color: #fff;
    border-bottom: 1px solid rgba(30, 58, 110, 0.5);
    padding: 12px 8px;
    font-weight: normal;
}

.equipment-modal .equipment-table .ant-table-tbody>tr>td {
    border-bottom: 1px solid rgba(255, 255, 255, 0.15) !important;
    color: #fff;
    padding: 12px 8px;
    background-color: transparent !important;
}

.equipment-modal .equipment-table .ant-table-tbody>tr:hover>td {
    background-color: rgba(30, 58, 110, 0.5) !important;
}

.equipment-modal .equipment-table .ant-table-tbody>tr:nth-child(odd)>td {
    background-color: rgba(13, 36, 86, 0.3);
}

.equipment-modal .equipment-table .ant-table-tbody>tr:nth-child(even)>td {
    background-color: transparent;
}

.equipment-modal .equipment-table .view-link {
    color: #4B9EFF;
}

.equipment-modal .equipment-table .view-link:hover {
    color: #7BB9FF;
}

.equipment-modal .equipment-table .ant-table-body::-webkit-scrollbar {
    width: 8px;
    background-color: #0D2456;
}

.equipment-modal .equipment-table .ant-table-body::-webkit-scrollbar-thumb {
    background-color: #1E3A6E;
    border-radius: 4px;
}

.equipment-modal .equipment-table .ant-table-body::-webkit-scrollbar-track {
    background-color: #0D2456;
}

.equipment-modal .equipment-table .ant-empty-description {
    color: #fff;
}

.equipment-modal .equipment-table .ant-spin-dot-item {
    background-color: #4B9EFF;
}

.equipment-modal .equipment-table .ant-spin-text {
    color: #4B9EFF;
}

/* 自定义抽屉动画效果 */
:deep(.ant-drawer) {
    transition: transform 0.3s ease-in-out;
}

:deep(.ant-drawer.ant-drawer-open) {
    transform: translateX(0);
}

:deep(.ant-drawer:not(.ant-drawer-open)) {
    transform: translateX(100%);
}

.ant-table-body {
    height: calc(100vh - 326px) !important;
}

.detail-modal .ant-modal-body .ant-modal-header {
    background-color: rgba(20, 30, 71, 0.89) !important;
}

.cluster-detail-panel {
    position: absolute;
    width: 220px;
    max-height: 460px;
    padding: 10px;
    z-index: 1000;
    background-image: url('@/assets/equipment/map/mapblock.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    color: white;
    border-radius: 4px;
    overflow-y: auto;

    .cluster-content {
        height: 270px;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: #0C5CC2 transparent;
    }

    .cluster-item {
        padding: 8px;
        cursor: pointer;
        border-bottom: 1px dashed rgba(255, 255, 255, 0.2);

        .cluster-name {
            font-weight: 500;
            margin-bottom: 6px;
            font-size: 16px;
            color: #FFFFFF;
            text-align: center;
        }

        .cluster-equipment {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .equipment-item {
                cursor: pointer;
                color: #CFD4E2;
                font-size: 13px;
                display: flex;
                align-items: center;
                flex: 1;
                justify-content: center;

                .equipment-count {
                    color: #00CFFF;
                    font-weight: bold;
                    margin-left: 2px;
                }
            }

            .equipment-divider1 {
                width: 1px;
                height: 16px;
                background: rgba(255, 255, 255, 0.3);
                margin: 0 6px;
            }
        }
    }

}
.equipment-divider {
    width: 0.0625rem;
    height: 3rem;
    background: rgba(255, 255, 255, 0.3);
    margin: 21px 0.375rem;
}
</style>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch, reactive, computed, createVNode } from 'vue';
import * as echarts from 'echarts/core';
import { PieChart } from 'echarts/charts';
import {
    TitleComponent,
    TooltipComponent,
    LegendComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { RightOutlined, LeftOutlined, CodeOutlined, BellOutlined, FullscreenOutlined, FullscreenExitOutlined, LogoutOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import backgroundImage from '@/assets/equipment/map/mapback.png';
// import 'echarts-gl';
import AMapLoader from '@amap/amap-jsapi-loader';
import { AnalysisApi } from '@/api/analysis';
import { useUserStore } from '@/store/modules/user';
import { message } from 'ant-design-vue';
import { logout } from '@/utils/page-tab-util';
import biaozhuImg from '@/assets/equipment/map/zhadian55555.svg';
import biaozhuxuanImg from '@/assets/equipment/map/biaozhuxuan.png';
// import DetailModal from '@/components/DetailModalColorFul/index.vue';
import HeaderNotice from '@/layout/components/header-notice.vue';
import { useI18n } from 'vue-i18n';
import { LoginApi } from '@/api/login/LoginApi';
import { BASE_URL, SESSION_KEY_MENU_TYPE, SSO_FLAG, GUNS_DEVOPS_URL } from '@/config/setting';
import { SsoUtil } from '@/utils/sso-util';
import { removeToken } from '@/utils/token-util';
import { initRem } from '../utils/rem';

// 假设设计稿宽度为1920px
initRem(1920);

const { t } = useI18n();
// 注册必要的组件
echarts.use([
    PieChart,
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    CanvasRenderer
]);

import { Modal } from 'ant-design-vue';

// 设备列表数据
const equipmentList = ref([]);
const equipmentModalVisible = ref(false);
const equipmentModalTitle = ref('');
const equipmentLoading = ref(false);
const currentOrgName = ref('');
//获取所有城市
import { EnumApi } from '@/api/common/enum';
const cityOptions = ref([])
const address = ref()
const initEnumData = async () => {
    let address = await EnumApi.getAddressTree('')
    cityOptions.value = address.data[0].children
}

const cascaderChange = (value, selectedOptions) => {
    console.log('value', value);
    loadMap(value[value.length - 1], 'province', '100000', '中国')
    console.log('selectedOptions', selectedOptions);
}

// 打开设备列表模态框
const openOneLevelEqu = async (level, amount) => {
    // 如果设备数量为0，则不打开模态框
    if (amount == 0) {
        message.info(`没有${level === 1 ? '一' : '二'}级设备数据`);
        return;
    }

    equipmentLoading.value = true;
    equipmentModalVisible.value = true;

    // 设置标题
    currentOrgName.value = titles.value || '二十九处风凰山';
    equipmentModalTitle.value = `${currentOrgName.value}项目部在籍${level === 1 ? '一' : '二'}级设备台账`;

    try {
        // 根据level参数获取不同级别的设备
        const params = {
            useOrg: currentOrgId.value,
            equNature: level, // 1表示一级设备，2表示二级设备
            pageNo: 1,
            pageSize: 100000
        };

        // 调用API获取设备列表
        const response = await AnalysisApi.getEquipmentListByLevel(params);

        if (response.data?.rows?.length > 0) {
            equipmentList.value = response.data.rows;
        } else {
            equipmentList.value = [];
            message.info(`没有查询到${level === 1 ? '一' : '二'}级设备数据`);
        }
    } catch (error) {
        console.error('获取设备列表失败:', error);
        message.error('获取设备列表失败');
    } finally {
        equipmentLoading.value = false;
    }
};

// 查看设备详情
const viewEquipmentDetail = (record) => {
    if (record && record.id) {
        // 使用 AnalysisApi 获取设备详情
        AnalysisApi.getEquipmentFullInfo({ id: record.id }).then(res => {
            res.data.type = 'reg';
            current.value = res.data;
            current.value.type = 'reg';
            showDetail.value = true;
        }).catch(error => {
            console.error('获取设备详情失败:', error);
            message.error('获取设备详情失败');
        });
    } else {
        message.error('设备ID不存在，无法查看详情');
    }
};

// 添加关闭模态框的处理函数
const handleModalClose = () => {
    showDetail.value = false;
    current.value = null;
};

// 添加必要的响应式变量
const showDetail = ref(false);
const current = ref(null);

const titles = ref()

const gotoBack = () => {
    window.location.href = '/buss/regEquLedger';
};
const userStore = useUserStore();
// 当前用户信息
const loginUser = computed(() => userStore.info ?? {});
// 设备调拨数据
// 设备调拨数据
// 地图全屏状态
const mapFullscreen = ref(false);

const oneLevelEquNum = ref(0);
const twoLevelEquNum = ref(0);
const currentOrgId = ref('')

// 切换地图全屏模式
const toggleFullscreenMap = () => {
    mapFullscreen.value = !mapFullscreen.value;

    // 隐藏或显示左右面板
    // leftPanelHidden.value = mapFullscreen.value;
    // rightPanelHidden.value = mapFullscreen.value;

    toggleLeftPanel();
    toggleRightPanel();

    // 调整地图大小
    setTimeout(() => {
        if (chinaMapChart.value) {
            chinaMapChart.value.resize();
        }

        // 如果高德地图正在显示，也需要调整其大小
        if (isShowMap.value && map) {
            map.resize();
        }
    }, 300); // 等待面板动画完成
};
const allocationData = ref([]);
// 饼图引用
const allocationChartRef = ref(null);
let allocationChart = null;

// 初始化设备调拨图表
const initAllocationChart = (allocationData) => {
    console.log(allocationData.value)
    if (!allocationChartRef.value) return;

    // 如果已经存在图表实例，先销毁
    if (allocationChart) {
        allocationChart.dispose();
    }

    // 初始化图表
    allocationChart = echarts.init(allocationChartRef.value);

    // 配置项
    const option = {
        grid: {
            left: '5%',
            right: '5%',
            bottom: '1%',
            top: '25%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: allocationData.value.map(item => item.label),
            axisLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.5)'
                }
            },
            axisLabel: {
                color: 'rgba(255, 255, 255, 0.8)',
                fontSize: 12
            },
            axisTick: {
                show: false
            },
            name: '',
            nameTextStyle: {
                color: 'rgba(255, 255, 255, 0.8)',
                fontSize: 12,
                align: 'right'
            }
        },
        yAxis: {
            type: 'value',
            axisLine: {
                show: true,
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.5)'
                }
            },
            splitLine: {
                show: false
            },
            axisLabel: {
                color: 'rgba(255, 255, 255, 0.8)',
                fontSize: 12
            },
            // name: '台',
            // nameTextStyle: {
            //     color: 'rgba(255, 255, 255, 0.8)',
            //     fontSize: 12
            // }
        },
        series: [
            {
                name: '设备调拨次数',
                type: 'bar',
                barWidth: '20%',
                data: allocationData.value.map(item => item.value),
                itemStyle: {
                    // borderRadius: [50, 50, 0, 0], // 左上、右上、右下、左下
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#00BFFF' },
                        { offset: 1, color: '#0072FF' }
                    ])
                },
                emphasis: {
                    itemStyle: {

                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#00F8FC' },
                            { offset: 1, color: '#0084FE' }
                        ])
                    }
                }
            }
        ],
        tooltip: {
            trigger: 'item',
            backgroundColor: '#193170',
            formatter: '{b}: {c}台',
            extraCssText: 'height: 30px;line-height:10px',
            textStyle: {
                color: '#fff',
                height: 10
            }
            // formatter: function (params) {
            //   console.log('params',params)
            //   return `<div style="background-color: #193170; width:100%; height:100%">
            //     <span>${params.name}:${params.data}台
            //   </div>`;
            // },
        }
    };

    // 设置配置项
    allocationChart.setOption(option);
};


// 在组件卸载前销毁图表
onUnmounted(() => {
    if (allocationChart) {
        allocationChart.dispose();
        allocationChart = null;
    }
});

// 监听窗口大小变化，重新调整图表大小
window.addEventListener('resize', () => {
    if (allocationChart) {
        allocationChart.resize();
    }
});



// Panel visibility states
const leftPanelHidden = ref(false);
const rightPanelHidden = ref(false);
const activeTab = ref('home');
const userAvatar = ref('https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png');

// 统计数据
const statsData = ref([
    { value: 0 },
    { value: 0 },
    { value: 0 },
    { value: 0 },
    { value: 0 },
    { value: 0 }
]);



// 加载状态
const loading = ref(false);

// Toggle panel visibility
const toggleLeftPanel = () => {
    leftPanelHidden.value = !leftPanelHidden.value;
    // Resize map after panel toggle animation completes
    setTimeout(() => {
        if (chinaMapChart.value) {
            chinaMapChart.value.resize();
        }
    }, 300);
};

const toggleRightPanel = () => {
    rightPanelHidden.value = !rightPanelHidden.value;
    // Resize map after panel toggle animation completes
    setTimeout(() => {
        if (chinaMapChart.value) {
            chinaMapChart.value.resize();
        }
    }, 300);
};


// 省份数据
// 省份数据
const provinceData = ref([
    {
        name: '北京市',
        shortName: '北京',
        value: 45,
        code: '110000',
        coordinate: [116.405285, 39.904989]
    },
    {
        name: '天津市',
        shortName: '天津',
        value: 38,
        code: '120000',
        coordinate: [117.190182, 39.125596]
    },
    {
        name: '河北省',
        shortName: '河北',
        value: 27,
        code: '130000',
        coordinate: [114.502461, 38.045474]
    },
    {
        name: '山西省',
        shortName: '山西',
        value: 29,
        code: '140000',
        coordinate: [112.549248, 37.857014]
    },
    {
        name: '内蒙古自治区',
        shortName: '内蒙古',
        value: 25,
        code: '150000',
        coordinate: [111.670801, 40.818311]
    },
    {
        name: '辽宁省',
        shortName: '辽宁',
        value: 31,
        code: '210000',
        coordinate: [123.429096, 41.796767]
    },
    {
        name: '吉林省',
        shortName: '吉林',
        value: 22,
        code: '220000',
        coordinate: [125.3245, 43.886841]
    },
    {
        name: '黑龙江省',
        shortName: '黑龙江',
        value: 24,
        code: '230000',
        coordinate: [126.642464, 45.756967]
    },
    {
        name: '上海市',
        shortName: '上海',
        value: 50,
        code: '310000',
        coordinate: [121.472644, 31.231706]
    },
    {
        name: '江苏省',
        shortName: '江苏',
        value: 42,
        code: '320000',
        coordinate: [118.767413, 32.041544]
    },
    {
        name: '浙江省',
        shortName: '浙江',
        value: 46,
        code: '330000',
        coordinate: [120.153576, 30.287459]
    },
    {
        name: '安徽省',
        shortName: '安徽',
        value: 33,
        code: '340000',
        coordinate: [117.283042, 31.86119]
    },
    {
        name: '福建省',
        shortName: '福建',
        value: 35,
        code: '350000',
        coordinate: [119.306239, 26.075302]
    },
    {
        name: '江西省',
        shortName: '江西',
        value: 28,
        code: '360000',
        coordinate: [115.892151, 28.676493]
    },
    {
        name: '山东省',
        shortName: '山东',
        value: 39,
        code: '370000',
        coordinate: [117.000923, 36.675807]
    },
    {
        name: '河南省',
        shortName: '河南',
        value: 34,
        code: '410000',
        coordinate: [113.665412, 34.757975]
    },
    {
        name: '湖北省',
        shortName: '湖北',
        value: 36,
        code: '420000',
        coordinate: [114.298572, 30.584355]
    },
    {
        name: '湖南省',
        shortName: '湖南',
        value: 32,
        code: '430000',
        coordinate: [112.982279, 28.19409]
    },
    {
        name: '广东省',
        shortName: '广东',
        value: 48,
        code: '440000',
        coordinate: [113.280637, 23.125178]
    },
    {
        name: '广西壮族自治区',
        shortName: '广西',
        value: 26,
        code: '450000',
        coordinate: [108.320004, 22.82402]
    },
    {
        name: '海南省',
        shortName: '海南',
        value: 30,
        code: '460000',
        coordinate: [110.33119, 20.031971]
    },
    {
        name: '重庆市',
        shortName: '重庆',
        value: 37,
        code: '500000',
        coordinate: [106.504962, 29.533155]
    },
    {
        name: '四川省',
        shortName: '四川',
        value: 32,
        code: '510000',
        coordinate: [104.065735, 30.659462]
    },
    {
        name: '贵州省',
        shortName: '贵州',
        value: 23,
        code: '520000',
        coordinate: [106.713478, 26.578343]
    },
    {
        name: '云南省',
        shortName: '云南',
        value: 27,
        code: '530000',
        coordinate: [102.712251, 25.040609]
    },
    {
        name: '西藏自治区',
        shortName: '西藏',
        value: 13,
        code: '540000',
        coordinate: [91.132212, 29.660361]
    },
    {
        name: '陕西省',
        shortName: '陕西',
        value: 31,
        code: '610000',
        coordinate: [108.948024, 34.263161]
    },
    {
        name: '甘肃省',
        shortName: '甘肃',
        value: 21,
        code: '620000',
        coordinate: [103.823557, 36.058039]
    },
    {
        name: '青海省',
        shortName: '青海',
        value: 18,
        code: '630000',
        coordinate: [101.778916, 36.623178]
    },
    {
        name: '宁夏回族自治区',
        shortName: '宁夏',
        value: 20,
        code: '640000',
        coordinate: [106.278179, 38.46637]
    },
    {
        name: '新疆维吾尔自治区',
        shortName: '新疆',
        value: 29,
        code: '650000',
        coordinate: [87.617733, 43.792818]
    },
    {
        name: '台湾省',
        shortName: '台湾',
        value: 32,
        code: '710000',
        coordinate: [121.509062, 25.044332]
    },
    {
        name: '香港特别行政区',
        shortName: '香港',
        value: 40,
        code: '810000',
        coordinate: [114.173355, 22.320048]
    },
    {
        name: '澳门特别行政区',
        shortName: '澳门',
        value: 38,
        code: '820000',
        coordinate: [113.54909, 22.198951]
    }
]);


let mapChart = null;

// 散点数据
const scatterData = [

];
// 在 script setup 部分添加以下变量和函数
const currentMapLevel = ref('province'); // 当前地图级别：china/province/city
// 三级下钻/上钻动态拉取阿里云行政区划
const ZOOM_MAX = 6;
const ZOOM_MIN = 1;
const currentLevel = ref('china'); // 'china' | 'province' | 'city'
const currentCode = ref('100000');
const currentName = ref('中国');
const parentCode = ref('');
const parentName = ref('');

// 获取下一级行政区划（省/市）
async function getSubAreas(code) {
    try {
        console.log(`获取区域 ${code} 的下级区域`);
        const res = await fetch(`https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=${code}_full`);
        if (!res.ok) {
            throw new Error(`获取下级区域失败: ${res.status}`);
        }
        const data = await res.json();

        // 确保返回的数据有features属性
        if (!data.features) {
            console.error('获取下级区域数据格式错误:', data);
            return [];
        }

        return data.features.map(f => ({
            name: f.properties.name,
            adcode: String(f.properties.adcode),
            parent: f.properties.parent && String(f.properties.parent.adcode)
        }));
    } catch (error) {
        console.error('获取下级区域失败:', error);
        return [];
    }
}

function getImage(img) {
    return new Promise(resolve => {
        const image = new Image()
        image.src = img
        image.onload = () => {
            const canvas = document.createElement(`canvas`)
            canvas.width = image.width
            canvas.height = image.height
            const ctx = canvas.getContext(`2d`)
            const x = canvas.width / 2
            const y = canvas.height / 2
            // 将绘图原点移到画布中心
            ctx.translate(x, y)
            // 旋转角度
            ctx.rotate((Math.PI / 180) * (Math.floor(Math.random() * 6) * 10))
            // 将画布原点移动
            ctx.translate(-x, -y)
            // 绘制图片
            ctx.drawImage(image, 0, 0, image.width, image.height)
            const ext = image.src.substring(image.src.lastIndexOf(`.`) + 1).toLowerCase()
            const dataURL = canvas.toDataURL(`image/` + ext)
            resolve(`image://` + dataURL)
        }
    })
}

var isShowMap = ref(false);

// 加载地图（中国/省/市），统一用adcode做地图名
async function loadMap(adcode, level, parentAdcode, parentNameVal) {
    // 获取当前 option
    // var option1 = mapChart.getOption();
    // 获取图片 data URL
    const imageDataUrlWithPrefix = await getImage(backgroundImage); // 调用你的getImage函数
    const imageDataUrl = imageDataUrlWithPrefix.replace('image://', ''); // 去掉前缀

    // 定义图片背景样式
    const imagePattern = {
        image: imageDataUrl, // 使用去掉前缀的 data URL
        repeat: 'repeat' // 或者 'no-repeat', 'repeat-x', 'repeat-y'
    };
    // 移除所有 effectScatter 系列
    // option1.series = (option1.series || []).filter(s => s.type !== 'effectScatter');

    const res = await fetch(`https://geo.datav.aliyun.com/areas_v3/bound/${adcode}_full.json`);
    const mapData = await res.json();
    echarts.registerMap(String(adcode), mapData);
    currentMapData = mapData.features.map(item => item.properties).filter(item => item.name !== '南海诸岛');
    // 动态适配中心点和缩放
    let option = {};
    let layoutCenter, layoutSize, center = undefined;
    if (level === 'china') {
        layoutCenter = ['52%', '64%'];
        layoutSize = '90%';
        option = {
            tooltip: { show: true, formatter: '{b}' },
            geo: [
                {
                    map: String(adcode),
                    roam: false, // 改为true，允许缩放和拖拽
                    animationDurationUpdate: 0,
                    zoom: 1.25, // 使用最小缩放阈值作为初始值
                    zlevel: 10,
                    aspectScale: 0.78,
                    layoutCenter: ['52%', '64%'],
                    layoutSize: '90%',
                    center: [107.5, 30], // 设置中国地图的中心点坐标
                    itemStyle: {
                        normal: {
                            borderColor: 'rgba(147, 235, 248, 0.6)',
                            borderWidth: 0.8,
                            areaColor: imagePattern,
                        },
                        emphasis: {
                            areaColor: '#176DF4',
                            label: {
                                show: false,
                                color: '#176DF4', // 高亮状态也指定为蓝色
                                fontSize: 16,
                                // ...其他
                            }
                        }
                    },
                    emphasis: {
                        areaColor: '#00F8FC', label: {
                            show: true,
                            color: '#fff', // 高亮状态也指定为蓝色
                            fontSize: 9,
                            // ...其他
                        }
                    },
                    label: {
                        show: true,
                        color: 'rgba(255, 255, 255, 0.8)',
                        fontSize: 9,
                        fontWeight: 'bold',
                        position: 'top',
                        formatter: function (params) {
                            const specialProvinces = ['甘肃省', '安徽省', '澳门特别行政区', '香港特别行政区'];
                      
                           
                            // 特殊省份不显示，由散点图层显示
                            return specialProvinces.includes(params.name) ? '' : params.name;
                        }
                    }
                },
                {
                    map: String(adcode),
                    roam: false,
                    animationDurationUpdate: 0,
                    zoom: 1.25, // 使用最小缩放阈值作为初始值
                    aspectScale: 0.78,
                    layoutCenter: ['52%', '64.5%'],
                    layoutSize: '90%',
                    zlevel: 1,
                    silent: true,
                    center: [107.5, 30], // 设置中国地图的中心点坐标
                    progressive: 1000,  // 渐进渲染
                    progressiveThreshold: 800,
                    itemStyle: {
                        areaColor: 'rgba(0, 207, 255, 0.12)', // 阴影色
                        borderColor: 'rgba(0, 248, 252, 0.38)',
                        borderWidth: 7,
                        shadowColor: 'rgba(0,207,255,0.18)',
                        shadowBlur: 40
                    },
                    label: {
                        show: false,
                        color: 'rgba(255, 255, 255, 0.87)',
                        fontSize: 9,
                        fontWeight: 'bold',
                        textBorderColor: '#0B1A4A',
                        textBorderWidth: 2,
                        textShadowColor: '#000',
                        textShadowBlur: 4
                    }
                },
            ],
            series: [...createProvinceLabels(mapData)]
        };

        //await initMap("mapjson", "main");
        getProvinceMapData()
    } else {
        layoutCenter = ['50%', '50%'];
        layoutSize = '100%';
        // 自动读取geojson的CP属性作为中心点
        if (mapData.features && mapData.features[0] && mapData.features[0].properties && mapData.features[0].properties.center) {
            center = mapData.features[0].properties.center;
            // 保存省级地图初始中心点
            provinceInitialCenter.value = center;
        } else {
            // 如果没有CP属性，使用默认中心点
            center = [107.5, 30];
        }
        // 计算地图的几何中心点
        function calculateMapCenter(mapData) {
            let minLng = Infinity, maxLng = -Infinity;
            let minLat = Infinity, maxLat = -Infinity;

            // 遍历所有要素的坐标，找到边界
            mapData.features.forEach(feature => {
                if (feature.geometry && feature.geometry.coordinates) {
                    const coords = feature.geometry.coordinates;

                    function processCoords(coordArray) {
                        coordArray.forEach(coord => {
                            if (Array.isArray(coord[0])) {
                                processCoords(coord);
                            } else {
                                const [lng, lat] = coord;
                                minLng = Math.min(minLng, lng);
                                maxLng = Math.max(maxLng, lng);
                                minLat = Math.min(minLat, lat);
                                maxLat = Math.max(maxLat, lat);
                            }
                        });
                    }

                    processCoords(coords);
                }
            });

            // 计算几何中心
            const centerLng = (minLng + maxLng) / 2;
            const centerLat = (minLat + maxLat) / 2;

            return [centerLng, centerLat];
        }

        console.log('地图中心点:', mapData.features[0].properties);
        // 优先使用计算的几何中心，确保地图居中显示
        center = calculateMapCenter(mapData);
        // 保存省级地图初始中心点
        provinceInitialCenter.value = center;
        console.log('使用的几何中心点:', center);
        option = {
            tooltip: { show: true, formatter: '{b}' },
            geo: [
                {
                    map: String(adcode),
                    roam: false,
                    animationDurationUpdate: 0,
                    zoom: 0.8,
                    aspectScale: 0.78,
                    layoutCenter,
                    layoutSize,
                    center, // 设置中国地图的中心点坐标
                    itemStyle: {
                        normal: {
                            borderColor: 'rgba(147, 235, 248, 0.6)',
                            borderWidth: 0.8,
                            areaColor: imagePattern,
                        },
                        emphasis: {
                            areaColor: '#176DF4', label: {
                                show: true,
                                color: '#fff', // 高亮状态也指定为蓝色
                                fontSize: 9,
                                // ...其他
                            }
                        },
                    },
                    emphasis: {
                        areaColor: '#00F8FC', label: {
                            show: true,
                            color: '#fff', // 高亮状态也指定为蓝色
                            fontSize: 9,
                            // ...其他
                        }
                    },
                    label: {
                        show: true,
                        color: 'rgba(255, 255, 255, 0.87)',
                        fontSize: 9,
                        fontWeight: 'bold',
                        textBorderColor: '#0B1A4A',
                        textBorderWidth: 2,
                        textShadowColor: '#000',
                        textShadowBlur: 4
                    }
                }
            ],
            series: []
        };

        getCityMapData(adcode)
    }

    // if (level === 'china') {
    //     option.series.push({
    //         type: 'effectScatter',
    //         coordinateSystem: 'geo',
    //         geoIndex: 0,
    //         data: scatterData.map(item => ({
    //             name: item.orgShortName || item.name,
    //             value: [Number(item.longitude), Number(item.latitude)],
    //             label: { show: true, formatter: item.orgShortName || item.name }
    //         })),
    //         symbolSize: 18,
    //         showEffectOn: 'render',
    //         rippleEffect: { brushType: 'stroke', scale: 6 },
    //         itemStyle: { color: '#00cfff', shadowBlur: 20, shadowColor: '#00cfff' },
    //         label: {
    //             show: true,
    //             color: '#00cfff',
    //             position: [-30, -50],
    //             fontSize: 16,
    //             backgroundColor: 'rgba(0,60,255,0.2)',
    //             borderColor: '#00cfff',
    //             borderWidth: 1,
    //             borderRadius: 4,
    //             padding: [4, 8]
    //         },
    //         zlevel: 100
    //     });
    // }

    mapChart.setOption(option, true);
    currentLevel.value = level;
    currentCode.value = String(adcode);
    currentName.value = parentNameVal || '';
    parentCode.value = parentAdcode || '';
    parentName.value = parentNameVal || '';
}

// 事件绑定，全部用adcode做下钻
function bindMapEvents(chart) {
    chart.on('click', async (params) => {
        console.log('点击区域:', params.name);

        try {
            if (currentLevel.value === 'china') {
                // 点击中国地图，下钻到省级
                const subAreas = await getSubAreas('100000');
                console.log('中国下级区域:', subAreas);
                const area = subAreas.find(a => a.name === params.name);
                if (area) {
                    console.log('找到省级区域:', area);
                    await loadMap(area.adcode, 'province', '100000', '中国');
                }
            } else if (currentLevel.value === 'province') {
                // 点击省级地图，下钻到市级
                const subAreas = await getSubAreas(currentCode.value);
                console.log('省级下级区域:', subAreas);
                const area = subAreas.find(a => a.name === params.name);
                if (area) {
                    console.log('找到市级区域:', area);
                    //await loadMap(area.adcode, 'city', currentCode.value, currentName.value);
                    //这里直接进入市级别的地图
                    // 在这里明确设置父级信息
                    parentCode.value = currentCode.value; // 市级代码
                    parentName.value = currentName.value; // 市级名称
                    console.log('设置父级信息 - 代码:', parentCode.value, '名称:', parentName.value);

                    // 隐藏 ECharts 地图
                    if(document.getElementById('main')) {
                        document.getElementById('main').style.display = 'none';
                    }

                    if(document.getElementById('amap-container')) {
                        document.getElementById('amap-container').style.display = 'block';
                    }
                    // document.getElementById('main').style.display = 'none';
                    //  document.getElementById('amap-container').style.display = 'block';
                    isShowMap.value = true;
                    currentLevel.value = 'city'
                    // 初始化高德地图
                    initAMap(area.adcode, area.name);
                }
            } else if (currentLevel.value === 'city') {
                // 点击市级地图，下钻到区县级
                const subAreas = await getSubAreas(currentCode.value);
                console.log('市级下级区域:', subAreas);
                const area = subAreas.find(a => a.name === params.name);
                if (area) {
                    console.log('找到区县级区域:', area);

                    // 在这里明确设置父级信息
                    parentCode.value = currentCode.value; // 市级代码
                    parentName.value = currentName.value; // 市级名称
                    console.log('设置父级信息 - 代码:', parentCode.value, '名称:', parentName.value);

                    // 隐藏 ECharts 地图
                    document.getElementById('main').style.display = 'none';
                    isShowMap.value = true;

                    // 初始化高德地图
                    initAMap(area.adcode, area.name);
                }
            }
        } catch (error) {
            console.error('点击事件处理失败:', error);
        }
    });
    chart.on('georoam', async (params) => {
        // 获取当前视图状态
        const option = chart.getOption();
        const view = option.geo[0];

        // 同步两个图层的视图状态
        for (let i = 0; i < option.geo.length; i++) {
            // 如果是缩放
            if (params.zoom) {
                option.geo[i].zoom = view.zoom;
                // 确保缩放时中心点也同步
                if (view.center) {
                    option.geo[i].center = view.center;
                }
            }
            // 如果是平移
            if (params.dx || params.dy) {
                option.geo[i].center = view.center;
            }
        }

        // 应用更新后的选项，确保两个图层同步移动
        chart.setOption({
            geo: option.geo
        }, false);
    });
}

// 定义缩放阈值
const ZOOM_MAX_THRESHOLD = 1.7; // 超过此值允许拖拽
const ZOOM_MIN_THRESHOLD = 1.2; // 低于此值禁止继续缩小
const ZOOM_DRAG_THRESHOLD = 1.3; // 超过此值允许拖拽（比最小值稍大一点）

const proZOOM_MAX_THRESHOLD = 0.9; // 省级地图最大缩放
const proZOOM_MIN_THRESHOLD = 0.8;  // 省级地图最小缩放
const proZOOM_DRAG_THRESHOLD = 0.82; // 省级地图拖拽阈值（比最小值稍大一点）
// 添加变量存储初始中心点
const initialCenter = ref(null);
const provinceInitialCenter = ref(null); // 新增省级地图初始中心点
const provinceOffsets = {
    '甘肃省': [-25, -55],  // 减小偏移量
    '安徽省': [10, 1],    // 减小偏移量
    '澳门特别行政区': [3, -2], // 减小偏移量
    '香港特别行政区': [6, -10], // 减小偏移量

};
// 在 loadMap 函数中，为每个省份创建单独的 series
const createProvinceLabels = (mapData) => {
    const labelSeries = [];

    mapData.features.forEach(feature => {
        const provinceName = feature.properties.name;
        const center = feature.properties.center || feature.properties.centroid;
        console.log('省份名称:', provinceOffsets[provinceName], '中心点:', center);
        if (provinceOffsets[provinceName] && center && provinceName !== '南海诸岛') {
            const offset = provinceOffsets[provinceName];

            labelSeries.push({
                type: 'scatter',
                coordinateSystem: 'geo',
                geoIndex: 0, // 确保使用第一个geo图层
                data: [{
                    name: provinceName,
                    value: center,
                    label: {
                        show: true,
                        formatter: provinceName,
                        color: 'rgba(255, 255, 255, 0.8)',
                        fontSize: 9,
                        fontWeight: 'bold',
                        offset: offset,
                        backgroundColor: 'transparent'
                    }
                }],
                symbolSize: 0,
                silent: true,
                zlevel: 50,
                animation: false // 禁用动画，减少跳动
            });
        }
    });

    return labelSeries;
};
// 初始化地图
const initMap = async (mapName, domId, adcode = '100000') => {
    try {
        const response = await fetch('https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json');
        const mapJsonData = await response.json();
        echarts.registerMap(mapName, mapJsonData);
        // 获取图片 data URL
        const imageDataUrlWithPrefix = await getImage(backgroundImage); // 调用你的getImage函数
        const imageDataUrl = imageDataUrlWithPrefix.replace('image://', ''); // 去掉前缀

        // 定义图片背景样式
        const imagePattern = {
            image: imageDataUrl, // 使用去掉前缀的 data URL
            repeat: 'repeat' // 或者 'no-repeat', 'repeat-x', 'repeat-y'
        };
        let option = {
            tooltip: { show: true, formatter: '{b}' },
            geo: [
                {
                    map: mapName,
                    //roam: true,
                    roam: false, // 修改为只允许缩放，不允许拖拽
                    animationDurationUpdate: 0,
                    zoom: ZOOM_MIN_THRESHOLD, // 使用最小缩放阈值作为初始值
                    zlevel: 10,
                    aspectScale: 0.78,
                    layoutCenter: ['52%', '64%'],
                    layoutSize: '90%',
                    center: [107.5, 30], // 设置中国地图的中心点坐标
                    itemStyle: {
                        normal: {
                            borderColor: 'rgba(147, 235, 248, 0.6)',
                            borderWidth: 0.8,
                            areaColor: imagePattern,
                        },
                        emphasis: {
                            areaColor: '#176DF4',
                            label: {
                                show: false,
                                color: '#176DF4', // 高亮状态也指定为蓝色
                                fontSize: 16,
                                // ...其他
                            }
                        }
                    },
                    emphasis: {
                        areaColor: '#00F8FC', label: {
                            show: true,
                            color: '#fff', // 高亮状态也指定为蓝色
                            fontSize: 9,
                            // ...其他
                        }
                    },
                    label: {
                        show: true,
                        color: 'rgba(255, 255, 255, 0.8)',
                        fontSize: 9,
                        fontWeight: 'bold',
                        position: 'top',
                        formatter: function (params) {
                            const specialProvinces = ['甘肃省', '澳门特别行政区', '香港特别行政区'];
                            console.log('params.name', params.name);
                            // 特殊省份不显示，由散点图层显示
                            return specialProvinces.includes(params.name) ? '' : params.name;
                        }
                    }
                },
                {
                    map: mapName,
                    roam: false,
                    animationDurationUpdate: 0,
                    zoom: ZOOM_MIN_THRESHOLD, // 使用最小缩放阈值作为初始值
                    aspectScale: 0.78,
                    layoutCenter: ['52%', '64.5%'],
                    layoutSize: '90%',
                    zlevel: 1,
                    silent: true,
                    center: [107.5, 30], // 设置中国地图的中心点坐标
                    progressive: 1000,  // 渐进渲染
                    progressiveThreshold: 800,
                    itemStyle: {
                        areaColor: 'rgba(0, 207, 255, 0.12)', // 阴影色
                        borderColor: 'rgba(0, 248, 252, 0.38)',
                        borderWidth: 7,
                        shadowColor: 'rgba(0,207,255,0.18)',
                        shadowBlur: 40
                    },
                    label: {
                        show: false,
                        color: 'rgba(255, 255, 255, 0.87)',
                        fontSize: 9,
                        fontWeight: 'bold',
                        textBorderColor: '#0B1A4A',
                        textBorderWidth: 2,
                        textShadowColor: '#000',
                        textShadowBlur: 4
                    }
                },
            ],
            series: [...createProvinceLabels(mapJsonData)]
        };
        option.series.push({
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            data: scatterData.map(item => ({
                name: item.orgShortName || item.name,
                value: [Number(item.longitude), Number(item.latitude)],
                label: { show: true, formatter: item.orgShortName || item.name, }
            })),
            symbolSize: 18,
            showEffectOn: 'render',
            rippleEffect: { brushType: 'stroke', scale: 3 },
            itemStyle: { color: '#00cfff', shadowBlur: 20, shadowColor: '#00cfff' },
            label: {
                show: true,
                color: '#00cfff',
                position: [-10, -20],
                fontSize: 16,
                backgroundColor: 'rgba(0,60,255,0.2)',
                borderColor: '#00cfff',
                borderWidth: 1,
                borderRadius: 4,
                padding: [4, 8],

            },
            zlevel: 100
        });
        const chart = echarts.init(document.getElementById(domId));
        chart.setOption(option);
        // 保存初始中心点
        initialCenter.value = option.geo[0].center || null;
        console.log('1111option.geo[0].center', option.geo[0].center);

        //bindMapEvents(chart);
        // 设置当前状态
        if (adcode === '100000') {
            currentLevel.value = 'china';
            currentCode.value = '100000';
            currentName.value = '中国';
            parentCode.value = '';
            parentName.value = '';
        }

        // 添加点击事件，点击时获取下一级地图
        chart.on('click', async (params) => {
            console.log('点击区域:', params.name);

            try {
                // 根据当前层级决定下一步操作
                if (currentLevel.value === 'china') {
                    // 获取省级行政区划
                    const subAreas = await getSubAreas('100000');
                    console.log('中国下级区域:', subAreas);
                    const area = subAreas.find(a => a.name === params.name);
                    if (area) {
                        console.log('找到省级区域:', area);
                        // 加载省级地图
                        //await loadMapByAdcode(chart, area.adcode, 'province', '100000', '中国');
                    } else {
                        console.log('未找到省级区域:', params.name);
                    }
                } else if (currentLevel.value === 'province') {
                    // 获取市级行政区划
                    const subAreas = await getSubAreas(currentCode.value);
                    console.log('省级下级区域:', subAreas);
                    const area = subAreas.find(a => a.name === params.name);
                    if (area) {
                        console.log('找到市级区域:', area);
                        // 加载市级地图
                        //await loadMapByAdcode(chart, area.adcode, 'city', currentCode.value, currentName.value);
                    } else {
                        console.log('未找到市级区域:', params.name);
                    }
                }
            } catch (error) {
                console.error('点击事件处理失败:', error);
            }
        });

        // 添加地图缩放拖拽同步事件
        chart.on('georoam', async (params) => {
          
            const option = chart.getOption();
            const view = option.geo[0];
            const zoom = view.zoom || 1;

            if (params.zoom) {
                console.log('当前缩放级别:', zoom);

                // 根据地图级别设置不同的阈值
                let minThreshold, maxThreshold;
                if (currentLevel.value === 'china') {
                    minThreshold = ZOOM_MIN_THRESHOLD;
                    maxThreshold = ZOOM_MAX_THRESHOLD;
                } else if (currentLevel.value === 'province') {
                    minThreshold = proZOOM_MIN_THRESHOLD;
                    maxThreshold = proZOOM_MAX_THRESHOLD;
                }

                // 防止过度缩小 - 主动重置
                if (zoom < minThreshold) {
                    console.log('缩放级别过小，重置到最小级别');
                    console.log('当前级别:', currentLevel.value);
                    console.log('当前缩放:', zoom);
                    console.log('最小阈值:', minThreshold);
                    console.log('省级初始中心点:', provinceInitialCenter.value);

                    // 主动重置为最小缩放级别
                    for (let i = 0; i < option.geo.length; i++) {
                        option.geo[i].zoom = minThreshold;
                        // 根据当前级别使用对应的初始中心点
                        if (currentLevel.value === 'china' && initialCenter.value) {
                            option.geo[i].center = initialCenter.value;
                        } else if (currentLevel.value === 'province' && provinceInitialCenter.value) {
                            option.geo[i].center = provinceInitialCenter.value;
                        }
                    }
                    // 立即应用重置
                    mapChart.setOption({ geo: option.geo }, false);
                    return;
                }
                // 防止过度放大
                else if (zoom > maxThreshold) {
                    console.log('缩放级别过大，重置到最大级别');
                    for (let i = 0; i < option.geo.length; i++) {
                        option.geo[i].zoom = maxThreshold;
                    }
                    mapChart.setOption({ geo: option.geo }, false);
                    return;
                }
                // 根据缩放级别决定是否允许拖拽
                else if (zoom >= ZOOM_DRAG_THRESHOLD) {
                    // 允许拖拽和缩放
                    for (let i = 0; i < option.geo.length; i++) {
                        option.geo[i].roam = true;
                    }
                } else {
                    // 只允许缩放，不允许拖拽
                    for (let i = 0; i < option.geo.length; i++) {
                        option.geo[i].roam = 'scale';
                    }
                }

                // 同步缩放状态
                for (let i = 0; i < option.geo.length; i++) {
                    if (view.center) {
                        option.geo[i].center = view.center;
                    }
                }
            }

            // 如果是拖拽操作且允许拖拽
            if ((params.dx || params.dy) && zoom >= maxThreshold) {
                for (let i = 0; i < option.geo.length; i++) {
                    option.geo[i].center = view.center;
                }
            }

            // 应用更新后的选项
            mapChart.setOption({
                geo: option.geo
            }, false);
        });

        chart.getZr().on('mousewheel', function (e) {
            const option = chart.getOption();
            const zoom = option.geo[0].zoom || 1;

            console.log('mousewheel事件 - 当前缩放:', zoom, '当前级别:', currentLevel.value);

            // 根据地图级别设置不同的最小阈值
            let minThreshold;
            if (currentLevel.value === 'china') {
                minThreshold = ZOOM_MIN_THRESHOLD;
            } else if (currentLevel.value === 'province') {
                minThreshold = proZOOM_MIN_THRESHOLD;
            }

            console.log('最小阈值:', minThreshold, 'deltaY:', e.event.deltaY);

            // 只阻止过度缩小，允许正常缩小
            if (zoom <= minThreshold && e.event.deltaY > 0) {
                console.log('阻止过度缩小');
                e.event.preventDefault();
                e.event.stopPropagation();
                e.stop();
                return false;
            }
        });

        return chart;
    } catch (error) {
        console.error('初始化地图失败:', error);
        return null;
    }
};
let currentMapData = [];
// 处理缩放下钻
const handleZoomDownDrill = async (params) => {
    console.log('缩放下钻:', params.name);
    const area = currentMapData.find(item => item.name === params.name);
    if (!area) return;
    const { adcode, level } = area;

    if (level === 'district') {
        // 如果是区县级，隐藏 ECharts 地图，显示高德地图
        document.getElementById('main').style.display = 'none';

        // 显示高德地图容器
        if (!document.getElementById('amap-container')) {
            const amapContainer = document.createElement('div');
            amapContainer.id = 'amap-container';
            amapContainer.style.width = '100%';
            amapContainer.style.height = '100vh';
            amapContainer.style.position = 'absolute';
            amapContainer.style.top = '0';
            amapContainer.style.left = '0';
            amapContainer.style.zIndex = '5';
            document.querySelector('.map-container').appendChild(amapContainer);
        } else {
            document.getElementById('amap-container').style.display = 'block';
        }

        // 初始化高德地图
        initAMap(adcode, params.name);
        return;
    }

    if (level === 'district' || !adcode) {
        alert('无此区域地图显示！');
        await loadMap('100000', 'china');
        return;
    }

    await loadMap(adcode, level === 'province' ? 'province' : 'city', currentCode.value, currentName.value);
};

// 处理缩放上钻
const handleZoomUpDrill = async () => {
    // 如果高德地图正在显示，先隐藏它
    if (document.getElementById('amap-container') &&
        document.getElementById('amap-container').style.display !== 'none') {
        document.getElementById('amap-container').style.display = 'none';
        document.getElementById('main').style.display = 'block';
    }

    if (currentLevel.value === 'city') {
        await loadMap(parentCode.value, 'province', '100000', '中国');
    } else if (currentLevel.value === 'province') {
        await loadMap('100000', 'china');
    }
};

// 窗口大小变化时调整图表大小
const handleResize = () => {
    if (mapChart) mapChart.resize();
};

// 同步两个地图的视图
const syncMapView = (sourceChart, targetChart) => {
    if (!sourceChart || !targetChart) return;

    const sourceOption = sourceChart.getOption();
    const targetOption = targetChart.getOption();

    // 获取源地图的中心点和缩放级别
    const center = sourceOption.geo[0].center;
    const zoom = sourceOption.geo[0].zoom;

    // 更新目标地图的所有geo组件
    if (targetOption.geo) {
        for (let i = 0; i < targetOption.geo.length; i++) {
            targetOption.geo[i].center = center;
            targetOption.geo[i].zoom = zoom;
        }
    }

    // 更新目标地图的所有series组件
    if (targetOption.series) {
        for (let i = 0; i < targetOption.series.length; i++) {
            if (targetOption.series[i].type === 'map') {
                targetOption.series[i].center = center;
                targetOption.series[i].zoom = zoom;
            }
        }
    }

    targetChart.setOption(targetOption, false);
};

// 设备资产数据
const assetData = reactive({
    totalAsset: 5680,
    residualValue: 5200,
    depreciationAmount: 230,
    averageLifespan: 18.6
});

const isWorldMap = ref(false);
// 添加切换地图类型的方法
const toggleMapType = (type) => {
    if (type === 'world') {
        isWorldMap.value = true;
        // 这里添加切换到世界地图的逻辑
        nextTick(() => {
            initWorldMap(); // 初始化世界地图
        });
    } else {
        isWorldMap.value = false;
        // 这里添加切换到中国地图的逻辑
        nextTick(() => {
            initChinaMap(); // 初始化中国地图
            getProvinceMapData();
        });
    }
};
import worldData from "@/assets/world.json";
// 初始化世界地图的方法
const initWorldMap = async () => {
    // 这里添加初始化世界地图的代码
    console.log('初始化世界地图');
    var mapName = "world"
    // 例如：使用 ECharts 初始化世界地图
    //world.json从本地取
    echarts.registerMap("world", worldData);
    // 获取图片 data URL
    const imageDataUrlWithPrefix = await getImage(backgroundImage); // 调用你的getImage函数
    const imageDataUrl = imageDataUrlWithPrefix.replace('image://', ''); // 去掉前缀

    // 定义图片背景样式
    const imagePattern = {
        image: imageDataUrl, // 使用去掉前缀的 data URL
        repeat: 'repeat' // 或者 'no-repeat', 'repeat-x', 'repeat-y'
    };

    const scatterData = [
        { name: '纽约', value: [-74.006, 40.712], count: 120 },
        { name: '伦敦', value: [-0.118, 51.509], count: 90 },
        { name: '东京', value: [139.692, 35.690], count: 150 },
        { name: '巴黎', value: [2.352, 48.857], count: 80 },
        { name: '悉尼', value: [151.209, -33.869], count: 70 },
        { name: '北京', value: [116.407, 39.904], count: 200 },
        { name: '莫斯科', value: [37.617, 55.756], count: 110 }
    ];
    let option = {
        tooltip: { show: true, formatter: '{b}' },
        geo: [
            {
                map: mapName,
                roam: false,
                animationDurationUpdate: 0,
                zoom: 1.25,
                zlevel: 10,
                aspectScale: 0.78,
                layoutCenter: ['52%', '64%'],
                layoutSize: '90%',
                itemStyle: {
                    normal: {
                        borderColor: 'rgba(147, 235, 248, 0.6)',
                        borderWidth: 0.8,

                        areaColor: imagePattern,
                    },
                    emphasis: {
                        areaColor: '#00F8FC',
                        label: {
                            show: false,
                            color: '#00cfff', // 高亮状态也指定为蓝色
                            fontSize: 16,
                            // ...其他
                        }
                    }
                },
                emphasis: {
                    areaColor: '#00F8FC', label: {
                        show: true,
                        color: '#fff', // 高亮状态也指定为蓝色
                        fontSize: 9,
                        // ...其他
                    }
                },
                label: {
                    show: true,
                    color: 'rgba(255, 255, 255, 0.87)',
                    fontSize: 9,
                    fontWeight: 'bold',
                    textBorderColor: '#0B1A4A',
                    textBorderWidth: 2,
                    textShadowColor: '#000',
                    textShadowBlur: 4
                }
            },
            {
                map: mapName,
                roam: false,
                animationDurationUpdate: 0,
                zoom: 1.25,
                aspectScale: 0.78,
                layoutCenter: ['52%', '64.5%'],
                layoutSize: '90%',
                zlevel: 1,
                silent: true,
                itemStyle: {
                    areaColor: 'rgba(0, 207, 255, 0.12)', // 阴影色
                    borderColor: 'rgba(0, 248, 252, 0.38)',
                    borderWidth: 7,
                    shadowColor: 'rgba(0,207,255,0.18)',
                    shadowBlur: 40
                },
                label: {
                    show: false,
                    color: 'rgba(255, 255, 255, 0.87)',
                    fontSize: 9,
                    fontWeight: 'bold',
                    textBorderColor: '#0B1A4A',
                    textBorderWidth: 2,
                    textShadowColor: '#000',
                    textShadowBlur: 4
                }
            },
        ],
        series: []
    };
    option.series.push({
        type: 'scatter',
        coordinateSystem: 'geo',
        geoIndex: 0,
        data: scatterData.map(item => ({
            name: item.orgShortName || item.name,
            value: [Number(item.longitude), Number(item.latitude)],
            label: { show: true, formatter: item.orgShortName || item.name }
        })),
        symbolSize: 18,
        showEffectOn: 'render',
        rippleEffect: { brushType: 'stroke', scale: 3 },
        itemStyle: { color: '#00cfff', shadowBlur: 20, shadowColor: '#00cfff' },
        label: {
            show: true,
            color: '#00cfff',
            position: [-30, -50],
            fontSize: 16,
            backgroundColor: 'rgba(0,60,255,0.2)',
            borderColor: '#00cfff',
            borderWidth: 1,
            borderRadius: 4,
            padding: [4, 8]
        },
        zlevel: 100
    });
    const chart = echarts.init(document.getElementById("world-map"));
    chart.setOption(option);

};

// 初始化中国地图的方法
const initChinaMap = async () => {
    // 这里添加初始化中国地图的代码
    console.log('初始化中国地图');
    // 例如：使用 ECharts 初始化中国地图
    mapChart = await initMap("mapjson", "main");
};

// 自定义弹框元素
var tooltipDom = document.createElement('div');
tooltipDom.style.cssText = `
    position: absolute;
    padding: 10px;
    background: rgba(0,0,0,0.7);
    color: #fff;
    border-radius: 5px;
    display: none;
    z-index: 9999;
    pointer-events: none;
`;
document.body.appendChild(tooltipDom)

const getCityMapData = async (provincecode) => {
    try {
        const response = await AnalysisApi.getCountForCityMap({ code: provincecode });
        if (response && response.data) {
            // 直接使用返回的数据创建散点
            const newScatterData = response.data.map(item => ({
                longitude: item.longitude.toString(),
                latitude: item.latitude.toString(),
                orgName: item.orgName || '',
                orgId: item.orgId,
                orgShortName: item.orgShortName || '',
                value: [item.longitude, item.latitude],
                count: item.oneLevelEquNum,
                oneLevelEquNum: item.oneLevelEquNum,
                twoLevelEquNum: item.twoLevelEquNum,
                addrCountyName: item.addrCountyName
            }));

            //scatterData.value = newScatterData;
            var data = updateMapScatter(newScatterData);
            // 更新地图散点
            if (mapChart) {
                const option = mapChart.getOption();
                option.series = option.series.filter(s => s.type !== 'scatter');
                option.series.push({
                    type: 'scatter',
                    coordinateSystem: 'geo',
                    geoIndex: 0,
                    data: data.map(item => {


                        // 如果是聚合点，使用聚合点的配置
                        if (item.clusterData) {
                            return {
                                name: item.name,
                                value: item.value,
                                symbolSize: [30, 30],
                                symbol: `image://${biaozhuImg}`,
                                itemStyle: { color: '#00cfff' },
                                clusterData: item.clusterData,
                                label: {
                                    show: true,
                                    formatter: `{number|${item.label.formatter}}`,
                                    position: 'inside',
                                    backgroundColor: 'transparent',
                                    borderWidth: 0,
                                    rich: {
                                        number: {
                                            color: '#FFFFFF',
                                            fontSize: 12,
                                            fontWeight: 'bold',
                                            align: 'center',
                                            verticalAlign: 'middle',
                                            backgroundColor: 'transparent',
                                            borderWidth: 0
                                        }
                                    }
                                },
                                tooltip: { show: false }
                            };
                        }
                        // 普通点使用原有配置
                        return {
                            name: item.name,
                            value: item.value,
                            orgId: item.orgId,
                            oneLevelEquNum: item.oneLevelEquNum,
                            twoLevelEquNum: item.twoLevelEquNum,
                            code: item.code,
                            symbol: item.symbol || 'image://' + biaozhuImg,
                            symbolSize: item.symbolSize || [20, 20],
                            // label: item.label || { show: false }
                            label: {
                                show: true,
                                formatter: `${item.orgShortName}(${item.count})`,
                                position: 'top',
                                color: '#F7FCFF',
                                fontSize: 12,
                                // backgroundColor: 'rgba(0,60,255,0.2)',
                                // borderColor: '#ffffff',
                                // borderWidth: 1,
                                // borderRadius: 4,
                                padding: [4, 8]
                            }
                        };
                    }),
                    showEffectOn: 'render',
                    rippleEffect: {
                        brushType: 'stroke',
                        scale: 3,
                        period: 4
                    },
                    itemStyle: {
                        color: '#00cfff',
                        shadowBlur: 20,
                        shadowColor: '#00cfff'
                    },
                    zlevel: 1
                });

                // // 移除所有现有的事件
                // mapChart.off('click');
                var customTooltip = document.getElementById('wer');

                // // 添加新的点击事件处理
                mapChart.on('click', (params) => {
                    console.log(params.event);

                    var pointInPixel = [params.event.offsetX, params.event.offsetY];
                    const option = mapChart.getOption();
                    const allData = option.series[0].data;

                    if (params.seriesType === 'scatter') {
                        const clickedData = allData.find(item =>
                            item.value[0] === params.data.value[0] &&
                            item.value[1] === params.data.value[1]
                        );

                        if (clickedData) {
                            // 这里处理聚合点的点击
                            // if (clickedData.clusterData) {
                            //     console.log('点击了聚合点:', clickedData);
                            //     showClusterDetail(clickedData);
                            // }

                            if (clickedData.clusterData?.length > 1) {
                                console.log('点击了聚合点:', clickedData);
                                // 调用显示聚合详情
                                showClusterDetail(clickedData, params.event);

                                // 隐藏其他弹窗
                                const customTooltip = document.getElementById('wer');
                                if (customTooltip && customTooltip.style.display === 'block') {
                                    customTooltip.style.display = 'none';
                                }


                            } else if (clickedData.clusterData?.length == 1) {
                                let maskLayer = document.getElementById('echarts-mask-layer');
                                if (!maskLayer) {
                                    maskLayer = document.createElement('div');
                                    maskLayer.id = 'echarts-mask-layer';
                                    maskLayer.style.position = 'absolute';
                                    maskLayer.style.top = '0';
                                    maskLayer.style.left = '0';
                                    maskLayer.style.width = '100%';
                                    maskLayer.style.height = '100%';
                                    maskLayer.style.zIndex = '10';
                                    maskLayer.style.background = 'transparent';

                                    // 添加点击事件处理
                                    maskLayer.addEventListener('click', (e) => {
                                        // 隐藏蒙版和重置所有点
                                        maskLayer.style.display = 'none';

                                        // 重置所有点的样式
                                        const resetOption = mapChart.getOption();


                                        mapChart.setOption(resetOption);
                                    });

                                    // 添加到地图容器
                                    document.querySelector('.map-container').appendChild(maskLayer);
                                } else {
                                    maskLayer.style.display = 'block';
                                }
                                titles.value = params.data.clusterData[0].orgName
                                currentOrgId.value = params.data.orgId
                                console.log('params.data', params.data);
                                oneLevelEquNum.value = params.data.clusterData[0].oneLevelEquNum
                                twoLevelEquNum.value = params.data.clusterData[0].twoLevelEquNum
                                customTooltip.style.display = 'block';
                                if (document.getElementById('cluster-left-panel')) {
                                    document.getElementById('cluster-left-panel').style.display = 'none';
                                }
                                customTooltip.style.left = (pointInPixel[0] - 74) + 'px';
                                customTooltip.style.top = (pointInPixel[1] - 85) + 'px';



                                // 获取标签的位置和大小，并创建一个"洞"
                                setTimeout(() => {
                                    // 找到标签元素
                                    const labelElements = document.querySelectorAll('.echarts-label');
                                    if (labelElements.length > 0) {
                                        const labelElement = labelElements[labelElements.length - 1];
                                        const rect = labelElement.getBoundingClientRect();
                                        const mapContainer = document.querySelector('.map-container');
                                        const containerRect = mapContainer.getBoundingClientRect();

                                        // 计算相对于容器的位置
                                        const left = rect.left - containerRect.left;
                                        const top = rect.top - containerRect.top;

                                        // 创建一个"洞"，让标签区域可点击
                                        const hole = document.createElement('div');
                                        hole.id = 'echarts-label-hole';
                                        hole.style.position = 'absolute';
                                        hole.style.left = `${left}px`;
                                        hole.style.top = `${top}px`;
                                        hole.style.width = `${rect.width}px`;
                                        hole.style.height = `${rect.height}px`;
                                        hole.style.zIndex = '11';
                                        hole.style.background = 'transparent';
                                        hole.style.pointerEvents = 'none';

                                        maskLayer.appendChild(hole);
                                    }
                                }, 100);
                            }
                        }

                        // if (clickedData) {
                        //     // 创建或显示蒙版层
                        //     let maskLayer = document.getElementById('echarts-mask-layer');
                        //     if (!maskLayer) {
                        //         maskLayer = document.createElement('div');
                        //         maskLayer.id = 'echarts-mask-layer';
                        //         maskLayer.style.position = 'absolute';
                        //         maskLayer.style.top = '0';
                        //         maskLayer.style.left = '0';
                        //         maskLayer.style.width = '100%';
                        //         maskLayer.style.height = '100%';
                        //         maskLayer.style.zIndex = '10';
                        //         maskLayer.style.background = 'transparent';

                        //         // 添加点击事件处理
                        //         maskLayer.addEventListener('click', (e) => {
                        //             // 隐藏蒙版和重置所有点
                        //             maskLayer.style.display = 'none';

                        //             // 重置所有点的样式
                        //             const resetOption = mapChart.getOption();
                        //             resetOption.series[0].data = resetOption.series[0].data.map(item => ({
                        //                 ...item,
                        //                 symbol: 'image://' + biaozhuImg,
                        //                 label: {
                        //                     show: false
                        //                 }
                        //             }));

                        //             mapChart.setOption(resetOption);
                        //         });

                        //         // 添加到地图容器
                        //         document.querySelector('.map-container').appendChild(maskLayer);
                        //     } else {
                        //         maskLayer.style.display = 'block';
                        //     }
                        //     titles.value = params.data.name
                        //     currentOrgId.value = params.data.orgId
                        //     oneLevelEquNum.value = params.data.oneLevelEquNum
                        //     twoLevelEquNum.value = params.data.twoLevelEquNum
                        //     customTooltip.style.display = 'block';
                        //     customTooltip.style.left = (pointInPixel[0] - 74) + 'px';
                        //     customTooltip.style.top = (pointInPixel[1] - 85) + 'px';

                        //     option.series[0].data = allData.map(item => ({
                        //         ...item,
                        //         symbol: item === clickedData ? 'image://' + biaozhuxuanImg : 'image://' + biaozhuImg,
                        //         label: {
                        //             show: false,
                        //             formatter: function (params) {

                        //                 // const pointName = params.data.name || '二十九处风凰山';

                        //                 // const level1Count = params.data.level1Count || 29;
                        //                 // const level2Count = params.data.level2Count || 29;
                        //                 // return [
                        //                 //     '{titleBox|' + pointName + '}',
                        //                 //     '{countBox|{count1|' + level1Count + '}{count2|' + level2Count + '}}',
                        //                 //     '{labelBox|{label1|一级设备}{label2|二级设备}}'
                        //                 // ].join('\n');
                        //             },
                        //             position: 'top',
                        //             align: 'center',
                        //             distance: 10,
                        //             backgroundColor: '#fff',
                        //             borderRadius: 5,
                        //             padding: [10, 15],
                        //             silent: true,
                        //             rich: {
                        //                 titleBox: {
                        //                     width: 170,
                        //                     color: '#00F8FC',
                        //                     fontSize: 16,
                        //                     fontWeight: 'bold',
                        //                     align: 'center',
                        //                     padding: [0, 0, 10, 0],
                        //                     lineHeight: 20
                        //                 },
                        //                 countBox: {
                        //                     width: 170,
                        //                     height: 30,
                        //                     align: 'center',
                        //                     padding: [0, 0, 5, 0]
                        //                 },
                        //                 count1: {
                        //                     width: 85,
                        //                     color: '#00F8FC',
                        //                     fontSize: 24,
                        //                     fontWeight: 'bold',
                        //                     align: 'center',
                        //                     padding: [0, 0, 0, 0]
                        //                 },
                        //                 count2: {
                        //                     width: 85,
                        //                     color: '#00F8FC',
                        //                     fontSize: 24,
                        //                     fontWeight: 'bold',
                        //                     align: 'center',
                        //                     padding: [0, 0, 0, 0]
                        //                 },
                        //                 labelBox: {
                        //                     width: 170,
                        //                     height: 20,
                        //                     align: 'center',
                        //                     padding: [0, 0, 0, 0]
                        //                 },
                        //                 label1: {
                        //                     width: 85,
                        //                     color: '#4B9EFF',
                        //                     fontSize: 12,
                        //                     align: 'center',
                        //                     padding: [0, 0, 0, 0],
                        //                     height: 20,
                        //                     lineHeight: 20,
                        //                     backgroundColor: {
                        //                         image: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAUCAYAAAAN+VqOAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAABxSURBVHgB7dexDcAgDETRw2KZLOM9GIRRMgqjwE9AkRCFkYnQe+X7uuJOEjNL6q2qQ+KYWdJlZkviuLvkiIhFYkXEJrH+Xnq/kCRJkiRJv+WcF4nVWtskTmvtkDjvCz0lzjmXHBFxSayI2CXWDVRuHYAV+Di8AAAAAElFTkSuQmCC'
                        //                     }
                        //                 },
                        //                 label2: {
                        //                     width: 85,
                        //                     color: '#4B9EFF',
                        //                     fontSize: 12,
                        //                     align: 'center',
                        //                     padding: [0, 0, 0, 0],
                        //                     height: 20,
                        //                     lineHeight: 20,
                        //                     backgroundColor: {
                        //                         image: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAUCAYAAAAN+VqOAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAABxSURBVHgB7dexDcAgDETRw2KZLOM9GIRRMgqjwE9AkRCFkYnQe+X7uuJOEjNL6q2qQ+KYWdJlZkviuLvkiIhFYkXEJrH+Xnq/kCRJkiRJv+WcF4nVWtskTmvtkDjvCz0lzjmXHBFxSayI2CXWDVRuHYAV+Di8AAAAAElFTkSuQmCC'
                        //                     }
                        //                 }
                        //             },
                        //             zlevel: 13
                        //         }
                        //     }));

                        //     mapChart.setOption(option);

                        //     // 获取标签的位置和大小，并创建一个"洞"
                        //     setTimeout(() => {
                        //         // 找到标签元素
                        //         const labelElements = document.querySelectorAll('.echarts-label');
                        //         if (labelElements.length > 0) {
                        //             const labelElement = labelElements[labelElements.length - 1];
                        //             const rect = labelElement.getBoundingClientRect();
                        //             const mapContainer = document.querySelector('.map-container');
                        //             const containerRect = mapContainer.getBoundingClientRect();

                        //             // 计算相对于容器的位置
                        //             const left = rect.left - containerRect.left;
                        //             const top = rect.top - containerRect.top;

                        //             // 创建一个"洞"，让标签区域可点击
                        //             const hole = document.createElement('div');
                        //             hole.id = 'echarts-label-hole';
                        //             hole.style.position = 'absolute';
                        //             hole.style.left = `${left}px`;
                        //             hole.style.top = `${top}px`;
                        //             hole.style.width = `${rect.width}px`;
                        //             hole.style.height = `${rect.height}px`;
                        //             hole.style.zIndex = '11';
                        //             hole.style.background = 'transparent';
                        //             hole.style.pointerEvents = 'none';

                        //             maskLayer.appendChild(hole);
                        //         }
                        //     }, 100);
                        // }
                    } else {
                        // 点击地图空白处时，恢复所有点的样式
                        option.series[0].data = allData.map(item => ({
                            ...item,
                            symbol: 'image://' + biaozhuImg,
                            label: {
                                show: false
                            }
                        }));
                        customTooltip.style.display = 'none';
                        if(document.getElementById('cluster-left-panel')){
                            document.getElementById('cluster-left-panel').style.display = 'none';
                        }
                        document.getElementById('cluster-left-panel').style.display = 'none';
                        mapChart.setOption(option);
                    }
                });

                // 添加一个事件监听器，防止地图移动时弹出框消失
                // mapChart.on('georoam', function (params) {
                //     // 保持弹出框显示
                //     const option = mapChart.getOption();
                //     if (customTooltip.style.display == 'block') {
                //         customTooltip.style.display = 'none';
                //     }

                //     // 隐藏聚合点详情面板
                //     if (document.getElementById('cluster-left-panel')) {
                //         document.getElementById('cluster-left-panel').style.display = 'none';
                //     }

                //     // 如果是缩放操作，重新计算点聚合
                //     if (params && params.zoom && scatterData.value && scatterData.value.length > 0) {
                //         const currentZoom = option.geo[0].zoom || 1;

                //         // 使用经纬度级别的聚合距离
                //         let clusterDistance;
                //         if (currentZoom < 2) {
                //             clusterDistance = 0.5; // 缩放小时，聚合距离大
                //         } else if (currentZoom < 4) {
                //             clusterDistance = 0.3; // 中等缩放
                //         } else {
                //             clusterDistance = 0.1; // 最大缩放时，聚合距离小
                //         }

                //         console.log(`当前缩放级别: ${currentZoom}, 聚合距离: ${clusterDistance}`); // 调试日志

                //         // 重新聚合点数据
                //         const clusteredData = clusterPoints(scatterData.value, clusterDistance);

                //         // 更新散点系列
                //         const newOption = mapChart.getOption();
                //         newOption.series = newOption.series.filter(s => s.type !== 'scatter');

                //         newOption.series.push({
                //             type: 'scatter',
                //             coordinateSystem: 'geo',
                //             geoIndex: 0,
                //             data: clusteredData.map(item => ({
                //                 name: item.name,
                //                 value: item.value,
                //                 symbol: `image://${biaozhuImg}`,
                //                 symbolSize: [30, 32],
                //                 label: {
                //                     show: true,
                //                     formatter: `{number|${item.clusterData ? item.clusterData.length : item.count}}`,
                //                     position: [0, -5],
                //                     backgroundColor: 'transparent',
                //                     borderWidth: 0,
                //                     rich: {
                //                         number: {
                //                             color: '#FFFFFF',
                //                             fontSize: 12,
                //                             fontWeight: 'bold',
                //                             align: 'center',
                //                             verticalAlign: 'middle',
                //                             backgroundColor: 'transparent',
                //                             borderWidth: 0
                //                         }
                //                     }
                //                 },
                //                 tooltip: { show: false },
                //                 clusterData: item.clusterData // 保存聚合数据用于点击处理
                //             })),
                //             symbolSize: 30,
                //             itemStyle: {
                //                 color: '#00cfff',
                //                 shadowBlur: 0,
                //                 shadowColor: 'transparent'
                //             },
                //             zlevel: 100
                //         });

                //         mapChart.setOption(newOption);
                //     }

                //     if (option.series && option.series[0] && option.series[0].data) {
                //         const hasActivePoint = option.series[0].data.some(item => item.label && item.label.show);
                //         if (hasActivePoint) {
                //             mapChart.setOption(option);

                //         }
                //     }
                // });


                // 添加地图缩放拖拽同步事件
                mapChart.on('georoam', async (params) => {
                    // 获取当前视图状态
                    const option = mapChart.getOption();
                    const view = option.geo[0];
                    const zoom = view.zoom || 1;

                    // 根据地图级别设置不同的阈值
                    let maxThreshold, minThreshold;
                    if (currentLevel.value === 'china') {
                        maxThreshold = ZOOM_MAX_THRESHOLD;
                        minThreshold = ZOOM_MIN_THRESHOLD;
                    } else if (currentLevel.value === 'province') {
                        maxThreshold = proZOOM_MAX_THRESHOLD;
                        minThreshold = proZOOM_MIN_THRESHOLD;
                    }

                    // 如果是缩放操作
                    if (params.zoom) {
                        // ... 原有的缩放逻辑处理

                        // 同步所有geo图层的视图状态
                        for (let i = 0; i < option.geo.length; i++) {
                            option.geo[i].zoom = view.zoom;
                            if (view.center) {
                                option.geo[i].center = view.center;
                            }
                        }
                    }

                    // 如果是拖拽操作
                    if (params.dx || params.dy) {
                        for (let i = 0; i < option.geo.length; i++) {
                            option.geo[i].center = view.center;
                        }
                    }

                    // 同步散点图层 - 关键添加这部分
                    if (option.series) {
                        for (let i = 0; i < option.series.length; i++) {
                            if (option.series[i].coordinateSystem === 'geo') {
                                option.series[i].geoIndex = 0;
                            }
                        }
                    }

                    // 应用更新后的选项，确保所有图层同步移动
                    mapChart.setOption({
                        geo: option.geo,
                        series: option.series // 添加series同步
                    }, false);
                });

                mapChart.getZr().on('mousewheel', function (e) {
                    const option = mapChart.getOption();
                    const zoom = option.geo[0].zoom || 1;

                    // 根据当前地图级别设置不同的阈值和中心点
                    let minThreshold, centerPoint;
                    if (currentLevel.value === 'china') {
                        minThreshold = ZOOM_MIN_THRESHOLD;
                        centerPoint = initialCenter.value;
                    } else if (currentLevel.value === 'province') {
                        minThreshold = proZOOM_MIN_THRESHOLD;
                        centerPoint = provinceInitialCenter.value;
                    }

                    // 如果当前已经是最小缩放级别，并且用户试图继续缩小
                    if (zoom <= minThreshold && e.event.deltaY > 0) {
                        e.event.preventDefault();
                        e.event.stopPropagation();
                        e.stop();

                        // 主动重置到最小缩放级别，但保持散点图层
                        const currentOption = mapChart.getOption();
                        for (let i = 0; i < currentOption.geo.length; i++) {
                            currentOption.geo[i].zoom = minThreshold;
                            if (centerPoint) {
                                currentOption.geo[i].center = centerPoint;
                            }
                        }
                        // 只更新geo配置，不影响series
                        mapChart.setOption({
                            geo: currentOption.geo
                        }, false, true); // 第三个参数true表示不合并，避免影响其他配置
                        return false;
                    }
                });


                mapChart.setOption(option);
            }
        }
    } catch (error) {
        console.error('获取省份地图数据失败:', error);
    }
};
const getProvinceMapData = async () => {

    try {
        const response = await AnalysisApi.getCountForCityMap({ code: "" });
        if (response && response.data) {
            // 将API返回的数据与provinceData匹配
            const newScatterData = provinceData.value.map(province => {
                // 查找返回数据中label与province.code匹配的项
                const matchedData = response.data.find(item => item.label === province.code);
                if (matchedData) {
                    return {
                        longitude: province.coordinate[0].toString(),
                        latitude: province.coordinate[1].toString(),
                        orgName: province.name,
                        orgShortName: province.shortName,
                        value: [province.coordinate[0], province.coordinate[1], matchedData.value],
                        count: matchedData.value
                    };
                }
                return null;
            }).filter(item => item !== null);

            scatterData.value = newScatterData;
            console.log('newScatterData', newScatterData);
            // 更新地图散点
            if (mapChart) {
                const option = mapChart.getOption();
                console.log('option.series1', option.series);
                option.series = option.series.filter(s => s.type !== 'scatter');
                console.log('option.series2', option.series);
                option.series.push({
                    type: 'scatter',
                    coordinateSystem: 'geo',
                    geoIndex: 0,
                    data: newScatterData.map(item => ({
                        name: item.orgShortName || item.orgName,
                        value: item.value,
                        symbol: `image://${biaozhuImg}`,
                        symbolSize: [30, 32],
                        label: {
                            show: true,
                            formatter: `{number|${item.count}}`,
                            position: 'inside',
                            backgroundColor: 'transparent',
                            borderWidth: 0,
                            rich: {
                                number: {
                                    color: '#FFFFFF',
                                    fontSize: 12,
                                    fontWeight: 'bold',
                                    align: 'center',
                                    verticalAlign: 'middle',
                                    backgroundColor: 'transparent',
                                    borderWidth: 0
                                }
                            },

                        },
                        tooltip: {
                            show: false
                        }
                    })),
                    symbolSize: 30,
                    itemStyle: {
                        color: '#00cfff',
                        shadowBlur: 0,  // 移除阴影模糊
                        shadowColor: 'transparent'  // 移除阴影颜色
                    },
                    zlevel: 100
                });
                mapChart.setOption(option);
            }
        }
    } catch (error) {
        console.error('获取省份地图数据失败:', error);
    }
};
const getBarAllocation = async (count) => {
    try {
        const response = await AnalysisApi.getBarAllocation({ limit: count });
        if (response && response.data) {
            allocationData.value = response.data;
            console.log(allocationData.value)

            initAllocationChart(allocationData);
        }
    } catch (error) {
        console.error('获取设备调拨数据失败:', error);
    }
};

const getCountForAll = async () => {
    try {
        const response = await AnalysisApi.getCountForAll();
        if (response && response.data) {
            statsData.value = response.data;
        }
    } catch (error) {
        console.error('获取设备调拨数据失败:', error);
    }
};

// 在组件顶部引入背景图
import popupBgImg from '@/assets/equipment/map/mapblock.png'; // 引入背景图

// 在setup中添加背景图URL
const popupBgUrl = ref('');

watch(isShowMap, (val) => {
    console.log(val)
  if (val) {
    nextTick(initAMap); // DOM 更新后初始化
  } else {
    destroyAMap(); // 隐藏时销毁
  }
});

onMounted(async () => {
    // 初始化两个地图
    mapChart = await initMap("mapjson", "main");

    const preventZoomOut = function (event) {
        if (isShowMap.value) return;

        if (!mapChart) return;

        const option = mapChart.getOption();
        if (!option || !option.geo || !option.geo[0]) return;

        const zoom = option.geo[0].zoom || 1;

        // // 如果当前已经是最小缩放级别，并且用户试图继续缩小
        // if (currentLevel.value === 'china' && zoom <= ZOOM_MIN_THRESHOLD && event.deltaY > 0) {
        //     event.preventDefault();
        //     event.stopPropagation();
        //     return false;
        // }

        // // 如果当前已经是最大缩放级别，并且用户试图继续放大
        // if (currentLevel.value === 'china' && zoom >= ZOOM_MAX_THRESHOLD && event.deltaY < 0) {
        //     event.preventDefault();
        //     event.stopPropagation();
        //     return false;
        // }

        if (currentLevel.value === 'china') {
            if (zoom <= ZOOM_MIN_THRESHOLD && event.deltaY > 0) {
                event.preventDefault();
                event.stopPropagation();
                return false;
            } else if (zoom >= ZOOM_MAX_THRESHOLD && event.deltaY < 0) {
                event.preventDefault();
                event.stopPropagation();
                return false;
            }

        } else if (currentLevel.value === 'province') {
            if (zoom <= proZOOM_MIN_THRESHOLD && event.deltaY > 0) {
                event.preventDefault();
                event.stopPropagation();
                return false;
            } else if (zoom >= proZOOM_MAX_THRESHOLD && event.deltaY < 0) {
                event.preventDefault();
                event.stopPropagation();
                return false;
            }
        }
    };

    // 在多个元素上添加事件监听
    document.getElementById('main').addEventListener('wheel', preventZoomOut, { passive: false, capture: true });
    document.querySelector('.map-container')?.addEventListener('wheel', preventZoomOut, { passive: false, capture: true });
    document.body.addEventListener('wheel', preventZoomOut, { passive: false, capture: true });

    await getProvinceMapData();
    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);
    if (mapChart) {
        bindMapEvents(mapChart);
    }
    getBarAllocation(6);
    getCountForAll();
    popupBgUrl.value = await getImage(popupBgImg);

    window.openOneLevelEqu = openOneLevelEqu;
    currentLevel.value = 'china';


    window.addEventListener('wheel', function (event) {
        if (event.ctrlKey === true || event.metaKey) {
            event.preventDefault();
        }
    }, { passive: false });

    //firefox
    window.addEventListener('DOMMouseScroll', function (event) {
        if (event.ctrlKey === true || event.metaKey) {
            event.preventDefault();
        }
    }, { passive: false })
    //setScale();

    initEnumData()
    
});

onUnmounted(() => {
    // 移除事件监听
    document.getElementById('main').removeEventListener('wheel', preventZoomOut, { passive: false, capture: true });
    document.querySelector('.map-container')?.removeEventListener('wheel', preventZoomOut, { passive: false, capture: true });
    document.body.removeEventListener('wheel', preventZoomOut, { passive: false, capture: true });

    // 移除窗口大小变化监听
    window.removeEventListener('resize', handleResize);

    // 销毁图表实例
    if (mapChart) mapChart.dispose();

    // 销毁高德地图实例

});
window._AMapSecurityConfig = {
    securityJsCode: "4e33db193c0c83c604c10a5eceb7bc61",
};
var map = ref({})
// 初始化天地图
const initAMap = async (adcode, areaName) => {
    try {
        // 动态加载OpenLayers脚本
        if (!window.ol) {
            await new Promise((resolve, reject) => {
                // 加载CSS
                const cssLink = document.createElement('link');
                cssLink.rel = 'stylesheet';
                cssLink.href = 'https://cdn.jsdelivr.net/gh/openlayers/openlayers.github.io@master/en/v6.5.0/css/ol.css';
                document.head.appendChild(cssLink);
                
                // 加载JS
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/gh/openlayers/openlayers.github.io@master/en/v6.5.0/build/ol.js';
                script.onload = () => resolve(window.ol);
                script.onerror = () => reject(new Error('OpenLayers加载失败'));
                document.head.appendChild(script);
            });
        }

        // 获取区域中心点
        let center = [116.397428, 39.90923]; // 默认北京坐标
        
        if (adcode && adcode !== '100000') {
            try {
                center = await getDistrictCenter(adcode);
            } catch (error) {
                console.warn('获取区域中心点失败，使用默认坐标:', error);
            }
        }

        console.log('使用坐标:', center);

        const tk = 'b0cf25a9fe9839fe9cea00f386c84238';
        
        // 创建投影和分辨率配置
        const projection = window.ol.proj.get('EPSG:4326');
        const projectionExtent = projection.getExtent();
        const size = window.ol.extent.getWidth(projectionExtent) / 256;
        const resolutions = new Array(20);
        const matrixIds = new Array(20);
        
        const targetCoordinate = window.ol.proj.fromLonLat(center);
        
        for (let z = 0; z < 20; ++z) {
            resolutions[z] = size / Math.pow(2, z);
            matrixIds[z] = z;
        }

        // 创建天地图图层
    const layer = new window.ol.layer.Tile({
        className: 'base-layer',
        source: new window.ol.source.WMTS({
            url: `https://t5.tianditu.gov.cn/vec_c/wmts?tk=${tk}`,
            layer: 'vec',
            matrixSet: 'c',
            format: 'tiles',
            projection: projection,
            tileGrid: new window.ol.tilegrid.WMTS({
                origin: window.ol.extent.getTopLeft(projectionExtent),
                resolutions: resolutions,
                matrixIds: matrixIds
            }),
            style: 'default',
            wrapX: true,
            crossOrigin: 'anonymous'
        })
    });

    const layer_B = new window.ol.layer.Tile({
        className: 'base-layer',
        source: new window.ol.source.WMTS({
            url: `https://t5.tianditu.gov.cn/cva_c/wmts?tk=${tk}`,
            layer: 'cva',
            matrixSet: 'c',
            format: 'tiles',
            projection: projection,
            tileGrid: new window.ol.tilegrid.WMTS({
                origin: window.ol.extent.getTopLeft(projectionExtent),
                resolutions: resolutions,
                matrixIds: matrixIds
            }),
            style: 'default',
            wrapX: true,
            crossOrigin: 'anonymous'
        })
    });

        // 扎点相关变量
        let vectorSource = new window.ol.source.Vector();
        let vectorLayer = new window.ol.layer.Vector({
            source: vectorSource,
            className: 'marker-layer'
        });

        // 创建地图实例
        map = new window.ol.Map({
            target: 'amap-container',
            layers: [layer, layer_B, vectorLayer],
            view: new window.ol.View({
                center: targetCoordinate,
                zoom: 12
            })
        });

        // 等待地图加载完成后获取钉点数据
        setTimeout(() => {
            // 获取可视区域边界
            const extent = map.getView().calculateExtent(map.getSize());
            const bottomLeft = window.ol.proj.toLonLat([extent[0], extent[1]]);
            const topRight = window.ol.proj.toLonLat([extent[2], extent[3]]);
            
            // 构建坐标区间
            const coordinateRange = {
                centerLng: center[0],
                centerLat: center[1],
                lngStart: bottomLeft[0] - 1,
                latStart: bottomLeft[1] - 1,
                lngEnd: topRight[0] + 1,
                latEnd: topRight[1] + 1
            };

            // 调用后台接口获取钉点数据
            AnalysisApi.getMapPoints(coordinateRange).then(res => {
                console.log("获取钉点数据", res.data.orgEquList);
                var points = res.data.orgEquList;
                if (points && points.length > 0) {
                    points.forEach(point => {
                        const feature = new window.ol.Feature({
                            geometry: new window.ol.geom.Point(window.ol.proj.fromLonLat([point.longitude, point.latitude])),
                            orgId: point.orgId,
                            orgName: point.orgName,
                            orgShortName: point.orgShortName,
                            oneLevelEquNum: point.oneLevelEquNum || 0,
                            twoLevelEquNum: point.twoLevelEquNum || 0,
                            longitude: point.longitude,
                            latitude: point.latitude
                        });
                        
                        const iconStyle = new window.ol.style.Style({
                            image: new window.ol.style.Icon({
                                src: biaozhuImg,
                                scale: 1,
                                anchor: [0.5, 1]
                            })
                        });
                        
                        feature.setStyle(iconStyle);
                        vectorSource.addFeature(feature);
                    });
                }
            });
        }, 1000);

        // 添加地图点击事件监听
        map.on('click', function(evt) {
            const feature = map.forEachFeatureAtPixel(evt.pixel, function(feature) {
                return feature;
            });
            
            if (feature) {
                // 设置数据
                currentOrgId.value = feature.get('orgId');
                titles.value = feature.get('orgName');
                oneLevelEquNum.value = feature.get('oneLevelEquNum') || 0;
                twoLevelEquNum.value = feature.get('twoLevelEquNum') || 0;
                
                const customTooltip = document.getElementById('wer');
                if (customTooltip) {
                    const pixel = evt.pixel;
                    const amapContainer = document.getElementById('amap-container');
                    const rect = amapContainer.getBoundingClientRect();
                    
                    customTooltip.style.left = (rect.left + pixel[0] - 100) + 'px';
                    customTooltip.style.top = (rect.top + pixel[1] - 150) + 'px';
                    customTooltip.style.display = 'block';
                    customTooltip.style.zIndex = '9999';
                    customTooltip.style.position = 'fixed';
                }
            }
        });

        // 添加缩放监听
        map.getView().on('change:resolution', () => {
            const currentZoom = map.getView().getZoom();
            console.log('当前缩放级别:', currentZoom);
            if (currentZoom <= 8) {
                document.getElementById('amap-container').style.display = 'none';
                document.getElementById('main').style.display = 'block';
                isShowMap.value = false;
                loadMap(parentCode.value, 'city', parentCode.value, parentName.value);
            }
        });

        return map;
        
    } catch (error) {
        console.error('初始化天地图失败:', error);
        const amapContainer = document.getElementById('amap-container');
        if (amapContainer) {
            amapContainer.innerHTML = '<div style="color: white; text-align: center; padding-top: 100px;">加载地图失败，请刷新重试</div>';
        }
        return null;
    }
};

// 在 initAMap 函数外部，添加隐藏弹出框的函数
const hideCustomTooltip = () => {
    const customTooltip = document.getElementById('wer');
    if (customTooltip) {
        customTooltip.style.display = 'none';
    }
};

// 获取区县中心点
const getDistrictCenter = async (adcode) => {
    try {
        console.log('查询adcode:', adcode);
        return new Promise((resolve) => {
            try {
                const tk = 'b0cf25a9fe9839fe9cea00f386c84238';
                
                // 构造API请求URL - 注意postStr需要正确编码
                const postStr = JSON.stringify({
                    "searchWord": adcode,
                    "searchType": "1",
                    "needSubInfo": "false",
                    "needAll": "false",
                    "needPolygon": "false",
                    "needPre": "false"
                });
                
                const apiUrl = `https://api.tianditu.gov.cn/administrative?postStr=${encodeURIComponent(postStr)}&tk=${tk}`;
                
                // 发送API请求
                fetch(apiUrl)
                    .then(response => response.json())
                    .then(data => {
                        console.log('天地图行政区划查询结果:', data);
                        
                        // 检查返回结果
                        if (data.status === '0' && data.data && data.data.length > 0) {
                            const district = data.data[0];
                            
                            // 天地图返回的数据结构中，中心点可能在不同字段
                            if (district.center) {
                                const [lng, lat] = district.center.split(',').map(Number);
                                console.log('获取到中心点:', [lng, lat]);
                                resolve([lng, lat]);
                            } else if (district.bound) {
                                // 如果没有center字段，通过bound计算中心点
                                const bounds = district.bound.split(';');
                                if (bounds.length >= 2) {
                                    const [minLng, minLat] = bounds[0].split(',').map(Number);
                                    const [maxLng, maxLat] = bounds[1].split(',').map(Number);
                                    
                                    const centerLng = (minLng + maxLng) / 2;
                                    const centerLat = (minLat + maxLat) / 2;
                                    
                                    console.log('通过边界计算得到中心点:', [centerLng, centerLat]);
                                    resolve([centerLng, centerLat]);
                                } else {
                                    console.warn('边界数据格式异常');
                                    resolve([116.397428, 39.90923]);
                                }
                            } else {
                                console.warn('未找到中心点或边界数据');
                                resolve([116.397428, 39.90923]);
                            }
                        } else {
                            console.warn('天地图查询无结果，状态:', data.status);
                            resolve([116.397428, 39.90923]);
                        }
                    })
                    .catch(error => {
                        console.error('天地图API请求失败:', error);
                        resolve([116.397428, 39.90923]);
                    });
                    
            } catch (error) {
                console.error('天地图查询初始化失败:', error);
                resolve([116.397428, 39.90923]);
            }
        });
    } catch (error) {
        console.error('获取区县中心点失败:', error);
        return [116.397428, 39.90923];
    }
};


/* 用户信息下拉点击 */
const onUserDropClick = ({ key }) => {
    if (key === 'logout') {
        // 退出登录
        Modal.confirm({
            title: t('layout.logout.title'),
            content: t('layout.logout.message'),
            icon: createVNode(ExclamationCircleOutlined),
            maskClosable: true,
            onOk: async () => {
                // 清除本地菜单类型缓存
                if (localStorage.getItem('menuType')) {
                    localStorage.removeItem('menuType');
                }

                // 如果开启了单点登录，跳转到单点的退出
                if (SSO_FLAG) {
                    // 清除token
                    removeToken();
                    // 调用sso退出接口
                    SsoUtil.ssoLogoutRedirect();
                } else {
                    // 调用退出接口
                    await LoginApi.logout();
                    // 清除缓存token并退出
                    logout();
                }
            }
        });
    }
};

// 切换到国家级地图
const switchToCountry = async () => {
    // 如果高德地图正在显示，先隐藏它
    if (document.getElementById('amap-container') &&
        document.getElementById('amap-container').style.display !== 'none') {
        document.getElementById('amap-container').style.display = 'none';
        document.getElementById('main').style.display = 'block';
        isShowMap.value = false;
    }
    await loadMap('100000', 'china');
    // 把所有的弹出框隐藏
    const customTooltip = document.getElementById('wer');
    if (customTooltip) {
        customTooltip.style.display = 'none';
    }
    // 隐藏聚合点详情弹窗
    const clusterPopup = document.getElementById('cluster-detail-popup');
    if (clusterPopup) {
        clusterPopup.style.display = 'none';
    }
    currentLevel.value = 'china';
};

// 切换到省级地图
const switchToProvince = async () => {
    // 如果当前是国家级，切换到默认省份（安徽）
    // 如果当前已经是省级，保持不变
    if (currentLevel.value === 'china') {
        // 这里使用安徽省的adcode，可以根据需要修改
        await loadMap('340000', 'province', '100000', '中国');
    } else if (currentLevel.value === 'city') {
        // 如果当前是市级，返回到其所属省

        await loadMap(parentCode.value, 'province', '100000', '中国');
    }

    const customTooltip = document.getElementById('wer');
    if (customTooltip) {
        customTooltip.style.display = 'none';
    }
    // 隐藏聚合点详情弹窗
    const clusterPopup = document.getElementById('cluster-detail-popup');
    if (clusterPopup) {
        clusterPopup.style.display = 'none';
    }
    //返回到省级的时候。把所有的内容都清除，把高德隐藏。展现echarts
    document.getElementById('main').style.display = 'block';
    document.getElementById('amap-container').style.display = 'none';

    currentLevel.value = 'province';
};

// 切换到市级地图
const switchToCity = async () => {
    // 如果当前是省级，切换到该省的默认市
    if (currentLevel.value === 'province') {
        // 获取当前省的下级区域
        const subAreas = await getSubAreas(currentCode.value);
        if (subAreas && subAreas.length > 0) {
            // 选择第一个市级区域
            await loadMap(subAreas[0].adcode, 'city', currentCode.value, currentName.value);
        }
    } else if (currentLevel.value === 'china') {
        // 如果当前是国家级，先切换到默认省，再切换到默认市
        await switchToProvince();
        await switchToCity();
    }

    const customTooltip = document.getElementById('wer');
    if (customTooltip) {
        customTooltip.style.display = 'none';
    }
    // 隐藏聚合点详情弹窗
    const clusterPopup = document.getElementById('cluster-detail-popup');
    if (clusterPopup) {
        clusterPopup.style.display = 'none';
    }
    currentLevel.value = 'city';
};


//echarts进行点聚合

// 添加点聚合功能
const clusterPoints = (points, distance = 30) => {
    if (!points || points.length === 0) return [];

    // 克隆点数据，避免修改原始数据
    const pointsData = JSON.parse(JSON.stringify(points));

    // 使用网格算法进行快速聚合
    const gridSize = distance; // 网格大小等于聚合距离
    const grid = new Map();

    // 将点分配到网格中
    pointsData.forEach((point, index) => {
        const gridX = Math.floor(point.value[0] * 1000 / gridSize); // 经度转网格坐标
        const gridY = Math.floor(point.value[1] * 1000 / gridSize); // 纬度转网格坐标
        const gridKey = `${gridX},${gridY}`;

        if (!grid.has(gridKey)) {
            grid.set(gridKey, []);
        }
        grid.get(gridKey).push({ ...point, originalIndex: index });
    });

    // 对每个网格内的点进行聚合
    const clusters = [];
    grid.forEach((gridPoints) => {
        if (gridPoints.length === 1) {
            // 单个点，直接添加
            clusters.push({
                center: gridPoints[0],
                points: [gridPoints[0]]
            });
        } else {
            // 多个点，选择中心点作为聚合中心
            const centerPoint = gridPoints[0]; // 简单选择第一个点作为中心
            clusters.push({
                center: centerPoint,
                points: gridPoints
            });
        }
    });

    console.log(`网格聚合结果: ${clusters.length}个聚合点, 原始点数: ${pointsData.length}`);

    // 转换为 ECharts 数据格式
    return clusters.map(cluster => {
        const count = cluster.points.length;

        return {
            name: cluster.points[0].addrCountyName,
            value: cluster.center.value,
            symbolSize: [30, 30],
            symbol: `image://${biaozhuImg}`,
            label: {
                show: count > 1,
                formatter: count.toString(),
                position: [0, -5],
                backgroundColor: 'transparent',
                borderWidth: 0,
                rich: {
                    number: {
                        color: '#FFFFFF',
                        fontSize: 12,
                        fontWeight: 'bold',
                        align: 'center',
                        verticalAlign: 'middle',
                        backgroundColor: 'transparent',
                        borderWidth: 0
                    }
                }
            },
            itemStyle: {
                color: '#00cfff',
                shadowBlur: 0,
                shadowColor: 'transparent'
            },
            // 存储原始点数据，用于点击展开
            clusterData: cluster.points
        };
    });
};

// 更新散点图层 - 修改为只返回数据，不描点
const updateMapScatter = (scatterData) => {
    if (!scatterData || scatterData.length === 0) return [];

    // 只在省级或市级地图上应用聚合
    let processedData;
    if (currentLevel.value === 'province' || currentLevel.value === 'city') {
        // 获取当前地图缩放级别（如果mapChart存在）
        const zoom = mapChart ? (mapChart.getOption().geo[0].zoom || 1) : 1;

        // 根据缩放级别调整聚合距离
        const clusterDistance = zoom < 2 ? 50 : (zoom < 4 ? 30 : 15);
        // 应用聚合
        processedData = clusterPoints(scatterData, clusterDistance);

    } else {
        // 国家级地图或其他级别不应用聚合
        processedData = scatterData.map(item => ({
            name: item.orgShortName || item.orgName,
            value: item.value,
            orgId: item.orgId,
            oneLevelEquNum: item.oneLevelEquNum,
            twoLevelEquNum: item.twoLevelEquNum,
            code: item.code,
            symbol: 'image://' + biaozhuImg,
            symbolSize: [20, 20],
            label: {
                show: false
            }
        }));
    }

    return processedData;
};

const showClusterPanel = ref(false);

const clusterPanelPosition = ref({ x: 0, y: 0 });

// 添加 ref 引用
const clusterDetailPanel = ref(null);
const clusterPointsData = ref([]);

// 添加一个变量来跟踪当前的隐藏函数
let currentHidePanel = null;

const showClusterDetail = (clusterData, clickEvent) => {
    console.log('showClusterDetail 被调用:', clusterData, clickEvent);
    
    const points = clusterData.clusterData;
    
    if (!points || points.length === 0) {
        console.error('没有聚合点数据');
        return;
    }
    
    // 清理之前的事件监听器
    if (currentHidePanel) {
        document.removeEventListener('click', currentHidePanel);
        currentHidePanel = null;
    }
    
    // 更新数据
    clusterPointsData.value = [...points];
    
    // 设置位置 - 调整为显示在扎点右侧
    if (clickEvent) {
        const x = clickEvent.offsetX || clickEvent.layerX;
        const y = clickEvent.offsetY || clickEvent.layerY;
        
        clusterPanelPosition.value = {
            x: x - 230, // 增加偏移量，显示在右侧
            y: y - 100 // 向上偏移更多，避免遮挡
        };
    }
    
    // 显示面板
    showClusterPanel.value = true;
    
    // 创建新的隐藏函数
    currentHidePanel = (e) => {
        if (!clusterDetailPanel.value?.contains(e.target)) {
            showClusterPanel.value = false;
            document.removeEventListener('click', currentHidePanel);
            currentHidePanel = null;
        }
    };
    
    setTimeout(() => {
        if (currentHidePanel) {
            document.addEventListener('click', currentHidePanel);
        }
    }, 100);
};
// 处理设备点击
const handleEquipmentClick = (orgId, orgName, level, count) => {
    console.log(`点击了${orgName}的${level === 1 ? '一' : '二'}级设备，数量：${count}`);

    currentOrgId.value = orgId;
    titles.value = orgName;

    if (count > 0) {
        openOneLevelEqu(level, count);
    }

    showClusterPanel.value = false;
};

// 处理悬停效果
const handleClusterItemHover = (event, isHover) => {
    event.target.style.backgroundColor = isHover ? 'rgba(4, 39, 142, 0.7)' : '';
};

const handleEquipmentHover = (event, isHover) => {
    event.target.style.textDecoration = isHover ? 'underline' : 'none';
};

// 修改地图点击事件处理
const handleMapClick = (params) => {
    if (params.componentType === 'series') {
        if (params.data.clusterData) {
            // 如果点击的是聚合点，展开显示详情
            showClusterDetail(params.data);
        } else {
            // 处理普通点击
            const orgId = params.data.orgId;
            if (orgId) {
                showOrgDetail(orgId);
            }
        }
    } else if (params.componentType === 'geo') {
        // 处理地图区域点击
        handleMapRegionClick(params);
    }
};
// 在地图初始化完成后添加事件监听
const initMapEvents = () => {
    if (!mapChart) return;

    // 添加点击事件
    mapChart.off('click');
    mapChart.on('click', handleMapClick);

    // 添加缩放事件
    mapChart.off('georoam');
    mapChart.on('georoam', () => {
        // 如果是省级或市级地图，更新散点聚合
        if (currentLevel.value === 'province' || currentLevel.value === 'city') {
            if (scatterData.value && scatterData.value.length > 0) {
                updateMapScatter(scatterData.value);
            }
        }
    });
};


</script>

<style lang="less" scoped>
.scale-outer {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #0a2377;
    /* 可自定义背景色 */
}



/* 顶部悬浮统计栏样式 - 使用特定前缀避免影响其他组件 */
.map-floating-stats {
    width: 100%;
    display: flex;
    text-align: center;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    // padding: 0 20px;
    position: relative;
    z-index: 2;
    /* 添加水平内边距 */
}

.map-top-controls {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    max-width: 1400px;
    /* 与统计栏保持一致 */
    margin-bottom: 10px;
    // padding: 0 20px;
    /* 添加水平内边距 */
}

.map-region-selector {
    // background: linear-gradient(to right, rgba(0, 48, 143, 0.7), rgba(0, 132, 254, 0.7));
    background-image: url(@/assets/equipment/map/shaixuan.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 120px;
    height: 40px;
    color: #fff;
    padding: 5px 15px;
    border-radius: 0 0 5px 5px;
    display: flex;
    align-items: center;
    font-size: 20px;
    width: 120px;
    justify-content: center;
    margin-right: 20px;

    span {
        margin-right: 5px;
    }
}

.map-world-map-toggle {
    color: #00F8FC;
    display: flex;
    align-items: center;
    font-size: 20px;

    span {
        margin-left: 5px;
    }
}

.map-stats-bar {
    width: 100%;
    max-width: 1400px;
    /* 增加最大宽度 */
    display: flex;
    justify-content: space-between;
    background-image: url('@/assets/equipment/map/main.svg');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    // background: linear-gradient(to right, rgba(0, 48, 143, 0.3), rgba(0, 132, 254, 0.3));
    // border: 1px solid rgba(0, 132, 254, 0.5);
    // border-radius: 5px;
    padding: 0;
    position: relative;
    height: 80px;
    // box-shadow: 0 0 10px rgba(0, 132, 254, 0.3);
    margin: 0 auto;
    /* 居中显示 */
}

.map-stat-item {
    display: flex;
    align-items: center;
    position: relative;
    flex: 1;
    justify-content: space-evenly;
    // padding: 0 3.9375rem;
    /* 增加内边距 */

    &:not(:last-child)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 70%;
        // background-image: url('@/assets/equipment/map/dash-line.png');
        background-repeat: repeat-y;
    }
}

.map-stat-icon {
    width: 24px;
    height: 24px;
    margin-right: 5px;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
        width: 24px;
        height: 24px;
    }
}

.map-stat-content {
    display: flex;
    flex-direction: column;
    width: 100%;

}

.map-stat-content1 {
    border-right: 1px solid rgba(255, 255, 255, 0.3);
}

.map-stat-label {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
    line-height: 1;
}

.map-stat-value {
    font-size: 24px;
    font-weight: 500;
    color: #00F8FC;
    line-height: 1.2;
    margin-top: 5px;
}

.map-top-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    // padding: 0 20px;
}

.map-controls-right {
    display: flex;
    align-items: center;
}

.map-world-map-toggle {
    //background-image: url(@/assets/equipment/map/qiehuan.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    // width: 180px;
    height: 40px;
    color: #fff;
    padding: 5px 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-right: 15px;
    color: #0085FF;

    span {
        margin-left: 5px;
    }
}

.map-fullscreen-toggle {
    // background-image: url(@/assets/equipment/map/qiehuan.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 120px;
    height: 40px;
    color: #fff;
    padding: 5px 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    span {
        margin-left: 5px;
    }
}

.dashboard-container {
    width: 100%;
    height: 100vh;
    background: url('@/assets/equipment/map/back.png') no-repeat;
    background-size: 100% 100%;
    color: #fff;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.header-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    background-color: #0a2377;
    color: white;
    padding: 0 20px;

    .logo-title {
        width: 30%;
        min-width: 250px;
    }

    .nav-tabs {
        width: 40%;
    }

    .user-controls {
        width: 30%;
        min-width: 250px;
        // padding-left: 40px;
        justify-content: end;

        img {
            width: 17px;
            height: 17px;
        }

        .homes {
            // style="font-size: 0.2133rem; margin-left: 10px;"
            font-size: 22px;
            margin-left: 10px
        }
    }
}

.logo-title {
    display: flex;
    align-items: center;

    .logo-img {
        height: 40px;
    }

    .platform-name {
        font-size: 18px;
        font-weight: 500;
    }
}

.nav-tabs {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    flex: 1;
    max-width: 650px;

    .tab {
        width: 130px;
        text-align: center;
        font-size: 20px;
        cursor: pointer;
        height: 60px;
        line-height: 60px;
        transition: all 0.3s;
        color: rgba(255, 255, 255, 0.8);

        &.active {
            color: rgba(23, 109, 244, 1);
            position: relative;
            background: rgba(23, 109, 244, 0.2);

            &:after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 3px;
                background-color: rgba(23, 109, 244, 1);
            }
        }

        &:hover:not(.active) {
            background-color: rgba(255, 255, 255, 0.1);
        }
    }
}

.user-controls {
    display: flex;
    align-items: center;
    padding: 0px 10px;

    .login-button {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 50px;
        background: linear-gradient(270deg, rgba(0, 140, 255, 0.58) 0%, rgba(0, 133, 255, 0.25) 50%, rgba(0, 157, 255, 0.09) 100%);
        border: none;
        border-radius: 24px !important;
        height: 42px;
        padding: 0 20px;
        box-shadow: 0 4px 12px rgba(78, 115, 248, 0.3);
        color: rgba(0, 133, 255, 1);

        .anticon {
            margin-right: 8px;
            font-size: 18px;
            color: rgba(0, 133, 255, 1);
        }

        span {
            font-size: 16px;
            font-weight: 500;
            color: rgba(0, 133, 255, 1)
        }
    }

    .notification {
        position: relative;
        margin-right: 20px;
        font-size: 20px;
        cursor: pointer;
        color: rgba(255, 255, 255, 0.8);

        .badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: #ff4d4f;
            color: white;
            font-size: 12px;
            height: 16px;
            min-width: 16px;
            line-height: 16px;
            text-align: center;
            border-radius: 8px;
            padding: 0 4px;
        }
    }

    .user-info {
        display: flex;
        align-items: center;
        cursor: pointer;
        margin-left: 70px;

        .ant-avatar {
            border: 2px solid rgba(255, 255, 255, 0.3);
            width: 40px !important;
            height: 40px !important;
        }

        span {
            margin-left: 8px;
            margin-right: 4px;
            font-size: 16px;
        }

        .down-icon {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }
    }
}

.dashboard-content {
    // flex: 1;
    display: flex;
    width: 100%;
    position: relative;
    overflow: hidden;
    height: 100%;
    padding: 20px 20px 40px 20px;
}

.left-panel {
    z-index: 2;
    width: 23%;
    height: 100%;
    // padding: 4px 23px;

    &.panel-hidden {
        transform: translateX(-29rem);
    }

    .toggle-btn {
        right: 0.5rem;
    }
}

/* Floating panels */
.floating-panel {
    position: absolute;
    top: 1rem;
    bottom: 1rem;
    width: 20rem;
    background-color: rgba(13, 43, 107, 0.7);
    border-radius: 0.5rem;
    backdrop-filter: blur(0.5rem);
    box-shadow: 0 0 1rem rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
    z-index: 10;

    &.left-panel {
        left: 1rem;
        transform: translateX(0);
        z-index: 2;

        &.panel-hidden {
            transform: translateX(-29rem);
        }

        .toggle-btn {
            right: 0.5rem;
        }
    }

    &.right-panel {
        right: 1rem;
        transform: translateX(0);

        &.panel-hidden {
            transform: translateX(29rem);
        }

        .toggle-btn {
            left: 0.5rem;
        }
    }
}

.panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .panel-title {
        font-size: 1.125rem;
        font-weight: 500;
        color: #fff;
    }

    .toggle-btn {
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.1);
        border: none;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        &:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
    }
}

.panel-content {
    flex: 1;
    // overflow-y: auto;
    // padding: 0.75rem;

    &::-webkit-scrollbar {
        width: 0.375rem;
    }

    &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 0.375rem;
    }
}


/* Floating stats bar */
.floating-stats {
    position: absolute;
    top: 1rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 5;
    width: 80%;
    max-width: 50rem;
}

.stats-bar {
    display: flex;
    justify-content: space-between;
    background-color: rgba(13, 43, 107, 0.7);
    border: 1px solid rgba(30, 58, 123, 0.8);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    backdrop-filter: blur(0.5rem);

    .stat-item {
        text-align: center;

        .stat-label {
            font-size: 0.75rem;
            color: #a0a8b9;
        }

        .stat-value {
            font-size: 1.125rem;
            font-weight: 500;
            color: #4fc3f7;
        }
    }
}


/* Data cards */
.data-card {
    background-color: rgba(13, 43, 107, 0.5);
    border: 1px solid rgba(30, 58, 123, 0.8);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    overflow: hidden;

    .card-header {
        // padding: 0.75rem 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        .card-title {
            font-size: 20px;
            font-weight: 400;
            color: #fff;
        }
    }

    .card-content {
        // padding: 5px;
        height: calc(100% - 3.2rem);
    }
}

/* Add other specific card content styles as needed */
/* 左侧面板设备状态卡片样式 */
.device-status-card {
    width: 100%;
    height: 30%;
    background: url('@/assets/equipment/map/biankuang.png') no-repeat;
    background-size: 100% 100%;
    position: relative;
    margin-bottom: 15px;
    padding: 0 5px 5px 5px;
    // flex: 1;

    // &::before {
    //     content: '';
    //     position: absolute;
    //     top: 0;
    //     left: 0;
    //     width: 100%;
    //     height: 100%;

    //     background-size: 100% 100%;
    //     pointer-events: none;
    // }
}

.card-header {
    // // height: 50px;
    // display: flex;
    // align-items: center;
    // // padding: 0 35px;
    // color: #ffffff;
    // font-size: 20px;
    // font-weight: 400;

    .header-icon {
        margin-right: 8px;
        color: #00f8fc;
    }
}

.card-content {
    // padding: 10px;
    height: calc(100% - 3.2rem);
}

.device-status-grid {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    // display: grid;
    // /* 使用网格布局 */
    // grid-template-columns: 1fr 1fr;
    // /* 2列 */
    // grid-template-rows: 1fr 1fr;
    // /* 2行 */
    // gap: 32px;
    // padding: 10px 0;

    // box-sizing: border-box;
    .one_contant {
        display: flex;
        align-items: center;
        height: 50%;
        padding-left: 30px;
        // margin-top: 10px;
    }
}

.status-item {
    display: flex;
    align-items: center;
    // justify-content: center;
    padding: 0;
    width: 50%;

    /* 移除内边距 */
}

/* 六边形图标 */
.status-icon.hexagon {
    // width: 50px;
    // /* 根据图例调整图标容器大小 */
    // height: 50px;
    margin-right: 8px;
    /* 调整图标和文字的间距 */
    display: flex;
    align-items: center;
    justify-content: center;
    // position: relative;

    // &::before {
    //     content: '';
    //     position: absolute;
    //     top: 0;
    //     left: 0;
    //     width: 100%;
    //     height: 100%;
    //     //background: url('@/assets/equipment/map/hexagon.png') no-repeat;
    //     background-size: contain;
    //     background-position: center;
    // }

    img {
        width: 50px;
        // width: 100px;
        // /* 调整内部图片大小 */
        // height: 100px;
        // object-fit: contain;
        /* 保持图片比例 */
        // position: relative;
        // z-index: 1;
    }
}

.online-dot {
    margin-right: 5px;
    font-size: 16px;
}

.divider-vertical {
    margin-top: -10px;
}

.status-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    /* 垂直居中 */
    line-height: 1.2;
    margin-left: 10px;
    /* 调整行高 */
}

.status-label {

    font-size: 16px;
    /* 调整字体大小 */
    margin-bottom: 2px;
    /* 调整标签和数字的间距 */
}

.status-count {
    display: flex;
    align-items: baseline;

    .count-number {
        font-size: 24px;
        /* 调整数字字体大小 */
        font-weight: 500;
        margin-right: 4px;
        /* 调整数字和单位的间距 */

        &.running {
            font-size: 24px;
            font-weight: 500;
            color: #00F8FC;
            line-height: 1.2;
        }

        &.maintenance {
            color: #00FC97;
        }

        &.disabled {
            color: #0084FE;
        }
    }

    .count-unit {
        color: rgba(255, 255, 255, 0.6);
        /* 调整颜色 */
        font-size: 14px;
        /* 调整单位字体大小 */
    }
}

/* 数据卡片通用样式 */
.data-card {
    margin-bottom: 20px;
}

/* 设备预警情况卡片样式 */
.device-warning-card {
    width: 100%;
    height: 37%;
    /* 调整高度为259px */
    background-size: 100% 100%;
    // position: relative;
    margin-bottom: 15px;
    background: url('@/assets/equipment/map/biankuang.png') no-repeat;
    background-size: 100% 100%;
    pointer-events: none;

    // &::before {
    //     content: '';
    //     position: absolute;
    //     top: 0;
    //     left: 0;
    //     width: 100%;
    //     height: 100%;
    //     background: url('@/assets/equipment/map/biankuang.png') no-repeat;
    //     background-size: 100% 100%;
    //     pointer-events: none;
    // }
}

.map-container {
    width: 54%;
    padding: 0 20px;
}

.warning-sections {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px;
}

.warning-section {
    display: flex;
    width: 49.5%;
    height: 100%;
    flex-direction: column;
    align-items: center;
    // padding: 20px 20px;
    // position: relative;
    justify-content: center;
    z-index: 999999;
    background: url('@/assets/equipment/map/bj1.svg') no-repeat;
    background-size: 100%;

    /* 中间地图容器样式 */
    .map-container {
        flex: 1;
        position: relative;
        height: 100%;
        overflow: hidden;
    }

    /* 地图图表样式 */
    .map-chart {
        width: 100%;
        height: 100vh;
        position: absolute;
        top: 0;
        left: 0px;
        z-index: 1;
    }

    // &:first-child::after {
    //     content: '';
    //     position: absolute;
    //     bottom: 0;
    //     left: 10%;
    //     width: 80%;
    //     height: 1px;
    //     background: rgba(0, 132, 254, 0.3);
    // }

    .section-title {
        font-size: 16px;
    }

    .section-stats {
        .divider-vertical {
            img {
                height: 105px;
            }
        }
    }

    .divider-horizontal {
        img {
            width: 160px;
        }
    }
}

.warning-section-margin {
    // margin-right: 10px;
}

.section-icon {
    margin: 10px 0 15px 0;
}

.icon-circle {
    // width: 60px;
    // height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    // position: relative;

    &.network {
        // background: radial-gradient(circle, #0066cc 0%, #003366 100%);
        box-shadow: 0 0 15px rgba(0, 102, 204, 0.5);
    }

    &.alarm {
        // background: radial-gradient(circle, #cc6600 0%, #993300 100%);
        box-shadow: 0 0 15px rgba(204, 102, 0, 0.5);
    }

    img {
        width: 95px;
        z-index: 999999;
        // height: 90px;
    }
}

.section-title {
    background: url('@/assets/equipment/map/titile.svg') no-repeat;
    background-size: 100% 100%;
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    padding: 5px 20px;
    border-radius: 2px;
    margin-bottom: 15px;
    text-align: center;
    width: 100%;
    z-index: 9999;
}

.section-stats {
    display: flex;
    justify-content: center;
    width: 100%;
    height: 70px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 10px;
}

.stat-label {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    margin-bottom: 5px;

    .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
        background-color: #999;
    }
}

.stat-value {
    font-size: 24px;
    font-weight: 500;

    &.online {
        color: #00F8FC;
    }

    &.offline {
        color: #FF7E00;
    }

    &.processed {
        color: #00F8FC;
    }

    &.unprocessed {
        color: #FF7E00;
    }
}

/* 为点添加不同颜色 */
.stat-label:has(+ .stat-value.online) .dot {
    background-color: #00F8FC;
}

.stat-label:has(+ .stat-value.offline) .dot {
    background-color: #FFC700;
}

.stat-label:has(+ .stat-value.processed) .dot {
    background-color: #00FC97;
}

.stat-label:has(+ .stat-value.unprocessed) .dot {
    background-color: #FF7E00;
}

/* 设备调拨次数分布卡片样式 */
.device-allocation-card {
    width: 100%;
    height: 33%;
    background: url('@/assets/equipment/map/biankuang.png') no-repeat;
    background-size: 100% 100%;
    padding: 0 5px;
    // position: relative;
    // margin-bottom: 20px;
    // flex: 1;

    // &::before {
    //     content: '';
    //     position: absolute;
    //     top: 0;
    //     left: 0;
    //     width: 100%;
    //     height: 100%;
    //     background-size: 100% 100%;
    //     pointer-events: none;
    // }
}

.card-header {
    // display: flex;
    // align-items: center;
    // padding: 15px 15px;
    background: url('@/assets/equipment/map/bj2.svg') no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: 50px;
    line-height: 45px;
    color: #ffffff;
    font-size: 20px;
    font-weight: 400;

    .title__eltit {
        margin-left: 30px;
    }

    .header-icon {
        margin-right: 8px;
        color: #00F8FC;
    }
}

.card-content {
    padding: 5px 0;
    height: calc(100% - 3.2rem);
}

.allocation-layout {
    display: flex;
    align-items: end;
    height: 100%;
    padding-bottom: 10px;
}

.chart-container {
    // position: relative;
    width: 100%;
    height: 220px;
}

.bar-chart {
    width: 100%;
    height: 100%;
}

.total-count {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;

    .total-label {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 4px;
    }

    .total-value {
        font-size: 24px;
        font-weight: 500;
        color: #00F8FC;
    }
}

.chart-legend {
    display: flex;
    flex-direction: column;
    width: 170px;
    height: 180px;
    overflow-y: auto;
    padding-right: 5px;
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.legend-marker {
    width: 10px;
    height: 10px;
    border-radius: 2px;
    margin-right: 8px;
}

.legend-label {
    flex: 1;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.legend-value {
    font-size: 12px;
    font-weight: 500;
    color: #ffffff;
    margin-left: 5px;
}

/* 右侧面板样式 */
.right-panel {
    // position: absolute;
    // padding: 4px 0px;
    // right: 20px;
    width: 23%;
    height: 100%;
    transition: transform 0.3s ease;
    z-index: 10;

    &.panel-hidden {
        transform: translateX(29rem);
    }
}

.panel-header {
    display: flex;
    align-items: center;
    padding: 10px;
    background: rgba(0, 32, 96, 0.7);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border: 1px solid rgba(0, 132, 254, 0.5);
    border-bottom: none;
}

.toggle-btn {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 84, 207, 0.5);
    border: 1px solid rgba(0, 132, 254, 0.5);
    border-radius: 4px;
    color: #fff;
    cursor: pointer;
    margin-right: 10px;
}

.panel-title {
    font-size: 16px;
    font-weight: 500;
    color: #fff;
}

.panel-content {
    height: 100%;
    // overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // padding: 10px 0;

    &::-webkit-scrollbar {
        width: 5px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(0, 132, 254, 0.5);
        border-radius: 5px;
    }
}

/* 设备成本总览卡片样式 */
.device-cost-card {
    width: 100%;
    height: 30%;
    background: url('@/assets/equipment/map/biankuang.png') no-repeat;
    background-size: 100% 100%;
    // position: relative;
    margin-bottom: 15px;
    padding: 0 5px;
    // flex: 1;

    // &::before {
    //     content: '';
    //     position: absolute;
    //     top: 0;
    //     left: 0;
    //     width: 100%;
    //     height: 100%;

    //     background-size: 100% 100%;
    //     pointer-events: none;
    // }
}

/* 成本概览样式 */
.cost-overview {
    display: flex;
    justify-content: space-around;
    padding: 0px 10px;
    // margin-top: 10px;
}

.cost-item {
    display: flex;
    align-items: center;
}

.cost-icon {
    // width: 40px;
    // height: 40px;
    // background: linear-gradient(135deg, rgba(0, 84, 207, 0.3), rgba(0, 132, 254, 0.3));
    // border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;

    img {
        width: 80px;
        // height: 60px;
    }
}

.cost-info {
    display: flex;
    flex-direction: column;
}

.cost-label {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: -9px;
}

.cost-value {
    display: flex;
    align-items: baseline;

    .value-number {
        font-size: 24px;
        font-weight: 500;
        color: #00F8FC;
    }

    .value-unit {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        margin-left: 4px;
    }
}

/* 分隔线样式 */
.divider-dashed {
    height: 1px;
    background-image: linear-gradient(to right, rgba(0, 132, 254, 0.5) 50%, transparent 50%);
    background-size: 8px 1px;
    background-repeat: repeat-x;
    margin: 5px 15px;
}

/* 成本明细样式 */
.cost-details {
    display: flex;
    justify-content: space-between;
    padding: 0 15px;
}

.detail-column {
    width: 48%;
}

.detail-item {
    display: flex;
    align-items: center;
    // margin-bottom: 5px;
}

.detail-label {
    width: 50px;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
}

.detail-bar-container {
    flex: 1;
    height: 5px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    margin: 0 8px;
    overflow: hidden;
}

.detail-bar {
    height: 100%;
    background: linear-gradient(90deg, #00F8FC, #0084FE);
    border-radius: 3px;
}

.detail-percentage {
    width: 35px;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    text-align: right;
}

/* 设备故障分析和设备使用效率卡片样式 */
// .device-fault-card,
// .device-efficiency-card {
//     // width: 358px;
//     // height: 250px;
//     background: url('@/assets/equipment/map/zuoshang.png') no-repeat;
//     background-size: 100% 100%;
//     // position: relative;
//     // margin-bottom: 20px;

//     &::before {
//         content: '';
//         position: absolute;
//         top: 0;
//         left: 0;
//         width: 100%;
//         height: 100%;

//         background-size: 100% 100%;
//         pointer-events: none;
//     }
// }

/* 设备资产概况卡片样式 */
.device-asset-card {
    width: 100%;
    // flex: 1;
    height: 30%;
    background: url('@/assets/equipment/map/biankuang.png') no-repeat;
    background-size: 100% 100%;
    // position: relative;
    margin-bottom: 15px;
    padding: 0 5px;
}

.asset-overview {
    display: flex;
    height: 100%;
    position: relative;
    // padding: 20px 0;
    // position: relative;
    // height: 220px;
    // padding: 10px 0 0;
    // margin-top: -40px;
    // left: 10px;
    // top: 25px;
}

/* 左侧数据区域 */
.asset-left {
    // width: 40%;
    position: absolute;
    left: -20px;
    top: 1px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    // padding: 20px 0 0;
}

/* 中央区域 */
.asset-center {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

/* 右侧数据区域 */
.asset-right {
    // width: 40%;
    position: absolute;
    right: -20px;
    top: 1px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    // padding: 20px 0 0;
}

/* 资产项样式 */
.asset-item {
    // position: relative;
    text-align: center;
}

/* 上部资产项 */
.asset-item.top {
    // margin-bottom: 20px;
    // margin-top: 0px;
    // margin-top: 10px;
    // margin-bottom: auto;
}

/* 下部资产项 */
.asset-item.bottom {
    // margin-bottom: 10px;
}

/* 左侧资产项文本右对齐 */
.asset-left .asset-item {
    text-align: right;
    height: 42%;
    // margin-bottom: 8px;
    // padding-right: 37px;
}

/* 右侧资产项文本左对齐 */
.asset-right .asset-item {
    text-align: left;
    height: 42%;
    // padding-left: 25px;
    // margin-bottom: 8px;
}

.asset-label {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 4px;
}

.asset-value {
    font-size: 22px;
    font-weight: 500;

    &.blue {
        color: #00A7FF;
    }

    &.cyan {
        color: #00F8FC;
    }

    .unit {
        font-size: 14px;
        margin-left: 2px;
    }
}

/* 中央图形样式 */
.center-graphic {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 中央图片 */
.center-image {
    // width: 100%;
    height: 75%;
    position: relative;
    z-index: 3;
}

/* 圆形动画 */
// .circle-outer,
// .circle-inner {
//     position: absolute;
//     border-radius: 50%;
//     border: 1px solid #00A7FF;
//     opacity: 0.6;
// }

// .circle-outer {
//     width: 90px;
//     height: 90px;
//     animation: pulse 3s infinite;
// }

// .circle-inner {
//     width: 60px;
//     height: 60px;
//     animation: pulse 3s infinite 1.5s;
// }

@keyframes pulse {
    0% {
        transform: scale(0.9);
        opacity: 0.8;
    }

    50% {
        transform: scale(1.1);
        opacity: 0.4;
    }

    100% {
        transform: scale(0.9);
        opacity: 0.8;
    }
}

/* 连接线样式 */
.connector-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.connector {
    position: absolute;
    background-color: #00A7FF;
    opacity: 0.8;
}

/* 左上连接线 */
.connector.top-left {
    width: 40px;
    height: 1px;
    top: 25%;
    left: 40%;
    transform: rotate(-45deg);
}

.connector.top-left::before,
.connector.top-left::after {
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #00F8FC;
    top: -2.5px;
}

.connector.top-left::before {
    left: -3px;
}

.connector.top-left::after {
    right: -3px;
}

/* 右上连接线 */
.connector.top-right {
    width: 40px;
    height: 1px;
    top: 25%;
    right: 40%;
    transform: rotate(45deg);
}

.connector.top-right::before,
.connector.top-right::after {
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #00F8FC;
    top: -2.5px;
}

.connector.top-right::before {
    left: -3px;
}

.connector.top-right::after {
    right: -3px;
}

/* 左下连接线 */
.connector.bottom-left {
    width: 40px;
    height: 1px;
    bottom: 25%;
    left: 40%;
    transform: rotate(45deg);
}

.connector.bottom-left::before,
.connector.bottom-left::after {
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #00F8FC;
    top: -2.5px;
}

.connector.bottom-left::before {
    left: -3px;
}

.connector.bottom-left::after {
    right: -3px;
}

/* 右下连接线 */
.connector.bottom-right {
    width: 40px;
    height: 1px;
    bottom: 25%;
    right: 40%;
    transform: rotate(-45deg);
}

.connector.bottom-right::before,
.connector.bottom-right::after {
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #00F8FC;
    top: -2.5px;
}

.connector.bottom-right::before {
    left: -3px;
}

.connector.bottom-right::after {
    right: -3px;
}

// .asset-overview {
//     // position: relative;
//     // height: 220px;
//     display: flex;
//     justify-content: center;
//     align-items: center;
// }

/* 中央图标 */
.asset-center-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
}

.hexagon-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #00F8FC, #0084FE);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.yuan-symbol {
    color: #ffffff;
    font-size: 36px;
    font-weight: 500;
}

/* 环形动画 */
// .circle-animation {
//     position: absolute;
//     border-radius: 50%;
//     border: 2px solid #0084FE;
//     top: 50%;
//     left: 50%;
//     transform: translate(-50%, -50%);

//     &.outer {
//         width: 140px;
//         height: 140px;
//         border-color: rgba(0, 132, 254, 0.5);
//         animation: rotate 20s linear infinite;
//     }

//     &.inner {
//         width: 100px;
//         height: 100px;
//         border-color: rgba(0, 248, 252, 0.5);
//         animation: rotate 15s linear infinite reverse;
//     }
// }

// @keyframes rotate {
//     from {
//         transform: translate(-50%, -50%) rotate(0deg);
//     }

//     to {
//         transform: translate(-50%, -50%) rotate(360deg);
//     }
// }

/* 四个角的资产项 */
.asset-item {
    // position: absolute;
    width: 140px;
    // height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    &.top-left {
        top: 20px;
        left: 20px;
        text-align: left;
    }

    &.top-right {
        top: 20px;
        right: 20px;
        text-align: right;
    }

    &.bottom-left {
        bottom: 20px;
        left: 20px;
        text-align: left;
    }

    &.bottom-right {
        bottom: 20px;
        right: 20px;
        text-align: right;
    }
}

.asset-label {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: -5px;
}

.asset-value {
    font-size: 22px;
    font-weight: 500;

    &.blue {
        color: #0084FE;
    }

    &.cyan {
        color: #00F8FC;
    }

    .unit {
        font-size: 14px;
        margin-left: 4px;
    }
}

/* 连接线背景 - 可以替换为实际图片 */
.connection-lines-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    //background: url('@/assets/equipment/map/connection-lines.png') no-repeat center;
    background-size: contain;
    z-index: 1;
}


/* 连接点样式 */
.connection-dot {
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #00F8FC;
    z-index: 3;
}

.top-left-dot {
    bottom: 0;
    right: 0;
}

.top-right-dot {
    bottom: 0;
    left: 0;
}

.bottom-left-dot {
    top: 0;
    right: 0;
}

.bottom-right-dot {
    top: 0;
    left: 0;
}

/* 设备故障统计卡片样式 */
.device-fault-card {
    width: 100%;
    height: 40%;
    background: url('@/assets/equipment/map/biankuang.png') no-repeat;
    background-size: 100% 100%;
    position: relative;
    padding: 0 5px;
    // margin-bottom: 15px;
    // flex: 1;

    // &::before {
    //     content: '';
    //     position: absolute;
    //     top: 0;
    //     left: 0;
    //     width: 100%;
    //     height: 100%;

    //     background-size: 100% 100%;
    //     pointer-events: none;
    // }
}

/* 故障概览区域 */
.fault-overview {
    display: flex;
    justify-content: space-evenly;
    padding: 0 9px;
}

.fault-stat-item {
    display: flex;
    align-items: center;
}

.fault-icon {
    width: 40px;
    height: 40px;
    margin-right: 22px;

    /* 如果有图标，可以添加背景图 */
    /* background: url('@/assets/equipment/icons/fault-icon.png') no-repeat center; */
    /* background-size: contain; */
    img {
        width: 60px;
    }
}

.fault-info {
    display: flex;
    flex-direction: column;
}

.fault-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: -10px;
}

.fault-value {
    display: flex;
    align-items: baseline;
}

.value-number {
    font-size: 24px;
    font-weight: 500;

    &.cyan {
        color: #00F8FC;
    }
}

.value-unit {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-left: 4px;
}

/* 故障分布区域 */
.fault-distribution {
    padding: 0 15px;
}

.distribution-title {
    text-align: center;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);

    position: relative;

    &::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 2px;
        background: #00F8FC;
    }
}

.distribution-chart {
    // height: 90px;

}

.fault-types {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: -10px;
}

.fault-type-item {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    margin: 0 8px;
    white-space: nowrap;
}


.fault-distribution-chart {
    height: 120px;
    width: 100%;
}

.Mo_ban {
    width: 350px;
    height: 185px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    box-sizing: border-box;
    border: 1px solid #FFFFFF;
    display: flex;
    position: relative;
}

#Mo_ban_img {
    width: 152px;
    height: 100%;
}

#Mo_ban_box {
    margin-left: 10px;
    width: 200px;
    height: 100%;
    font-size: 16px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0px;
    color: #FFFFFF;
    margin-left: 20px;
}

#Mo_ban_box>div {
    margin-bottom: 10px;
}

#Mo_ban_look {
    display: inline-block;
    padding: 5px 21px;
    font-size: 15px;
    cursor: pointer;
    text-align: center;
    text-decoration: none;
    outline: none;
    color: #fff;
    background-color: rgb(16, 185, 214);
    border: none;
    border-radius: 27px;
}

.amap-info-content {
    background: transparent !important;
}

:deep(.ant-dropdown) {
    background: transparent !important;
}

:deep(.ant-dropdown-menu) {
    background: transparent !important;
    box-shadow: none !important;
}

.map-level-buttons {
    position: absolute;
    bottom: 100px;
    right: 480px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: center;
}

.map-level-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(0, 48, 102, 0.7);
    border: 1px solid rgba(147, 235, 248, 0.4);
    color: #ffffff;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.map-level-button.active {
    background: #0066ff;
    box-shadow: 0 0 15px rgba(0, 102, 255, 0.6);
    border-color: rgba(255, 255, 255, 0.6);
}

.map-level-button:hover {
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(0, 102, 255, 0.4);
}

.map-level-button .active {
    background: #0066ff;
    box-shadow: 0 0 15px rgba(0, 102, 255, 0.6);
    border-color: rgba(255, 255, 255, 0.6);
}

/deep/ .ele-notice-trigger.ant-badge {
    margin-left: 0 !important;
    font-size: 24px !important;
}

.custom-table {
    :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
        background-color: #FFFFFF !important;
    }
}


/deep/ .ant-badge-count {
    z-index: auto;
    min-width: 20px;
    height: 20px;
    padding: 0 6px;
    color: #fff;
    font-weight: normal;
    font-size: 12px;
    line-height: 20px;
    white-space: nowrap;
    text-align: center;
    background: var(--highlight-color);
    border-radius: 20px;
    box-shadow: 0 0 0 1px var(--shadow-color-inverse);
    margin-top: 10px !important;
    right: -2px !important;
}

// .custom-tooltip-style{
//   background-color: #193170;
// }

.selectbj {
    width: 250px;
    background: url('@/assets/equipment/map/selectbj.svg') no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    :deep(.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
        background-color: rgba(9, 57, 151, .1) !important;
        border: 0;
        box-shadow: none !important;

    }

    .ant-select {
        color: white;
    }

    :deep(.ant-select-selection-placeholder) {
        color: white !important;
    }

    :deep(.ant-select-arrow) {
        color: white !important;
        right: 35px;
    }

    .selectbj1 {
        width: 200px;
        margin-left: 20px;
    }
}
</style>


<style lang="less">
.cityOpt {
    background-color: rgba(4, 39, 142, 0.95) !important;
    color: white !important;

    .ant-cascader-menu {
        border-right: 0 !important;

        .ant-cascader-menu-item:hover {
            background: rgba(23, 109, 244, 0.8);
        }

        .ant-cascader-menu-item-expand .ant-cascader-menu-item-expand-icon,
        .ant-cascader-menu-item-loading-icon {
            color: white !important;
        }

        .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled),
        .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {
            background: rgba(23, 109, 244, 0.8);
        }
    }
}

#cluster-left-panel {
    background: url('@/assets/equipment/map/hoverbj.svg') no-repeat !important;
    background-size: 100% !important;

}

#cluster-left-panel ::-webkit-scrollbar {
    width: 14px !important;
}

#cluster-left-panel ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3) !important;
    border-radius: 7px !important;
}

#cluster-left-panel ::-webkit-scrollbar-thumb {
    background: #FF0000 !important;
    border-radius: 7px !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
}

#cluster-left-panel ::-webkit-scrollbar-thumb:hover {
    background: #FF3333 !important;
}
</style>






