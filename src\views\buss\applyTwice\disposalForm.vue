<template>
  <div class="ele-body">
    <div class="equipment-acceptance">

      <div class="form-title">设备处置单</div>

      <!-- 申请信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          经办信息
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">经办人</div>
            <div class="value">
              {{ formData.handledUserStr }}
            </div>
            <!-- <a-input v-model:value="formData.handledUserName" disabled /> -->
          </div>

          <div class="form-item">
            <div class="label">经办单位</div>
            <div class="value">
              {{ formData.handledOrgStr }}
            </div>
            <!-- <a-input v-model:value="formData.handledDeptName" disabled /> -->
          </div>
          <div class="form-item">
            <div class="label">经办日期</div>
            <div class="value">
              {{ formData.applyDate }}
            </div>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item" data-field="businessTitle">
            <div class="label required">业务标题</div>
            <a-input v-model:value="formData.businessTitle" placeholder="请输入业务标题" maxlength="16" />
          </div>
          <div class="form-item" data-field="assessmentOrg">
            <div class="label required">评估单位</div>
            <a-input v-model:value="formData.assessmentOrg" placeholder="请输入评估单位" />
          </div>
          <div class="form-item" data-field="acquirerName">
            <div class="label required">收购方名称</div>
            <div class="value">
              <a-input v-model:value="formData.acquirerName" placeholder="请输入收购方名称" />
            </div>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item" data-field="disposalDate">
            <div class="label required">处置日期</div>
            <div class="value">
              <a-date-picker v-model:value="formData.disposalDate" placeholder="选择日期" style="width: 100%"
                value-format="YYYY-MM-DD" :class="['date-picker']" />
            </div>
          </div>
          <div class="form-item" data-field="disposalType">
            <div class="label required">处置方式</div>
            <div class="value">
              <a-select v-model:value="formData.disposalType" placeholder="请选择处置方式">
                <!-- <a-select-option value="">全部</a-select-option> -->
                <a-select-option v-for="item in disposalTypeList" :key="item.label" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>
          <div class="form-item" data-field="disposeFileList">
            <div class="label required">处置资料</div>
            <div class="value" ref="containerRef">

              <!-- <template v-if="formData.disposeFileList.length == 0"> -->
                <InputUploader :max-count="10" button-text="上传文件"
                  :accept-types="['.jpg', '.jpeg', '.png', '.mp4', '.mov', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', '.pdf']"
                  :initial-files="formData.disposeFileList" :show-border="true" @file-uploaded="onTechFileUploaded"
                  @file-removed="onTechFileRemoved" @upload-status-change="onUploadStatusChange" :backgroundColor="'#fff'" :width="containerWidth"/>
              <!-- </template> -->
              <!-- <div v-else class="tech-file-item">
                <div class="file-info">
                  <file-outlined />
                  <a :href="formData.disposeFileList[0].fileUrl" target="_blank" class="file-link">
                    {{ formData.disposeFileList[0].fileOriginName }}
                  </a>
                </div>
                <close-outlined class="delete-icon" @click="removeTechFile" />
              </div> -->
            </div>
          </div>
        </div>
      </div>

      <!-- 设备信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          设备明细
        </div>

        <div data-field="tables">
          <div style="display: flex;justify-content: space-between;align-items: center;">
            <div style="display: flex;">
              <a-button type="link" style="margin-bottom: 5px; padding-left: 0; display: flex; align-items: center;"
                @click="goToregisteredList">
                <i class="iconfont icon-sbbf" style="margin-right: 6px;height: 100%;"></i>
                报废设备选择
              </a-button>
              <a-button type="link" style="margin-bottom: 5px; padding-left: 0; display: flex; align-items: center;"
                @click="goTodeviceList">
                <i class="iconfont icon-equipment" style="margin-right: 6px;height: 100%;"></i>
                在籍设备选择
              </a-button>
            </div>
            <div style="cursor: pointer;" @click="handleClickright">
              <!-- <span>点击到右侧，填写报废信息</span> -->
              <img style="margin-left: 10px; height: 34px;" src="@/assets/images/btn2.svg" alt="">
            </div>
          </div>

          <a-table :columns="columns" :data-source="formData.bussDisposalEquipmentList" bordered :pagination="false"
            :scroll="{ x: 'max-content' }" class="custom-table">

            <template #bodyCell="{ column, index, record }">
              <template v-if="column.dataIndex === 'useOrg'">
                <div class="form-item" style="margin-bottom: 0;">
                  <a-tree-select v-model:value="record.useOrg" show-search
                    :dropdown-style="{ maxHeight: '440px', overflow: 'auto' }" placeholder="请选择使用单位"
                    :tree-data="orgList" :field-names="{ label: 'name', value: 'id' }" tree-node-filter-prop="name"
                    style="width: 100%;" :treeExpandedKeys="expandedKeys" @treeExpand="handleTreeExpand">
                    <template #title="node">
                      <span @click="(e) => useOrgchange(e, node)" style="display: inline-block; width: 100%;">
                        {{ node.name }}
                      </span>
                    </template>
                  </a-tree-select>
                </div>
              </template>
              <template v-if="column.dataIndex === 'storageLocationStr'">
                <div class="form-item" style="margin-bottom: 0;">
                  <a-input v-model:value="record.storageLocationStr" placeholder="请输入存放地点" />
                  <!-- <a-select v-model:value="record.storageLocationStr" placeholder="全部" style="width: 100%;">
                    <a-select-option v-for="item in record.cfddList" :key="item.label" :value="item.value">
                      {{ item.label }}
                    </a-select-option>
                  </a-select> -->
                </div>
              </template>
              <template v-if="column.dataIndex === 'scrapYear'">
                <div class="form-item" style="margin-bottom: 0;">
                  <a-date-picker v-model:value="record.scrapYear" picker="year" placeholder="请选择年份" value-format="YYYY"
                    style="width: 100%" />
                </div>
              </template>
              <template v-if="column.dataIndex === 'assessmentMoney'">
                <div class="form-item" style="margin-bottom: 0;">
                  <a-input-number :min="0" :step="0.01" :precision="2" v-model:value="record.assessmentMoney"
                    placeholder="请输入评估金额" />
                </div>
              </template>
              <template v-if="column.dataIndex === 'disposalMoney'">
                <div class="form-item" style="margin-bottom: 0;">
                  <a-input-number :min="0" :step="0.01" :precision="2" v-model:value="record.disposalMoney"
                    placeholder="请输入处置金额" />
                </div>
              </template>
              <template v-if="column.dataIndex === 'disposalTaxRate'">
                <div class="form-item" style="margin-bottom: 0;">
                  <a-input-number v-model:value="record.disposalTaxRate" placeholder="请输入税率" />
                </div>
              </template>
              <template v-if="column.dataIndex === 'disposalTaxeMoney'">
                <div class="form-item" style="margin-bottom: 0;">
                  <a-input-number :min="0" :step="0.01" :precision="2" v-model:value="record.disposalTaxeMoney"
                    placeholder="请输入税金" />
                </div>
              </template>
              <template v-if="column.dataIndex === 'disposalRemark'">
                <div class="form-item" style="margin-bottom: 0;">
                  <!-- <a-input v-model:value="record.disposalRemark" placeholder="请输入备注" /> -->
                  <new-input v-model:value="record.disposalRemark" :placeholder="'请输入备注'"/>
                </div>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" danger @click="handleDelete(record)" style="padding: 0;">
                    <i class="iconfont icon-delete" style="margin-right: 6px;"></i>
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </div>

      <!-- 附件 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          附件
        </div>


        <FileUploader :max-size="5" :max-count="10" button-text="上传文件"
          :accept-types="['.jpg', '.jpeg', '.png', '.mp4', '.mov', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', '.pdf']"
          :initial-files="fileViewList" @file-uploaded="onFileUploaded" @file-removed="onFileRemoved"
          @upload-status-change="onUploadStatusChange" />
      </div>

      <!-- 申请信息 -->
      <!-- <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          备注
        </div>
        <div>
          <div class="form-item">
           
            <div class="value">
              <a-textarea :rows="4" v-model:value="formData.remark" placeholder="请输入补充说明" />
            </div>
          </div>
        </div>
      </div> -->
      <!-- 底部按钮 -->
      <div class="bottom-buttons">
        <!-- <a-button @click="handleSave" :loading="saving" class="save-btn">保存</a-button> -->
        <a-button type="primary" @click="handleSubmit" :loading="submitting" :disabled="isUploading"
          class="submit-btn">提交</a-button>
      </div>
    </div>
    <deviceList-edit v-model:visible="showEdit" @getList="getList" :data="formData.bussDisposalEquipmentList"
      @done="reload" v-if="showEdit"></deviceList-edit>
    <registeredList-edit v-model:visible="showEdit1" @getList1="getList1" :data="formData.bussDisposalEquipmentList"
      @done="reload" v-if="showEdit1"></registeredList-edit>
  </div>
</template>
<script>
export default {
  name: 'BussApplyTwiceDisposalFormTwice',
}
</script>
<script setup>

import { ref, onMounted, nextTick,h } from 'vue';
import { message } from 'ant-design-vue';
import { equipmentScrappingApi } from '@/api/buss/equipmentScrappingApi';
import { FileUploadUrl } from '@/api/system/operation/FileApi';
import { getToken } from '@/utils/token-util';
import { API_BASE_PREFIX } from '@/config/setting';
import DeviceListEdit from '../registeredList/deviceList-edit.vue';
import registeredListEdit from '../registeredList/registeredList-edit.vue';
import { useUserStore } from '@/store/modules/user';
import { useRouter, useRoute } from 'vue-router';
import { EnumApi } from '@/api/common/enum';
import { UnitApi } from '@/api/common/UnitApi'
import FileUploader from '@/components/FileUploader/index.vue';
import InputUploader from '@/components/InputUploader/index.vue';
import newInput from '@/components/ipttext/newInput.vue'
const userStore = useUserStore();
const router = useRouter();
const loading = ref(false);
const saving = ref(false);
const submitting = ref(false);

// 业务标题和关联投资计划的独立字段
const investmentPlan = ref('');
const isTransfer = ref('1'); // 添加是否转入设备字段，默认为'1'（否）
const yearFormat = 'YYYY';
// 上传相关配置
const uploadUrl = `${API_BASE_PREFIX}${FileUploadUrl}?secretFlag=0`;
const headers = {
  Authorization: getToken()
};
const disposalTypeList = ref()
// 初始化枚举数据
const initEnumData = async () => {
  try {
    // 获取设备状态枚举
    const equConditionData = await EnumApi.getEnumList({ enumName: 'DisposalTypeEnum' });
    disposalTypeList.value = equConditionData.data;
  } catch (error) {
    message.error('获取枚举数据失败');
  }
};
initEnumData()
const columns = [
  {
    title: '序号',
    width: 80,
    customRender: ({ text, record, index }) => {
      return `${index + 1}`;
    }
  },
  { title: '设备编号', dataIndex: 'code', width: 100 },
  {
    title: '财务卡片编号',
    dataIndex: 'financialNumber',
    width: 130,
  },
  { title: '设备名称', dataIndex: 'equNameStr', width: 120 },
  { title: '设备类别/种类', dataIndex: 'equSubTypeStr', width: 120 },
  { title: '规格型号', dataIndex: 'equModelStr', width: 100, },
  {
    title: '型号备注',
    dataIndex: 'equModelInfo',
    width: 100,
  },
  { title: '管理单位', dataIndex: 'managementOrgStr', width: 120 },
  { title: '财务原值', dataIndex: 'financialOriginalValue', width: 120 },
  { title: '净值', dataIndex: 'netWorth', width: 120 },
  { title: '管理状态', dataIndex: 'managementStatusStr', width: 100 },
  { title: '设备状态', dataIndex: 'equConditionStr', width: 100 },
  { title: '生产厂家', dataIndex: 'manufacturer', width: 150 },
  { title: '出厂日期', dataIndex: 'productionDate', width: 120 },
  { title: '出厂编号', dataIndex: 'factoryNumber', width: 120 },
  { title: '设备类别', dataIndex: 'equTypeStr', width: 120 },
  { title: '设备种类', dataIndex: 'equSubTypeStr', width: 120 },
  { title: '设备性质', dataIndex: 'equNatureStr', width: 120 },
  { title: '固定资产分类', dataIndex: 'fixedAssetsStr', width: 120 },
  { title: '产权单位', dataIndex: 'propertyOrgStr', width: 120 },
  { title: '功率kw', dataIndex: 'power', width: 100 },
  { title: '设备型号编码', dataIndex: 'modelCode', width: 120 },
  { title: '购置年度', dataIndex: 'purchaseYear', width: 100 },
  {
    title: '使用单位',
    dataIndex: 'useOrg',
    width: 200,
  },
  {
    title: '存放地点',
    dataIndex: 'storageLocationStr',
    width: 160,
  },
  {
    title: '报废年份',
    dataIndex: 'scrapYear',
    width: 140,
  },
  {
    title: () => h('span', [
      h('span', { style: { color: 'red' } }, '*'),
      '评估金额(元)'
    ]),
    dataIndex: 'assessmentMoney',
    width: 140,
  },
  {
    title: () => h('span', [
      h('span', { style: { color: 'red' } }, '*'),
      '处置金额(元)'
    ]),
    dataIndex: 'disposalMoney',
    width: 140,
  },
  {
    title: () => h('span', [
      h('span', { style: { color: 'red' } }, '*'),
      '税率(%)'
    ]),
    dataIndex: 'disposalTaxRate',
    width: 140,
  },
  {
    title: () => h('span', [
      h('span', { style: { color: 'red' } }, '*'),
      '税金(元)'
    ]),
    dataIndex: 'disposalTaxeMoney',
    width: 140,
  },
  {
    title: '备注',
    dataIndex: 'disposalRemark',
    width: 200,
  },
  // { title: '设备状态', dataIndex: 'equConditionStr', width: 120 },
  // { title: '管理状态', dataIndex: 'managementStatusStr', width: 120 },
  // 右侧固定列
  { title: '操作', key: 'action', width: 80, fixed: 'right' }
]



// 是否显示编辑弹窗
const showEdit = ref(false)
const showEdit1 = ref(false)
// 当前编辑数据
const current = ref(null)
// 文件列表相关
const fileList = ref([]);
const fileViewList = ref([]);
const techFileList = ref([]); // 技术资料上传组件的文件列表
const techFileViewList = ref([]); // 技术资料显示列表
const KeyId = ref()
const getProcessDefinitionByKey = async () => {
  try {

    const id = await equipmentScrappingApi.getProcessDefinitionByKey({ key: 'equipment_disposal' });
    KeyId.value = id.data.id
  } catch (error) {

  }
}

// 在 script 部分添加上传状态变量

const isUploading = ref(false);

const onFileUploaded = (file) => {
  console.log('文件上传成功:', file);
  if (!fileList.value) fileList.value = [];
  if (!fileViewList.value) fileViewList.value = [];
  fileList.value.push(file);
  fileViewList.value.push(file);
};

const onFileRemoved = (file) => {
  console.log('文件已删除:', file);
  fileList.value = fileList.value.filter(f => f.uid !== file.uid);
  fileViewList.value = fileViewList.value.filter(f => f.fileId !== file.fileId);
};

const isShowbutton = ref(true);

const onTechFileUploaded = (file) => {
  console.log('技术文件上传成功:', formData.value.disposeFileList, file);
  if (!formData.value.disposeFileList) {
    formData.value.disposeFileList = [];
  }
  // if(formData.value.disposeFileList.length>0){
  //   formData.value.disposeFileList = [];
  // }
  formData.value.disposeFileList.push(file);
  techFileViewList.value.push(file);
  isShowbutton.value = false;
};

const onTechFileRemoved = (file) => {
  console.log('技术文件已删除:', file);
  formData.value.disposeFileList = formData.value.disposeFileList.filter(f => f.uid !== file.uid);
  techFileViewList.value = techFileViewList.value.filter(f => f.uid !== file.uid);
  isShowbutton.value = true;
};

// 添加上传状态变更处理函数
const onUploadStatusChange = (status) => {
  isUploading.value = status;
};
// 验证表单数据
const validateForm = () => {
  // 按顺序定义需要验证的字段
  const fieldsToValidate = [
    { field: 'businessTitle', label: '业务标题', type: 'input' },
    { field: 'assessmentOrg', label: '评估单位', type: 'input' },
    { field: 'acquirerName', label: '收购方名称', type: 'input' },
    { field: 'disposalDate', label: '处置日期', type: 'date' },
    { field: 'disposalDate', label: '处置方式', type: 'select' },
  ];

  // 依次验证每个字段
  for (const { field, label, type } of fieldsToValidate) {
    if (!formData.value[field]) {
      message.error(`请${type === 'select' ? '选择' : type === 'date' ? '选择' : '输入'}${label}`);

      const formItem = document.querySelector(`[data-field="${field}"]`);
      if (formItem) {
        // 滚动到可视区域，并确保元素在视图中间
        formItem.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // 使用 nextTick 确保 DOM 更新后再执行点击操作
        nextTick(() => {
          setTimeout(() => {
            switch (type) {
              case 'input': {
                const input = formItem.querySelector('input');
                input?.focus();
                break;
              }
              case 'select': {
                const select = formItem.querySelector('.ant-select-selector');
                select?.click();
                break;
              }
              case 'date': {
                const datePicker = formItem.querySelector('.date-picker');
                if (datePicker) {
                  // 先聚焦
                  const input = datePicker.querySelector('input');
                  input?.focus();
                  // 然后触发点击以打开日期选择面板
                  setTimeout(() => {
                    datePicker.click();
                  }, 100);
                }
                break;
              }
            }
          }, 500); // 等待滚动完成后再聚焦
        });
      }
      return false;
    }
  }
  return true;
};
// const validateForm1 = () => {
//   const fieldsToValidate1 = []
//   formData.value.bussDisposalEquipmentList.forEach((item, index) => {
//     let obj = { field: 'useOrg', label: '使用单位', type: 'input' }
//     fieldsToValidate1.push(obj)
//   });
//   for (const { field, label, type } of fieldsToValidate1) {
//     formData.value.bussDisposalEquipmentList.forEach((item, index) => {
//       if (!formData.value.bussDisposalEquipmentList[index][field]) {
//         message.error(`请${type === 'select' ? '选择' : type === 'date' ? '选择' : '输入'}${label}`);
//         console.log('field-----', field)
//         const formItem = document.querySelector(`[data-field="${field}${index}"]`);

//         console.log('formItem-----', formItem)
//         if (formItem) {
//           // 滚动到可视区域，并确保元素在视图中间
//           formItem.scrollIntoView({
//             behavior: 'smooth',
//             block: 'center'
//           });

//           // 使用 nextTick 确保 DOM 更新后再执行点击操作
//           nextTick(() => {
//             setTimeout(() => {
//               switch (type) {
//                 case 'input': {
//                   const input = formItem.querySelector('input');
//                   input?.focus();
//                   break;
//                 }
//                 case 'select': {
//                   const select = formItem.querySelector('.ant-select-selector');
//                   select?.click();
//                   break;
//                 }
//                 case 'date': {
//                   const datePicker = formItem.querySelector('.date-picker');
//                   if (datePicker) {
//                     // 先聚焦
//                     const input = datePicker.querySelector('input');
//                     input?.focus();
//                     // 然后触发点击以打开日期选择面板
//                     setTimeout(() => {
//                       datePicker.click();
//                     }, 100);
//                   }
//                   break;
//                 }
//               }
//             }, 500); // 等待滚动完成后再聚焦
//           });
//         }
//       }
//     });
//   }
// }

// 构建提交数据的方法
const buildRequestData = () => {
  let bussRegisteredEquipmentList = []
  let bussScrapedEquipmentList = []
  for (let i = 0; i < formData.value.bussDisposalEquipmentList.length; i++) {
    if (formData.value.bussDisposalEquipmentList[i].typeObj == '1') {
      bussRegisteredEquipmentList.push(formData.value.bussDisposalEquipmentList[i])
    }
    if (formData.value.bussDisposalEquipmentList[i].typeObj == '2') {
      bussScrapedEquipmentList.push(formData.value.bussDisposalEquipmentList[i])
    }

  }
  const requestData = {
    bussWorksheet: {
      name: formData.value.businessTitle,
      procInstanceId: '',
      isResubmit: true,
      id: ''
    },
    bussTransferForm: {
      applyTitle: formData.value.businessTitle,
      handledUser: formData.value.handledUser, // 经办人
      handledOrg: formData.value.handledOrg, // 经办单位
      applyDate: formData.value.applyDate, // 申请日期
      assessmentOrg: formData.value.assessmentOrg, // 评估单位
      acquirerName: formData.value.acquirerName, // 收购方式
      disposalDate: formData.value.disposalDate,//处置日期
      disposalType: formData.value.disposalType,//处置方式
      remark: formData.value.remark,// 备注
      disposeFileList: formData.value.disposeFileList,
      fileList: fileViewList.value, // 附件
    },
    // 设备基本信息，包含技术资料
    bussRegisteredEquipmentList: bussRegisteredEquipmentList,
    bussScrapedEquipmentList: bussScrapedEquipmentList,
  };
  console.log('requestData', requestData);


  if (datalistAll.value.bussWorksheet) {
    // requestData.bussWorksheet.id = ''
    // requestData.value.procInstanceId=""
    // requestData.value.isResubmit = true
    requestData.bussTransferForm.id = datalistAll.value.bussTransferForm.id
    requestData.bussTransferForm.worksheetId = datalistAll.value.bussTransferForm.worksheetId
  }



  console.log('提交的数据：', requestData);
  return requestData;
};
const resData = ref({})
// 保存方法
// const handleSave = async () => {
//   try {
//     saving.value = true;
//     const params = buildRequestData();
//     let res = await equipmentScrappingApi.savecz(params);
//     console.log('res', res)
//     if (res.success) {
//       message.success('保存成功');
//     } else {
//       message.error(res.message || '保存失败');
//     }
//   } catch (error) {
//     message.error('保存失败：' + error.message);
//   } finally {
//     saving.value = false;
//   }
// };

const handleSave = async () => {
  try {
    saving.value = true;
    let params = {};
    params = buildRequestData()
    // if (datalistAll.value.bussTransferForm) {
    //   params.bussWorksheet.id = datalistAll.value.bussWorksheet.id
    //   params.bussTransferForm.id = datalistAll.value.bussTransferForm.id
    //   params.bussTransferForm.worksheetId = datalistAll.value.bussTransferForm.worksheetId
    // }
    // console.log('params--', params)

    let res = await equipmentScrappingApi.savecz(params);
    if (res.success) {
      datalistAll.value = res.data
      message.success('保存成功');
      console.log('res.data', res.data)
      //
    } else {
      message.error('保存失败');
    }
  } catch (error) {
    message.error(error.message.split(",")[0]);
  } finally {
    saving.value = false;
  }
};

// 提交方法
const handleSubmit = async () => {
  try {
    // 执行验证
    // console.log('bog.validateForm()1', validateForm1())
    console.log('validateForm()', validateForm())
    if (!validateForm()) {
      console.log('validateForm()', !validateForm())
      return; // 验证不通过直接返回,不执行提交
    }
    if (formData.value.disposeFileList.length == 0) {
      message.error('请选择处置资料！')
      const formItem = document.querySelector(`[data-field="disposeFileList"]`);
      formItem.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
      return
    }

    if (formData.value.bussDisposalEquipmentList.length == 0) {
      message.error('请选择设备！')
      const formItem = document.querySelector(`[data-field="tables"]`);
      formItem.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
      return
    }

    submitting.value = true;

    // 构建内部formData结构
    let innerFormData = {
      formDatas: {
        bussWorksheet: {
          name: formData.value.businessTitle,
          ...bussWorksheet.value
        },
        bussTransferForm: {
          applyTitle: formData.value.businessTitle,
          fileList: fileViewList.value, // 附件
          handledUser: formData.value.handledUser, // 经办人
          handledOrg: formData.value.handledOrg, // 经办单位
          applyDate: formData.value.applyDate, // 申请日期
          assessmentOrg: formData.value.assessmentOrg, // 评估单位
          acquirerName: formData.value.acquirerName, // 收购方式
          disposalDate: formData.value.disposalDate,//处置日期
          disposalType: formData.value.disposalType,//处置方式
          remark: formData.value.remark,// 备注
          disposeFileList: formData.value.disposeFileList,
          id: ''
        },
        bussRegisteredEquipmentList: [],
        bussScrapedEquipmentList: []
      }
    };
    const lastColumn = document.querySelector('.ant-table-thead .ant-table-cell:nth-last-child(2)');
    for (let i = 0; i < formData.value.bussDisposalEquipmentList.length; i++) {
      if (!formData.value.bussDisposalEquipmentList[i].assessmentMoney && formData.value.bussDisposalEquipmentList[i].assessmentMoney != 0) {
        lastColumn.scrollIntoView({ behavior: "smooth", inline: 'center' });
        message.error('第' + (i + 1) + '行评估金额不能为空')
        return
      }

      if (!formData.value.bussDisposalEquipmentList[i].disposalMoney && formData.value.bussDisposalEquipmentList[i].disposalMoney != 0) {
        lastColumn.scrollIntoView({ behavior: "smooth", inline: 'center' });
        message.error('第' + (i + 1) + '行处置金额不能为空')
        return
      }

      if (!formData.value.bussDisposalEquipmentList[i].disposalTaxRate && formData.value.bussDisposalEquipmentList[i].disposalTaxRate != 0) {
        lastColumn.scrollIntoView({ behavior: "smooth", inline: 'center' });
        message.error('第' + (i + 1) + '行税率不能为空')
        return
      }

      if (!formData.value.bussDisposalEquipmentList[i].disposalTaxeMoney && formData.value.bussDisposalEquipmentList[i].disposalTaxeMoney != 0) {
        lastColumn.scrollIntoView({ behavior: "smooth", inline: 'center' });
        message.error('第' + (i + 1) + '行税金不能为空')
        return
      }

      if (formData.value.bussDisposalEquipmentList[i].typeObj == '1') {
        innerFormData.formDatas.bussRegisteredEquipmentList.push(formData.value.bussDisposalEquipmentList[i])
      } else {
        innerFormData.formDatas.bussScrapedEquipmentList.push(formData.value.bussDisposalEquipmentList[i])
      }

    }
    console.log('datalistAll.value', datalistAll.value);

    // if (datalistAll.value.bussWorksheet && datalistAll.value.bussTransferForm) {
    //   innerFormData.formDatas.bussWorksheet.id = datalistAll.value.bussWorksheet.id
    //   innerFormData.formDatas.bussTransferForm.id = datalistAll.value.bussTransferForm.id
    //   innerFormData.formDatas.bussTransferForm.worksheetId = datalistAll.value.bussTransferForm.worksheetId
    // }
    console.log(innerFormData)

    // 构建最终提交数据结构
    const submitData = {
      processDefinitionId: KeyId.value,
      variables: {
        formData: JSON.stringify(innerFormData)
      }
    };
    console.log('submitData', submitData)
    const res = await equipmentScrappingApi.submit(submitData);

    if (res.success) {
      message.success('提交成功');
      router.push('/buss/disposalEquipment');
    } else {
      message.error('提交失败');
    }
  } catch (error) {
    message.error(error.message);
  } finally {
    submitting.value = false;
  }
};

//获取当前日期
const getdate = () => {
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth() + 1; // 月份从0开始，需要加1
  const day = currentDate.getDate();
  console.log(year + "-" + month + "-" + day);
  return `${year}-${month}-${day}`
}

const formData = ref({
  handledUser: '', // 经办人
  handledUserStr: '', // 经办人
  handledOrg: '', // 经办单位
  handledOrgStr: '',
  applyDate: '', // 申请日期
  id: null,
  assessmentOrg: '', // 评估单位
  acquirerName: '', // 收购方式
  disposalDate: '',//处置日期
  disposalType: null,//处置方式
  remark: '',// 备注
  disposeFileList: [],//处置资料
  bussDisposalEquipmentList: []
});

// 附件列表数据
const attachments = ref([
  {
    name: '',
    unit: '',
    quantity: 1,
    manufacturer: '',
    serialNo: ''
  }
]);

// 添加新附件
const addAttachment = () => {
  attachments.value.push({
    name: '',
    unit: '',
    quantity: 1,
    manufacturer: '',
    serialNo: ''
  });
};

// 删除附件
const removeAttachment = (index) => {
  attachments.value.splice(index, 1);
};

// 处理普通附件上传
const handleChange = (info) => {
  console.log('当前上传的文件:', info.file);
  console.log('所有文件列表:', info.fileList);

  // 保持原始fileList与上传组件同步
  fileList.value = info.fileList;

  // 更新显示用的文件列表
  if (info.file.status === 'done') {
    console.log(fileViewList.value);
    console.log(fileViewList.value.length)
    if (fileViewList.value.length > 0) {

      let fileShowList = info.fileList
        .filter(file => file.status === 'done') // 只保留上传完成的文件
        .map(file => ({
          fileId: file.response.data.fileId,
          fileUrl: file.response.data.fileUrl,
          fileOriginName: file.name,
          uid: file.uid,
          status: file.status
        }))
        .filter(newFile =>
          !fileViewList.value.some(existingFile =>
            existingFile.uid === newFile.uid || // 通过 uid 判断重复
            existingFile.fileId === newFile.fileId // 或通过 fileId 判断重复
          )
        );

      // 合并并更新列表
      fileViewList.value = [...fileViewList.value, ...fileShowList];

    } else {
      fileViewList.value = info.fileList
        .filter(file => file.status === 'done')  // 只保留上传完成的文件
        .map(file => ({
          fileId: file.response.data.fileId, fileUrl: file.response.data.fileUrl,
          fileOriginName: file.name,
          uid: file.uid,
          status: file.status
        }));
    }
    // fileViewList.value = info.fileList
    //   .filter(file => file.status === 'done')  // 只保留上传完成的文件
    //   .map(file => ({
    //     fileId: file.response.data.fileId,fileUrl:file.response.data.fileUrl,
    //     fileName: file.name,
    //     uid: file.uid,
    //     status: file.status
    //   }));

    // 上传成功提示
    message.success(`${info.file.name} 上传成功`);
  } else if (info.file.status === 'error') {
    // 处理单个文件的状态提示
    message.error(`${info.file.name} 上传失败`);
  }
};

// 处理技术资料上传
const handleTechFileChange = (info) => {
  // 保持原始列表与上传组件同步
  techFileList.value = info.fileList;

  if (info.file.status === 'done' && info.file.response) {
    const response = info.file.response;
    if (response.success) {
      // 更新显示列表
      techFileViewList.value = [{
        fileId: response.data.fileId,
        fileOriginName: info.file.name,
        fileUrl: response.data.fileUrl,
        uid: info.file.uid,
        status: 'done'
      }];

      // 更新formData中的techFile用于显示
      formData.value.disposeFileList = [{
        fileId: response.data.fileId,
        fileOriginName: info.file.name,
        fileUrl: response.data.fileUrl,
      }];
    } else {
      message.error('技术资料上传失败');
    }
  } else if (info.file.status === 'error') {
    message.error('上传出错：' + info.file.error.message);
  }
};

// 删除技术资料
const removeTechFile = () => {
  formData.value.disposeFileList = [];
  techFileList.value = [];
  techFileViewList.value = [];
};

// 删除普通附件
const handleRemove = (file) => {
  // 从显示列表中删除
  const viewIndex = fileViewList.value.findIndex(f => f.uid === file.uid);
  if (viewIndex > -1) {
    fileViewList.value.splice(viewIndex, 1);
  }

  // 从上传列表中删除
  const fileIndex = fileList.value.findIndex(f => f.uid === file.uid);
  if (fileIndex > -1) {
    fileList.value.splice(fileIndex, 1);
  }
};

// 审批记录相关方法
const showApprovalRecord = () => {
  // 处理审批记录的显示逻辑
  console.log('显示审批记录');
};
const datalist = ref([])
const datalistAll = ref({})
const bussWorksheet = ref()
const initializeData = async () => {
  loading.value = true;
  try {
    const { data } = await equipmentScrappingApi.getWorksheetInfos({ procInstanceId: procInstanceId.value.id });
    const loginUser = userStore.info;
    // const loginUser = computed(() => userStore.info ?? {});
    const isNewRecord = !data.bussWorksheet &&
      !data.bussTransferForm &&
      !data.bussDisposalEquipmentList;

    if (isNewRecord) {
      // 新记录时只初始化申请相关信息
      formData.value = {
        ...formData.value,
        handledUser: loginUser.userId, // 存储用户ID
        handledUserName: loginUser.realName, // 存储显示用的真实姓名
        handledOrg: loginUser.organizationId, // 存储组织ID
        applyDate: getdate(), // 申请日期
        handledDeptName: loginUser.organizationName, // 存储显示用的组织名称
      };

    } else {
      // 有数据时，直接使用后端返回的数据
      // datalist.value = [...data.bussScrapedEquipmentList,...data.bussRegisteredEquipmentList]
      datalistAll.value = data
      formData.value = {
        ...data.bussTransferForm,
        businessTitle: data.bussTransferForm.applyTitle,
        handledUserStr: data.bussTransferForm.applyUserStr,
        handledOrgStr: data.bussTransferForm.applyOrgStr,
        applyDate: getdate(), // 申请日期
      };

      formData.value.id = "";
      console.log('formData.value', formData.value);
      // 加载已有数据时
      if (data.bussWorksheet) {
        bussWorksheet.value = data.bussWorksheet
        bussWorksheet.value.id = ""
        bussWorksheet.value.procInstanceId = ""
        bussWorksheet.value.isResubmit = true
      }

      for (let i = 0; i < data.bussScrapedEquipmentList.length; i++) {
        data.bussScrapedEquipmentList[i].typeObj = '2'
      }
      for (let i = 0; i < data.bussRegisteredEquipmentList.length; i++) {
        data.bussRegisteredEquipmentList[i].typeObj = '1'
      }
      formData.value.bussDisposalEquipmentList = [...data.bussScrapedEquipmentList, ...data.bussRegisteredEquipmentList]
      console.log('formData.value', formData.value);

      fileViewList.value = data.bussTransferForm.fileList
      for (let i = 0; i < data.bussTransferForm.fileList.length; i++) {
        fileViewList.value[i].fileOriginName = fileViewList.value[i].fileOriginName

      }
      console.log('11111111', formData.value)
    }
  } catch (error) {
    // message.error('获取数据失败：' + error.message);
  } finally {
    loading.value = false;
  }
};


const goTodeviceList = async (row) => {
  current.value = row;
  showEdit.value = true;
};

const goToregisteredList = async (row) => {
  current.value = row;
  showEdit1.value = true;
};

const handleClickright = () => {
  const lastColumn = document.querySelector('.ant-table-thead .ant-table-cell:nth-last-child(2)');
  lastColumn.scrollIntoView({ behavior: "smooth", inline: 'center' });
}

const list1 = ref([])
const list2 = ref([])


const getList = (obj) => {
  if (obj) {
    // for (let i = 0; i < obj.length; i++) {
    //   obj[i].cfddList = []
    //   getAddrOrgList(obj[i].useOrg,i)
    // }
    formData.value.bussDisposalEquipmentList = obj
  }
  console.log('formData.value.bussDisposalEquipmentList-------1', formData.value.bussDisposalEquipmentList);
}

const getList1 = (obj) => {
  if (obj) {
    // for (let i = 0; i < obj.length; i++) {
    //   obj[i].cfddList = []
    //   getAddrOrgList(obj[i].useOrg,i)
    // }
    formData.value.bussDisposalEquipmentList = obj
  }
  console.log('formData.value.bussDisposalEquipmentList--------2', formData.value.bussDisposalEquipmentList);
}

const concatList = () => {
  formData.value.bussDisposalEquipmentList = [...list1.value, ...list2.value]
}

const handleDelete = (res) => {
  console.log('res', res)
  formData.value.bussDisposalEquipmentList = formData.value.bussDisposalEquipmentList.filter(item => item.equId !== res.equId);
}

const orgList = ref()
// 获取组织机构树
const getOrgList = async () => {
  try {
    const res = await UnitApi.getUseOrgTree();
    orgList.value = res[0].children;
    for (let i = 0; i < orgList.value.length; i++) {
      orgList.value[i].selectable = false
    }
    console.log('orgList.value', orgList.value);

  } catch (error) {
    message.error('获取组织机构失败');
  }
};
getOrgList()
const outOrgList = ref()
const getAddrOrgList = async (pId, i) => {
  const hqddList = await UnitApi.getAddrOrgList(pId)
  formData.value.bussDisposalEquipmentList[i].cfddList = hqddList
}
const expandedKeys = ref([])
const handleTreeExpand = (node) => {
  console.log('node', node);

  expandedKeys.value = node
}
const useOrgchange = (e, node) => {
  console.log('expandedKeys', expandedKeys.value);
  console.log('node', node);
  if (node.children.length > 0) {
    const key = orgList.value.find(item => item.name === node.name).id;
    if (expandedKeys.value.includes(key)) {
      expandedKeys.value = expandedKeys.value.filter(k => k !== key);
    } else {
      expandedKeys.value = [...expandedKeys.value, key];
    }
  }

};

// const useOrgchange = async (value, node, extra, index) => {

//   // console.log(value);

//   // // 清空当前行的存放地点
//   // getAddrOrgList(value, index)
//   // formData.value.bussDisposalEquipmentList[index].storageLocationStr = null
// };

const procInstanceId = ref({
  id: ''
});
const route = useRoute();

const containerRef = ref();
const containerWidth = ref(0);

const updateContainerWidth = () => {
  if (containerRef.value) {
    containerWidth.value = containerRef.value.offsetWidth;
  }
};

onMounted(() => {
  if (route.query?.procInsId) {
    procInstanceId.value.id = route.query.procInsId;
    sessionStorage.setItem('equipmentAcceptanceProcId', procInstanceId.value.id);
  } else if (sessionStorage.getItem('equipmentAcceptanceProcId')) {
    procInstanceId.value.id = sessionStorage.getItem('equipmentAcceptanceProcId');
  }
  if (!procInstanceId.value.id) {
    message.error('流程定义ID未找到，请联系管理员');
    return;
  }
  initializeData();
  getProcessDefinitionByKey()
  updateContainerWidth()
  window.addEventListener('resize', updateContainerWidth)
});
</script>

<style lang="less" scoped>
.custom-table {
  margin-top: 16px;

  :deep(.ant-table) {

    // 提高固定列的层级
    .ant-table-fixed-left,
    .ant-table-fixed-right {
      background: #fff;
      z-index: 3; // 增加层级
    }

    .ant-table-cell:not(.ant-table-cell-fix-right-first) {
      white-space: nowrap !important; // 强制不换行
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      line-height: 1 !important;
      font-size: 14px !important;

      >span,
      >div {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }
    }

    // 大屏幕样式（默认）
    @media screen and (min-width: 1920px) {
      .ant-table-cell {
        padding: 20px 20px !important;
        height: 60px !important;
      }

      .ant-table-row {
        height: 60px !important;
      }
    }

    // 中等屏幕样式
    @media screen and (min-width: 1366px) and (max-width: 1919px) {
      .ant-table-cell {
        padding: 10px 20px !important;
        height: 40px !important;
      }

      .ant-table-row {
        height: 40px !important;
      }
    }

    // 小屏幕样式
    @media screen and (max-width: 1365px) {
      .ant-table-cell {
        padding: 4px 8px !important;
        height: 32px !important;
      }

      .ant-table-row {
        height: 32px !important;
      }
    }

    // 调整固定列单元格样式
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      z-index: 3 !important; // 增加层级
      background: #ECF4FE !important;
    }

    // 调整表头固定列样式
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right {
        z-index: 4 !important; // 确保表头在最上层
        background: #DAECFF !important;
      }
    }

    // 优化阴影效果
    .ant-table-fixed-right::before,
    .ant-table-fixed-left::before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      width: 10px;
      pointer-events: none;
      z-index: 2; // 阴影层级低于固定列
      transition: box-shadow .3s;
    }

    .ant-table-fixed-left::before {
      right: 0;
      box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
    }

    .ant-table-fixed-right::before {
      left: 0;
      box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
    }

    // 设置表格内容的层级
    .ant-table-content {
      z-index: 1;
    }

    // 确保滚动区域正确显示
    .ant-table-body {
      overflow-x: auto !important;
      overflow-y: auto !important;
    }

    // 固定列不换行
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right:not(.ant-table-cell-fix-right-first) {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .ant-table-row {
      height: 24px !important;
    }

    // 表头固定列不换行
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right:not(.ant-table-cell-fix-right-first) {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.approval-record-btn {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  z-index: 100;

  .bg-image {
    display: block;
    max-width: 100%;
    height: auto;
    object-fit: contain;
    background-color: transparent;
  }

  .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .icon {
      width: 15px;
      height: 15px;
      margin-bottom: 4px;
    }

    .text {
      writing-mode: vertical-lr;
      font-size: 14px;
      color: #333;
      letter-spacing: 2px;
    }
  }
}

.equipment-acceptance {
  width: 100%;
  max-width: 1920px; // 最大宽度限制
  height: calc(100vh - 100px);
  overflow: auto;
  margin: 0 auto; // 居中
  padding: 24px 9.06vw 24px 8.18vw; // 转换为vw: 174/1920*100=9.06, 157/1920*100=8.18
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);

  .form-title {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 24px;
  }

  .section {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;

      .blue-bar {
        width: 3px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
      }
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: clamp(16px, 2vw, 24px);
    margin-bottom: 24px;
    justify-content: space-between;

    .form-item {
      display: flex;
      flex-direction: column;
      min-width: 300px; // 最小宽度
      max-width: 300px; // 最大宽度

      .label {
        height: 32px;
        line-height: 32px;
        margin-bottom: 8px;
        color: #0C0B0B;

        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 4px;
        }
      }

      .value {
        width: 100%;
        height: 32px;
        color: #666;

        :deep(.ant-input),
        :deep(.ant-select),
        :deep(.ant-picker),
        :deep(.ant-input-number) {
          width: 100%;
          height: 32px;
        }

        :deep(.ant-input-number-input) {
          height: 16px;
          line-height: 16px;
        }

        :deep(.ant-select-selector) {
          height: 32px !important;

          .ant-select-selection-item {
            line-height: 32px;
          }
        }

        :deep(.ant-picker) {
          .ant-picker-input {
            height: 16px;
            line-height: 16px;

            input {
              height: 16px;
              line-height: 16px;
            }
          }
        }
      }
    }
  }

  .acceptance-no {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    max-width: 300px; // 与其他表单项保持一致的最大宽度

    :deep(.first-input) {
      width: 65% !important; // 验资前面的输入框占65%
    }

    :deep(.second-input) {
      width: 35% !important; // 号前面的输入框占35%
    }

    .separator {
      color: #666;
      white-space: nowrap;
    }
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .equipment-acceptance {
    padding: 24px 6vw;

    .form-grid {
      gap: 16px;
    }
  }
}

@media screen and (max-width: 1366px) {
  .equipment-acceptance {
    padding: 24px 4vw;

    .form-grid {
      grid-template-columns: repeat(2, minmax(300px, 1fr));
    }
  }
}

@media screen and (max-width: 1024px) {
  .equipment-acceptance {
    padding: 24px 2vw;

    .form-grid {
      grid-template-columns: repeat(1, 1fr);

      .form-item {
        max-width: none;
      }
    }
  }
}

.percentage-input {
  position: relative;
  display: flex;
  align-items: center;

  .ant-input {
    width: 100%;
  }

  .percentage-symbol {
    position: absolute;
    right: 12px;
    color: #666;
  }
}

.attachment-table {
  width: 89%;
  border: 1px solid #e8e8e8;
  border-radius: 2px;


  .table-header,
  .table-row,
  .add-row {
    // 添加add-row到统一高度设置中
    display: flex;
    padding: 12px 8px;

    border-bottom: 1px solid #e8e8e8;
    align-items: center;
    gap: 12px;
    min-height: 56px; // 统一设置最小高度
  }

  .table-header {
    font-weight: 500;
  }

  .table-row:last-child {
    border-bottom: none;
  }

  // 使用百分比和flex布局
  .col-serial {
    width: 5%; // 序号列较窄
    min-width: 40px;
  }

  .col-name {
    width: 25%; // 名称及型号需要较大空间
    min-width: 180px;
  }

  .col-unit {
    width: 10%; // 单位列较窄
    min-width: 80px;
  }

  .col-quantity {
    width: 10%; // 数量列较窄
    min-width: 80px;
  }

  .col-manufacturer {
    width: 20%; // 生产厂家需要适中空间
    min-width: 150px;
  }

  .col-serial-no {
    width: 20%; // 出厂编号需要适中空间
    min-width: 150px;
  }

  .col-action {
    width: 10%; // 操作列较窄
    min-width: 60px;
    text-align: center;

    .delete-btn {
      color: #FF4D4F;
      cursor: pointer;
    }
  }

  :deep(.ant-input),
  :deep(.ant-input-number) {
    width: 100%;
  }


  // 响应式调整
  @media screen and (max-width: 1366px) {
    .col-name {
      width: 22%; // 较小屏幕时稍微压缩名称列
    }

    .col-manufacturer,
    .col-serial-no {
      width: 18%; // 压缩这两列
    }
  }

  @media screen and (max-width: 1024px) {
    overflow-x: auto; // 当屏幕太小时允许横向滚动

    .table-header,
    .table-row {
      min-width: 900px; // 确保在小屏幕上内容不会过度压缩
    }
  }

  .add-row {
    justify-content: center; // 水平居中
    align-items: center; // 垂直居中
    cursor: pointer;
    border-bottom: none; // 最后一行不需要底部边框

    a {
      display: flex;
      align-items: center; // 图标和文字垂直居中
      color: #1890ff;

      .anticon {
        margin-right: 4px;
      }
    }

    &:hover {
      background: #f5f5f5;
    }
  }
}

.upload-section {
  .upload-header {
    display: flex;
    align-items: center;
    gap: 12px;

    .upload-button {
      display: inline-flex;
      align-items: center;
      padding: 4px 15px;
      background: #0085FF;
      border-radius: 2px;
      color: white;
      cursor: pointer;
      font-size: 14px;

      .anticon {
        margin-right: 4px;
      }
    }

    .upload-tip {
      color: #999;
      font-size: 12px;
    }
  }

  .file-list {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    //display: grid;
    //grid-template-columns: repeat(3, 0.3fr);
    //gap: 16px;

    .file-item {
      display: flex;
      align-items: center;
      padding: 8px;
      // background: #F7F9FC;
      border-radius: 2px;
      width: 32%;

      .file-link {
        word-break: break-all;
      }

      .file-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #2D5CF6;
      }

      .file-name {
        flex: 1;
        font-size: 14px;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-actions {
        margin-left: 8px;

        .delete-icon {
          color: #999;
          cursor: pointer;

          &:hover {
            color: #FF4B4B;
          }
        }

        .anticon-sync {
          color: #2D5CF6;
        }
      }
    }
  }
}

.tech-file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 12px;
  // border: 1px solid #d9d9d9;
  border-radius: 2px;

  .file-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      color: #2D5CF6;
    }

    .file-name {
      color: #333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .delete-icon {
    color: #999;
    cursor: pointer;

    &:hover {
      color: #ff4d4f;
    }
  }
}

.tech-upload-button {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  color: #666;
  font-size: 14px;

  .anticon {
    margin-right: 4px;
    font-size: 14px;
  }

  &:hover {
    color: #2D5CF6;
    border-color: #2D5CF6;
  }
}

.value {
  :deep(.ant-input-affix-wrapper) {
    .ant-input-suffix {
      color: #666;
    }
  }
}

.bottom-buttons {
  margin-top: 150px;
  margin-bottom: 89rpx;
  bottom: 180px; // 修改为距离底部80px
  left: 0;
  right: 0;
  padding: 0 32px; // 调整左右内边距
  text-align: right;
  z-index: 100;

  .save-btn {
    height: 32px;
    padding: 0 16px;
    border: 1px solid #DCDFE6;
    color: #606266;
    background: transparent !important;
    border-radius: 4px;

    &:hover {
      border-color: #409EFF;
      color: #409EFF;
    }
  }

  .submit-btn {
    height: 32px;
    padding: 0 16px;
    margin-left: 27px; // 调整按钮之间的间距
    background: #409EFF;
    border-radius: 4px;
    border: none;

    &:hover {
      background: #66B1FF;
    }
  }
}

.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>
