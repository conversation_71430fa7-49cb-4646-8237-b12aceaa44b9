import Request from '@/utils/request-util';

/**
 * 设备验收API
 *
 * <AUTHOR> @date 2024/01/09
 */
export class EquipmentAcceptanceApi {
  /**
   * 获取新建数据
   *
   * <AUTHOR> @date 2024/01/09
   */
  static getNewBuilt() {
    return Request.get('/apiBus/registeredEquipment/newBuilt');
  }

  static save(params) {
    return Request.post('/apiBus/registeredEquipment/saveDraft', params);
  }

  static submit(params) {
    return Request.post('/flowableHandleTask/start', params);
  }

  static approval(params) {
    return Request.post('/flowableHandleTask/submit', params);
  }

  static end(params) {
    return Request.post('/flowableInstance/end', params);
  }
  static getData(params) {
    return Request.getAndLoadData('/apiBus/registeredEquipment/page', params);
  }

  static getHistoryEquipmentFullInfo(params) {
    return Request.get('/apiBus/registeredEquipment/getHistoryEquipmentFullInfo', params);
  }

  /**
   * 导出设备数据
   *
   * @param {Object} params - 查询参数，与getData接口使用相同的参数
   */
  static exportData(params) {
    return Request.downLoad('/api/apiBus/registeredEquipment/export', params);
  }

  static uploadExcel(params) {
    return Request.post('/apiBus/registeredEquipment/importExcel', params);
  }

  static getEditData(params) {
    return Request.get('/apiBus/registeredEditEquipment/newBuilt', params);
  }

  static getEquipmentByCode(params) {
    return Request.get('/apiBus/equipmentClassificationCode/detail', params);
  }

  static getOriginalValue(params) {
    return Request.get('/apiBus/registeredEquipment/getOriginalValue', params);
  }

  static getDetailByEquCode(params) {
    return Request.get('/apiBus/disposaledEquipment/getEquipmentFullInfoByCode', params);
  }

  static getWorksheetInfo(params) {
    return Request.get('/apiBus/registeredEquipment/getWorksheetInfo', params);
  }

  static getEquipmentFullInfo(params) {
    return Request.get('/apiBus/registeredEquipment/getEquipmentFullInfo', params);
  }
  static getScrapedEquipmentFullInfo(params) {
    return Request.get('/apiBus/scrapedEquipment/getEquipmentFullInfo', params);
  }
  static getDisposaledEquipmentFullInfo(params) {
    return Request.get('/apiBus/disposaledEquipment/getEquipmentFullInfo', params);
  }

  static edit(params) {
    return Request.post('/apiBus/registeredEquipment/edit', params);
  }
  static editScraped(params) {
    return Request.post('/apiBus/scrapedEquipment/edit', params);
  }
  static editDisposaled(params) {
    return Request.post('/apiBus/disposaledEquipment/edit', params);
  }

  
  
  static getProcessDefinitionByProcessInstanceId(params) {
    return Request.get('/workFlowCommon/getProcessDefinitionByProcessInstanceId', params);
  }
}

