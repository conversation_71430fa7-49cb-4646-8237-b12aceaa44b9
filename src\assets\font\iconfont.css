@font-face {
  font-family: "iconfont"; /* Project id 4900640 */
  src: url('iconfont.woff2?t=1751005196400') format('woff2'),
       url('iconfont.woff?t=1751005196400') format('woff'),
       url('iconfont.ttf?t=1751005196400') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-tooltip:before {
  content: "\e612";
}

.icon-a-rongqi1:before {
  content: "\e611";
}

.icon-delete:before {
  content: "\e60f";
}

.icon-equipment:before {
  content: "\e611";
}

.icon-shanchu-copy:before {
  content: "\e60f";
}

.icon-search:before {
  content: "\e60b";
}

.icon-cz:before {
  content: "\e60a";
}

.icon-sbbf:before {
  content: "\e609";
}

.icon-import:before {
  content: "\e607";
}

.icon-rongqi-copy:before {
  content: "\e606";
}

