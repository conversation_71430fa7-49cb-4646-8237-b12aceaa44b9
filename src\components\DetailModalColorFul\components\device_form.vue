<template>

  <a-form ref="formRef" :model="formData"  :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
    :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }">
    <a-row :gutter="16">
      <a-col :md="12" :sm="24" :xs="24" style="padding-right: 0px;">
        <div class="rows">
          <div class="rows_1">
            <span class="rows_1_1">
              设备编号
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              <a-tooltip placement="topLeft" :title="formData.code">
                <span>{{ formData.code.length > 17 ? formData.code.substring(0, 17) + '...' : formData.code }}</span>
              </a-tooltip>
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              设备种类
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
                {{ formData.equSubTypeStr }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              规格型号
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
             {{ formData.equModelStr }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              设备性质
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ formData.equNatureStr }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              设备来源
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ formData.equSourceStr }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              数量
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ formData.num }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              生产厂家
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ formData.manufacturer }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              出厂日期
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ formData.productionDate }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              验收单号
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ formData.acceptanceNumber }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              功率(kw)
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ formData.power }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              设备代码
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ formData.modelCode }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              技术资料
            </span>
          </div>
          <div class="rows_2">
            <div class="rows_2_2 value" style="width: 100%;text-align: center;" ref="containerRef">
              <div v-if="formData.technicalFileList && formData.technicalFileList.length > 0">
                  <!-- <div v-for="(file, index) in formData.technicalFileList" :key="file.fileId" class="file-item">
                     <a :href="`${file.fileUrl}?filename=${file.fileOriginName}`"  target="_blank" class="file-link">
                      {{ file.fileOriginName }}
                    </a>
                  </div> -->
                      <InputUploader 
    :max-count="10" 
    button-text="上传文件" 
    :accept-types="['.jpg', '.jpeg', '.png', '.mp4', '.mov', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', '.pdf']"
    :initial-files="formData.technicalFileList" 
    :show-border="false"
    @file-uploaded="onTechFileUploaded"
    @file-removed="onTechFileRemoved" 
    @upload-status-change="onUploadStatusChange" 
       :read-only="true"
       :width="containerWidth"
  />
                </div>
                <div v-else>无技术资料</div>
            </div>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">

            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">

            </span>
          </div>
        </div>
      </a-col>
      <a-col :md="12" :sm="24" :xs="24" style="padding-left: 0px;">
        <div class="rows2">
          <div class="rows2_2">
            <span class="rows2_2_2">
              设备类别
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.equTypeStr }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              设备名称
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.equNameStr }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              型号备注
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.equModelInfo }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              购置年度
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.purchaseYear }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              单位
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.unit }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              设备合同价(含税)
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.equContractPriceTax }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              出厂编号
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.factoryNumber }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              合同编号
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.contractNumber }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              验收日期
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.acceptanceDate }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
             设备重要性
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.importanceStr }}
            </span>
          </div>
        </div>

        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              保养周期
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.serviceIntervalTypeStr }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>
      </a-col>
    </a-row>
  </a-form>
</template>

<script setup>
import { defineComponent, reactive, toRefs, ref, watch } from 'vue';
import iconData from 'ele-admin-pro/es/ele-icon-picker/icons';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: '设备详情'
  }
});
const containerWidth = ref(0)
const containerRef = ref()
const updateContainerWidth = () => {
  if (containerRef.value) {
    containerWidth.value = containerRef.value.offsetWidth
  }
}
const detailData = ref({});
const formData = ref({});
watch(() => props.data, (val) => {
  console.log(val)
  if(val){
    if(val.type == "scrap"){
      formData.value = val.bussScrapedEquipment;
    }else if(val.type == "disposal"){
      formData.value = val.bussDisposaledEquipment;
    }else{
      formData.value = val.bussRegisteredEquipment;
    }
    //detailData.value = val.formData;
    console.log(formData.value)
    updateContainerWidth()
  window.addEventListener('resize', updateContainerWidth)
  }

}, { immediate: true });
</script>

<style lang="less" scoped>
.rows {
  display: flex;
  height: 53px;
  align-items: center;
  border: 1px solid rgba(172, 180, 201, 0.2);

  .rows_1 {
    width: 300px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
color:rgba(255, 255, 255, 0.8);
    border-right: 1px solid rgba(172, 180, 201, 0.2);

    .rows_1_1 {
      margin-right: 16px;
    }
  }

  .rows_2 {
    .rows_2_2 {
      margin-left: 16px;
      color:rgba(255, 255, 255, 0.8);
    }
  }
}


.rows2 {
  display: flex;
  height: 53px;
  align-items: center;
  border-top: 1px solid rgba(172, 180, 201, 0.2);
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);

  .rows2_2 {
    width: 300px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color:rgba(255, 255, 255, 0.8);
    border-right: 1px solid rgba(172, 180, 201, 0.2);

    .rows2_2_2 {
      margin-right: 16px;
    }
  }

  .rows2_3 {
    .rows2_3_3 {
      margin-left: 16px;
      color:rgba(255, 255, 255, 0.8);
    }
  }
}

.rows1 {
  border-top: 0;
  border-left: 1px solid rgba(172, 180, 201, 0.2);
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);
}

.rows22{
  border-top: 0;
  border-left: 0;
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);
}
</style>




