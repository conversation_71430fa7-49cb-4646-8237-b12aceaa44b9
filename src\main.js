import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import permission from './utils/permission';
import i18n from './i18n';
import './styles/index.less';
import * as antIcons from '@ant-design/icons-vue';
import '@/assets/font/iconfont.css'
import * as echarts from 'echarts'
import importDirective from '@/directives';

// 自定义组件
import CommonDrawer from '@/components/CommonDrawer/index.vue'
import HnFileIcon from '@/components/HnFileIcon/HnFileIcon.vue';

// 确保全局可用
window.echarts = echarts
const app = createApp(App);
import { message } from 'ant-design-vue';
importDirective(app);
// 全局配置 message
message.config({
  top: '40%',      // 距离顶部的位置
  maxCount: 1,     // 最大显示数量
  duration: 2, // 持续时间（秒）
});
app.use(router);
app.use(store);
app.use(permission);
app.use(i18n);
//
// 全局注册抽屉
app.component('common-drawer', CommonDrawer);
app.component('HnFileIcon', HnFileIcon);

app.mount('#app');

window.addEventListener('error', (event) => {
  if (event.message.includes('Failed to fetch dynamically imported module')) {
    const chunkPath = event.filename;
    console.warn(`Chunk加载失败: ${chunkPath}, 尝试重新加载`);
    
    // 5秒后重试
    setTimeout(() => {
      window.location.reload();
    }, 5000);
  }
}, true);

// 注册图标组件到全局
Object.keys(antIcons).forEach(key => {
  app.component(key, antIcons[key]);
});
app.config.globalProperties.$antIcons = antIcons;
