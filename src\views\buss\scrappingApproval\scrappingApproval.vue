<template>
  <div class="ele-body">
  <div class="equipment-acceptance">

    <div class="form-title">设备处置单</div>

    <!-- 申请信息 -->
    <div class="section">
      <div class="section-title">
        <div class="blue-bar"></div>
        申请信息
      </div>

      <div class="form-grid">
        <div class="form-item">
          <div class="label">申请人</div>
          <div >张三</div>
        </div>
        <div class="form-item">
          <div class="label">申请日期</div>
          <div >2024-08-12</div>
        </div>
        <div class="form-item">
          <div class="label">申请单位</div>
          <div >中铁第三建设（集团）有限责任公司机电处</div>
        </div>
      </div>

      <div class="form-grid">
        <div class="form-item">
          <div class="label">业务标题</div>
          {{ businessTitle }}
        </div>
        <div class="form-item">
          <div class="label">关联投资计划</div>
          {{ investmentPlan }}
        </div>
        <div class="form-item">
          <div class="label">是否为转入设备</div>
          <div>{{ isTransfer === '2' ? '是' : '否' }}</div>
        </div>

      </div>

      <div class="form-grid" v-if="isTransfer === '2'">
        <div class="form-item">
          <div class="label">转入设备编号</div>
          <a-input placeholder="默认文字"  />
        </div>
      </div>
    </div>

    <!-- 设备信息 -->
    <div class="section">
      <div class="section-title">
        <div class="blue-bar"></div>
        设备信息
      </div>
{{ formData }}
      <div class="equipment-info-table">
        <div class="info-row">
          <div class="info-cell">
            <div class="label">设备编号</div>
            <div class="value">{{ formData.code }}</div>
          </div>
          <div class="info-cell">
            <div class="label">设备类别</div>
            <div class="value">{{ formData.equType }}</div>
          </div>
          <div class="info-cell">
            <div class="label">设备种类</div>
            <div class="value">{{ formData.equSubType }}</div>
          </div>
        </div>

        <div class="info-row">
          <div class="info-cell">
            <div class="label">设备名称</div>
            <div class="value">{{ formData.equName }}</div>
          </div>
          <div class="info-cell">
            <div class="label">规格型号</div>
            <div class="value">{{ formData.equModel }}</div>
          </div>
          <div class="info-cell">
            <div class="label">型号备注</div>
            <div class="value">{{ formData.equModelNote }}</div>
          </div>
        </div>

        <div class="info-row">
          <div class="info-cell">
            <div class="label">设备性质</div>
            <div class="value">{{ formData.equNature }}</div>
          </div>
          <div class="info-cell">
            <div class="label">购置年度</div>
            <div class="value">{{ formData.purchaseYear }}</div>
          </div>
          <div class="info-cell">
            <div class="label">设备来源</div>
            <div class="value">{{ formData.equSource }}</div>
          </div>
        </div>

        <div class="info-row">
          <div class="info-cell">
            <div class="label">单位</div>
            <div class="value">{{ formData.unit }}</div>
          </div>
          <div class="info-cell">
            <div class="label">数量</div>
            <div class="value">{{ formData.num }}</div>
          </div>
          <div class="info-cell">
            <div class="label">设备合同价(含税)</div>
            <div class="value">{{ formData.equContractPriceTax }}</div>
          </div>
        </div>

        <div class="info-row">
          <div class="info-cell">
            <div class="label">生产厂家</div>
            <div class="value">{{ formData.manufacturer }}</div>
          </div>
          <div class="info-cell">
            <div class="label">出厂编号</div>
            <div class="value">{{ formData.factoryNumber }}</div>
          </div>
          <div class="info-cell">
            <div class="label">出厂日期</div>
            <div class="value">{{ formData.productionDate }}</div>
          </div>
        </div>

        <div class="info-row">
          <div class="info-cell">
            <div class="label">合同编号</div>
            <div class="value">{{ formData.contractNumber }}</div>
          </div>
          <div class="info-cell">
            <div class="label">验收单号</div>
            <div class="value">{{ formData.acceptanceNumber }}</div>
          </div>
          <div class="info-cell">
            <div class="label">验收日期</div>
            <div class="value">{{ formData.acceptanceDate }}</div>
          </div>
        </div>

        <div class="info-row">
          <div class="info-cell">
            <div class="label">功率(kw)</div>
            <div class="value">{{ formData.power }}</div>
          </div>
          <div class="info-cell">
            <div class="label">设备重要性</div>
            <div class="value">{{ formData.importance }}</div>
          </div>
          <div class="info-cell">
            <div class="label">保养周期</div>
            <div class="value">{{ formData.serviceInterval }}</div>
          </div>
        </div>

        <div class="info-row">
          <div class="info-cell">
            <div class="label">设备重要性</div>
            <div class="value">{{ formData.importance }}</div>
          </div>
          <div class="info-cell">
            <div class="label">技术资料</div>
            <div class="value tech-files">
              <span v-for="(file, index) in formData.techFiles" :key="index"
                    :class="['tech-file-tag', file.type]">
                {{ file.name }}
              </span>
            </div>
          </div>
          <div class="info-cell">
            <div class="label"></div>
            <div class="value"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用情况 -->
    <div class="section">
      <div class="section-title">
        <div class="blue-bar"></div>
        使用情况
      </div>
{{ formData }}
      <div class="equipment-info-table">
        <div class="info-row">
          <div class="info-cell">
            <div class="label">产权单位</div>
            <div class="value">{{ formData.propertyOrg }}</div>
          </div>
          <div class="info-cell">
            <div class="label">管理状态</div>
            <div class="value">{{ formData.managementStatus }}</div>
          </div>
          <div class="info-cell">
            <div class="label">管理单位</div>
            <div class="value">{{ formData.managementOrg }}</div>
          </div>
        </div>

        <div class="info-row">
          <div class="info-cell">
            <div class="label">设备状态</div>
            <div class="value">{{ formData.equCondition }}</div>
          </div>
          <div class="info-cell">
            <div class="label">使用单位</div>
            <div class="value">{{ formData.useOrg }}</div>
          </div>
          <div class="info-cell">
            <div class="label">存放地点</div>
            <div class="value">{{ formData.storageLocationStr }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 财务信息 -->
    <div class="section">
      <div class="section-title">
        <div class="blue-bar"></div>
        财务信息
      </div>

      <div class="equipment-info-table">
        <div class="info-row">
          <div class="info-cell">
            <div class="label">财务卡片编号</div>
            <div class="value">{{ formData.financialNumber }}</div>
          </div>
          <div class="info-cell">
            <div class="label">财务组织</div>
            <div class="value">{{ formData.financialOrg }}</div>
          </div>
          <div class="info-cell">
            <div class="label">财务原值</div>
            <div class="value">{{ formData.financialOriginalValue }}</div>
          </div>
        </div>

        <div class="info-row">
          <div class="info-cell">
            <div class="label">固定资产分类</div>
            <div class="value">{{ formData.fixedAssets }}</div>
          </div>
          <div class="info-cell">
            <div class="label">资金来源</div>
            <div class="value">{{ formData.sourceOfFunds }}</div>
          </div>
          <div class="info-cell">
            <div class="label">递延收益</div>
            <div class="value">{{ formData.deferredIncome }}</div>
          </div>
        </div>

        <div class="info-row">
          <div class="info-cell">
            <div class="label">已计提折旧月份</div>
            <div class="value">{{ formData.alreadyAccruedMonths }}</div>
          </div>
          <div class="info-cell">
            <div class="label">折旧方式</div>
            <div class="value">{{ formData.depreciationMethod }}</div>
          </div>
          <div class="info-cell">
            <div class="label">折旧年限</div>
            <div class="value">{{ formData.depreciationPeriod }}</div>
          </div>
        </div>

        <div class="info-row">
          <div class="info-cell">
            <div class="label">折旧月份</div>
            <div class="value">{{ formData.depreciationMonth }}</div>
          </div>
          <div class="info-cell">
            <div class="label">残值率</div>
            <div class="value">{{ formData.residualRate }}%</div>
          </div>
          <div class="info-cell">
            <div class="label">预计净残值</div>
            <div class="value">{{ formData.netSalvage }}</div>
          </div>
        </div>

        <div class="info-row">
          <div class="info-cell">
            <div class="label">历史原值</div>
            <div class="value">{{ formData.historicalOriginalValue }}</div>
          </div>
          <div class="info-cell">
            <div class="label">累计折旧额</div>
            <div class="value">{{ formData.depreciationAmount }}</div>
          </div>
          <div class="info-cell">
            <div class="label">当月折旧额</div>
            <div class="value">{{ formData.currentMonthDepreciationAmount }}</div>
          </div>
        </div>

        <div class="info-row">
          <div class="info-cell">
            <div class="label">净值</div>
            <div class="value">{{ formData.netWorth }}</div>
          </div>
          <div class="info-cell">
            <div class="label">税率</div>
            <div class="value">{{ formData.taxRate }}<span v-if="formData.taxRate">%</span></div>
          </div>
          <div class="info-cell">
            <div class="label"></div>
            <div class="value"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要附件 -->
    <div class="section">
      <div class="section-title">
        <div class="blue-bar"></div>
        主要附件
      </div>
{{ attachments }}
      <a-table
        :columns="attachmentColumns"
        :data-source="attachments"
        :pagination="false"
        bordered
      >
      <template #emptyText>
          <div class="custom-empty">
            <img src="@/assets/images/noData.png"/>
            <!-- <p>抱歉，暂时还没有数据</p> -->
          </div>
        </template>
        <template #bodyCell="{ column, text }">
          <template v-if="column.dataIndex === 'serial'">
            {{ text }}
          </template>
        </template>
      </a-table>
    </div>

    <!-- 附件 -->
    <div class="section">
      <div class="section-title">
        <div class="blue-bar"></div>
        附件
      </div>

      <div class="upload-section">
        <div class="upload-header">
          <a-upload
            v-model:file-list="fileList"
            :multiple="true"
            :action="uploadUrl"
            :headers="headers"
            :showUploadList="false"
            @change="handleChange"
          >
            <div class="upload-button">
              <upload-outlined />
              点击上传
            </div>
          </a-upload>
          <span class="upload-tip">最大1GB/个，支持图片、视频、文档等常见文件格式(zip、rar等压缩文件除外)</span>
        </div>

        <div class="file-list">

          <div v-for="(file, index) in fileViewList" :key="file.uid" class="file-item">
            <div class="file-icon">
              <file-excel-outlined v-if="file.fileName.endsWith('.xls') || file.fileName.endsWith('.xlsx')" />
              <file-word-outlined v-if="file.fileName.endsWith('.doc') || file.fileName.endsWith('.docx')" />
              <file-pdf-outlined v-if="file.fileName.endsWith('.pdf')" />
              <video-camera-outlined v-if="file.fileName.endsWith('.mov') || file.fileName.endsWith('.mp4')" />
              <file-outlined v-else />
            </div>
           <a :href="`${file.fileUrl}?filename=${file.fileName}`"  target="_blank" class="file-link">
                  {{ file.fileName }}
                </a>
            <div class="file-actions">
              <sync-outlined v-if="file.status === 'uploading'" spin />
              <close-outlined
                v-else
                class="delete-icon"
                @click="handleRemove(file)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 审批说明 -->
    <div class="section">
      <div class="section-title">
        <div class="blue-bar"></div>
        审批说明
      </div>

      <div class="approval-description">
        <a-textarea
          v-model:value="formData.approvalDescription"
          :rows="4"
          placeholder="请输入审批说明"
          :maxlength="500"

        />
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-buttons">
      <a-button
        @click="handleSave"
        :loading="saving"
        danger
      >拒绝</a-button>
      <a-button
        type="primary"
        @click="handleSubmit"
        :loading="submitting"
        class="submit-btn"
      >同意</a-button>
    </div>
  </div>

</div>
</template>

<script setup>

import { ref, onMounted,nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { RegisteredEquipmentApi } from '@/api/workflow/RegisteredEquipmentApi';
import { FileUploadUrl } from '@/api/system/operation/FileApi';
import { getToken } from '@/utils/token-util';
import { API_BASE_PREFIX } from '@/config/setting';
import { useRouter } from 'vue-router';
import { EquipmentAcceptanceApi } from '@/api/buss/EquipmentAcceptanceApi';

const loading = ref(false);
const saving = ref(false);
const submitting = ref(false);

// 业务标题和关联投资计划的独立字段
const businessTitle = ref('');
const investmentPlan = ref('');
const isTransfer = ref('1'); // 添加是否转入设备字段，默认为'1'（否）

// 上传相关配置
const uploadUrl = `${API_BASE_PREFIX}${FileUploadUrl}?secretFlag=0`;
const headers = {
  Authorization: getToken()
};

// 文件列表相关
const fileList = ref([]);
const fileViewList = ref([]);
const techFileList = ref([]); // 技术资料上传组件的文件列表
const techFileViewList = ref([]); // 技术资料显示列表

// 定义验证规则
const rules = {
  contractNumber: [{ required: true, message: '请输入合同编号' }],
  acceptanceDate: [{ required: true, message: '请选择验收日期' }],
  equType: [{ required: true, message: '请选择设备类别' }],
  equSubType: [{ required: true, message: '请选择设备种类' }],
  equName: [{ required: true, message: '请输入设备名称' }],
  equModel: [{ required: true, message: '请输入规格型号' }],
  equNature: [{ required: true, message: '请选择设备性质' }],
  equSource: [{ required: true, message: '请选择设备来源' }],
  importance: [{ required: true, message: '请选择设备重要性' }],
  propertyOrg: [{ required: true, message: '请选择产权单位' }],
  managementOrg: [{ required: true, message: '请选择管理单位' }],
  useOrg: [{ required: true, message: '请选择使用单位' }],
  equCondition: [{ required: true, message: '请输入设备状态' }],
  storageLocationStr: [{ required: true, message: '请输入存放地点' }]
};


// 构建提交数据的方法
const buildRequestData = () => {
  const requestData = {
    bussWorksheet: {
      name: businessTitle.value
    },
    bussTransferForm: {
      applyTitle: businessTitle.value,
      investmentPlan: investmentPlan.value
    },
    // 设备基本信息，包含技术资料
    bussRegisteredEquipmentList: [
      {
        ...formData.value,
        technicalFileList: techFileViewList.value, // 技术资料
        fileList:fileViewList.value, // 附件
        // 主要附件模块数据
        bussEquipmentAccessoryList: attachments.value.map(item => ({
      name: item.name,
      unit: item.unit,
      quantity: item.quantity,
      manufacturer: item.manufacturer,
      serialNo: item.serialNo
    })),
      }
    ],

  };

  console.log('提交的数据：', requestData);
  return requestData;
};

// 保存方法
const handleSave = async () => {
  try {
    saving.value = true;
    const params = buildRequestData();
    const res = await EquipmentAcceptanceApi.save(params);
    if (res.success) {
      message.success('保存成功');
    } else {
      message.error(res.message || '保存失败');
    }
  } catch (error) {
    message.error('保存失败：' + error.message);
  } finally {
    saving.value = false;
  }
};

// 提交方法
const handleSubmit = async () => {
  try {
    // 执行验证
    if (!validateForm()) {
      return; // 验证不通过直接返回,不执行提交
    }

    submitting.value = true;

    // 构建内部formData结构
    const innerFormData = {
      formDatas: {
        bussWorksheet: {
          name: businessTitle.value
        },
        bussTransferForm: {
          apply_title: businessTitle.value,
          investmentPlan: investmentPlan.value,
          isTransfer: isTransfer.value
        },
        bussRegisteredEquipmentList: [
          {
            ...formData.value,
            technicalFileList: techFileViewList.value.map(file => ({
              fileId: file.fileId
            })),
            bussEquipmentAccessoryList: attachments.value.map(item => ({
              equId: item.equId,
              baseCode: item.baseCode,
              name: item.name,
              unit: item.unit,
              quantity: item.quantity,
              manufacturer: item.manufacturer,
              serialNo: item.serialNo
            })),
            fileList: fileViewList.value.map(file => ({
              fileId: file.fileId
            }))
          }
        ]
      }
    };

    // 构建最终提交数据结构
    const submitData = {
      processDefinitionId: 'equipment_acceptance:1:1910232819951554561',
      variables: {
        formData: JSON.stringify(innerFormData)
      }
    };

    const res = await EquipmentAcceptanceApi.submit(submitData);

    if (res.success) {
      message.success('提交成功');
    } else {
      message.error(res.message || '提交失败');
    }
  } catch (error) {
    message.error('提交失败：' + error.message);
  } finally {
    submitting.value = false;
  }
};

const formData = ref({
  id: null,
  baseCode: '', // 基础编码
  code: '', // 编码
  equType: null, // 设备类别
  equSubType: null, // 设备种类
  equName: null, // 设备名称
  equModel: null, // 规格型号
  equModelNote: '', // 型号备注
  equNature: null, // 设备性质
  purchaseYear: '', // 购置年度
  equSource: null, // 设备来源
  unit: '', // 单位
  num: 1, // 数量
  equContractPriceTax: null, // 设备合同价（含税）
  manufacturer: '', // 生产厂家
  factoryNumber: '', // 出厂编号
  productionDate: null, // 出厂日期
  contractNumber: '', // 合同编号
  acceptanceNumber: null, // 验收单号
  acceptanceChar: '', // 验字
  acceptanceDate: null, // 验收日期
  power: '', // 功率
  importance: null, // 设备重要性
  modelCode: '', // 设备型号编码
  serviceInterval: '', // 保养周期
  propertyOrg: null, // 产权单位
  managementStatus: null, // 管理状态
  managementOrg: null, // 管理单位
  equCondition: null, // 设备状态
  useOrg: null, // 使用单位
  storageLocationStr: null, // 存放地点
  financialNumber: '', // 财务卡片编号
  financialOrg: '', // 财务组织
  financialOriginalValue: null, // 财务原值
  fixedAssets: '', // 固定资产分类
  sourceOfFunds: '', // 资金来源
  deferredIncome: '', // 递延收益
  alreadyAccruedMonths: '', // 已计提折旧月份
  depreciationMethod: null, // 折旧方式
  depreciationPeriod: '', // 折旧年限
  depreciationMonth: '', // 折旧月份
  residualRate: null, // 残值率
  netSalvage: null, // 预计净残值
  historicalOriginalValue: null, // 历史原值
  depreciationAmount: null, // 累计折旧额
  currentMonthDepreciationAmount: null, // 当月折旧额
  netWorth: null, // 净值
  taxRate: 13, // 税率
  approvalDescription: '', // 审批说明
});

// 附件列表数据
const attachments = ref([
  {
    name: '',
    unit: '',
    quantity: 1,
    manufacturer: '',
    serialNo: ''
  }
]);

// 定义表格列
const attachmentColumns = [
  {
    title: '序号',
    dataIndex: 'serial',
    width: '5%',
    customRender: ({ index }) => index + 1
  },
  {
    title: '名称及型号',
    dataIndex: 'name',
    width: '25%'
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: '10%'
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    width: '10%'
  },
  {
    title: '生产厂家',
    dataIndex: 'manufacturer',
    width: '25%'
  },
  {
    title: '出厂编号',
    dataIndex: 'serialNo',
    width: '25%'
  }
];

// 添加新附件
const addAttachment = () => {
  attachments.value.push({
    name: '',
    unit: '',
    quantity: 1,
    manufacturer: '',
    serialNo: ''
  });
};

// 删除附件
const removeAttachment = (index) => {
  attachments.value.splice(index, 1);
};

// 处理普通附件上传
const handleChange = (info) => {
  console.log('当前上传的文件:', info.file);
  console.log('所有文件列表:', info.fileList);

  // 保持原始fileList与上传组件同步
  fileList.value = info.fileList;

  // 更新显示用的文件列表
  fileViewList.value = info.fileList
    .filter(file => file.status === 'done')  // 只保留上传完成的文件
    .map(file => ({
      fileId: file.response.data.fileId,fileUrl:file.response.data.fileUrl,
      fileName: file.name,
      uid: file.uid,
      status: file.status
    }));

  // 处理单个文件的状态提示
  if (info.file.status === 'error') {
    message.error('上传出错：' + info.file.error.message);
  }
};

// 处理技术资料上传
const handleTechFileChange = (info) => {
  // 保持原始列表与上传组件同步
  techFileList.value = info.fileList;

  if (info.file.status === 'done' && info.file.response) {
    const response = info.file.response;
    if (response.success) {
      // 更新显示列表
      techFileViewList.value = [{
        fileId: response.data.fileId,
        fileName: info.file.name,
        uid: info.file.uid,
        status: 'done'
      }];

      // 更新formData中的techFile用于显示
      formData.value.techFile = {
        fileId: response.data.fileId,
        fileName: info.file.name
      };
    } else {
      message.error('技术资料上传失败');
    }
  } else if (info.file.status === 'error') {
    message.error('上传出错：' + info.file.error.message);
  }
};



// 审批记录相关方法
const showApprovalRecord = () => {
  // 处理审批记录的显示逻辑
  console.log('显示审批记录');
};

const initializeData = async (id) => {
  loading.value = true;
  try {
    const { data } = await RegisteredEquipmentApi.getToDo({procInstanceId: id});
    console.log('获取的数据：', data.bussTransferForm.isTransfer);
    // 处理业务标题和关联投资计划
    if (data.bussTransferForm) {
      businessTitle.value = data.bussTransferForm.applyTitle || '';
      investmentPlan.value = data.bussTransferForm.investmentPlan || '';
      isTransfer.value = data.bussTransferForm.isTransfer || '1'; // 使用'1'作为默认值
    }

    // 处理设备基本信息
    if (data.bussRegisteredEquipmentList?.length > 0) {
      const equipmentData = data.bussRegisteredEquipmentList[0];

      // 更新基本表单数据
      formData.value = {
        ...formData.value,
        ...equipmentData
      };
      console.log(555,equipmentData.bussEquipmentAccessoryList);

      // 处理主要配件数据
      if (equipmentData.bussEquipmentAccessoryList?.length > 0) {
        attachments.value = equipmentData.bussEquipmentAccessoryList.map(item => ({
          name: item.name || '',
          unit: item.unit || '',
          quantity: item.quantity || 1,
          manufacturer: item.manufacturer || '',
          serialNo: item.serialNo || ''
        }));
      }

      // 处理附件数据
      if (equipmentData.fileList?.length > 0) {
        fileViewList.value = equipmentData.fileList.map(file => ({
          fileId: file.fileId,
          fileName: file.fileOriginName,
          fileUrl: file.fileUrl,
          uid: file.fileId // 使用fileId作为uid
        }));
      }

      // 处理技术资料
      if (equipmentData.technicalFileList?.length > 0) {
        techFileViewList.value = equipmentData.technicalFileList.map(file => ({
          fileId: file.fileId,
          fileName: file.fileOriginName,
          fileUrl: file.fileUrl,
          uid: file.fileId
        }));

        // 更新formData中的技术资料显示
        formData.value.techFile = {
          fileId: techFileViewList.value[0].fileId,
          fileName: techFileViewList.value[0].fileName
        };
      }
    }

  } catch (error) {
    message.error('获取数据失败：' + error.message);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  const route = useRouter();
  const id = route.currentRoute.value.query.id;
  if (id) {
    initializeData(id);
  } else {
    message.error('未找到审批单ID');
  }
});
</script>

<style lang="less" scoped>
.approval-record-btn {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  z-index: 100;

  .bg-image {
    display: block;
    max-width: 100%;
    height: auto;
    object-fit: contain;
    background-color: transparent;
  }

  .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .icon {
      width: 15px;
      height: 15px;
      margin-bottom: 4px;
    }

    .text {
      writing-mode: vertical-lr;
      font-size: 14px;
      color: #333;
      letter-spacing: 2px;
    }
  }
}
.equipment-acceptance {
  width: 100%;
  max-width: 1920px; // 最大宽度限制
  height: calc(100vh - 100px);
  overflow: auto;
  margin: 0 auto; // 居中
  padding: 24px 9.06vw 24px 8.18vw; // 转换为vw: 174/1920*100=9.06, 157/1920*100=8.18
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.4);
box-sizing: border-box;
border: 1px solid #FFFFFF;
backdrop-filter: blur(398px);
box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);
  .form-title {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 24px;
  }

  .section {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;

      .blue-bar {
        width: 3px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
      }
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: clamp(16px, 2vw, 24px);
    margin-bottom: 24px;
    justify-content: space-between;

    .form-item {
      display: flex;
      flex-direction: column;
      min-width: 300px; // 最小宽度
      max-width: 300px; // 最大宽度

      .label {
        height: 32px;
        line-height: 32px;
        margin-bottom: 8px;
        color: #0C0B0B;

        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 4px;
        }
      }

      .value {
        width: 100%;
        height: 32px;
        background: rgba(255, 255, 255, 0.6);
box-sizing: border-box;
/* -line-列表 */
border: 0.5px solid rgba(30, 41, 64, 0.08);
        :deep(.ant-input),
        :deep(.ant-select),
        :deep(.ant-picker),
        :deep(.ant-input-number) {
          width: 100%;
          height: 32px;
        }

        :deep(.ant-input-number-input) {
          height: 16px;
          line-height: 16px;
        }

        :deep(.ant-select-selector) {
          height: 32px !important;

          .ant-select-selection-item {
            line-height: 32px;
          }
        }

        :deep(.ant-picker) {
          .ant-picker-input {
            height: 16px;
            line-height: 16px;

            input {
              height: 16px;
              line-height: 16px;
            }
          }
        }
      }
    }
  }

  .acceptance-no {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    max-width: 300px; // 与其他表单项保持一致的最大宽度

    :deep(.first-input) {
      width: 65% !important; // 验资前面的输入框占65%
    }

    :deep(.second-input) {
      width: 35% !important; // 号前面的输入框占35%
    }

    .separator {
      color: #666;
      white-space: nowrap;
    }
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .equipment-acceptance {
    padding: 24px 6vw;

    .form-grid {
      gap: 16px;
    }
  }
}

@media screen and (max-width: 1366px) {
  .equipment-acceptance {
    padding: 24px 4vw;

    .form-grid {
      grid-template-columns: repeat(2, minmax(300px, 1fr));
    }
  }
}

@media screen and (max-width: 1024px) {
  .equipment-acceptance {
    padding: 24px 2vw;

    .form-grid {
      grid-template-columns: repeat(1, 1fr);

      .form-item {
        max-width: none;
      }
    }
  }
}

.percentage-input {
  position: relative;
  display: flex;
  align-items: center;

  .ant-input {
    width: 100%;
  }

  .percentage-symbol {
    position: absolute;
    right: 12px;
    color: #666;
  }
}

.section {
  .a-table {
    margin-top: 16px;
  }
}

.upload-section {
  .upload-header {
    display: flex;
    align-items: center;
    gap: 12px;

    .upload-button {
      display: inline-flex;
      align-items: center;
      padding: 4px 15px;
      background: #0085FF;
      border-radius: 2px;
      color: white;
      cursor: pointer;
      font-size: 14px;

      .anticon {
        margin-right: 4px;
      }
    }

    .upload-tip {
      color: #999;
      font-size: 12px;
    }
  }

  .file-list {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    //display: grid;
    //grid-template-columns: repeat(3, 0.3fr);
    //gap: 16px;

    .file-item {
      display: flex;
      align-items: center;
      padding: 8px;
      // background: #F7F9FC;
      border-radius: 2px;
      width: 32%;

      .file-link{
        word-break:break-all;
      }

      .file-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #2D5CF6;
      }

      .file-name {
        flex: 1;
        font-size: 14px;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-actions {
        margin-left: 8px;

        .delete-icon {
          color: #999;
          cursor: pointer;

          &:hover {
            color: #FF4B4B;
          }
        }

        .anticon-sync {
          color: #2D5CF6;
        }
      }
    }
  }
}

.tech-file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;

  .file-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      color: #2D5CF6;
    }

    .file-name {
      color: #333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .delete-icon {
    color: #999;
    cursor: pointer;

    &:hover {
      color: #ff4d4f;
    }
  }
}

.tech-upload-button {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  color: #666;
  font-size: 14px;

  .anticon {
    margin-right: 4px;
    font-size: 14px;
  }

  &:hover {
    color: #2D5CF6;
    border-color: #2D5CF6;
  }
}

.value {
  :deep(.ant-input-affix-wrapper) {
    .ant-input-suffix {
      color: #666;
    }
  }
}

.bottom-buttons {
  margin-top: 150px;
  margin-bottom: 89rpx;
  bottom: 180px; // 修改为距离底部80px
  left: 0;
  right: 0;
  padding: 0 32px; // 调整左右内边距
  text-align: right;
  z-index: 100;

  .reject-btn {
    height: 32px;
    padding: 0 16px;
    border: 1px solid #f30d05;
    color: #f30d05;
    background: transparent !important;
    border-radius: 4px;

    &:hover {
      border-color: #409EFF;
      color: #409EFF;
    }
  }

  .submit-btn {
    height: 32px;
    padding: 0 16px;
    margin-left: 27px; // 调整按钮之间的间距
    background: #409EFF;
    border-radius: 4px;
    border: none;

    &:hover {
      background: #66B1FF;
    }
  }
}
.equipment-info-table {
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  border: 0.5px solid rgba(30, 41, 64, 0.08);; // 加深整体边框颜色

  .info-row {
    display: flex;

    &:last-child {
      border-bottom: none;
    }

    .info-cell {
      flex: 1;
      display: flex;
      min-height: 44px;
      border-bottom: 0.5px solid #d9d9d9; // 加深底部边框颜色

      .label {
        width: 120px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        background: #E2F0FF;
        color: #333;
        font-size: 14px;
        padding: 0 16px;
        box-sizing: border-box;
        border-bottom: none; // 移除label的单独底边
      }

      .value {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 0 16px;
        background: #fff;
        color: #333;
        font-size: 14px;
        border-bottom: none; // 移除value的单独底边
      }

      border-right: 1px solid #d9d9d9; // 加深右侧边框颜色
      &:last-child {
        border-right: none;
      }
    }

    &:last-child {
      .info-cell {
        border-bottom: none;
      }
    }
  }

  // 技术资料样式
  .tech-files {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    .preview-btn, .view-btn {
      display: inline-flex;
      align-items: center;
      color: #1890FF;
      cursor: pointer;
      margin-right: 16px;

      .anticon {
        margin-right: 4px;
      }

      &:hover {
        opacity: 0.8;
      }
    }
  }
}
.section {
  :deep(.ant-table-thead) {
    > tr > th {
      background: #E2F0FF;
      box-sizing: border-box;
      border: 0.5px solid rgba(30, 41, 64, 0.08);

      // 重置 hover 状态的背景色
      &:hover {
        background: #E2F0FF !important;
      }
    }
  }

  // 确保表格边框样式一致
  :deep(.ant-table) {
    border: 0.5px solid rgba(30, 41, 64, 0.08);
  }

  :deep(.ant-table-cell) {
    border: 0.5px solid rgba(30, 41, 64, 0.08) !important;
  }
}
.approval-description {
  padding: 16px;
  background: transparent;

  .ant-textarea {
    width: 100%;
    border: 1px solid #E2F0FF;
    border-radius: 4px;
    box-sizing: border-box;
    &:hover, &:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}
.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>

















