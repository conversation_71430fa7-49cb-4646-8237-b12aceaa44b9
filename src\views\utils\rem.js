// utils/rem.js
export function setRemUnit(designWidth = 1920) {
  const docEl = document.documentElement;
  const clientWidth = docEl.clientWidth || window.innerWidth;
  
  if (!clientWidth) return;
  
  // 计算根字体大小，例如设计稿1920px，根字体设为10px
  const fontSize = (clientWidth / designWidth) * 16;
  
  // 设置根字体大小
  docEl.style.fontSize = `${fontSize}px`;
}

// 初始化
export function initRem(designWidth = 1920) {
  setRemUnit(designWidth);
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    setRemUnit(designWidth);
  });
  
  // 监听页面方向变化
  window.addEventListener('orientationchange', () => {
    setRemUnit(designWidth);
  });
}
