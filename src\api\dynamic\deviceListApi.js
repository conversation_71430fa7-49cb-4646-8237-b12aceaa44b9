import Request from '@/utils/request-util';

/**
 * 表单管理api
 *
 * <AUTHOR>
 * @date 2021/7/5 21:14
 */
export class deviceListApi {
  /**
   * 获取在籍设备列表
   *
   * <AUTHOR>
   * @date 2022/5/8 20:36
   */
  static pageRegisteredToOutboundFrom(params) {
    return Request.getAndLoadData('/apiBus/registeredEquipment/dynamicPage', params);
  }
  static downLoad(params) {
        return Request.downLoad('/api/apiBus/registeredEquipment/exportDynamicPage', params);
      }
}
