import Request from '@/utils/request-util';

/**
 * 枚举接口
 *
 * <AUTHOR> @date 2024/01/09
 */
export class EnumApi {
  /**
   * 获取枚举列表
   *
   * @param {String} enumName - 枚举名称
   * @returns {Promise} 返回枚举数据
   */
  static getEnumList(params) {
    return Request.get('/apiBus/common/getEnumList', params);
  }

  static uploadExcel(params) {
    return Request.post('/sysFileInfo/upload', params);
  }

  static getProcessDefinitionByKey(params) {
    return Request.get('/workFlowCommon/getProcessDefinitionByKey', params);
  }

  //获取全球公共树
  static getAddressTree(params) {
    return Request.get('/apiBus/common/getAddressTree', params);
  }

  //获取地址单位
  static getAddrOrgList(params) {
    return Request.get('/apiBus/common/getAddrOrgList', params);
  }

  //获取使用单位
  static getUseOrgList(params) {
    return Request.get('/apiBus/common/getUseOrgList', params);
  }

  //获取管理单位
  static getVirtualManagementOrgList(params) {
    return Request.get('/apiBus/common/getVirtualManagementOrgList', params);
  }

  //获取办事员

  static getUserListBy0rg(params) {
    return Request.get('/apiBus/common/getUserListByOrg', params);
  }


  //获取产权单位
  static getPropertyOrgList(params) {
    return Request.get('/apiBus/common/getPropertyOrgList', params);
  }

  //获取管理单位
  static getManagementOrgList(params) {
    return Request.get('/apiBus/common/getManagementOrgList', params);
  }

  static checkTaskExist(params) {
    return Request.get('/workFlowCommon/checkTaskExist', params);
  }
}
