<!-- 处置信息 -->
<template>
  <a-form ref="formRef" :model="detailData" :rules="rules" :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
    :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }">
    <a-row :gutter="16">
      <a-col :md="12" :sm="24" :xs="24" style="padding-right: 0px;">
        <div class="rows">
          <div class="rows_1">
            <span class="rows_1_1">
              经办人
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{  detailData.handledUserStr }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              经办单位
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
                {{ detailData.handledOrgStr }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              处置流程
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              <a href="#" @click="gotoView">
             {{ detailData.worksheetId }}
             </a>
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              处置日期
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ detailData.disposalDate }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              收购方名称
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ detailData.acquirerName }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              处置方式
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ detailData.disposalTypeStr }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
             
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
            
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
            
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
            
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
             
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
            
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
             
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
             
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
             
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
       
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
            
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
             
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
             
            </span>
          </div>
        </div>
      </a-col>
      <a-col :md="12" :sm="24" :xs="24" style="padding-left: 0px;">
        <div class="rows2">
          <div class="rows2_2">
            <span class="rows2_2_2">
              评估单位
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ detailData.assessmentOrg }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              评估值
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ detailData1.assessmentMoney }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              出售价格
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ detailData1.disposalMoney }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              税率
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ detailData1.disposalTaxRate }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              税金
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ detailData1.disposalTaxeMoney }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              处置资料
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ detailData.disposeFileList }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
             
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
             
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
          
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
             
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
            
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
            
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              
            </span>
          </div>
        </div>
      
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
             
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
            
            </span>
          </div>
        </div>
      </a-col>
    </a-row>
  </a-form>
</template>

<script setup>
import { defineComponent, reactive, toRefs, ref, watch } from 'vue';
import iconData from 'ele-admin-pro/es/ele-icon-picker/icons';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: '设备详情'
  }
});
const gotoView=()=>{
  const queryParams2 = new URLSearchParams({
        id:detailData.value.procInstanceId,
        procInsId: detailData.value.procInstanceId,
        worksheetId: detailData.value.worksheetId, // 传递工作单ID到待办页面
        fileName:"disposalApproval"
      }).toString();

      window.open(`/done?${queryParams2}`, '_blank');
}
const detailData = ref({})
const detailData1 = ref({})
watch(() => props.data, (val) => {
  console.log(val)
  if( val){
    detailData.value = val.disposaledEquipmentTransferForm;
    detailData1.value = val.bussDisposaledEquipment
  }
 
}, { immediate: true });
</script>


<style lang="less" scoped>
.rows {
  display: flex;
  height: 53px;
  align-items: center;
  border: 1px solid rgba(172, 180, 201, 0.2);

  .rows_1 {
    width: 145px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #F1F8FF;
    border-right: 1px solid rgba(172, 180, 201, 0.2);

    .rows_1_1 {
      margin-right: 16px;
    }
  }

  .rows_2 {
    flex:1; // 20250724 王文胜
    .rows_2_2 {
      margin-left: 16px;
    }
  }
}


.rows2 {
  display: flex;
  height: 53px;
  align-items: center;
  border-top: 1px solid rgba(172, 180, 201, 0.2);
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);

  .rows2_2 {
    width: 145px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: rgba(241, 248, 255, 1);
    border-right: 1px solid rgba(172, 180, 201, 0.2);

    .rows2_2_2 {
      margin-right: 16px;
    }
  }

  .rows2_3 {
    flex:1; // 20250724 王文胜
    .rows2_3_3 {
      margin-left: 16px;
    }
  }
}

.rows1 {
  border-top: 0;
  border-left: 1px solid rgba(172, 180, 201, 0.2);
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);
}

.rows22{
  border-top: 0;
  border-left: 0;
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);
}
</style>



