import Request from '@/utils/request-util';

/**
 * 表单管理api
 *
 * <AUTHOR>
 * @date 2021/7/5 21:14
 */
export class leaseExternallyApi {
  /**
   * 分页获取列表
   *
   * <AUTHOR>
   * @date 2022/5/8 20:36
   */
  static todoTaskPage(params) {
    return Request.getAndLoadData('/flowableTodoTask/page', params);
  }

  /**
   * 分页获取列表
   *
   * <AUTHOR>
   * @date 2022/5/8 20:36
   */
  static doneTaskPage(params) {
    return Request.getAndLoadData('/flowableDoneTask/page', params);
  }

  /**
     * 分页获取列表
     *
     * <AUTHOR>
     * @date 2022/5/8 20:36
     */
  static findPage(params) {
    return Request.getAndLoadData('/flowableInstance/my', params);
  }

  static save(params) {
      return Request.post('/apiBus/leaseTransferFormEquipment/saveDraft', params);
    }
  
    static getProcessDefinitionByKey(params) {
      return Request.get('/workFlowCommon/getProcessDefinitionByKey', params);
    }
  
    static start(params) {
      return Request.post('/flowableHandleTask/start', params);
    }
  
    static submit(params) {
      return Request.post('/flowableHandleTask/submit', params);
    }
  
    static getNewBuilt() {
      return Request.get('/apiBus/leaseTransferFormEquipment/newBuilt');
    }
    
    static getToDo(params) {
      return Request.get('/apiBus/leaseTransferFormEquipment/getWorksheetInfo', params);
    }

  /**
   * 获取使用项目列表
   *
   * @param {Object} params - 请求参数
   * @param {String} params.useOrgId - 使用单位ID
   * @returns {Promise} 返回使用项目列表
   */
  static getUseProjectList(params) {
    return Request.get('/apiBus/leaseExternally/getUseProjectList', params);
  }

  static end(params) {
    return Request.post('/flowableInstance/end', params);
  }
  
}

