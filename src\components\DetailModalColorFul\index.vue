<template>
  <a-modal
    v-model:visible="visible"
    :width="1000"
    :title="title"
    :maskClosable="false"
    :footer="null"
    @cancel="onClose"
    class="detail-modal1"
    :destroyOnClose="true"
  >
    <!-- 右上角历史记录按钮 -->
    <template #title>
      <div class="modal-title-container">
        <span>{{ title }}</span>
        <div
          v-if="detailData.type === 'reg'"
          class="history-link"
          @click="showHistory"
        >
          <span style="color: #1890ff; cursor: pointer;">历史记录 &gt;</span>
        </div>
      </div>
    </template>

    <!-- 标签页 -->
    <a-tabs v-model:activeKey="activeKey" @change="tabChange">
      <a-tab-pane :key="tabItem.key" :tab="tabItem.name" v-for="tabItem in tabList"></a-tab-pane>
    </a-tabs>

    <!-- 内容区域 -->
    <div class="modal-content">
      <!-- 只在模态框可见时渲染内容 -->
      <template v-if="visible">
        <!-- 设备信息 -->
        <device-form1 v-if="activeKey === '1'" :data="detailData" />
        <!-- 使用情况 -->
        <usage-form1 v-if="activeKey === '2'" :data="detailData" />
        <!-- 财务信息 -->
        <finance-form1 v-if="activeKey === '3'" :data="detailData" />
        <!-- 租赁费用 -->
        <lease-form1 v-if="activeKey === '4'" :data="detailData" />
        <!-- 报废信息 -->
        <scrap-form1 v-if="activeKey === '5' && detailData.type!=='reg'" :data="detailData" />
        <!-- 处置信息 -->
        <disposal-form1 v-if="activeKey === '8' && detailData.type==='disposal'" :data="detailData" />
        <!-- 主要附件 -->
        <main-form1 v-if="activeKey === '6'" :data="detailData" />
        <!-- 动态信息 -->
        <dynamic-form1 v-if="activeKey === '7'" :data="detailData" />
        <!-- 相关附件 -->
        <file-form1 v-if="activeKey === '9'&& detailData.type==='reg'" :data="detailData" />
      </template>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue';
import DeviceForm1 from '@/components/DetailModalColorFul/components/device_form.vue';
import UsageForm1 from '@/components/DetailModalColorFul/components/usage_form.vue';
import FinanceForm1 from '@/components/DetailModalColorFul/components/finance_form.vue';
import LeaseForm1 from '@/components/DetailModalColorFul/components/lease_form.vue';
import MainForm1 from '@/components/DetailModalColorFul/components/main_form.vue';
import DynamicForm1 from '@/components/DetailModalColorFul/components/dynamic_form.vue';
import ScrapForm1 from '@/components/DetailModalColorFul/components/scrap_form.vue';
import DisposalForm1 from '@/components/DetailModalColorFul/components/disposal_form.vue';
import FileForm1 from '@/components/DetailModalColorFul/components/file_form.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: '设备详情'
  },
});

const emit = defineEmits(['update:visible', 'close']);

const activeKey = ref('1');
const detailData = ref({
  type: 'reg',
  code: "",
  // 添加其他可能被访问的属性的默认值
  name: "",
  model: "",
  manufacturer: "",
  // 根据 device-form.vue 中访问的属性添加更多默认值
});

let tabList = ref([]);

watch(() => props.data, (val) => {
  if (val) {
    // 使用合并而不是替换，保留默认值
    detailData.value = { ...detailData.value, ...val };
  } else {
    // 如果 val 为 null 或 undefined，重置为默认值
    detailData.value = {
      type: 'reg',
      code: "",
      name: "",
      model: "",
      manufacturer: "",
      // 其他默认值
    };
  }

  // 设置标签页列表
  if(detailData.value.type === 'reg'){
    tabList.value = [
      { key: '1', name: '设备信息' },
      { key: '2', name: '使用情况' },
      { key: '3', name: '财务信息' },
      { key: '4', name: '租赁费用' },
      { key: '6', name: '主要附件' },
      { key: '7', name: '动态信息' },
      { key: '9', name: '相关附件' }
    ];
  } else if(props.data?.type === 'scrap'){
    tabList.value = [
      { key: '1', name: '设备信息' },
      { key: '2', name: '使用情况' },
      { key: '3', name: '财务信息' },
      { key: '4', name: '租赁费用' },
      { key: '5', name: '报废信息' },
      { key: '6', name: '主要附件' },
      { key: '7', name: '动态信息' },
    ];
  } else if(props.data?.type === 'disposal'){
    if(detailData.value.scrapedEquipmentTransferForm){
      tabList.value = [
        { key: '1', name: '设备信息' },
        { key: '2', name: '使用情况' },
        { key: '3', name: '财务信息' },
        { key: '4', name: '租赁费用' },
        { key: '5', name: '报废信息' },
        { key: '6', name: '主要附件' },
        { key: '7', name: '动态信息' },
        { key: '8', name: '处置信息' },
      ];
    } else {
      tabList.value = [
        { key: '1', name: '设备信息' },
        { key: '2', name: '使用情况' },
        { key: '3', name: '财务信息' },
        { key: '4', name: '租赁费用' },
        { key: '6', name: '主要附件' },
        { key: '7', name: '动态信息' },
        { key: '8', name: '处置信息' },
      ];
    }
  }
}, { immediate: true });

const tabChange = (key) => {
  activeKey.value = key;
};

const onClose = () => {
  emit('update:visible', false);

  // 使用 setTimeout 延迟重置数据，等待模态框关闭动画完成
  setTimeout(() => {
    detailData.value = { type: 'reg', code: "" };
    emit('close');
  }, 300);
};

// 确保 visible 变化时也处理 detailData
watch(() => props.visible, (val) => {
  if (!val) {
    // 当模态框关闭时，延迟重置 detailData
    setTimeout(() => {
      detailData.value = { type: 'reg', code: "" };
    }, 300);
  }
});

// 历史记录功能（占位，需要实现）
const showHistory = () => {
  console.log('显示历史记录');
  // 这里可以实现显示历史记录的逻辑
};
</script>
<style>
/* 修改模态框背景颜色 */
.detail-modal1 .ant-modal-content {
  background-color: #131E46 !important; /* 深蓝色背景 */
  background-image: url('@/assets/equipment/map/modal-bg.png'); /* 背景图片 */
  background-repeat: no-repeat;
  background-size: cover;
  border: 1px solid #1E3A6E;
  border-radius: 4px;
  height: 800px;
}

/* 修改模态框标题栏样式 */
/* 直接选择 header 元素 */
.detail-modal1 .ant-modal-header {
  background-color: #131E46 !important;
  border-bottom: 1px solid #1E3A6E !important;
  padding: 16px 24px !important;
  position: relative !important;
}

.detail-modal1 .ant-modal-header::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 3px !important;
  background: linear-gradient(90deg, #0085FF, #00C6FF) !important;
}

/* 修改标题文字颜色 */
.detail-modal1 .ant-modal-title {
  color: #fff;
  font-size: 18px;
  font-weight: bold;
}

.detail-modal1 .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #1769EA !important;
}

.detail-modal1 .ant-tabs-tab.ant-tabs-tab-active {
  background-color: transparent !important;
}

.detail-modal1 .ant-tabs-tab{
  color: #fff !important;
}


.detail-modal1 .ant-radio-button-wrapper{
  background-color: transparent !important;
    color: #fff !important;
}


.detail-modal1 .ant-table-cell{
  color: #fff !important;
}

.detail-modal1 .ant-modal-confirm-body .ant-modal-confirm-title{
  color: #fff !important;
}

/* 修改关闭按钮颜色 */
.detail-modal1 :deep(.ant-modal-close) {
  color: #fff;
}

/* 修改标签页样式 */
.detail-modal1 :deep(.ant-tabs-nav) {
  margin-bottom: 16px;
  background-color: transparent;
}

.detail-modal1 :deep(.ant-tabs-tab) {
  color: rgba(255, 255, 255, 0.7);
  padding: 12px 16px;
}

.detail-modal1 :deep(.ant-tabs-tab-active) {
  color: #fff;
  background-color: transparent;
}

.detail-modal1 :deep(.ant-tabs-tab-active .ant-tabs-tab-btn) {
  color: #0066ff !important;
  font-weight: bold;
}

.detail-modal1 :deep(.ant-tabs-ink-bar) {
  background-color: #0066ff;
}

/* 表格样式 */
.detail-modal1 :deep(.ant-table) {
  background-color: transparent;
  color: #fff;
}

.detail-modal1 :deep(.ant-table-thead > tr > th) {
  background-color: rgba(30, 58, 110, 0.5);
  color: #fff;
  border-bottom: 1px solid #1E3A6E;
}

.detail-modal1 :deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid #1E3A6E;
  color: rgba(255, 255, 255, 0.85);
}

.detail-modal1 :deep(.ant-table-tbody > tr:hover > td) {
  background-color: rgba(30, 58, 110, 0.3);
}

/* 表单样式 */
.modal-content {
  color: #fff;
}

.modal-content :deep(.ant-form-item-label > label) {
  color: rgba(255, 255, 255, 0.85);
}

.modal-content :deep(.ant-input),
.modal-content :deep(.ant-select-selector) {
  background-color: rgba(30, 58, 110, 0.3);
  border: 1px solid #1E3A6E;
  color: #fff;
}

/* 标题容器样式 */
.modal-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.ant-modal-body{
   background-color: rgba(20, 30, 71, 0.89);
}

.ant-modal-body .ant-modal-confirm-title{
  color:#fff
}

.ant-modal-body .ant-modal-confirm-content{
  color:#fff
}

/* 修改内容区域样式 */
.modal-content {
  max-height: calc(100vh - 426px);
  overflow-y: auto;
  padding: 0 16px;
  color: rgba(255, 255, 255, 0.8); /* 内容文字颜色 */
  background: #0A1E42 !important;
}

.ant-modal-content{
   background: #0A1E42 !important;
}

/* 自定义滚动条样式 */
.modal-content::-webkit-scrollbar {
  width: 8px;
  background-color: #0D2456;
}

.modal-content::-webkit-scrollbar-thumb {
  background-color: #1E3A6E;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-track {
  background-color: #0D2456;
}

/* 其他样式保持不变 */
.modal-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.history-link {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.history-link:hover span {
  text-decoration: underline;
  color: #4B9EFF;
}

/* 修改历史记录链接颜色 */
.history-link span {
  color: #4B9EFF !important;
  cursor: pointer;
}

.ant-modal-close{
  color: #ffffff;
}
</style>

