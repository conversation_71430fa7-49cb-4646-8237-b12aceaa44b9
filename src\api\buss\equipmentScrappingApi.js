import Request from '@/utils/request-util';

/**
 * 设备验收API
 *
 * <AUTHOR> @date 2024/01/09
 */
export class equipmentScrappingApi {
  /**
   * 获取新建数据
   *
   * <AUTHOR> @date 2024/01/09
   */
  static submit(params) {
    return Request.post('/flowableHandleTask/start', params);
  }

  static getNewBuilt() {
    return Request.get('/apiBus/scrapedEquipment/newBuilt');
  }

  static getczNewBuilt() {
    return Request.get('/apiBus/disposaledEquipment/newBuilt');
  }

  static getProcessDefinitionByKey(key) {
    return Request.get('/workFlowCommon/getProcessDefinitionByKey',key );
  }

   static save(params) {
      return Request.post('/apiBus/scrapedEquipment/saveDraft', params);
    }

    static getWorksheetInfo(params) {
        return Request.get('/apiBus/scrapedEquipment/getWorksheetInfo', params);
      }

    static savecz(params) {
      return Request.post('/apiBus/disposaledEquipment/saveDraft', params);
    }

    static getData(params) {
      return Request.getAndLoadData('/apiBus/registeredEquipment/dynamicPage', params);
    }

    static getData1(params) {
      return Request.getAndLoadData('/apiBus/disposaledEquipment/pageRegisteredToScrapedForm', params);
    }



    static getRegistered(params) {
      return Request.getAndLoadData('/apiBus/disposaledEquipment/pageScrapedToDisposaledForm', params);
    }

    static getWorksheetInfos(params) {
        return Request.get('/apiBus/disposaledEquipment/getWorksheetInfo', params);
      }
}
