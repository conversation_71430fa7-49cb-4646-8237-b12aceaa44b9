import Request from '@/utils/request-util';

/**
 * 设备验收API
 *
 * <AUTHOR> @date 2024/01/09
 */
export class AnalysisApi {
  /**
   * 获取新建数据
   *
   * <AUTHOR> @date 2024/01/09
   */
  static getData(params) {
    return Request.get('/analysis/reportRegisteredReal/getReport',params);
  }



  static getChildrenList(params) {
    return Request.get('/analysis/reportRegisteredReal/getReportDataList',params);
  }


  static getCountForProvinceMap(params) {
    return Request.get('/analysis/analysisHome/getCountForProvinceMap',params);
  }

  static getCountForCityMap(params) {
    return Request.get('/analysis/analysisHome/getCountForCityMap',params);
  }

  static getBarAllocation(params) {
    return Request.get('/analysis/analysisHome/getBarAllocation',params);
  }

  static getMapPoints(params) {
    return Request.get('/analysis/analysisHome/getOrgByLngAndLat', params);
  }

  static getCountForAll(params) {
    return Request.get('/analysis/analysisHome/getCountForAll', params);
  }

  static getEquipmentListByLevel(params) {
    return Request.get('/analysis/analysisHome/findEquipmentPage', params);
  }

  static getEquipmentFullInfo(params) {
    return Request.get('/apiBus/registeredEquipment/getEquipmentFullInfo', params);
  }
}
