import Request from '@/utils/request-util';

/**
 * 单位api
 *
 * <AUTHOR>
 * @date 2021/4/7 11:47
 */
export class UnitApi {
  /**
   * 获取产权单位
   *
   * <AUTHOR>
   * @date 2021/4/7 11:47
   */
  static async getPropertyOrgList(params) {
    return await Request.getAndLoadData('/apiBus/common/getPropertyOrgList', params);
  }

  /**
   * 获取管理单位
   *
   * <AUTHOR>
   * @date 2021/8/8 22:55
   */
  static async getManagementOrgList(params) {
    return await Request.getAndLoadData('/apiBus/common/getManagementOrgList',params);
  }

  /**
   * 获取使用单位
   *
   * <AUTHOR>
   * @date 2021/8/8 22:55
   */
  static async getUseOrgList(params) {
    return await Request.getAndLoadData('/apiBus/common/getUseOrgList',params);
  }

  /**
   * 获取地点
   *
   * <AUTHOR>
   * @date 2021/8/8 22:55
   */
  static async getAddrOrgList(pId) {
    return await Request.getAndLoadData('/apiBus/common/getAddrOrgList?pId='+pId);
  }

  /**
   * 获取人员
   *
   * <AUTHOR>
   * @date 2021/8/8 22:55
   */
  static async getUserListByOrg(params) {
    return await Request.getAndLoadData('/apiBus/common/getUserListByOrg', params);
  }

  /**
   * 获取使用单位树结构
   *
   * <AUTHOR>
   * @date 2021/8/8 22:55
   */
  static async getUseOrgTree(params) {
    return await Request.getAndLoadData('/apiBus/common/getUseOrgTree',params);
  }

static async getRegistered(params) {
    return await Request.getAndLoadData('/apiBus/registeredEquipment/dynamicEquipmentByCode',params);
  }

}
