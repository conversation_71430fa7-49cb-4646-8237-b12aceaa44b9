<template>
  <div class="tree-cascade-select">
    <a-select
      v-model:value="selectedValues"
      :mode="props.multiple ? 'multiple' : undefined"
      :placeholder="props.placeholder"
      :options="selectOptions"
      :open="dropdownVisible"
      @dropdownVisibleChange="handleDropdownVisibleChange"
      @change="handleChange"
      style="width: 100%"
      :popupClassName="'cascade-select-dropdown'"
      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
    >
      <template #dropdownRender>
        <div class="cascade-dropdown" ref="cascadeDropdown">
          <!-- 第一级菜单 -->
          <div class="dropdown-menu" ref="firstMenu">
            <div 
              v-for="item in processedOptions" 
              :key="item.id"
              class="submenu-item"
              :class="{ 'submenu-item-active': firstLevelSelectedId === item.id }"
              @mouseenter="handleItemHover(item, 0)"
              @click="handleItemClick(item, 0)"
            >
              <component 
                :is="props.multiple ? 'a-checkbox' : 'a-radio'"
                :checked="props.multiple ? selectedValues.includes(item.id) : selectedValues === item.id"
                @click.stop="toggleSelection(item.id)"
              >
                {{ item.name }}
              </component>
              <right-outlined v-if="item.children?.length" class="submenu-arrow" />
            </div>
          </div>
          <!-- 子菜单 -->
          <template v-for="(level, index) in cascadeLevels" :key="index">
            <div 
              class="dropdown-submenu" 
              v-show="level.visible"
              :style="getSubmenuStyle(index)"
            >
              <div 
                v-for="item in level.options" 
                :key="item.id"
                class="submenu-item"
                :class="{ 'submenu-item-active': level.selectedId === item.id }"
                @mouseenter="handleItemHover(item, index + 1)"
                @click="handleItemClick(item, index + 1)"
              >
                <component 
                  :is="props.multiple ? 'a-checkbox' : 'a-radio'"
                  :checked="props.multiple ? selectedValues.includes(item.id) : selectedValues === item.id"
                  @click.stop="toggleSelection(item.id)"
                >
                  {{ item.name }}
                </component>
                <right-outlined v-if="item.children?.length" class="submenu-arrow" />
              </div>
            </div>
          </template>
        </div>
      </template>
    </a-select>
  </div>
</template>

<script setup>
import { nextTick } from 'vue';
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { RightOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  options: {
    type: Array,
    default: () => []
  },
  value: {
    type: [Array, String, Number], // 修改类型定义以支持单选和多选
    default: () => []
  },
  multiple: {
    type: Boolean,
    default: false
  },
  placeholder: {  // 新增 placeholder 属性
    type: String,
    default: '请选择'
  }
});

const emit = defineEmits(['update:value', 'change']);

const dropdownVisible = ref(false);
const selectedValues = ref(props.multiple ? [] : '');
const cascadeLevels = ref([]);
const firstLevelSelectedId = ref(null);
const dropdownRect = ref(null);
const firstMenu = ref(null);
const cascadeDropdown = ref(null);

// 初始化 selectedValues
watch(() => props.value, (newValue) => {
  selectedValues.value = props.multiple ? (newValue || []) : (newValue || '');
}, { immediate: true });

// 添加一个计算属性来处理选项
const processedOptions = computed(() => {
  // 如果传入的数组为空或只有一个根节点，直接返回空数组
  if (!props.options || props.options.length === 0) {
    return [];
  }
  // 获取第一个节点的子节点作为顶层选项
  return props.options[0]?.children || [];
});

// 获取子菜单样式
const getSubmenuStyle = (index) => {
  return {
    position: 'absolute',
    top: '0',
    left: `${(index + 1) * 164}px`, // 160px宽度 + 4px间距
    minWidth: '160px'
  };
};

// 监听下拉框打开状态
const handleDropdownVisibleChange = (visible) => {
  dropdownVisible.value = visible;
  if (!visible) {
    cascadeLevels.value = [];
    firstLevelSelectedId.value = null;
    dropdownRect.value = null;
  } else {
    nextTick(() => {
      const dropdown = document.querySelector('.cascade-select-dropdown');
      if (dropdown) {
        dropdownRect.value = dropdown.getBoundingClientRect();
      }
    });
  }
};

const firstLevelOptions = computed(() => {
  // 构建所有选项的映射，包括子选项
  const buildOptionsMap = (items, map = new Map()) => {
    items.forEach(item => {
      map.set(item.id, item);
      if (item.children?.length) {
        buildOptionsMap(item.children, map);
      }
    });
    return map;
  };
  
  // 存储所有选项的映射关系
  const optionsMap = buildOptionsMap(processedOptions.value);
  
  // 将选中的值转换为显示标签
  const getLabel = (value) => {
    const item = optionsMap.get(value);
    return item ? item.name : value;
  };

  // 设置 Select 组件的选项
  return processedOptions.value.map(item => ({
    label: item.name,
    value: item.id,
    item: item
  }));
});

const handleItemHover = (item, levelIndex) => {
  if (item.children?.length) {
    // 修改这里的逻辑，保持之前的子菜单状态
    if (levelIndex >= cascadeLevels.value.length) {
      cascadeLevels.value.push({
        options: item.children,
        visible: true,
        selectedId: null
      });
    } else {
      cascadeLevels.value[levelIndex] = {
        options: item.children,
        visible: true,
        selectedId: null
      };
      // 清除后续级别
      cascadeLevels.value.length = levelIndex + 1;
    }
  } else {
    // 如果没有子项，清除后续所有级别
    cascadeLevels.value.length = levelIndex;
  }
};

const handleItemClick = (item, levelIndex) => {
  if (levelIndex === 0) {
    firstLevelSelectedId.value = item.id;
  } else {
    cascadeLevels.value[levelIndex - 1].selectedId = item.id;
  }
};

const toggleSelection = (id) => {
  if (props.multiple) {
    const index = selectedValues.value.indexOf(id);
    if (index === -1) {
      selectedValues.value.push(id);
    } else {
      selectedValues.value.splice(index, 1);
    }
  } else {
    selectedValues.value = id;
    // 单选模式下，选择后自动关闭下拉框
    dropdownVisible.value = false;
  }
  emit('update:value', selectedValues.value);
  emit('change', selectedValues.value);
};

const handleChange = (values) => {
  selectedValues.value = values;
  emit('update:value', values);
  emit('change', values);
};

// 添加一个计算属性用于显示选中的标签
const selectedLabels = computed(() => {
  const findLabel = (id, options) => {
    for (const option of options) {
      if (option.id === id) {
        return option.name;
      }
      if (option.children?.length) {
        const label = findLabel(id, option.children);
        if (label) return label;
      }
    }
    return null;
  };

  return selectedValues.value.map(value => {
    const label = findLabel(value, props.options);
    return label || value;
  });
});

const selectOptions = computed(() => {
  const getAllOptions = (items, result = new Map()) => {
    items.forEach(item => {
      result.set(item.id, {
        label: item.name,
        value: item.id
      });
      if (item.children?.length) {
        getAllOptions(item.children, result);
      }
    });
    return result;
  };

  const optionsMap = getAllOptions(processedOptions.value);
  return Array.from(optionsMap.values());
});

watch(() => props.value, (newValue) => {
  selectedValues.value = props.multiple ? (newValue || []) : (newValue || '');
}, { immediate: true });

// 清理工作
onUnmounted(() => {
  dropdownRect.value = null;
});
</script>

<style lang="less" scoped>
.tree-cascade-select {
  width: 100%;
  position: relative;
}

.cascade-dropdown {
  position: relative;
  display: flex;
  gap: 4px;
}

.dropdown-menu,
.dropdown-submenu {
  flex-shrink: 0;
  min-width: 160px;
  min-height: 120px;
  max-height: 280px;
  overflow-y: auto;
  background: #fff;
  border-radius: 2px;
  border: 1px solid #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.submenu-item {
  padding: 5px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  white-space: nowrap;

  &:hover {
    background-color: #f5f5f5;
  }

  &-active {
    background-color: #e6f7ff;
  }

  .submenu-arrow {
    color: rgba(0, 0, 0, 0.45);
    margin-left: 8px;
  }
}

:deep(.ant-select-dropdown) {
  padding: 0;
  background: transparent !important;
  overflow: visible !important;
   // 移除下拉框的边框和阴影
   box-shadow: none !important;
  border: none !important;
}
.dropdown-menu,
.dropdown-submenu {
  height: 280px;
  overflow-y: auto;
  background: #fff;
  border-radius: 2px;
  border: 1px solid #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  flex-shrink: 0;
}

:deep(.cascade-select-dropdown) {
  overflow: visible !important;
  
  .ant-select-dropdown-content {
    overflow: visible !important;
  }
}

// <!-- 多选模式 -->
// <tree-cascade-select
//   v-model:value="selectedIds"
//   :options="cascadeOptions"
//   :multiple="true"
//   @change="handleSelectionChange"
// />

// <!-- 单选模式 -->
// <tree-cascade-select
//   v-model:value="selectedId"
//   :options="cascadeOptions"
//   :multiple="false"
//   @change="handleSelectionChange"
// />
</style>




















