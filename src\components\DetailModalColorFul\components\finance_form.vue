<template>

  <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ md: { span: 7 }, sm: { span: 24 } }"
    :wrapper-col="{ md: { span: 17 }, sm: { span: 24 } }">
    <a-row :gutter="16">

      <a-col :md="12" :sm="24" :xs="24" style="padding-right: 0px;">
        <div class="rows">
          <div class="rows_1">
            <span class="rows_1_1">
              财务卡片编号
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              <a-tooltip placement="topLeft" :title="formData.financialNumber">
                <span>{{ formData.financialNumber?.length > 17 ? formData.financialNumber.substring(0, 17) + '...' : formData.financialNumber }}</span>
              </a-tooltip>
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
             财务原值
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
                {{ formData.financialOriginalValue }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
             资金来源
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
             {{ formData.sourceOfFunds }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              已计提折旧月份
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ formData.alreadyAccruedMonths }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              折旧年限
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ formData.depreciationPeriod }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
             残值率
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ formData.residualRate }}<span v-if="formData.residualRate">%</span>
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              历史原值
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ formData.historicalOriginalValue }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
              当月折旧额
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ formData.currentMonthDepreciationAmount }}
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">
             税率
            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">
              {{ formData.taxRate }}<span v-if="formData.taxRate">%</span>
            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">

            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">

            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">

            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">

            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">

            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">

            </span>
          </div>
        </div>
        <div class="rows rows1">
          <div class="rows_1">
            <span class="rows_1_1">

            </span>
          </div>
          <div class="rows_2">
            <span class="rows_2_2">

            </span>
          </div>
        </div>
      </a-col>
      <a-col :md="12" :sm="24" :xs="24" style="padding-left: 0px;">
        <div class="rows2">
          <div class="rows2_2">
            <span class="rows2_2_2">
              财务组织
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.financialOrg }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              固定资产分类
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.fixedAssetsStr }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              递延收益
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.deferredIncome }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              折旧方式
            </span>
          </div>

          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.depreciationMethodStr }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              折旧月份
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.depreciationMonth }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              预计净残值
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.netSalvage }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              累计折旧额
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.depreciationAmount }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">
              净值
            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">
              {{ formData.netWorth }}
            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>

        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>
        <div class="rows2 rows22">
          <div class="rows2_2">
            <span class="rows2_2_2">

            </span>
          </div>
          <div class="rows2_3">
            <span class="rows2_3_3">

            </span>
          </div>
        </div>
      </a-col>
    </a-row>
  </a-form>
  <div class="update-info">
    <span>更新日期：2024-07-21</span>
  </div>
</template>

<script setup>
import { defineComponent, reactive, toRefs, ref, watch } from 'vue';
import iconData from 'ele-admin-pro/es/ele-icon-picker/icons';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: '设备详情'
  }
});
const detailData = ref({});
const formData = ref({});
watch(() => props.data, (val) => {
  console.log(val)
  if(val){
    if(val.type == "scrap"){
      formData.value = val.bussScrapedEquipment;
    }else if(val.type == "disposal"){
      formData.value = val.bussDisposaledEquipment;
    }else{
      formData.value = val.bussRegisteredEquipment;
    }
  }
}, { immediate: true });
</script>

<style lang="less" scoped>
.rows {
  display: flex;
  height: 53px;
  align-items: center;
  border: 1px solid rgba(172, 180, 201, 0.2);

  .rows_1 {
    width: 300px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
color:rgba(255, 255, 255, 0.8);
    border-right: 1px solid rgba(172, 180, 201, 0.2);

    .rows_1_1 {
      margin-right: 16px;
    }
  }

  .rows_2 {
    .rows_2_2 {
      margin-left: 16px;
      color:rgba(255, 255, 255, 0.8);
    }
  }
}


.rows2 {
  display: flex;
  height: 53px;
  align-items: center;
  border-top: 1px solid rgba(172, 180, 201, 0.2);
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);

  .rows2_2 {
    width: 300px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color:rgba(255, 255, 255, 0.8);
    border-right: 1px solid rgba(172, 180, 201, 0.2);

    .rows2_2_2 {
      margin-right: 16px;
    }
  }

  .rows2_3 {
    .rows2_3_3 {
      margin-left: 16px;
      color:rgba(255, 255, 255, 0.8);
    }
  }
}

.rows1 {
  border-top: 0;
  border-left: 1px solid rgba(172, 180, 201, 0.2);
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);
}

.rows22{
  border-top: 0;
  border-left: 0;
  border-right: 1px solid rgba(172, 180, 201, 0.2);
  border-bottom: 1px solid rgba(172, 180, 201, 0.2);
}
</style>




