<template>
  <div class="ele-body">
    <div class="reg-body">
      <!-- 顶部按钮组 -->
      <div class="header-tools">
        <div class="left">
          <a-button class="filter-button" style="display: flex;align-items: center;justify-content: space-between;"
            @click="isShowSearch = !isShowSearch">
            <!-- <template #icon><filter-outlined /></template> -->
            <i class="iconfont icon-search" style="margin-right: 6px;"></i>
            筛选
          </a-button>
          <div class="search-input">
            <a-input v-model:value="queryParams.processName" placeholder="请输入业务标题" :style="{ width: '240px' }"
              @keyup.enter="handleSearch" allow-clear @change="handleChange">
              <template #suffix>
                <SearchOutlined class="search-icon" @click="handleSearch" />
              </template>
            </a-input>
          </div>
        </div>
        <div class="right">
          <a-radio-group v-model:value="activeTab" @change="handleTabChange" class="custom-radio-group">
            <a-radio-button value="submitted" v-privilege="'equipmentform:registered:submitted'">已提交</a-radio-button>
            <a-radio-button value="todo" v-privilege="'equipmentform:registered:todo'">待办</a-radio-button>
            <a-radio-button value="done" v-privilege="'equipmentform:registered:done'">已办</a-radio-button>
          </a-radio-group>
        </div>
      </div>

      <!-- 搜索工具栏 -->
      <div class="search-form" v-if="isShowSearch">
        <a-row :gutter="16">
          <a-col :span="7">
            <a-form-item label="申请日期">
              <a-range-picker v-model:value="applyStartDate" picker="date" :placeholder='placeholders'
                :value-format="yearFormat" style="width: 100%;" />
            </a-form-item>
          </a-col>
          <a-col :span="7">
            <a-form-item label="结束日期">
              <a-range-picker v-model:value="applyEndDate" picker="date" :placeholder='placeholders'
                :value-format="yearFormat" style="width: 100%;" :disabled="flag"/>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="审批状态">
              <a-select v-model:value="queryParams.workSheetStatus" placeholder="全部" style="width: 100%;" :disabled="flag">
                <a-select-option value="">全部</a-select-option>
                <!-- <a-select-option value="0">草稿</a-select-option> -->
                <a-select-option value="1">审批中</a-select-option>
                <a-select-option value="-1">已撤回</a-select-option>
                <a-select-option value="99">已通过</a-select-option>
                <a-select-option value="100">已拒绝</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="2" style="display: flex; justify-content: end;">
            <a-button type="primary" style="width: 100%;" class="search-button" @click="handleSearch">查询</a-button>
            <!-- <a-button class="reset-button" @click="handleReset" style="margin-left: 20px;">重置</a-button> -->
          </a-col>
          <a-col :span="2" style="display: flex; justify-content: end;">
            <!-- <a-button type="primary" class="search-button" @click="handleSearch">查询</a-button> -->
            <a-button class="reset-button" style="width: 100%;" @click="handleReset">重置</a-button>
          </a-col>
        </a-row>
        <!-- <div class="search-row">
          <div class="search-row1">
            <div class="search-item">
              <span class="label">申请日期:</span>
              <a-range-picker v-model:value="applyStartDate" picker="date" :placeholder='placeholders'
                :value-format="yearFormat" style="width: 220px" />
            </div>
            <div class="search-item">
              <span class="label">结束日期:</span>
              <a-range-picker v-model:value="applyEndDate" picker="date" :placeholder='placeholders'
                :value-format="yearFormat" style="width: 220px" />
            </div>
            <div class="search-item">
              <span class="label">审批状态:</span>
              <a-select v-model:value="queryParams.workSheetStatus" placeholder="全部" style="width: 120px;">
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="1">审批中</a-select-option>
                <a-select-option value="-1">已撤回</a-select-option>
                <a-select-option value="99">已通过</a-select-option>
                <a-select-option value="100">已拒绝</a-select-option>
              </a-select>
            </div>
          </div>

          <div>
            <a-button type="primary" class="search-button" @click="handleSearch">查询</a-button>
            <a-button class="reset-button" @click="handleReset" style="margin-left: 20px;">重置</a-button>
          </div>
        </div> -->
      </div>

      <!-- 数据表格 -->
      <a-table :columns="columns" :data-source="tableData"  :pagination="false"
        class="custom-table" style="width: 100%;" :scroll="scroll">
        <template #emptyText>
          <div class="custom-empty">
            <img src="@/assets/images/noData.png" />
            <!-- <p>抱歉，暂时还没有数据</p> -->
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <div class="action-column">
              <a @click="handleView(record)">查看</a>
            </div>
          </template>
        </template>
      </a-table>

      <!-- 表格底部统计信息 -->
      <div class="table-footer" :class="{ 'follow-page': isShowSearch }">
        <div class="total-info">
          共{{ pagination.total }}条数据
        </div>
        <!-- <a-pagination v-model:current="pagination.current" :total="pagination.total"
                    :pageSize="pagination.pageSize" @change="handleTableChange" /> -->
        <a-pagination v-model:current="pagination.current" :total="pagination.total" :showLessItems="true"
          @change="handleTableChange" :showSizeChanger="true" :defaultPageSize="20" :pageSizeOptions="['20', '50', '100']"
          @showSizeChange="handleSizeChange" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch ,nextTick} from 'vue';
import { HandleTaskApi } from '@/api/workflow/HandleTaskApi';
import { AppliedApi } from '@/api/workflow/AppliedApi';
import { EnumApi } from '@/api/common/enum';
import { useRouter } from 'vue-router';
import { FilterOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

const router = useRouter();
const scroll = ref({ x: 'max-content', y: 'calc(100vh - 270px)' })


const handleChange = (e) =>{
  nextTick(()=>{
     if (e.type === 'click') {
        handleSearch()
      }
  })
}

// 初始化响应式状态
const activeTab = ref('submitted');
const searchText = ref('');
const isShowSearch = ref(false);
const tableData = ref([]);
const selectedRowKeys = ref([]);
const yearFormat = 'YYYY-MM-DD';
const applyStartDate = ref([])
const applyEndDate = ref([])
const flag = ref(false)

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: (total) => `共 ${total} 条`
});

// 查询参数
const queryParams = ref({
  processName: null,
  applyStartDateStart: null,
  applyStartDateEnd: null,
  applyEndDateStart: null,
  applyEndDateEnd: null,
  workSheetStatus: null,
  key: 'equipment_acceptance_batch,equipment_acceptance,equipment_acceptance_edit',
  pageSize: 20,
  pageNo: 1
});

// 表格列定义
const columns = [
  {
    title: '序号',
    dataIndex: 'serialNo',
    width: 80,
    align: 'center',
    customRender: ({ text, record, index }) => {
      return `${(pagination.value.current - 1) * pagination.value.pageSize + index + 1}`;
    }
  },
  {
    title: '业务标题',
    dataIndex: 'businessTitle',
    width: 250,
    ellipsis: true
  },
  {
    title: '申请设备台数',
    dataIndex: 'equipmentCount',
    width: 140,
    align: 'center'
  },
  {
    title: '审批状态',
    dataIndex: 'processStatus',
    width: 100,
    align: 'center'
  },
  {
    title: '申请日期',
    dataIndex: 'applyDate',
    width: 100,
    align: 'center'
  },
  {
    title: '结束日期',
    dataIndex: 'endDate',
    width: 100,
    align: 'center'
  },
  {
    title: '',  // 空白分隔列
    dataIndex: 'spacer',
    width: 200,
    align: 'center'
  },
  {
    title: '操作',
    key: 'action',
    width: 140,
    align: 'center',
    fixed: 'right'
  }
];

// 添加状态选项的响应式变量
const statusOptions = ref([]);

// 初始化枚举数据
const initEnumData = async () => {
  try {
    // 获取审批状态枚举
    const statusData = await EnumApi.getEnumList({ enumName: 'WorkSheetStatusEnum' });
    statusOptions.value = statusData.data;
  } catch (error) {
    message.error('获取枚举数据失败');
  }
};

// 获取列表数据
const fetchData = async () => {

  const params = {
    pageNo: pagination.value.current,
    pageSize: pagination.value.pageSize,
    key: "equipment_acceptance_batch,equipment_acceptance,equipment_acceptance_edit",
    ...queryParams.value,
  };

  try {
    let response;
    switch (activeTab.value) {
      case 'todo':
        response = await HandleTaskApi.todoTaskPage(params);
        break;
      case 'submitted':
        response = await AppliedApi.findPage(params);
        break;
      case 'done':
        response = await HandleTaskApi.doneTaskPage(params);
        break;
    }

    if (response?.rows) {
      tableData.value = response.rows.map((item, index) => {
        const transferForm = item.transferForm || {};
        return {
          serialNo: (pagination.value.current - 1) * pagination.value.pageSize + index + 1,
          businessTitle: transferForm.applyTitle || item.applyTitle,
          equipmentCount: transferForm.applyCount || 0,
          processStatus: formatProcessStatus(transferForm.status),
          // applyDate: formatDate(transferForm.applyDate),
          // endDate: formatDate(transferForm.applyEndDate),
          applyDate: transferForm.applyDate || '/',
          endDate: transferForm.applyEndDate || '/',
          ...item
        };
      });

      pagination.value = {
        ...pagination.value,
        total: response.totalRows || 0
      };
    }
  } catch (error) {
    console.error('获取数据失败:', error);
  }
};

// 格式化处理状态
const formatProcessStatus = (status) => {
  const statusItem = statusOptions.value.find(item => item.value === status);
  return statusItem ? statusItem.label : status;
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  if (typeof date === 'string' && date.startsWith('SQ')) {
    return date;
  }
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}${month}${day}`;
};

// 事件处理函数
const handleTabChange = () => {
  tableData.value = [];
  pagination.value.current = 1;
  // fetchData();


  if (activeTab.value == 'todo') {
    flag.value = true
  } else {
    flag.value = false
  }
  isShowSearch.value = false
  handleReset()
};

const handleSearch = () => {
  // 重置分页到第一页
  pagination.value.current = 1;
  // queryParams.value.applyStartDateStart = applyStartDate.value[0]
  // queryParams.value.applyStartDateEnd = applyStartDate.value[1]
  // queryParams.value.applyEndDateStart = applyEndDate.value[0]
  // queryParams.value.applyEndDateEnd = applyEndDate.value[1]
  if (applyStartDate.value && applyStartDate.value.length > 0) {
    queryParams.value.applyStartDateStart = applyStartDate.value[0]
    queryParams.value.applyStartDateEnd = applyStartDate.value[1]
  } else {
    queryParams.value.applyStartDateStart = null
    queryParams.value.applyStartDateEnd = null
  }

  if (applyEndDate.value && applyEndDate.value.length > 0) {
    queryParams.value.applyEndDateStart = applyEndDate.value[0]
    queryParams.value.applyEndDateEnd = applyEndDate.value[1]
  } else {
    queryParams.value.applyEndDateStart = null
    queryParams.value.applyEndDateEnd = null
  }
  // 构建查询参数，包含分页信息
  // const params = {
  //     pageNo: pagination.value.current,
  //     pageSize: pagination.value.pageSize,
  //     keyword: searchText.value,
  //     ...queryParams.value,
  //     key: "equipment_acceptance_batch,equipment_acceptance,equipment_acceptance_edit"
  // };
  // 调用查询接口
  fetchData();
};

const handleReset = () => {
  queryParams.value = {
    processName: null,
    applyStartDateStart: null,
    applyStartDateEnd: null,
    applyEndDateStart: null,
    applyEndDateEnd: null,
    workSheetStatus: null,
    key: 'equipment_acceptance_batch,equipment_acceptance,equipment_acceptance_edit',
    pageSize: 20,
    pageNo: 1
  };
  applyStartDate.value = []
  applyEndDate.value = []
  pagination.value.current = 1;
  fetchData();
};

const isSizeChanging = ref(false); // 添加标志位

const handleSizeChange = (current, size) => {
  isSizeChanging.value = true; // 设置标志位
  pagination.value.pageSize = size;
  pagination.value.current = 1; // 切换每页条数时重置为第一页
  queryParams.value.pageNo = 1;
  queryParams.value.pageSize = size;
  fetchData();

  // 延迟重置标志位，避免handleTableChange干扰
  setTimeout(() => {
    isSizeChanging.value = false;
  }, 100);
};

const handleTableChange = (current) => {
  // 如果正在切换页码大小，忽略此次调用
  if (isSizeChanging.value) {
    return;
  }


  pagination.value.current = current;
  queryParams.value.pageNo = current;
  queryParams.value.pageSize = pagination.value.pageSize;
  fetchData();
};

const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};

const handleView = (record) => {
  const worksheet = record.worksheet || {}; // 获取工作单信息

  switch (activeTab.value) {
    case 'todo':
      const queryParams = new URLSearchParams({
        id: record.procIns.id,
        taskId: record.id,
        processDefinitionId: record.procIns.procDef.id,
        actId: record.activityId,
        procInsId: record.procIns.id,
        worksheetId: worksheet.id
      }).toString();

      window.open(`/todo?${queryParams}`, '_blank');
      break;
    case 'submitted':
      const queryParams1 = new URLSearchParams({
        id: record.worksheet.procInstanceId,
        taskId: record.id,
        processDefinitionId: record.procDef.id,
        actId: record.activityId,
        procInsId: record.worksheet.procInstanceId,
        worksheetId: worksheet.id, // 传递工作单ID到待办页面
      }).toString();

      window.open(`/completed?${queryParams1}`, '_blank');
      break;
    case 'done':
      const queryParams2 = new URLSearchParams({
        id: record.procIns.id,
        taskId: record.id,
        processDefinitionId: record.procIns.procDef.id,
        actId: record.activityId,
        procInsId: record.procIns.id,
        worksheetId: worksheet.id, // 传递工作单ID到待办页面
      }).toString();

      window.open(`/done?${queryParams2}`, '_blank');
      break;
  }

};

const handleEdit = (record) => {
  router.push({
    path: '/buss/edit',
    query: {
      id: record.id,
      type: activeTab.value
    }
  });
};

watch(
  () => isShowSearch.value,
  (sum) => {
    if (sum) {
      scroll.value = { x: 'max-content',  y: 'calc(100vh - 350px)'}
    }else{
      scroll.value = { x: 'max-content',  y: 'calc(100vh - 280px)'}
    }
  }
);

// 创建广播频道（与发送方使用相同名称）
const approvalChannel = ref(null);
// 生命周期钩子
onMounted(() => {
  const nodeList = document.getElementsByClassName('custom-radio-group')[0].children
  if (nodeList.length > 0) {
    switch (nodeList[0].innerText) {
      case '已提交':
        activeTab.value = 'submitted'
        break;
      case '待办':
        activeTab.value = 'todo'
        break;
      case '已办':
        activeTab.value = 'done'
        break;
    }
  }
  initEnumData();
  fetchData();
  approvalChannel.value = new BroadcastChannel('/dynamic/regApplicationRecord');
  approvalChannel.value.onmessage = (e) => {

    if (e.data.type === 'APPROVAL_COMPLETED') {
      // 收到审批完成消息，刷新列表数据
      // alert('111111')
      // message.success('检测到审批更新，正在刷新数据...');
      fetchData(); // 调用现有数据刷新方法
    }
  };
  // window.addEventListener('focus', () => fetchData());
});

//浏览器页面切换到的时候重新刷新页面

// 浏览器页签切换到的时候重新刷新列表数据
</script>

<style lang="less" scoped>
.reg-body {
  height: 100%;
  overflow: auto;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  border-radius: 6px;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);
  padding: 16px 16px;
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;

  .header-tools {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;

    .left {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .filter-button {
        min-width: 80px;
        height: 32px;
        border-radius: 7px;
      }

      .search-input {
        width: clamp(280px, 20vw, 320px);
        display: flex;
        align-items: center;

        :deep(.ant-input) {
          width: 100%;
          height: 24px;

        }

        .search-icon {
          cursor: pointer;
        }
      }
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .tool-button {
        height: 32px;
        border-radius: 7px;

      }

      .colorBlue {
        color: #176DF4
      }

      .link-button {
        height: 32px;
        padding: 0 8px;
      }
    }
  }



  .search-form {

    padding: 0 16px; // 统一内边距
    border-radius: 8px; // 增加圆角


    .search-row {
      display: flex;
      flex-wrap: wrap;
      gap: 24px; // 增加间距
      margin-bottom: 12px; // 增加行间距

      &:last-child {
        margin-bottom: 0;
      }

      .search-item {
        display: flex;
        align-items: center;
        // min-width: 300px;
        flex: 1;

        .label {
          min-width: 80px;
          // margin-right: 12px; // 增加标签和输入框的间距
          color: #666;
          font-size: 14px;
        }

        :deep(.ant-select),
        :deep(.ant-input) {
          width: 64%;
          height: 32px;

          .ant-select-selector {
            background: #fff; // 确保选择器背景为白色
            border-radius: 4px;
          }
        }

        :deep(.ant-input) {
          background: #fff; // 确保输入框背景为白色
          border-radius: 4px;
        }
      }

      .search-button,
      .reset-button {
        height: 32px;
        min-width: 80px;
        margin-left: auto;
        border-radius: 4px; // 统一按钮圆角
      }

      .search-button {
        background: #1890ff; // 查询按钮使用主题蓝色
      }

      .reset-button {
        background: #fff; // 重置按钮使用白色背景
        border: 1px solid #d9d9d9;
      }
    }
  }

  .custom-radio-group .ant-radio-button-wrapper-checked {
    background: #FFFFFF !important;
    border-color: #1890ff !important;
    border-radius: 2px !important;
  }

  .custom-radio-group .ant-radio-button-wrapper {
    background: #E0E9F4;
  }

  .table-footer {
    position: fixed; // 默认固定定位
    bottom: 0px;
    background: #ECF5FE;
    width: calc(100% - 32px);
    max-width: 1888px;
    margin: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px 24px;
    border-radius: 8px;
    z-index: 10;

    .total-info {
      color: #666;
      font-size: 14px;
    }

    // 当筛选展开时的样式
    &.follow-page {
      transform: none;
      margin-top: 16px;
    }
  }

  // 表格容器样式
  .custom-table {
    overflow-x: auto !important;
    width: 100%;
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .reg-body {
    .search-form {
      .search-row {
        .search-item {
          width: 50%;
        }
      }
    }
  }
}

@media screen and (max-width: 1366px) {
  .reg-body {
    .search-form {
      .search-row {
        .search-item {
          min-width: calc(50% - 16px);
        }
      }
    }
  }
}

@media screen and (max-width: 1024px) {
  .reg-body {
    .header-tools {

      .left,
      .right {
        width: 100%;
        justify-content: space-between;
      }
    }

    .search-form {
      .search-row {
        .search-item {
          min-width: 100%;
        }
      }
    }

    .table-footer {
      flex-direction: column;
      text-align: center;

      .total-info {
        width: 100%;
      }
    }
  }
}

// 表格样式增强，确保在所有浏览器中都能正确显示滚动条
:deep(.ant-table) {

  // 表格布局设置
  table {
    table-layout: fixed !important; // 强制表格使用固定布局
    min-width: 100% !important;
    width: max-content !important; // 确保表格内容超出时显示滚动条
  }

  // 单元格样式
  .ant-table-cell {
    max-width: 100% !important; // 限制单元格最大宽度
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    line-height: 1 !important;
    font-size: 14px !important;

    >span,
    >div {
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }
  }

  // 确保表格内容可以水平滚动
  .ant-table-content {
    overflow-x: auto !important;
    min-width: 100% !important;
    z-index: 0 !important;
  }

  // 确保滚动区域正确显示
  .ant-table-body {
    overflow-x: auto !important;
    overflow-y: auto !important;
    min-width: 100% !important;
  }

  // 兼容360浏览器
  .ant-table-container {
    overflow-x: auto !important;
  }

  // 大屏幕样式（默认）
  @media screen and (min-width: 1920px) {
    .ant-table-cell {
      padding: 20px 20px !important;
      height: 60px !important;
    }

    .ant-table-row {
      height: 60px !important;
    }
  }

  // 中等屏幕样式
  @media screen and (min-width: 1366px) and (max-width: 1919px) {
    .ant-table-cell {
      padding: 10px 20px !important;
      height: 40px !important;
    }

    .ant-table-row {
      height: 40px !important;
    }
  }

  // 小屏幕样式
  @media screen and (max-width: 1365px) {
    .ant-table-cell {
      padding: 4px 8px !important;
      height: 32px !important;
    }

    .ant-table-row {
      height: 32px !important;
    }
  }

  // 固定列样式
  .ant-table-fixed-left,
  .ant-table-fixed-right {
    background: #fff !important;
    box-shadow: none !important; // 移除原有阴影
    z-index: 3 !important; // 提高固定列的层级
  }

  // 隐藏特定列
  tr>.ant-table-cell-fix-left:nth-child(6) {
    display: none !important;
  }

  // 调整固定列单元格样式
  .ant-table-cell-fix-left,
  .ant-table-cell-fix-right {
    z-index: 3 !important; // 增加层级
    background: #ECF4FE !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  // 调整表头固定列样式
  .ant-table-thead {

    th.ant-table-cell-fix-left,
    th.ant-table-cell-fix-right {
      z-index: 4 !important; // 确保表头在最上层
      background: #DAECFF !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }

    .ant-table-cell-scrollbar {
      box-shadow: none;
    }
  }

  // 优化阴影效果
  .ant-table-fixed-right::before,
  .ant-table-fixed-left::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    bottom: 0 !important;
    width: 10px !important;
    pointer-events: none !important;
    z-index: 2 !important; // 阴影层级低于固定列
    transition: box-shadow .3s !important;
  }

  .ant-table-fixed-left::before {
    right: 0 !important;
    box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15) !important;
  }

  .ant-table-fixed-right::before {
    left: 0 !important;
    box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15) !important;
  }
}

.custom-radio-group {
  :deep(.ant-radio-button-wrapper) {
    background: transparent;
    border: none;
    color: #666;
    padding: 0 16px;
    height: 32px;
    line-height: 32px;

    &-checked {
      background: #E2F0FF;
      color: #1890ff;
      border-radius: 4px;
    }

    &:hover {
      color: #1890ff;
    }

    &::before {
      display: none;
    }
  }
}

// 修复行样式
:deep(.ant-row) {
  flex-wrap: nowrap !important;
}

:deep(.ant-form-item-label) {
  overflow: visible !important;
}

// 悬停样式
// :deep(.ant-table-tbody > tr.ant-table-row:hover > td),
// :deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
//   background-color: #f0f7ff !important; // 使用自定义悬停背景色
// }

// 全局滚动条样式，确保在360浏览器中可见
// :deep(*::-webkit-scrollbar) {
//   width: 4px !important; // 垂直滚动条宽度
//   height: 4px !important; // 水平滚动条高度
// }

// :deep(*::-webkit-scrollbar-thumb) {
//   background: rgba(0, 0, 0, 0.3) !important; // 滚动条颜色
//   border-radius: 4px !important;
// }

// :deep(*::-webkit-scrollbar-thumb:hover) {
//   background: rgba(0, 0, 0, 0.5) !important; // 悬停时更深的颜色
// }

// :deep(*::-webkit-scrollbar-track) {
//   background: rgba(0, 0, 0, 0.05) !important; // 轻微可见的轨道
// }

// // 确保表格容器允许滚动 - 360浏览器兼容性修复
// .custom-table {
//   overflow-x: auto !important;
//   width: 100% !important;
// }

// // 确保表格内容可以水平滚动 - 360浏览器兼容性修复
// :deep(.ant-table-content) {
//   overflow-x: auto !important;
//   min-width: 100% !important;
// }

// // 确保滚动区域正确显示 - 360浏览器兼容性修复
// :deep(.ant-table-body) {
//   overflow-x: auto !important;
//   overflow-y: auto !important;
//   min-width: 100% !important;
// }

// // 兼容360浏览器
// :deep(.ant-table-container) {
//   overflow-x: auto !important;
// }
.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>

