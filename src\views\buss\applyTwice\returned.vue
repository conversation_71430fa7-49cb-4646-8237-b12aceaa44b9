<template>
  <div class="ele-body">
    <div class="equipment-acceptance">

      <div class="form-title">设备退库单</div>

      <!-- 申请信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          申请信息
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">申请人</div>
            <div class="value">
              {{ formData.applyUserStr }}
            </div>

          </div>
          <div class="form-item">
            <div class="label">申请单位</div>
            <div class="value">
              {{ formData.applyOrgStr }}
            </div>

          </div>
          <div class="form-item">
            <div class="label">申请日期</div>
            <div class="value">
              {{ formData.applyDate }}
            </div>
          </div>

        </div>

        <div class="form-grid">
          <div class="form-item" data-field="applyTitle">
            <div class="label required">业务标题</div>
            <a-input v-model:value="formData.applyTitle" placeholder="请输入业务标题" maxlength="16" />
          </div>
        </div>
      </div>

      <!-- 发货信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          退库信息
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">管理单位</div>
            <div class="value">
              {{ formData.returnManagerOrgStr }}
            </div>

          </div>
          <div class="form-item">
            <div class="label">退库单位</div>
            <div class="value">
              {{ formData.returnOrgStr }}
            </div>

          </div>
          <div class="form-item" data-field="returnUseOrg">
            <div class="label required">使用单位</div>
            <div class="value">
              <!-- {{ formData.returnUseOrgStr }} -->
              <a-select v-model:value="formData.returnUseOrg" placeholder="请选择" @change="sbkChange">
                <a-select-option v-for="item in useOrgList" :key="item.label" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>


        </div>
        <div class="form-grid">
          <div class="form-item">
            <div class="label">管理单位</div>
            <div class="value">
              {{ formData.receiveManagerOrgStr }}
            </div>

          </div>
          <div class="form-item" data-field="receiveOrg">
            <div class="label required">接收单位</div>
            <div class="value">
              <a-select v-model:value="formData.receiveOrg" placeholder="请选择" @change="orgchange($event)">
                <a-select-option v-for="item in propertyOrgList" :key="item.label" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>
          <div class="form-item" data-field="receiveUseOrg">
            <div class="label required">使用单位</div>
            <div class="value">
              <a-select v-model:value="formData.receiveUseOrg" placeholder="请选择">
                <a-select-option v-for="item in receiveUseOrgList" :key="item.label" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>


        </div>
        <div class="form-grid">
          <div class="form-item" data-field="planReturnWarehouseDate">
            <div class="label required">预计退库日期</div>
            <div class="value">
              <a-date-picker v-model:value="formData.planReturnWarehouseDate" placeholder="选择日期" style="width: 100%"
                value-format="YYYY-MM-DD" :class="['date-picker']" />
            </div>
          </div>
        </div>
      </div>
      <!-- 设备信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          设备明细
        </div>

        <div data-field="tables">
          <div>
            <a-button type="link" style="margin-bottom: 5px; padding-left: 0; display: flex; align-items: center;"
              @click="goTodeviceList">
              <i class="iconfont icon-equipment" style="margin-right: 6px;height: 100%;"></i>
              在籍设备选择
            </a-button>
          </div>
          <a-table :columns="columns" :data-source="formData.bussEquipmentProcessTrackingList" bordered
            :pagination="false" :scroll="{ x: 'max-content' }" class="custom-table" ref="myTable">

            <template #bodyCell="{ column, index, record }">
              <template v-if="column.dataIndex === 'code'">
                <div class="form-item" style="margin-bottom: 0;">
                  <a-input v-model:value="record.code" placeholder="请输入设备编码" :style="{ width: '240px' }"
                    @keyup.enter="handleSearch(record)">
                    <template #suffix>
                      <search-outlined style="cursor: pointer;" class="search-icon" @click="handleSearch(record)" />
                    </template>
                  </a-input>
                </div>
              </template>
              <template v-if="column.dataIndex === 'returnWarehouseRemark'">
                <div class="form-item" style="margin-bottom: 0;">
                  <!-- <a-input v-model:value="record.returnWarehouseRemark" placeholder="请输入封存信息备注" /> -->
                  <new-input v-model:value="record.returnWarehouseRemark" :placeholder="'请输入封存信息备注'"/>
                </div>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" danger @click="handleDelete(record)" style="padding: 0;">
                    <i class="iconfont icon-delete" style="margin-right: 6px;"></i>
                  </a-button>
                </a-space>
              </template>
            </template>
            <template #footer>
              <a @click="addAttachment" style="width: 100%; display: inline-block; text-align: center;">
                <plus-outlined />
                新增一行
              </a>
            </template>
          </a-table>
        </div>
      </div>

      <!-- 附件 -->


      <!-- 申请信息 -->
      <!-- <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          备注
        </div>
        <div>
          <div class="form-item">
            <div class="value">
              <a-textarea :rows="4" v-model:value="formData.returnWarehouseRemark" placeholder="请输入补充说明" />
            </div>
          </div>
        </div>
      </div> -->
      <!-- 底部按钮 -->
      <div class="bottom-buttons">
        <!-- <a-button @click="handleSave" :loading="saving" class="save-btn">保存</a-button> -->
        <a-button type="primary" @click="handleSubmit" :loading="submitting" class="submit-btn">提交</a-button>
      </div>
    </div>
    <deviceList-edit v-model:visible="showEdit" @getList="getList" :data="formData.bussEquipmentProcessTrackingList"
      @done="reload" v-if="showEdit" :queryParam="queryParam"></deviceList-edit>
  </div>
</template>
<script>
export default {
  name: 'BussApplyTwiceReturnedTwice',
}
</script>
<script setup>

import { ref, onMounted, nextTick, onBeforeUnmount, watch } from 'vue';
import { message } from 'ant-design-vue';
import { equipmentScrappingApi } from '@/api/buss/equipmentScrappingApi';
import { FileUploadUrl } from '@/api/system/operation/FileApi';
import { getToken } from '@/utils/token-util';
import { API_BASE_PREFIX } from '@/config/setting';
import DeviceListEdit from '@/views/dynamic/deviceList/deviceList-edit.vue';
import { useUserStore } from '@/store/modules/user';
import TreeCascadeSelect from '@/components/TreeCascadeSelect/index.vue';
import { OrganizationApi } from '@/api/system/organization/OrganizationApi';
import { returnedApi } from '@/api/dynamic/returnedApi';
import { UnitApi } from '@/api/common/UnitApi'
import { EnumApi } from '@/api/common/enum';
import { useRouter,useRoute } from 'vue-router';
import newInput from '@/components/ipttext/newInput.vue'
const userStore = useUserStore();
const loginUser = userStore.info;
const router = useRouter();
const loading = ref(false);
const saving = ref(false);
const submitting = ref(false);



// 上传相关配置
const uploadUrl = `${API_BASE_PREFIX}${FileUploadUrl}?secretFlag=0`;
const headers = {
  Authorization: getToken()
};

const columns = [
  {
    title: '序号',
    width: 80,
    customRender: ({ text, record, index }) => {
      return `${index + 1}`;
    }
  },
  { title: '设备编号', dataIndex: 'code', width: 120, },
  { title: '财务卡片编号', dataIndex: 'financialNumber', width: 200, },
  { title: '设备名称', dataIndex: 'equNameStr', width: 200, },
  { title: '规格型号', dataIndex: 'equModelStr', width: 140, },
  { title: '型号备注', dataIndex: 'equModelInfo', width: 250, },
  { title: '管理单位', dataIndex: 'managementOrgStr', width: 200 },
  { title: '存放地点', dataIndex: 'storageLocationStr', width: 250, },
  { title: '使用单位', dataIndex: 'useOrgStr', width: 200, },
  { title: '财务原值', dataIndex: 'financialOriginalValue', width: 120 },
  { title: '净值', dataIndex: 'netWorth', width: 120 },
  { title: '管理状态', dataIndex: 'managementStatusStr', width: 100 },
  { title: '设备状态', dataIndex: 'equConditionStr', width: 100 },
  { title: '生产厂家', dataIndex: 'manufacturer', width: 150 },
  { title: '出厂日期', dataIndex: 'productionDate', width: 120 },
  { title: '出厂编号', dataIndex: 'factoryNumber', width: 120 },
  { title: '设备类别', dataIndex: 'equTypeStr', width: 200 },
  { title: '设备种类', dataIndex: 'equSubTypeStr', width: 200 },
  { title: '设备性质', dataIndex: 'equNatureStr', width: 120 },
  { title: '固定资产分类', dataIndex: 'fixedAssetsStr', width: 160 },
  { title: '产权单位', dataIndex: 'propertyOrgStr', width: 120 },
  { title: '功率kw', dataIndex: 'power', width: 100 },
  { title: '设备型号编码', dataIndex: 'modelCode', width: 160 },
  { title: '购置年度', dataIndex: 'purchaseYear', width: 100 },
  {
    title: '退库信息备注',
    dataIndex: 'returnWarehouseRemark',
    width: 160,
    fixed: 'right'
  },
  // 右侧固定列
  { title: '操作', key: 'action', width: 80, fixed: 'right' }
]

// 是否显示编辑弹窗
const showEdit = ref(false)
// 当前编辑数据
const current = ref(null)
// 文件列表相关
const fileList = ref([]);
const fileViewList = ref([]);
const techFileList = ref([]); // 技术资料上传组件的文件列表
const techFileViewList = ref([]); // 技术资料显示列表
const childs = ref({})
const KeyId = ref()
const datalist = ref([])
const getProcessDefinitionByKey = async () => {
  try {

    const id = await equipmentScrappingApi.getProcessDefinitionByKey({ key: 'equipment_return_warehouse' });
    KeyId.value = id.data.id
  } catch (error) {

  }
}
// 验证表单数据
const validateForm = () => {
  // 按顺序定义需要验证的字段
  const fieldsToValidate = [
    { field: 'applyTitle', label: '业务标题', type: 'input' },
    { field: 'returnUseOrg', label: '使用单位', type: 'select' },
    { field: 'receiveOrg', label: '接收单位', type: 'select' },
    { field: 'receiveUseOrg', label: '使用单位', type: 'select' },
    { field: 'planReturnWarehouseDate', label: '预计退库日期', type: 'date' },
  ];

  // 依次验证每个字段
  for (const { field, label, type } of fieldsToValidate) {
    if (!formData.value[field]) {
      message.error(`请${type === 'select' ? '选择' : type === 'date' ? '选择' : '输入'}${label}`);

      const formItem = document.querySelector(`[data-field="${field}"]`);
      if (formItem) {
        // 滚动到可视区域，并确保元素在视图中间
        formItem.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
        const tableBody = document.querySelector('.ant-table-body');
        if (tableBody) {
          // 方式一：直接滚动到最右边
          tableBody.scrollTo({
            left: tableBody.scrollWidth,  // scrollWidth 是内容的总宽度
            behavior: 'smooth'
          });
        }
        // 使用 nextTick 确保 DOM 更新后再执行点击操作
        nextTick(() => {
          setTimeout(() => {
            switch (type) {
              case 'input': {
                const input = formItem.querySelector('input');
                input?.focus();
                break;
              }
              case 'select': {
                const select = formItem.querySelector('.ant-select-selector');
                select?.click();
                break;
              }
              case 'date': {
                const datePicker = formItem.querySelector('.date-picker');
                if (datePicker) {
                  // 先聚焦
                  const input = datePicker.querySelector('input');
                  input?.focus();
                  // 然后触发点击以打开日期选择面板
                  setTimeout(() => {
                    datePicker.click();
                  }, 100);
                }
                break;
              }
            }
          }, 500); // 等待滚动完成后再聚焦
        });
      }
      return false;
    }
  }
  return true;
};

// 构建提交数据的方法
const buildRequestData = () => {
  let bussEquipmentProcessTrackingList = formData.value.bussEquipmentProcessTrackingList?.filter(item => item.equId);

  const requestData = {
    bussWorksheet: {
      name: formData.value.applyTitle
    },
    bussTransferForm: {
      ...formData.value
    },
    // 设备基本信息，包含技术资料
    bussEquipmentProcessTrackingList: bussEquipmentProcessTrackingList,

  };
  console.log('datalistAll.value----', datalistAll.value);


  if (datalistAll.value.bussWorksheet) {
    requestData.bussWorksheet.id = datalistAll.value.bussWorksheet.id
    requestData.bussTransferForm.id = datalistAll.value.bussTransferForm.id
    requestData.bussTransferForm.worksheetId = datalistAll.value.bussTransferForm.worksheetId
  }

  console.log('提交的数据：', requestData);
  return requestData;
};
// 保存方法
const handleSave = async () => {
  try {
    saving.value = true;
    let params = {};
    params = buildRequestData()
    console.log('params--', params)
    let res = await returnedApi.saveDraft(params);
    if (res.success) {
      datalistAll.value = res.data
      message.success('保存成功');
      console.log('res.data', res.data)
      //
    } else {
      message.error('保存失败');
    }
  } catch (error) {
    // message.error('保存失败：' + error.message);
  } finally {
    saving.value = false;
  }
};

// 提交方法
const handleSubmit = async () => {
  try {
    // 执行验证
    // console.log('bog.validateForm()1', validateForm1())
    console.log('validateForm()', validateForm())
    if (!validateForm()) {
      console.log('validateForm()', !validateForm())
      return; // 验证不通过直接返回,不执行提交
    }
    formData.value.bussEquipmentProcessTrackingList = formData.value.bussEquipmentProcessTrackingList?.filter(item => item.equId);
    const lastColumn = document.querySelector('.ant-table-thead .ant-table-cell:nth-last-child(2)');
    if (!formData.value.bussEquipmentProcessTrackingList || formData.value.bussEquipmentProcessTrackingList.length == 0) {
      message.error('请选择设备！')
      const formItem = document.querySelector(`[data-field="tables"]`);
      formItem.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
      return
    }

    for (let i = 0; i < formData.value.bussEquipmentProcessTrackingList.length; i++) {
      if (!formData.value.bussEquipmentProcessTrackingList[i].returnWarehouseRemark) {
        lastColumn.scrollIntoView({ behavior: "smooth", inline: 'center' });
        message.error('第' + (i + 1) + '行退库信息备注')
        return
      }
    }

    submitting.value = true;


    // 构建内部formData结构
    const innerFormData = {
      formDatas: {
        bussWorksheet: {
          name: formData.value.applyTitle
        },
        bussTransferForm: {
          ...formData.value
          // applyTitle: formData.value.applyTitle,
          // fileList: fileViewList.value, // 附件
          // handledUser: formData.value.handledUser, // 经办人
          // handledOrg: formData.value.handledOrg, // 经办单位
          // scrapDate: formData.value.scrapDate, // 报废日期
          // scrapApprovalNumber: formData.value.scrapApprovalNumber, // 审批文号
          // returnWarehouseRemark: formData.value.returnWarehouseRemark,// 备注
        },
        bussEquipmentProcessTrackingList: formData.value.bussEquipmentProcessTrackingList,
      }
    };
    console.log('datalistAll.value+++', datalistAll.value);

    if (datalistAll.value.bussTransferForm) {
      innerFormData.formDatas.bussWorksheet.id = ''
      innerFormData.formDatas.bussWorksheet.procInstanceId = ''
      innerFormData.formDatas.bussWorksheet.isResubmit = true
      innerFormData.formDatas.bussTransferForm.id = ''
      innerFormData.formDatas.bussTransferForm.worksheetId = datalistAll.value.bussTransferForm.worksheetId
    }
    console.log(innerFormData)

    // 构建最终提交数据结构
    const submitData = {
      processDefinitionId: KeyId.value,
      variables: {
        formData: JSON.stringify(innerFormData)
      }
    };
    console.log('submitData', submitData)
    const res = await equipmentScrappingApi.submit(submitData);

    if (res.success) {
      message.success('提交成功');
      router.push('/dynamic/returned');
    } else {
      message.error('提交失败');
    }
  } catch (error) {
    message.error('提交失败');
  } finally {
    submitting.value = false;
  }
};

//获取当前日期
const getdate = () => {
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth() + 1; // 月份从0开始，需要加1
  const day = currentDate.getDate();
  console.log(year + "-" + month + "-" + day);
  return `${year}-${month}-${day}`
}

//表单数据
const formData = ref({
});

const formDatas = ref({
  id: null,
  applyUser: loginUser.userId, // 申请人ID
  applyUserStr: loginUser.realName, // 申请人
  applyOrg: loginUser.organizationId, // 申请单位ID
  applyOrgStr: loginUser.organizationName, // 申请单位
  applyDate: getdate(), // 申请日期
  applyTitle: null,//业务标题
  returnOrg: loginUser.organizationId,// 退库单位ID
  returnOrgStr: loginUser.organizationName,// 退库单位
  returnManagerOrg: loginUser.organizationId,//管理单位ID
  returnManagerOrgStr: null,//管理单位
  returnUseOrg: null,//使用单位
  returnUseOrgStr: null,
  planReturnWarehouseDate: null,// 预计封存日期
  receiveOrg: null,
  receiveManagerOrg: null,
  receiveManagerOrgStr: null,
  receiveUseOrg: null,
  propertyOrg: null,//产权单位
  bussEquipmentProcessTrackingList: []
});


// 附件列表数据
const attachments = ref([
  {
    name: '',
    unit: '',
    quantity: 1,
    manufacturer: '',
    serialNo: ''
  }
]);

// 删除附件
const removeAttachment = (index) => {
  attachments.value.splice(index, 1);
};

// 处理普通附件上传
const handleChange = (info) => {
  console.log('当前上传的文件:', info.file);
  console.log('所有文件列表:', info.fileList);

  // 保持原始fileList与上传组件同步
  fileList.value = info.fileList;

  // 更新显示用的文件列表
  fileViewList.value = info.fileList
    .filter(file => file.status === 'done')  // 只保留上传完成的文件
    .map(file => ({
      fileId: file.response.data.fileId,fileUrl:file.response.data.fileUrl,
      fileName: file.name,
      uid: file.uid,
      status: file.status
    }));
  console.log('fileViewList.value', fileViewList.value)
  // 处理单个文件的状态提示
  if (info.file.status === 'error') {
    message.error('上传出错：' + info.file.error.message);
  }
};

// 处理技术资料上传
const handleTechFileChange = (info) => {
  // 保持原始列表与上传组件同步
  techFileList.value = info.fileList;

  if (info.file.status === 'done' && info.file.response) {
    const response = info.file.response;
    if (response.success) {
      // 更新显示列表
      techFileViewList.value = [{
        fileId: response.data.fileId,
        fileName: info.file.name,
        uid: info.file.uid,
        status: 'done'
      }];

      // 更新formData中的techFile用于显示
      formData.value.techFile = {
        fileId: response.data.fileId,
        fileName: info.file.name
      };
    } else {
      message.error('技术资料上传失败');
    }
  } else if (info.file.status === 'error') {
    message.error('上传出错：' + info.file.error.message);
  }
};

// 删除技术资料
const removeTechFile = () => {
  formData.value.techFile = null;
  techFileList.value = [];
  techFileViewList.value = [];
};

// 删除普通附件
const handleRemove = (file) => {
  // 从显示列表中删除
  const viewIndex = fileViewList.value.findIndex(f => f.uid === file.uid);
  if (viewIndex > -1) {
    fileViewList.value.splice(viewIndex, 1);
  }

  // 从上传列表中删除
  const fileIndex = fileList.value.findIndex(f => f.uid === file.uid);
  if (fileIndex > -1) {
    fileList.value.splice(fileIndex, 1);
  }
};

// 审批记录相关方法
const showApprovalRecord = () => {
  // 处理审批记录的显示逻辑
  console.log('显示审批记录');
};
const datalistAll = ref({})
const initializeData = async () => {
  loading.value = true;
  try {
    const { data } = await returnedApi.getToDo({procInstanceId: procInstanceId.value.id});
    const isNewRecord = !data.bussWorksheet &&
      !data.bussTransferForm &&
      !data.bussEquipmentProcessTrackingList;
    if (!isNewRecord) {
      // 加载已有数据时
      if (data.bussTransferForm) {
        formData.value = {
          ...formData.value,
          ...data.bussTransferForm,
        };

      }

      if (formData.value.applyUser) {
        formData.value = data.bussTransferForm
        formData.value.bussEquipmentProcessTrackingList = data.bussEquipmentProcessTrackingList
        formData.value.applyDate = getdate();
        datalistAll.value = data
      }

      if (!formData.value.applyUser) {
        formData.value = {
          ...formData.value,
          ...formDatas.value,
          returnManagerOrgStr:data.bussTransferForm.returnManagerOrgStr,
          returnManagerOrg:data.bussTransferForm.returnManagerOrg,
        }
        datalistAll.value = data
      }
      // formData.value = data.bussTransferForm
      // // getPropertyOrg()
      // // getUseOrg(formData.value.outOrg)
      // formData.value.bussEquipmentProcessTrackingList = data.bussEquipmentProcessTrackingList
      // datalistAll.value = data
      // console.log('datalistAll.value0000', datalistAll.value);

    }
    getUseOrg()
    getUseOrg1(formData.value.receiveOrg)
  } catch (error) {
    // message.error('获取数据失败：' + error.message);
  } finally {
    loading.value = false;
  }
};

const queryParam = ref()
const goTodeviceList = async (row) => {
  if (!formData.value.receiveOrg) {
    message.error('请选择接收单位！')
    return
  }
  if (!formData.value.returnUseOrg) {
    message.error('请选择退库使用单位！')
    return
  }
  queryParam.value = { managementStatus: '3,5', equCondition: '1', propertyOrg: formData.value.receiveOrg, managementOrg: formData.value.returnManagerOrg, useOrg: formData.value.returnUseOrg }
  current.value = row;
  showEdit.value = true;
};


const getList = (obj) => {
  if (obj) {
    for (let i = 0; i < obj.length; i++) {
      obj[i].returnWarehouseRemark = '整体运输'

    }
    formData.value.bussEquipmentProcessTrackingList = obj
  }
}

const handleDelete = (res) => {
  if (res.uuId) {
    formData.value.bussEquipmentProcessTrackingList = formData.value.bussEquipmentProcessTrackingList.filter(item => item.uuId !== res.uuId);
  }
  if (res.equId) {
    formData.value.bussEquipmentProcessTrackingList = formData.value.bussEquipmentProcessTrackingList.filter(item => item.equId !== res.equId);
  }
}

const propertyOrgList = ref([])
const useOrgList = ref([])
const receiveUseOrgList = ref()
//初始化枚举数据
const getPropertyOrg = async () => {
  try {
    // 获取管理单位
    const gldwlist = await UnitApi.getPropertyOrgList({ workSheetType: 'equipment_return_warehouse' });
    propertyOrgList.value = gldwlist;
    console.log('gldwlist', gldwlist)
  } catch (error) {
    // message.error('获取枚举数据失败');
  }
};
const getUseOrg = async () => {
  try {
    //获取使用单位
    const sydwList = await UnitApi.getUseOrgList({ pId: formData.value.applyOrg,workSheetTypeAndStep:'equipment_return_warehouse_1_1',
isLibrary:false,
isVirtual:false });
    useOrgList.value = sydwList;
  } catch (error) {
    // message.error('获取枚举数据失败');
  }
};

const getUseOrg1 = async (value) => {
  try {
    const sydwList = await EnumApi.getUseOrgList({ pId: value,isLibrary:true });
    if (sydwList) {
      receiveUseOrgList.value = sydwList.data;
    } else {
      receiveUseOrgList.value = []
    }
  } catch (error) {
    // message.error('获取枚举数据失败');
  }
};
//获取下拉选value
const orgchange = (value) => {
  formData.value.bussEquipmentProcessTrackingList = [{
          code: '',
          uuId: SomeTools.guid()
        }]
  const str = propertyOrgList.value.find(item => item.value === value);
  console.log('str', str);
  formData.value.receiveManagerOrgStr = str.label
  formData.value.receiveManagerOrg = value
  getUseOrg1(value)
}

import SomeTools from '@/utils/someTools.js'

const sbkChange = (e) => {
  formData.value.bussEquipmentProcessTrackingList = [{
          code: '',
          uuId: SomeTools.guid()
        }]
}

// 添加新附件
const addAttachment = () => {
  if (!formData.value.receiveOrg) {
    message.error('请选择接收单位！')
    return
  }
  if (!formData.value.returnUseOrg) {
    message.error('请选择退库使用单位！')
    return
  }
  formData.value.bussEquipmentProcessTrackingList.push({
    code: '',
    uuId: SomeTools.guid()
  });
  console.log('formData.value.bussEquipmentProcessTrackingList', formData.value.bussEquipmentProcessTrackingList);

};

const handleSearch = async (record) => {
  const uniqueSampleTypeIds = new Set(formData.value.bussEquipmentProcessTrackingList.map(item => item.code));
  const hasDuplicateSampleTypeId = formData.value.bussEquipmentProcessTrackingList.length !== uniqueSampleTypeIds.size;
  if (hasDuplicateSampleTypeId) {
    record.code = ''
    message.error('请勿重复输入设备编号！')
    return
  }

  queryParam.value = { searchText: record.code, managementStatus: '3,5', equCondition: '1', propertyOrg: formData.value.receiveOrg, managementOrg: formData.value.returnManagerOrg, useOrg: formData.value.returnUseOrg }
  try {
    if (record.code) {
      let obj = await UnitApi.getRegistered(queryParam.value)
      obj.equId = obj.id
      obj.returnWarehouseRemark = '整体运输'
      delete obj.id
      console.log('obj', obj);

      if (record.uuId) {
        for (let i = 0; i < formData.value.bussEquipmentProcessTrackingList.length; i++) {
          if (formData.value.bussEquipmentProcessTrackingList[i].uuId == record.uuId) {
            formData.value.bussEquipmentProcessTrackingList[i] = obj
          }
        }
      }

      if (record.equId) {
        for (let i = 0; i < formData.value.bussEquipmentProcessTrackingList.length; i++) {
          if (formData.value.bussEquipmentProcessTrackingList[i].equId == record.equId) {
            formData.value.bussEquipmentProcessTrackingList[i] = obj
          }
        }
      }
    }

  } catch (e) {
    console.log(e);

  }

}

const procInstanceId = ref({
  id: ''
});
const route = useRoute();


onMounted(() => {
  if (route.query?.procInsId) {
    procInstanceId.value.id = route.query.procInsId;
    sessionStorage.setItem('equipmentAcceptanceProcId', procInstanceId.value.id);
  } else if (sessionStorage.getItem('equipmentAcceptanceProcId')) {
    procInstanceId.value.id = sessionStorage.getItem('equipmentAcceptanceProcId');
  }
  if (!procInstanceId.value.id) {
    message.error('流程定义ID未找到，请联系管理员');
    return;
  }
  initializeData();
  getProcessDefinitionByKey()
  getPropertyOrg()
});
</script>

<style lang="less" scoped>
.custom-table {
  margin-top: 16px;

  :deep(.ant-table) {

    // 提高固定列的层级
    .ant-table-fixed-left,
    .ant-table-fixed-right {
      background: #fff;
      z-index: 3; // 增加层级
    }

    .ant-table-cell:not(.ant-table-cell-fix-right-first) {
      white-space: nowrap !important; // 强制不换行
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      line-height: 1 !important;
      font-size: 14px !important;

      >span,
      >div {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }
    }

    // 大屏幕样式（默认）
    @media screen and (min-width: 1920px) {
      .ant-table-cell {
        padding: 20px 20px !important;
        height: 60px !important;
      }

      .ant-table-row {
        height: 60px !important;
      }
    }

    // 中等屏幕样式
    @media screen and (min-width: 1366px) and (max-width: 1919px) {
      .ant-table-cell {
        padding: 10px 20px !important;
        height: 40px !important;
      }

      .ant-table-row {
        height: 40px !important;
      }
    }

    // 小屏幕样式
    @media screen and (max-width: 1365px) {
      .ant-table-cell {
        padding: 4px 8px !important;
        height: 32px !important;
      }

      .ant-table-row {
        height: 32px !important;
      }
    }

    // 调整固定列单元格样式
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      z-index: 3 !important; // 增加层级
      background: #ECF4FE !important;
    }

    // 调整表头固定列样式
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right {
        z-index: 4 !important; // 确保表头在最上层
        background: #DAECFF !important;
      }
    }

    // 优化阴影效果
    .ant-table-fixed-right::before,
    .ant-table-fixed-left::before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      width: 10px;
      pointer-events: none;
      z-index: 2; // 阴影层级低于固定列
      transition: box-shadow .3s;
    }

    .ant-table-fixed-left::before {
      right: 0;
      box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
    }

    .ant-table-fixed-right::before {
      left: 0;
      box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
    }

    // 设置表格内容的层级
    .ant-table-content {
      z-index: 1;
    }

    // 确保滚动区域正确显示
    .ant-table-body {
      overflow-x: auto !important;
      overflow-y: auto !important;
    }

    // 固定列不换行
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right:not(.ant-table-cell-fix-right-first) {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .ant-table-row {
      height: 24px !important;
    }

    // 表头固定列不换行
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right:not(.ant-table-cell-fix-right-first) {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.approval-record-btn {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  z-index: 100;

  .bg-image {
    display: block;
    max-width: 100%;
    height: auto;
    object-fit: contain;
    background-color: transparent;
  }

  .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .icon {
      width: 15px;
      height: 15px;
      margin-bottom: 4px;
    }

    .text {
      writing-mode: vertical-lr;
      font-size: 14px;
      color: #333;
      letter-spacing: 2px;
    }
  }
}

.equipment-acceptance {
  width: 100%;
  max-width: 1920px; // 最大宽度限制
  height: calc(100vh - 100px);
  overflow: auto;
  margin: 0 auto; // 居中
  padding: 24px 9.06vw 24px 8.18vw; // 转换为vw: 174/1920*100=9.06, 157/1920*100=8.18
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);

  .form-title {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 24px;
  }

  .section {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;

      .blue-bar {
        width: 3px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
      }
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: clamp(16px, 2vw, 24px);
    margin-bottom: 24px;
    justify-content: space-between;

    .form-item {
      display: flex;
      flex-direction: column;
      min-width: 300px; // 最小宽度
      max-width: 300px; // 最大宽度

      .label {
        height: 32px;
        line-height: 32px;
        margin-bottom: 8px;
        color: #0C0B0B;

        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 4px;
        }
      }

      .value {
        width: 100%;
        height: 32px;
        color: #666;

        :deep(.ant-input),
        :deep(.ant-select),
        :deep(.ant-picker),
        :deep(.ant-input-number) {
          width: 100%;
          height: 32px;
        }

        :deep(.ant-input-number-input) {
          height: 16px;
          line-height: 16px;
        }

        :deep(.ant-select-selector) {
          height: 32px !important;

          .ant-select-selection-item {
            line-height: 32px;
          }
        }

        :deep(.ant-picker) {
          .ant-picker-input {
            height: 16px;
            line-height: 16px;

            input {
              height: 16px;
              line-height: 16px;
            }
          }
        }
      }
    }
  }

  .acceptance-no {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    max-width: 300px; // 与其他表单项保持一致的最大宽度

    :deep(.first-input) {
      width: 65% !important; // 验资前面的输入框占65%
    }

    :deep(.second-input) {
      width: 35% !important; // 号前面的输入框占35%
    }

    .separator {
      color: #666;
      white-space: nowrap;
    }
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .equipment-acceptance {
    padding: 24px 6vw;

    .form-grid {
      gap: 16px;
    }
  }
}

@media screen and (max-width: 1366px) {
  .equipment-acceptance {
    padding: 24px 4vw;

    .form-grid {
      grid-template-columns: repeat(2, minmax(300px, 1fr));
    }
  }
}

@media screen and (max-width: 1024px) {
  .equipment-acceptance {
    padding: 24px 2vw;

    .form-grid {
      grid-template-columns: repeat(1, 1fr);

      .form-item {
        max-width: none;
      }
    }
  }
}

.percentage-input {
  position: relative;
  display: flex;
  align-items: center;

  .ant-input {
    width: 100%;
  }

  .percentage-symbol {
    position: absolute;
    right: 12px;
    color: #666;
  }
}

.attachment-table {
  width: 89%;
  border: 1px solid #e8e8e8;
  border-radius: 2px;


  .table-header,
  .table-row,
  .add-row {
    // 添加add-row到统一高度设置中
    display: flex;
    padding: 12px 8px;

    border-bottom: 1px solid #e8e8e8;
    align-items: center;
    gap: 12px;
    min-height: 56px; // 统一设置最小高度
  }

  .table-header {
    font-weight: 500;
  }

  .table-row:last-child {
    border-bottom: none;
  }

  // 使用百分比和flex布局
  .col-serial {
    width: 5%; // 序号列较窄
    min-width: 40px;
  }

  .col-name {
    width: 25%; // 名称及型号需要较大空间
    min-width: 180px;
  }

  .col-unit {
    width: 10%; // 单位列较窄
    min-width: 80px;
  }

  .col-quantity {
    width: 10%; // 数量列较窄
    min-width: 80px;
  }

  .col-manufacturer {
    width: 20%; // 生产厂家需要适中空间
    min-width: 150px;
  }

  .col-serial-no {
    width: 20%; // 出厂编号需要适中空间
    min-width: 150px;
  }

  .col-action {
    width: 10%; // 操作列较窄
    min-width: 60px;
    text-align: center;

    .delete-btn {
      color: #FF4D4F;
      cursor: pointer;
    }
  }

  :deep(.ant-input),
  :deep(.ant-input-number) {
    width: 100%;
  }


  // 响应式调整
  @media screen and (max-width: 1366px) {
    .col-name {
      width: 22%; // 较小屏幕时稍微压缩名称列
    }

    .col-manufacturer,
    .col-serial-no {
      width: 18%; // 压缩这两列
    }
  }

  @media screen and (max-width: 1024px) {
    overflow-x: auto; // 当屏幕太小时允许横向滚动

    .table-header,
    .table-row {
      min-width: 900px; // 确保在小屏幕上内容不会过度压缩
    }
  }

  .add-row {
    justify-content: center; // 水平居中
    align-items: center; // 垂直居中
    cursor: pointer;
    border-bottom: none; // 最后一行不需要底部边框

    a {
      display: flex;
      align-items: center; // 图标和文字垂直居中
      color: #1890ff;

      .anticon {
        margin-right: 4px;
      }
    }

    &:hover {
      background: #f5f5f5;
    }
  }
}

.upload-section {
  .upload-header {
    display: flex;
    align-items: center;
    gap: 12px;

    .upload-button {
      display: inline-flex;
      align-items: center;
      padding: 4px 15px;
      background: #0085FF;
      border-radius: 2px;
      color: white;
      cursor: pointer;
      font-size: 14px;

      .anticon {
        margin-right: 4px;
      }
    }

    .upload-tip {
      color: #999;
      font-size: 12px;
    }
  }

  .file-list {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    //display: grid;
    //grid-template-columns: repeat(3, 0.3fr);
    //gap: 16px;

    .file-item {
      display: flex;
      align-items: center;
      padding: 8px;
      // background: #F7F9FC;
      border-radius: 2px;
      width: 32%;

      .file-link{
        word-break:break-all;
      }

      .file-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #2D5CF6;
      }

      .file-name {
        flex: 1;
        font-size: 14px;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-actions {
        margin-left: 8px;

        .delete-icon {
          color: #999;
          cursor: pointer;

          &:hover {
            color: #FF4B4B;
          }
        }

        .anticon-sync {
          color: #2D5CF6;
        }
      }
    }
  }
}

.tech-file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;

  .file-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      color: #2D5CF6;
    }

    .file-name {
      color: #333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .delete-icon {
    color: #999;
    cursor: pointer;

    &:hover {
      color: #ff4d4f;
    }
  }
}

.tech-upload-button {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  color: #666;
  font-size: 14px;

  .anticon {
    margin-right: 4px;
    font-size: 14px;
  }

  &:hover {
    color: #2D5CF6;
    border-color: #2D5CF6;
  }
}

.value {
  :deep(.ant-input-affix-wrapper) {
    .ant-input-suffix {
      color: #666;
    }
  }
}

.bottom-buttons {
  margin-top: 150px;
  margin-bottom: 89rpx;
  bottom: 180px; // 修改为距离底部80px
  left: 0;
  right: 0;
  padding: 0 32px; // 调整左右内边距
  text-align: right;
  z-index: 100;

  .save-btn {
    height: 32px;
    padding: 0 16px;
    border: 1px solid #DCDFE6;
    color: #606266;
    background: transparent !important;
    border-radius: 4px;

    &:hover {
      border-color: #409EFF;
      color: #409EFF;
    }
  }

  .submit-btn {
    height: 32px;
    padding: 0 16px;
    margin-left: 27px; // 调整按钮之间的间距
    background: #409EFF;
    border-radius: 4px;
    border: none;

    &:hover {
      background: #66B1FF;
    }
  }
}
.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>
