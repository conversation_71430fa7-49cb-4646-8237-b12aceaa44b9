<template>
  <div class="ele-body">
    <div class="equipment-acceptance">

      <div class="form-title">设备处置单</div>

      <!-- 申请信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          经办信息
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">经办人</div>
            <div class="value">张三</div>
          </div>

          <div class="form-item">
            <div class="label">经办单位</div>
            <div class="value">中铁第三建设（集团）有限责任公司机电处</div>
          </div>
          <div class="form-item">
            <div class="label">评估单位</div>
            <a-input v-model:value="businessTitle" placeholder="请输入评估单位" />
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item">
            <div class="label">收购方名称</div>
            <div class="value">
              <a-input v-model:value="businessTitle" placeholder="请输入审批文号" />
            </div>
          </div>
          <div class="form-item">
            <div class="label">处置日期</div>
            <div class="value">
              <a-date-picker v-model:value="formData.productionDate" placeholder="选择日期" style="width: 100%"
                value-format="YYYY-MM-DD" :class="['date-picker']" />
            </div>
          </div>
          <div class="form-item">
            <div class="label">处置方式</div>
            <div class="value">
              <a-input v-model:value="businessTitle" placeholder="请输入审批文号" />
            </div>
          </div>
        </div>

        <div class="form-grid">
          <div class="form-item">
          <div class="label">处置资料</div>
          <div class="value">
            <template v-if="!formData.techFile">
              <a-upload
                :maxCount="1"
                :action="uploadUrl"
                :headers="headers"
                :showUploadList="false"
                :file-list="techFileList"
                @change="handleTechFileChange"
              >
                <div class="tech-upload-button">
                  <upload-outlined />
                  <span>选择文件</span>
                </div>
              </a-upload>
            </template>
            <div v-else class="tech-file-item">
              <div class="file-info">
                <file-outlined />
                <span class="file-name">{{ formData.techFile.fileName }}</span>
              </div>
              <close-outlined class="delete-icon" @click="removeTechFile" />
            </div>
          </div>
        </div>
          <!-- <div class="form-item">
            <div class="label">处置资料</div>
            <div class="value">
              <a-input v-model:value="businessTitle" placeholder="请输入审批文号">
                <template #suffix>
                  <a-tooltip>
                    <info-circle-outlined style="color: rgba(0, 0, 0, 0.45)" />
                  </a-tooltip>
                </template>
              </a-input>
            </div>
          </div> -->
        </div>
      </div>

      <!-- 设备信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          设备明细
        </div>

        <div>
          <div style="display: flex;">
            <a-button type="link" style="margin-bottom: 5px; padding-left: 0; display: flex; align-items: center;"
              @click="goTodeviceList">
              <i class="iconfont icon-sbbf" style="margin-right: 6px;height: 100%;"></i>
              报废设备选择
            </a-button>
            <a-button type="link" style="margin-bottom: 5px; padding-left: 0; display: flex; align-items: center;"
              @click="goToregisteredList">
              <i class="iconfont icon-equipment" style="margin-right: 6px;height: 100%;"></i>
              在籍设备选择
            </a-button>
          </div>
          <a-table :columns="columns" :data-source="datass" bordered :pagination="false">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-space>
                  <a @click="handleView(record)">查看</a>
                  <!-- <a @click="handleEdit(record)">编辑</a> -->
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </div>

      <!-- 附件 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          附件
        </div>

        <div class="upload-section">
          <div class="upload-header">
            <a-upload v-model:file-list="fileList" :multiple="true" :action="uploadUrl" :headers="headers"
              :showUploadList="false" @change="handleChange">
              <div class="upload-button">
                <upload-outlined />
                点击上传
              </div>
            </a-upload>
            <span class="upload-tip">最大1GB/个，支持图片、视频、文档等常见文件格式(zip、rar等压缩文件除外)</span>
          </div>

          <div class="file-list">

            <div v-for="(file, index) in fileViewList" :key="file.uid" class="file-item">
              <div class="file-icon">
                <file-excel-outlined v-if="file.fileName.endsWith('.xls') || file.fileName.endsWith('.xlsx')" />
                <file-word-outlined v-else-if="file.fileName.endsWith('.doc') || file.fileName.endsWith('.docx')" />
                <file-pdf-outlined v-else-if="file.fileName.endsWith('.pdf')" />
                <video-camera-outlined v-else-if="file.fileName.endsWith('.mov') || file.fileName.endsWith('.mp4')" />
                <file-outlined v-else />
              </div>
           <a :href="`${file.fileUrl}?filename=${file.fileName}`"  target="_blank" class="file-link">
                  {{ file.fileName }}
                </a>
              <div class="file-actions">
                <sync-outlined v-if="file.status === 'uploading'" spin />
                <close-outlined v-else class="delete-icon" @click="handleRemove(file)" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 申请信息 -->
      <div class="section">
        <div class="section-title">
          <div class="blue-bar"></div>
          备注
        </div>
        <div>
          <div class="form-item">
            <!-- <div class="label">审批文号</div> -->
            <div class="value">
              <a-textarea :rows="4" v-model:value="businessTitle" placeholder="请输入补充说明" />
            </div>
          </div>
        </div>
      </div>
      <!-- 底部按钮 -->
      <div class="bottom-buttons">
        <a-button @click="handleSave" :loading="saving" class="save-btn">保存</a-button>
        <a-button type="primary" @click="handleSubmit" :loading="submitting" class="submit-btn">提交</a-button>
      </div>
    </div>
    <deviceList-edit v-model:visible="showEdit" @getList="getList" @done="reload" v-if="showEdit"></deviceList-edit>

    <registeredList-edit v-model:visible="showEdit1" @getList="getList1" @done="reload"
      v-if="showEdit1"></registeredList-edit>
  </div>
</template>

<script setup>

import { ref, onMounted, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { EquipmentAcceptanceApi } from '@/api/buss/EquipmentAcceptanceApi';
import { FileUploadUrl } from '@/api/system/operation/FileApi';
import { getToken } from '@/utils/token-util';
import { API_BASE_PREFIX } from '@/config/setting';
import DeviceListEdit from '../deviceList/deviceList-edit.vue';
import registeredListEdit from '../registeredList/registeredList-edit.vue';

const loading = ref(false);
const saving = ref(false);
const submitting = ref(false);

// 业务标题和关联投资计划的独立字段
const businessTitle = ref('');
const investmentPlan = ref('');
const isTransfer = ref('1'); // 添加是否转入设备字段，默认为'1'（否）

// 上传相关配置
const uploadUrl = `${API_BASE_PREFIX}${FileUploadUrl}?secretFlag=0`;
const headers = {
  Authorization: getToken()
};

const columns = [
  {
    title: '序号',
    dataIndex: 'serialNo',
    width: 80,
  },
  {
    title: '设备编号',
    dataIndex: 'mc',
    width: 120,
  },
  {
    title: '财务卡片编号',
    dataIndex: 'dw',
    width: 160,
  },
  {
    title: '设备名称',
    dataIndex: 'sl',
    width: 160,
  },
  {
    title: '规格型号',
    dataIndex: 'sccj',
    width: 120,
  },
  {
    title: '型号备注',
    dataIndex: 'ccbh',
    width: 160,
  },
  {
    title: '管理单位',
    dataIndex: 'ccbh',
    width: 160,
  },
  {
    title: '使用单位',
    dataIndex: 'ccbh',
    width: 160,
  },
  {
    title: '存放地点',
    dataIndex: 'ccbh',
    width: 160,
  },
]

// 是否显示编辑弹窗
const showEdit = ref(false)
const showEdit1 = ref(false)
// 当前编辑数据
const current = ref(null)
// 文件列表相关
const fileList = ref([]);
const fileViewList = ref([]);
const techFileList = ref([]); // 技术资料上传组件的文件列表
const techFileViewList = ref([]); // 技术资料显示列表

// 定义验证规则
const rules = {
  contractNumber: [{ required: true, message: '请输入合同编号' }],
  acceptanceDate: [{ required: true, message: '请选择验收日期' }],
  equType: [{ required: true, message: '请选择设备类别' }],
  equSubType: [{ required: true, message: '请选择设备种类' }],
  equName: [{ required: true, message: '请输入设备名称' }],
  equModel: [{ required: true, message: '请输入规格型号' }],
  equNature: [{ required: true, message: '请选择设备性质' }],
  equSource: [{ required: true, message: '请选择设备来源' }],
  importance: [{ required: true, message: '请选择设备重要性' }],
  propertyOrg: [{ required: true, message: '请选择产权单位' }],
  managementOrg: [{ required: true, message: '请选择管理单位' }],
  useOrg: [{ required: true, message: '请选择使用单位' }],
  equCondition: [{ required: true, message: '请输入设备状态' }],
  storageLocationStr: [{ required: true, message: '请输入存放地点' }]
};

// 验证表单数据
const validateForm = () => {
  // 按顺序定义需要验证的字段
  const fieldsToValidate = [
    // { field: 'contractNumber', label: '合同编号', type: 'input' },
    // { field: 'acceptanceDate', label: '验收日期', type: 'date' },
    // { field: 'productionDate', label: '出厂日期', type: 'date' }, // 确保包含出厂日期
    // { field: 'equType', label: '设备类别', type: 'select' },
    // { field: 'equSubType', label: '设备种类', type: 'select' },
    // { field: 'equName', label: '设备名称', type: 'input' },
    // { field: 'equModel', label: '规格型号', type: 'input' },
    // { field: 'equNature', label: '设备性质', type: 'select' },
    // { field: 'equSource', label: '设备来源', type: 'select' },
    // { field: 'importance', label: '设备重要性', type: 'select' },
    // { field: 'propertyOrg', label: '产权单位', type: 'select' },
    // { field: 'managementOrg', label: '管理单位', type: 'select' },
    // { field: 'useOrg', label: '使用单位', type: 'select' },
    // { field: 'equCondition', label: '设备状态', type: 'input' },
    // { field: 'storageLocationStr', label: '存放地点', type: 'input' }
  ];

  // 依次验证每个字段
  for (const { field, label, type } of fieldsToValidate) {
    if (!formData.value[field]) {
      message.error(`请${type === 'select' ? '选择' : type === 'date' ? '选择' : '输入'}${label}`);

      const formItem = document.querySelector(`[data-field="${field}"]`);
      if (formItem) {
        // 滚动到可视区域，并确保元素在视图中间
        formItem.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // 使用 nextTick 确保 DOM 更新后再执行点击操作
        nextTick(() => {
          setTimeout(() => {
            switch (type) {
              case 'input': {
                const input = formItem.querySelector('input');
                input?.focus();
                break;
              }
              case 'select': {
                const select = formItem.querySelector('.ant-select-selector');
                select?.click();
                break;
              }
              case 'date': {
                const datePicker = formItem.querySelector('.date-picker');
                if (datePicker) {
                  // 先聚焦
                  const input = datePicker.querySelector('input');
                  input?.focus();
                  // 然后触发点击以打开日期选择面板
                  setTimeout(() => {
                    datePicker.click();
                  }, 100);
                }
                break;
              }
            }
          }, 500); // 等待滚动完成后再聚焦
        });
      }
      return false;
    }
  }

  return true;
};

// 构建提交数据的方法
const buildRequestData = () => {
  const requestData = {
    bussWorksheet: {
      name: businessTitle.value
    },
    bussTransferForm: {
      applyTitle: businessTitle.value,
      investmentPlan: investmentPlan.value
    },
    // 设备基本信息，包含技术资料
    bussRegisteredEquipmentList: [
      {
        ...formData.value,
        technicalFileList: techFileViewList.value, // 技术资料
        fileList: fileViewList.value, // 附件
        // 主要附件模块数据
        bussEquipmentAccessoryList: attachments.value.map(item => ({
          name: item.name,
          unit: item.unit,
          quantity: item.quantity,
          manufacturer: item.manufacturer,
          serialNo: item.serialNo
        })),
      }
    ],

  };

  console.log('提交的数据：', requestData);
  return requestData;
};

// 保存方法
const handleSave = async () => {
  try {
    saving.value = true;
    const params = buildRequestData();
    const res = await EquipmentAcceptanceApi.save(params);
    if (res.success) {
      message.success('保存成功');
    } else {
      message.error(res.message || '保存失败');
    }
  } catch (error) {
    message.error('保存失败：' + error.message);
  } finally {
    saving.value = false;
  }
};

// 提交方法
const handleSubmit = async () => {
  try {
    // 执行验证
    if (!validateForm()) {
      return; // 验证不通过直接返回,不执行提交
    }

    submitting.value = true;

    // 构建内部formData结构
    const innerFormData = {
      formDatas: {
        bussWorksheet: {
          name: businessTitle.value
        },
        bussTransferForm: {
          apply_title: businessTitle.value,
          investmentPlan: investmentPlan.value,
          isTransfer: isTransfer.value
        },
        bussRegisteredEquipmentList: [
          {
            ...formData.value,
            technicalFileList: techFileViewList.value.map(file => ({
              fileId: file.fileId
            })),
            bussEquipmentAccessoryList: attachments.value.map(item => ({
              equId: item.equId,
              baseCode: item.baseCode,
              name: item.name,
              unit: item.unit,
              quantity: item.quantity,
              manufacturer: item.manufacturer,
              serialNo: item.serialNo
            })),
            fileList: fileViewList.value.map(file => ({
              fileId: file.fileId
            }))
          }
        ]
      }
    };
    console.log(innerFormData)

    // 构建最终提交数据结构
    const submitData = {
      processDefinitionId: 'equipment_acceptance:1:1910232819951554561',
      variables: {
        formData: JSON.stringify(innerFormData)
      }
    };

    const res = await EquipmentAcceptanceApi.submit(submitData);

    if (res.success) {
      message.success('提交成功');
    } else {
      message.error(res.message || '提交失败');
    }
  } catch (error) {
    message.error('提交失败：' + error.message);
  } finally {
    submitting.value = false;
  }
};

const formData = ref({
  id: null,
  baseCode: '', // 基础编码
  code: '', // 编码
  equType: null, // 设备类别
  equSubType: null, // 设备种类
  equName: null, // 设备名称
  equModel: null, // 规格型号
  equModelNote: '', // 型号备注
  equNature: null, // 设备性质
  purchaseYear: '', // 购置年度
  equSource: null, // 设备来源
  unit: '', // 单位
  num: 1, // 数量
  equContractPriceTax: null, // 设备合同价（含税）
  manufacturer: '', // 生产厂家
  factoryNumber: '', // 出厂编号
  productionDate: null, // 出厂日期
  contractNumber: '', // 合同编号
  acceptanceNumber: null, // 验收单号
  acceptanceChar: '', // 验字
  acceptanceDate: null, // 验收日期
  power: '', // 功率
  importance: null, // 设备重要性
  modelCode: '', // 设备型号编码
  serviceInterval: '', // 保养周期
  propertyOrg: null, // 产权单位
  managementStatus: null, // 管理状态
  managementOrg: null, // 管理单位
  equCondition: null, // 设备状态
  useOrg: null, // 使用单位
  storageLocationStr: null, // 存放地点
  financialNumber: '', // 财务卡片编号
  financialOrg: '', // 财务组织
  financialOriginalValue: null, // 财务原值
  fixedAssets: '', // 固定资产分类
  sourceOfFunds: '', // 资金来源
  deferredIncome: '', // 递延收益
  alreadyAccruedMonths: '', // 已计提折旧月份
  depreciationMethod: null, // 折旧方式
  depreciationPeriod: '', // 折旧年限
  depreciationMonth: '', // 折旧月份
  residualRate: null, // 残值率
  netSalvage: null, // 预计净残值
  historicalOriginalValue: null, // 历史原值
  depreciationAmount: null, // 累计折旧额
  currentMonthDepreciationAmount: null, // 当月折旧额
  netWorth: null, // 净值
  taxRate: 13, // 税率
});

// 附件列表数据
const attachments = ref([
  {
    name: '',
    unit: '',
    quantity: 1,
    manufacturer: '',
    serialNo: ''
  }
]);

// 添加新附件
const addAttachment = () => {
  attachments.value.push({
    name: '',
    unit: '',
    quantity: 1,
    manufacturer: '',
    serialNo: ''
  });
};

// 删除附件
const removeAttachment = (index) => {
  attachments.value.splice(index, 1);
};

// 处理普通附件上传
const handleChange = (info) => {
  console.log('当前上传的文件:', info.file);
  console.log('所有文件列表:', info.fileList);

  // 保持原始fileList与上传组件同步
  fileList.value = info.fileList;

  // 更新显示用的文件列表
  fileViewList.value = info.fileList
    .filter(file => file.status === 'done')  // 只保留上传完成的文件
    .map(file => ({
      fileId: file.response.data.fileId,fileUrl:file.response.data.fileUrl,
      fileName: file.name,
      uid: file.uid,
      status: file.status
    }));

  // 处理单个文件的状态提示
  if (info.file.status === 'error') {
    message.error('上传出错：' + info.file.error.message);
  }
};

// 处理技术资料上传
const handleTechFileChange = (info) => {
  // 保持原始列表与上传组件同步
  techFileList.value = info.fileList;

  if (info.file.status === 'done' && info.file.response) {
    const response = info.file.response;
    if (response.success) {
      // 更新显示列表
      techFileViewList.value = [{
        fileId: response.data.fileId,
        fileName: info.file.name,
        uid: info.file.uid,
        status: 'done'
      }];

      // 更新formData中的techFile用于显示
      formData.value.techFile = {
        fileId: response.data.fileId,
        fileName: info.file.name
      };
    } else {
      message.error('技术资料上传失败');
    }
  } else if (info.file.status === 'error') {
    message.error('上传出错：' + info.file.error.message);
  }
};

// 删除技术资料
const removeTechFile = () => {
  formData.value.techFile = null;
  techFileList.value = [];
  techFileViewList.value = [];
};

// 删除普通附件
const handleRemove = (file) => {
  // 从显示列表中删除
  const viewIndex = fileViewList.value.findIndex(f => f.uid === file.uid);
  if (viewIndex > -1) {
    fileViewList.value.splice(viewIndex, 1);
  }

  // 从上传列表中删除
  const fileIndex = fileList.value.findIndex(f => f.uid === file.uid);
  if (fileIndex > -1) {
    fileList.value.splice(fileIndex, 1);
  }
};

// 审批记录相关方法
const showApprovalRecord = () => {
  // 处理审批记录的显示逻辑
  console.log('显示审批记录');
};

const initializeData = async () => {
  loading.value = true;
  try {
    const { data } = await EquipmentAcceptanceApi.getNewBuilt();

    const isNewRecord = !data.approve &&
      !data.bussWorksheet &&
      !data.bussTransferForm &&
      !data.bussRegisteredEquipmentList;

    if (!isNewRecord && data.bussRegisteredEquipmentList?.length > 0) {
      const equipmentData = data.bussRegisteredEquipmentList[0];
      formData.value = {
        ...formData.value,
        ...equipmentData
      };
    }
  } catch (error) {
    message.error('获取数据失败：' + error.message);
  } finally {
    loading.value = false;
  }
};


const goTodeviceList = async (row) => {
  showEdit.value = true;
};

const goToregisteredList = async (row) => {
  showEdit1.value = true;
};

const datass = ref([])
const getList = (dataList) => {
  datass.value = dataList.selectedRows
}

const getList1 = (dataList) => {
  datass.value = dataList.selectedRows
}

onMounted(() => {
  initializeData();
});
</script>

<style lang="less" scoped>
.approval-record-btn {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  z-index: 100;

  .bg-image {
    display: block;
    max-width: 100%;
    height: auto;
    object-fit: contain;
    background-color: transparent;
  }

  .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .icon {
      width: 15px;
      height: 15px;
      margin-bottom: 4px;
    }

    .text {
      writing-mode: vertical-lr;
      font-size: 14px;
      color: #333;
      letter-spacing: 2px;
    }
  }
}

.equipment-acceptance {
  width: 100%;
  max-width: 1920px; // 最大宽度限制
  height: calc(100vh - 100px);
  overflow: auto;
  margin: 0 auto; // 居中
  padding: 24px 9.06vw 24px 8.18vw; // 转换为vw: 174/1920*100=9.06, 157/1920*100=8.18
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);

  .form-title {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 24px;
  }

  .section {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;

      .blue-bar {
        width: 3px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
      }
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: clamp(16px, 2vw, 24px);
    margin-bottom: 24px;
    justify-content: space-between;

    .form-item {
      display: flex;
      flex-direction: column;
      min-width: 300px; // 最小宽度
      max-width: 300px; // 最大宽度

      .label {
        height: 32px;
        line-height: 32px;
        margin-bottom: 8px;
        color: #0C0B0B;

        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 4px;
        }
      }

      .value {
        width: 100%;
        height: 32px;
        color: #666;

        :deep(.ant-input),
        :deep(.ant-select),
        :deep(.ant-picker),
        :deep(.ant-input-number) {
          width: 100%;
          height: 32px;
        }

        :deep(.ant-input-number-input) {
          height: 16px;
          line-height: 16px;
        }

        :deep(.ant-select-selector) {
          height: 32px !important;

          .ant-select-selection-item {
            line-height: 32px;
          }
        }

        :deep(.ant-picker) {
          .ant-picker-input {
            height: 16px;
            line-height: 16px;

            input {
              height: 16px;
              line-height: 16px;
            }
          }
        }
      }
    }
  }

  .acceptance-no {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    max-width: 300px; // 与其他表单项保持一致的最大宽度

    :deep(.first-input) {
      width: 65% !important; // 验资前面的输入框占65%
    }

    :deep(.second-input) {
      width: 35% !important; // 号前面的输入框占35%
    }

    .separator {
      color: #666;
      white-space: nowrap;
    }
  }
}

// 响应式布局
@media screen and (max-width: 1600px) {
  .equipment-acceptance {
    padding: 24px 6vw;

    .form-grid {
      gap: 16px;
    }
  }
}

@media screen and (max-width: 1366px) {
  .equipment-acceptance {
    padding: 24px 4vw;

    .form-grid {
      grid-template-columns: repeat(2, minmax(300px, 1fr));
    }
  }
}

@media screen and (max-width: 1024px) {
  .equipment-acceptance {
    padding: 24px 2vw;

    .form-grid {
      grid-template-columns: repeat(1, 1fr);

      .form-item {
        max-width: none;
      }
    }
  }
}

.percentage-input {
  position: relative;
  display: flex;
  align-items: center;

  .ant-input {
    width: 100%;
  }

  .percentage-symbol {
    position: absolute;
    right: 12px;
    color: #666;
  }
}

.attachment-table {
  width: 89%;
  border: 1px solid #e8e8e8;
  border-radius: 2px;


  .table-header,
  .table-row,
  .add-row {
    // 添加add-row到统一高度设置中
    display: flex;
    padding: 12px 8px;

    border-bottom: 1px solid #e8e8e8;
    align-items: center;
    gap: 12px;
    min-height: 56px; // 统一设置最小高度
  }

  .table-header {
    font-weight: 500;
  }

  .table-row:last-child {
    border-bottom: none;
  }

  // 使用百分比和flex布局
  .col-serial {
    width: 5%; // 序号列较窄
    min-width: 40px;
  }

  .col-name {
    width: 25%; // 名称及型号需要较大空间
    min-width: 180px;
  }

  .col-unit {
    width: 10%; // 单位列较窄
    min-width: 80px;
  }

  .col-quantity {
    width: 10%; // 数量列较窄
    min-width: 80px;
  }

  .col-manufacturer {
    width: 20%; // 生产厂家需要适中空间
    min-width: 150px;
  }

  .col-serial-no {
    width: 20%; // 出厂编号需要适中空间
    min-width: 150px;
  }

  .col-action {
    width: 10%; // 操作列较窄
    min-width: 60px;
    text-align: center;

    .delete-btn {
      color: #FF4D4F;
      cursor: pointer;
    }
  }

  :deep(.ant-input),
  :deep(.ant-input-number) {
    width: 100%;
  }

  // 响应式调整
  @media screen and (max-width: 1366px) {
    .col-name {
      width: 22%; // 较小屏幕时稍微压缩名称列
    }

    .col-manufacturer,
    .col-serial-no {
      width: 18%; // 压缩这两列
    }
  }

  @media screen and (max-width: 1024px) {
    overflow-x: auto; // 当屏幕太小时允许横向滚动

    .table-header,
    .table-row {
      min-width: 900px; // 确保在小屏幕上内容不会过度压缩
    }
  }

  .add-row {
    justify-content: center; // 水平居中
    align-items: center; // 垂直居中
    cursor: pointer;
    border-bottom: none; // 最后一行不需要底部边框

    a {
      display: flex;
      align-items: center; // 图标和文字垂直居中
      color: #1890ff;

      .anticon {
        margin-right: 4px;
      }
    }

    &:hover {
      background: #f5f5f5;
    }
  }
}

.upload-section {
  .upload-header {
    display: flex;
    align-items: center;
    gap: 12px;

    .upload-button {
      display: inline-flex;
      align-items: center;
      padding: 4px 15px;
      background: #0085FF;
      border-radius: 2px;
      color: white;
      cursor: pointer;
      font-size: 14px;

      .anticon {
        margin-right: 4px;
      }
    }

    .upload-tip {
      color: #999;
      font-size: 12px;
    }
  }

  .file-list {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    //display: grid;
    //grid-template-columns: repeat(3, 0.3fr);
    //gap: 16px;

    .file-item {
      display: flex;
      align-items: center;
      padding: 8px;
      // background: #F7F9FC;
      border-radius: 2px;
      width: 32%;

      .file-link{
        word-break:break-all;
      }

      .file-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #2D5CF6;
      }

      .file-name {
        flex: 1;
        font-size: 14px;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-actions {
        margin-left: 8px;

        .delete-icon {
          color: #999;
          cursor: pointer;

          &:hover {
            color: #FF4B4B;
          }
        }

        .anticon-sync {
          color: #2D5CF6;
        }
      }
    }
  }
}

.tech-file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;

  .file-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      color: #2D5CF6;
    }

    .file-name {
      color: #333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .delete-icon {
    color: #999;
    cursor: pointer;

    &:hover {
      color: #ff4d4f;
    }
  }
}

.tech-upload-button {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  color: #666;
  font-size: 14px;

  .anticon {
    margin-right: 4px;
    font-size: 14px;
  }

  &:hover {
    color: #2D5CF6;
    border-color: #2D5CF6;
  }
}

.value {
  :deep(.ant-input-affix-wrapper) {
    .ant-input-suffix {
      color: #666;
    }
  }
}

.bottom-buttons {
  margin-top: 150px;
  margin-bottom: 89rpx;
  bottom: 180px; // 修改为距离底部80px
  left: 0;
  right: 0;
  padding: 0 32px; // 调整左右内边距
  text-align: right;
  z-index: 100;

  .save-btn {
    height: 32px;
    padding: 0 16px;
    border: 1px solid #DCDFE6;
    color: #606266;
    background: transparent !important;
    border-radius: 4px;

    &:hover {
      border-color: #409EFF;
      color: #409EFF;
    }
  }

  .submit-btn {
    height: 32px;
    padding: 0 16px;
    margin-left: 27px; // 调整按钮之间的间距
    background: #409EFF;
    border-radius: 4px;
    border: none;

    &:hover {
      background: #66B1FF;
    }
  }
}
.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>
