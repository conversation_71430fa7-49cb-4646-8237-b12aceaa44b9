<template>
  <div class="input-uploader-container" v-click-outside="closeExpandedArea">
    <!-- 展开的预览区域 - 上方显示 -->
    <div v-if="isExpanded && dropdownPosition === 'top'" class="preview-area preview-area-top">
      <div class="preview-list">
        <!-- 已上传的文件 -->
        <div
          v-for="(file, index) in fileViewList"
          :key="file.uid"
          class="preview-item"
        >
          <div class="file-row">
            <div class="file-icon">
              <img src="/public/icon/icon-wrapper.svg" v-if="file.fileName.endsWith('.xls') || file.fileName.endsWith('.xlsx')" />
              <img src="/public/icon/word.svg" v-else-if="file.fileName.endsWith('.doc') || file.fileName.endsWith('.docx')" />
              <img src="/public/icon/pdf.svg" v-else-if="file.fileName.endsWith('.pdf')" />
              <img src="/public/icon/mp4.svg" v-else-if="file.fileName.endsWith('.mov') || file.fileName.endsWith('.mp4')" />
              <img src="/public/icon/ppt.svg" v-else-if="file.fileName.endsWith('.ppt') || file.fileName.endsWith('.pptx')" />
              <img src="/public/icon/jpg.svg" v-else-if="file.fileName.endsWith('.png') || file.fileName.endsWith('.jpg') || file.fileName.endsWith('.jpeg') || file.fileName.endsWith('.heif')" />
              <img src="/public/icon/reader.svg" v-else />
            </div>
            <div class="file-name" @click="openFileUrl(file)" :title="file.fileName">
              {{ file.fileName }}
            </div>
            <div class="delete-btn" @click.stop="handleRemove(file)" v-if="!readOnly">
              <img src="/public/icon/delete1.svg" alt="">
            </div>
          </div>

          <!-- 上传进度 -->
          <div class="progress-bar" v-if="file.percent !== 100">
            <a-progress :percent="file.percent || 0" size="small" />
          </div>
        </div>

        <!-- 添加文件按钮 -->
        <div class="add-file-row" @click="triggerUpload" v-if="!readOnly && fileViewList.length < maxCount">
          <plus-outlined />
          <span>上传本地文件</span>
        </div>
      </div>
    </div>

    <!-- 输入框样式的上传区域 -->
    <div
      class="upload-input-box"
      :class="{
        'expanded': isExpanded,
        'no-border': !showBorder,
        'read-only': readOnly,
        'expanded-top': isExpanded && dropdownPosition === 'top',
        'expanded-bottom': isExpanded && dropdownPosition === 'bottom'
      }"
      :style="{
        height: containerHeight,
        backgroundColor: backgroundColor
      }"
    >
      <div class="input-content" ref="inputContentRef">
        <!-- 已上传的文件预览（小图标） -->
        <div class="mini-previews" :class="{ 'read-only-previews': readOnly }">
          <div
            v-for="(file, index) in displayedFiles"
            :key="file.uid"
            class="mini-preview"
            @click="openFileUrl(file)"
            :title="file.fileName"
          >
            <div class="file-icon">
              <img src="/public/icon/icon-wrapper.svg" v-if="file.fileName.endsWith('.xls') || file.fileName.endsWith('.xlsx')" />
              <img src="/public/icon/word.svg" v-else-if="file.fileName.endsWith('.doc') || file.fileName.endsWith('.docx')" />
              <img src="/public/icon/pdf.svg" v-else-if="file.fileName.endsWith('.pdf')" />
              <img src="/public/icon/mp4.svg" v-else-if="file.fileName.endsWith('.mov') || file.fileName.endsWith('.mp4')" />
              <img src="/public/icon/ppt.svg" v-else-if="file.fileName.endsWith('.ppt') || file.fileName.endsWith('.pptx')" />
              <img src="/public/icon/jpg.svg" v-else-if="file.fileName.endsWith('.png') || file.fileName.endsWith('.jpg') || file.fileName.endsWith('.jpeg') || file.fileName.endsWith('.heif')" />
              <img src="/public/icon/reader.svg" v-else />
            </div>
          </div>
          <!-- 显示更多文件的提示 -->
          <div
            v-if="showMoreIndicator"
            class="mini-preview more-files"
            @click="toggleExpand"
            title="查看更多文件"
          >
            <div class="more-icon">...</div>
          </div>
        </div>

        <!-- 右侧按钮组 -->
        <div class="button-group">
          <!-- 上传按钮 -->
          <div class="upload-btn" @click="triggerUpload" v-if="!readOnly">
            <plus-outlined />
          </div>

          <!-- 展开/收起按钮 -->
          <div class="expand-btn" @click="toggleExpand" v-if="fileViewList.length > 0 && !readOnly">
            <caret-down-outlined 
              :class="{ 
                'rotated': isExpanded && dropdownPosition === 'bottom',
                'rotated-up': isExpanded && dropdownPosition === 'top'
              }" 
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 展开的预览区域 - 下方显示 -->
    <div v-if="isExpanded && dropdownPosition === 'bottom'" class="preview-area preview-area-bottom">
      <div class="preview-list">
        <!-- 已上传的文件 -->
        <div
          v-for="(file, index) in fileViewList"
          :key="file.uid"
          class="preview-item"
        >
          <div class="file-row">
            <div class="file-icon">
              <img src="/public/icon/icon-wrapper.svg" v-if="file.fileName.endsWith('.xls') || file.fileName.endsWith('.xlsx')" />
              <img src="/public/icon/word.svg" v-else-if="file.fileName.endsWith('.doc') || file.fileName.endsWith('.docx')" />
              <img src="/public/icon/pdf.svg" v-else-if="file.fileName.endsWith('.pdf')" />
              <img src="/public/icon/mp4.svg" v-else-if="file.fileName.endsWith('.mov') || file.fileName.endsWith('.mp4')" />
              <img src="/public/icon/ppt.svg" v-else-if="file.fileName.endsWith('.ppt') || file.fileName.endsWith('.pptx')" />
              <img src="/public/icon/jpg.svg" v-else-if="file.fileName.endsWith('.png') || file.fileName.endsWith('.jpg') || file.fileName.endsWith('.jpeg') || file.fileName.endsWith('.heif')" />
              <img src="/public/icon/reader.svg" v-else />
            </div>
            <div class="file-name" @click="openFileUrl(file)" :title="file.fileName">
              {{ file.fileName }}
            </div>
            <div class="delete-btn" @click.stop="handleRemove(file)" v-if="!readOnly">
              <img src="/public/icon/delete1.svg" alt="">
            </div>
          </div>

          <!-- 上传进度 -->
          <div class="progress-bar" v-if="file.percent !== 100">
            <a-progress :percent="file.percent || 0" size="small" />
          </div>
        </div>

        <!-- 添加文件按钮 -->
        <div class="add-file-row" @click="triggerUpload" v-if="!readOnly && fileViewList.length < maxCount">
          <plus-outlined />
          <span>上传本地文件</span>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      :multiple="allowMultiple"
      :accept="acceptString"
      style="display: none"
      @change="handleFileSelect"
    />

    <!-- 文件预览模态框 -->
    <a-modal
      v-model:open="previewVisible"
      :footer="null"
      :width="800"
      centered
    >
      <div v-if="previewFileType === 'image'">
        <img :src="previewFileUrl" style="width: 100%" />
      </div>
      <div v-else class="document-preview">
        <div class="document-info">
          <div class="large-file-icon" :class="getFileTypeClass(previewFileName)">
            {{ getFileExtension(previewFileName) }}
          </div>
          <div class="file-details">
            <div class="file-name">{{ previewFileName }}</div>
            <div class="file-actions">
              <a-button type="primary" @click="downloadFile(previewFileUrl, previewFileName)">
                下载文件
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { PlusOutlined, CaretDownOutlined, CloseOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { getToken } from '@/utils/token-util';
import { API_BASE_PREFIX } from '@/config/setting';
import { FileUploadUrl } from '@/api/system/operation/FileApi';
import axios from 'axios';

// 点击外部关闭指令
const vClickOutside = {
  mounted(el, binding) {
    el._clickOutside = (event) => {
      if (!(el === event.target || el.contains(event.target))) {
        binding.value(event);
      }
    };
    document.addEventListener('click', el._clickOutside);
  },
  unmounted(el) {
    document.removeEventListener('click', el._clickOutside);
  }
};

const props = defineProps({
  // 上传URL，默认使用系统文件上传接口
  action: {
    type: String,
    default: `${API_BASE_PREFIX}${FileUploadUrl}?secretFlag=0`
  },
  // 允许的文件类型，如 ['.jpg', '.png', '.pdf']
  acceptTypes: {
    type: Array,
    default: () => []
  },
  // 最大文件大小（MB）
  maxSize: {
    type: Number,
    default: 5
  },
  // 最大上传数量
  maxCount: {
    type: Number,
    default: 10
  },
  // 是否允许多文件上传
  allowMultiple: {
    type: Boolean,
    default: true
  },
  // 上传按钮文本
  buttonText: {
    type: String,
    default: '点击上传'
  },
  // 提示文本
  tipText: {
    type: String,
    default: ''
  },
  // 初始文件列表 - 用于回显
  initialFiles: {
    type: Array,
    default: () => []
  },
  backgroundColor: {
    type: String,
    default: ''
  },
  // 是否为只读模式（不显示上传和删除按钮）
  readOnly: {
    type: Boolean,
    default: false
  },
  // 是否显示输入框边框
  showBorder: {
    type: Boolean,
    default: true
  },
  // 组件宽度
  width: {
    type: [String, Number],
    default: '100%'
  },
  // 输入框高度
  height: {
    type: [String, Number],
    default: 32
  },
  disabled: {
    type: Boolean,
    default: false
  },
  // 下拉框位置：'top' | 'bottom'
  dropdownPosition: {
    type: String,
    default: 'bottom',
    validator: (value) => ['top', 'bottom'].includes(value)
  }
})

const emit = defineEmits(['update:files', 'file-uploaded', 'file-removed', 'upload-status-change'])

const fileInput = ref()
const inputContentRef = ref()
const fileList = ref([])
const fileViewList = ref([])
const isExpanded = ref(false)
const previewVisible = ref(false)
const previewFileUrl = ref('')
const previewFileName = ref('')
const previewFileType = ref('')
const uploading = ref(false)
const isInitializing = ref(false)

// 计算容器宽度
const containerWidth = computed(() => {
  if (typeof props.width === 'number') {
    return `${props.width}px`
  }
  return props.width
})

// 计算输入框高度
const containerHeight = computed(() => {
  if (typeof props.height === 'number') {
    return `${props.height}px`
  }
  return props.height
})

// 上传URL
const uploadUrl = computed(() => props.action)

const acceptString = ref('.jpg,.jpeg,.png,.heif,.pdf,.xls,.xlsx,.doc,.docx,.ppt,.pptx,.mp4,.mov');

// 请求头
const headers = {
  Authorization: getToken()
}

// 初始化文件列表
onMounted(() => {
  initializeFiles()
})

watch(() => props.initialFiles, (newFiles, oldFiles) => {
  if (!isInitializing.value && JSON.stringify(newFiles) !== JSON.stringify(oldFiles)) {
    initializeFiles()
  }
}, { deep: true })

const initializeFiles = () => {
  isInitializing.value = true

  if (props.initialFiles && props.initialFiles.length > 0) {
    const initialFilesList = props.initialFiles.map(file => ({
      fileId: file.fileId || '',
      fileUrl: file.fileUrl || '',
      fileName: file.fileName || file.fileOriginName || '',
      uid: file.uid || file.fileId || Date.now().toString(),
      status: 'done',
      percent: 100
    }))

    fileViewList.value = [...initialFilesList]
    fileList.value = initialFilesList.map(file => ({
      uid: file.uid,
      name: file.fileName,
      status: 'done',
      url: file.fileUrl,
      response: {
        data: {
          fileId: file.fileId,
          fileUrl: file.fileUrl
        }
      }
    }))
  } else {
    fileViewList.value = []
    fileList.value = []
  }

  isInitializing.value = false
}

// 判断是否为图片文件
const isImage = (fileName) => {
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
  const extension = getFileExtension(fileName).toLowerCase()
  return imageTypes.includes(extension)
}

// 获取文件扩展名
const getFileExtension = (filename) => {
  return filename.split('.').pop().toLowerCase()
}

// 获取文件类型样式类
const getFileTypeClass = (filename) => {
  const ext = getFileExtension(filename)
  const typeMap = {
    pdf: 'pdf-icon',
    doc: 'word-icon',
    docx: 'word-icon',
    xls: 'excel-icon',
    xlsx: 'excel-icon',
    ppt: 'ppt-icon',
    pptx: 'ppt-icon',
    txt: 'txt-icon'
  }
  return typeMap[ext] || 'default-icon'
}

// 格式化文件名
const formatFileName = (fileName) => {
  if (fileName.length > 15) {
    return fileName.substring(0, 12) + '...' + fileName.substring(fileName.lastIndexOf('.'))
  }
  return fileName
}

// 触发文件选择
const triggerUpload = () => {
  if (props.readOnly) return
  fileInput.value.click()
}

// 上传前验证
const beforeUpload = (file, fileList) => {
  // 验证文件类型
  if (props.acceptTypes.length > 0) {
    const fileName = file.name.toLowerCase()
    const isValidType = props.acceptTypes.some(type =>
      fileName.endsWith(type.toLowerCase())
    )

    if (!isValidType) {
      message.error(`请上传${props.acceptTypes.join('、')}格式的文件`)
      return false
    }
  }

  // 验证文件大小
  const maxSizeBytes = props.maxSize * 1024 * 1024
  if (file.size > maxSizeBytes) {
    message.error(`文件大小不能超过${props.maxSize}MB`)
    return false
  }

  return true
}

// 处理文件选择
const handleFileSelect = (event) => {
  if (props.readOnly) return

  const files = Array.from(event.target.files)

  files.forEach(file => {
    if (!beforeUpload(file, [file])) {
      return
    }

    // 创建文件对象
    const fileInfo = {
      fileId: '',
      fileUrl: '',
      fileName: file.name,
      uid: Date.now() + Math.random(),
      status: 'uploading',
      percent: 0
    }

    // 添加到视图列表
    fileViewList.value = [...fileViewList.value, fileInfo]

    // 开始上传
    customRequest({
      file,
      onProgress: ({ percent }) => {
        const index = fileViewList.value.findIndex(item => item.uid === fileInfo.uid)
        if (index >= 0) {
          const newFileViewList = [...fileViewList.value]
          newFileViewList[index] = {
            ...newFileViewList[index],
            percent
          }
          fileViewList.value = newFileViewList
          emit('update:files', JSON.parse(JSON.stringify(fileViewList.value)))
        }
      },
      onSuccess: (response) => {
        const index = fileViewList.value.findIndex(item => item.uid === fileInfo.uid)
        if (index >= 0) {
          const newFileViewList = [...fileViewList.value]
          newFileViewList[index] = {
            ...newFileViewList[index],
            fileId: response.data.fileId,
            fileUrl: response.data.fileUrl,
            status: 'done',
            percent: 100
          }
          fileViewList.value = newFileViewList
          emit('update:files', JSON.parse(JSON.stringify(fileViewList.value)))
          emit('file-uploaded', newFileViewList[index])
        }
      },
      onError: (error) => {
        const index = fileViewList.value.findIndex(item => item.uid === fileInfo.uid)
        if (index >= 0) {
          const newFileViewList = fileViewList.value.filter(item => item.uid !== fileInfo.uid)
          fileViewList.value = newFileViewList
          emit('update:files', JSON.parse(JSON.stringify(newFileViewList)))
        }
        message.error(`上传失败：${error.message || '未知错误'}`)
      }
    })
  })

  // 清空input
  event.target.value = ''
}

// 自定义上传方法
const customRequest = async (options) => {
  const { file, onProgress, onSuccess, onError } = options

  try {
    uploading.value = true
    emit('upload-status-change', true)

    const formData = new FormData()
    formData.append('file', file)

    const response = await axios.post(uploadUrl.value, formData, {
      headers: {
        ...headers,
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress({ percent })
      }
    })

    if (response.data && response.data.success) {
      onSuccess(response.data)
      message.success('文件上传成功')
    } else {
      throw new Error(response.data?.message || '上传失败')
    }
  } catch (error) {
    console.error('上传错误:', error)
    onError(error)
  } finally {
    uploading.value = false
    emit('upload-status-change', false)
  }
}

// 切换展开状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

// 关闭展开区域
const closeExpandedArea = (event) => {
  if (isExpanded.value) {
    isExpanded.value = false
  }
}

// 打开文件URL
const openFileUrl = (file) => {
  window.open(`${file.fileUrl}?filename=${file.fileName}`, '_blank')
}

// 预览文件
const previewFile = (file) => {
  previewFileName.value = file.fileName
  previewFileUrl.value = file.fileUrl
  previewFileType.value = isImage(file.fileName) ? 'image' : 'document'
  previewVisible.value = true
}

// 下载文件
const downloadFile = (url, filename) => {
  const link = document.createElement('a')
  link.href = `${url}?filename=${filename}`
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 删除文件
const handleRemove = (file) => {
  if (props.readOnly) return

  const newFileViewList = fileViewList.value.filter(item => item.uid !== file.uid)
  fileViewList.value = newFileViewList

  const newFileList = fileList.value.filter(item => item.uid !== file.uid)
  fileList.value = newFileList

  emit('update:files', JSON.parse(JSON.stringify(newFileViewList)))
  emit('file-removed', file)

  event?.stopPropagation()
}

// 动态计算显示的文件数量
const displayedFiles = computed(() => {
  if (fileViewList.value.length === 0) {
    return []
  }

  // 如果文件数量很少，全部显示
  if (fileViewList.value.length <= 2) {
    return fileViewList.value
  }

  // 调整计算参数，让显示更宽松
  const buttonGroupWidth = 80 // 按钮组宽度
  const padding = 5 // 内边距
  const moreIndicatorWidth = 25 // "+N"指示器宽度
  const fileIconWidth = 28 // 每个文件图标宽度（20px图标 + 8px间距）

  // 获取实际像素宽度
  let actualWidth = containerWidth.value
  console.log('容器宽度:', actualWidth, '类型:', typeof actualWidth)

  console.log('实际容器宽度:', actualWidth, '原始值:', containerWidth.value)
  const availableWidth = actualWidth.split("px")[0]*1 - buttonGroupWidth - padding

  // 计算最大可显示数量（不考虑更多指示器）
  const maxPossibleCount = Math.floor(availableWidth / fileIconWidth)
  console.log('可显示文件数量:', maxPossibleCount, '可用宽度:', availableWidth, '每个图标宽度:', fileIconWidth)
  // 如果连两个都显示不了，强制显示两个
  if (maxPossibleCount < 2) {
    return fileViewList.value.slice(0, Math.min(2, fileViewList.value.length))
  }

  // 如果能显示所有文件，就全部显示
  if (maxPossibleCount >= fileViewList.value.length) {
    return fileViewList.value
  }

  // 需要显示"更多"指示器的情况
  // 计算预留更多指示器空间后能显示多少个
  const maxWithMoreIndicator = Math.floor((availableWidth - moreIndicatorWidth) / fileIconWidth)

  // 确保至少显示2个文件图标
  const displayCount = Math.max(1, maxWithMoreIndicator)

  // 如果显示数量已经等于总数量，就不需要更多指示器了
  if (displayCount >= fileViewList.value.length) {
    return fileViewList.value
  }

  return fileViewList.value.slice(0, displayCount)
})

// 修改更多文件的判断条件
const showMoreIndicator = computed(() => {
  return fileViewList.value.length > displayedFiles.value.length;
});

// 监听容器宽度变化
const updateContainerWidth = () => {
  if (inputContentRef.value) {
    containerWidth.value = inputContentRef.value.offsetWidth
  }
}

// 监听窗口大小变化
onMounted(() => {
  updateContainerWidth()
  window.addEventListener('resize', updateContainerWidth)

  // 使用 ResizeObserver 监听容器大小变化
  if (window.ResizeObserver && inputContentRef.value) {
    const resizeObserver = new ResizeObserver(() => {
      updateContainerWidth()
    })
    resizeObserver.observe(inputContentRef.value)
  }
})

// 监听 props.width 变化
watch(() => props.width, () => {
  setTimeout(updateContainerWidth, 0)
})
</script>

<style scoped>
.input-uploader-container {
  position: relative;
}

.upload-input-box {
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  padding: 8px 12px;
  transition: all 0.3s;
  min-height: 32px;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 999;
}

.upload-input-box.no-border {
  border: none;
  box-shadow: none;
}

.upload-input-box:hover {
  border-color: #40a9ff;
}

.upload-input-box.no-border:hover {
  border: none;
}

.upload-input-box.expanded-bottom {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  position: relative;
  z-index: 1001;
}

.upload-input-box.expanded-top {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  position: relative;
  z-index: 1001;
}

.upload-input-box.expanded-bottom.no-border,
.upload-input-box.expanded-top.no-border {
  border: none;
  box-shadow: none;
}

.input-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
}

.mini-previews {
  display: flex;
  gap: 4px;
  overflow: hidden;
  flex-wrap: nowrap;
  flex: 1;
  min-width: 0;
  cursor: pointer;
}

.mini-preview {
  width: 30px;
  height: 30px;
  flex-shrink: 0;
  cursor: pointer;
}

.file-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-type-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  font-weight: bold;
  text-transform: uppercase;
}

.pdf-icon { background: #ff4d4f; color: white; }
.word-icon { background: #1890ff; color: white; }
.excel-icon { background: #52c41a; color: white; }
.ppt-icon { background: #fa8c16; color: white; }
.txt-icon { background: #722ed1; color: white; }
.default-icon { background: #8c8c8c; color: white; }

.button-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
  margin-left: 8px;
}

.upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  color: #666;
  transition: all 0.3s;
  flex-shrink: 0;
}

.upload-btn:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

.expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  cursor: pointer;
  color: #666;
  transition: all 0.3s;
  flex-shrink: 0;
}

.expand-btn:hover {
  color: #40a9ff;
}

.expand-btn .rotated {
  transform: rotate(180deg);
}

.expand-btn .rotated-up {
  transform: rotate(0deg);
}

/* 下方显示的预览区域 */
.preview-area-bottom {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 4px;
  padding: 12px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  max-height: 300px;
  overflow-y: auto;
}

/* 上方显示的预览区域 */
.preview-area-top {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  margin-bottom: 4px;
  padding: 12px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  max-height: 300px;
  overflow-y: auto;
}

.preview-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.preview-item {
  width: 100%;
}

.file-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 8px;
  background: transparent;
  border: none;
  border-radius: 4px;
  min-height: 28px;
}

.file-row .file-name {
  flex: 1;
  font-size: 13px;
  color: #1890ff;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.file-row .file-name:hover {
  text-decoration: underline;
}

.file-row .delete-btn {
  width: 14px;
  height: 14px;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #999;
  font-size: 12px;
  flex-shrink: 0;
}

.file-row .delete-btn:hover {
  color: #ff4d4f;
}

.progress-bar {
  margin-top: 2px;
  padding: 0 8px;
}

.add-file-row {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: none;
  background: transparent;
  cursor: pointer;
  color: #666;
  font-size: 12px;
  min-height: 24px;
  margin-top: 4px;
}

.add-file-row:hover {
  color: #1890ff;
}

.add-file-row .anticon {
  font-size: 12px;
}

.add-file-row span {
  font-size: 12px;
}

.document-preview {
  padding: 20px;
  text-align: center;
}

.document-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.large-file-icon {
  width: 60px;
  height: 60px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  text-align: left;
}

.file-details .file-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
  max-width: 100%;
}

.file-actions {
  margin-top: 12px;
}

/* 添加更多文件提示的样式 */
.more-files {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.more-icon {
  font-size: 14px;
  font-weight: bold;
  color: #666;
}

.more-files:hover .more-icon {
  color: #40a9ff;
}

.upload-input-box.read-only {
  cursor: default;
}

.upload-input-box.read-only:hover {
  border-color: #d9d9d9;
}

.upload-input-box.read-only.no-border:hover {
  border: none;
}
</style>


