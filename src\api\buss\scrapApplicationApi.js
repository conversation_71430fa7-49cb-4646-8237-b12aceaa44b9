import Request from '@/utils/request-util';

/**
 * 已办任务api
 *
 * <AUTHOR>
 * @date 2022/5/15 17:10
 */
export class scrapApplicationApi {
  /**
   * 分页获取列表
   *
   * <AUTHOR>
   * @date 2022/5/8 20:36
   */
  static findPage(params) {
    return Request.getAndLoadData('/flowableInstance/my', params);
  }

  /**
    * 分页获取列表
    *
    * <AUTHOR>
    * @date 2022/5/8 20:36
    */
  static todoTaskPage(params) {
    return Request.getAndLoadData('/flowableTodoTask/page', params);
  }

  /**
   * 分页获取列表
   *
   * <AUTHOR>
   * @date 2022/5/8 20:36
   */
  static doneTaskPage(params) {
    return Request.getAndLoadData('/flowableDoneTask/page', params);
  }

  static getToDo(params) {
    return Request.get('/apiBus/scrapedEquipment/getWorksheetInfo', params);
  }

  static getToDoNo(params) {
    return Request.get('/apiBus/disposaledEquipment/getWorksheetInfo', params);
  }

  static getDoNe(params) {
    return Request.get('/apiBus/scrapedEquipment/getWorksheetInfo', params);
  }

  static submit(params) {
    return Request.post('/flowableHandleTask/submit', params);
  }

  static end(params) {
    return Request.post('/flowableInstance/end', params);
  }
}
