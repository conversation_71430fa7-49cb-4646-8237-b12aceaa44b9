<template>
  <div class="map-container">
    <div id="map" class="map"></div>
    <div class="control-panel">
      <div class="panel-item">
        <label>底图样式:</label>
        <a-select v-model:value="mapStyle" @change="changeMapStyle" style="width: 120px">
          <a-select-option value="dark">蓝黑色</a-select-option>
          <a-select-option value="satellite">卫星图</a-select-option>
          <a-select-option value="normal">标准</a-select-option>
        </a-select>
      </div>
      <div class="panel-item">
        <a-button type="primary" @click="addRandomMarkers">添加扎点</a-button>
        <a-button @click="clearMarkers">清除扎点</a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import 'ol/ol.css';
import Map from 'ol/Map';
import View from 'ol/View';
import TileLayer from 'ol/layer/Tile';
import XYZ from 'ol/source/XYZ';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import Feature from 'ol/Feature';
import Point from 'ol/geom/Point';
import { Style, Fill, Stroke, Circle, Icon } from 'ol/style';
import { fromLonLat } from 'ol/proj';

const TIANDITU_KEY = 'b0cf25a9fe9839fe9cea00f386c84238';

const map = ref(null);
const mapStyle = ref('dark');
const markers = ref([]);
const vectorLayer = ref(null);

// 模拟扎点数据
const mockMarkerData = [
  { name: '北京站点', lng: 116.397428, lat: 39.90923, type: '重要站点' },
  { name: '上海站点', lng: 121.473701, lat: 31.230416, type: '普通站点' },
  { name: '广州站点', lng: 113.280637, lat: 23.125178, type: '重要站点' },
  { name: '深圳站点', lng: 114.085947, lat: 22.547, type: '普通站点' },
];

// 创建蓝黑色科技风底图图层
const createOSMLayer = () => {
  return new TileLayer({
    source: new XYZ({
      url: `https://t{0-7}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&childLevel=0&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${TIANDITU_KEY}`,
      wrapX: false
    }),
    className: 'tech-blue-layer'
  });
};

// 创建标注图层 - 根据缩放级别控制显示
const createLabelLayer = () => {
  const labelLayer = new TileLayer({
    source: new XYZ({
      url: `https://t{0-7}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&childLevel=0&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${TIANDITU_KEY}`,
      wrapX: false
    }),
    className: 'tech-blue-label',
    minZoom: 6, // 只在缩放级别6以上显示标注
    maxZoom: 10 // 缩放级别10以上隐藏，避免显示太多小地名
  });
  
  return labelLayer;
};

// 创建卫星图层
const createSatelliteLayer = () => {
  return new TileLayer({
    source: new XYZ({
      url: `https://t{0-7}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&childLevel=0&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${TIANDITU_KEY}`,
      wrapX: false
    }),
    className: 'satellite-layer'
  });
};

// 创建卫星标注图层
const createSatelliteLabelLayer = () => {
  return new TileLayer({
    source: new XYZ({
      url: `https://t{0-7}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${TIANDITU_KEY}`,
      wrapX: false
    }),
    className: 'satellite-label-layer'
  });
};

// 创建矢量图层用于扎点 - 使用青色图标
const createVectorLayer = () => {
  return new VectorLayer({
    source: new VectorSource(),
    style: new Style({
      image: new Icon({
        src: 'data:image/svg+xml;base64,' + btoa(`
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="10" cy="10" r="8" fill="#00d4ff" stroke="#ffffff" stroke-width="2"/>
            <circle cx="10" cy="10" r="4" fill="#ffffff"/>
          </svg>
        `),
        scale: 1.0,
        anchor: [0.5, 0.5]
      })
    })
  });
};

// 初始化地图
const initMap = () => {
  vectorLayer.value = createVectorLayer();
  
  map.value = new Map({
    target: 'map',
    layers: [
      createOSMLayer(),
      createLabelLayer(),
      vectorLayer.value
    ],
    view: new View({
      center: fromLonLat([116.397428, 39.90923]),
      zoom: 5
    })
  });
  
  addInitialMarkers();
};

// 切换地图样式
const changeMapStyle = (style) => {
  const layers = map.value.getLayers();
  
  const layersToRemove = [];
  layers.forEach(layer => {
    if (layer !== vectorLayer.value) {
      layersToRemove.push(layer);
    }
  });
  layersToRemove.forEach(layer => {
    map.value.removeLayer(layer);
  });
  
  if (style === 'normal') {
    map.value.addLayer(createOSMLayer());
    map.value.addLayer(createLabelLayer());
  } else if (style === 'satellite') {
    map.value.addLayer(createSatelliteLayer());
    map.value.addLayer(createSatelliteLabelLayer());
  }
  
  map.value.removeLayer(vectorLayer.value);
  map.value.addLayer(vectorLayer.value);
};

// 添加初始扎点
const addInitialMarkers = () => {
  mockMarkerData.forEach(data => {
    addMarker(data);
  });
};

// 添加扎点 - 使用圆形图标
const addMarker = (data) => {
  const feature = new Feature({
    geometry: new Point(fromLonLat([data.lng, data.lat])),
    name: data.name,
    type: data.type
  });
  
  // 根据类型设置不同的图标颜色
  const iconColor = data.type === '重要站点' ? '#ff6b6b' : '#00d4ff';
  const iconSvg = `
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="10" cy="10" r="8" fill="${iconColor}" stroke="#ffffff" stroke-width="2"/>
      <circle cx="10" cy="10" r="4" fill="#ffffff"/>
    </svg>
  `;
  
  feature.setStyle(new Style({
    image: new Icon({
      src: 'data:image/svg+xml;base64,' + btoa(iconSvg),
      scale: 1.0,
      anchor: [0.5, 0.5]
    })
  }));
  
  vectorLayer.value.getSource().addFeature(feature);
  markers.value.push(feature);
};

// 添加随机扎点
const addRandomMarkers = () => {
  const randomCount = Math.floor(Math.random() * 5) + 3;
  
  for (let i = 0; i < randomCount; i++) {
    const randomData = {
      name: `随机站点${Date.now()}-${i}`,
      lng: 110 + Math.random() * 20,
      lat: 25 + Math.random() * 20,
      type: Math.random() > 0.5 ? '重要站点' : '普通站点'
    };
    
    addMarker(randomData);
  }
  
  message.success(`成功添加 ${randomCount} 个随机扎点`);
};

// 清除所有扎点
const clearMarkers = () => {
  vectorLayer.value.getSource().clear();
  markers.value = [];
  message.success('已清除所有扎点');
};

onMounted(() => {
  initMap();
});

onUnmounted(() => {
  if (map.value) {
    map.value.setTarget(null);
  }
});
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #1a2f4a, #2d4a6b);
}

.map {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a2f4a, #2d4a6b);
}

/* 深蓝色底图样式 - 让陆地显示为深蓝色，海洋为亮蓝色 */
/* :deep(.tech-blue-layer) {
  filter: 
    Grayscale: 90%
    Sepia: 46%
    Invert: 100%
    Saturate: 428%
} */

:deep(.tech-blue-layer) {
  filter: 
    grayscale(33%)    /* 不应用灰度 */
    sepia(65%)        /* 不应用棕褐色调 */
    invert(100%)       /* 反转颜色，可能需要调整这个值 */
    saturate(262%)    /* 增加饱和度，可能需要调整这个值 */

}

/* 白色文字标注 */
:deep(.tech-blue-label) {
  filter: 
    invert(100%)         /* 反转颜色，使文字变白 */
    brightness(2)        /* 提高亮度 */
    contrast(1.3);       /* 增强对比度 */
  opacity: 1 !important;
}

/* 卫星图深蓝处理 */
:deep(.satellite-layer) {
  filter: 
    invert(0.8) 
    hue-rotate(200deg) 
    brightness(0.7) 
    contrast(1.6) 
    saturate(2) !important;
}

:deep(.satellite-label-layer) {
  filter: 
    invert(0.8) 
    hue-rotate(180deg) 
    brightness(1.5) 
    contrast(1.3) !important;
  opacity: 0.9 !important;
}

/* OpenLayers控件深蓝主题 */
:deep(.ol-zoom) {
  background-color: rgba(26, 35, 50, 0.95) !important;
  border: 1px solid rgba(0, 255, 255, 0.4) !important;
  box-shadow: 0 0 8px rgba(0, 255, 255, 0.2) !important;
}

:deep(.ol-zoom button) {
  background-color: rgba(26, 35, 50, 0.95) !important;
  color: #00ffff !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
  transition: all 0.3s ease !important;
}

:deep(.ol-zoom button:hover) {
  background-color: rgba(0, 255, 255, 0.15) !important;
  color: #ffffff !important;
  border-color: #00ffff !important;
  box-shadow: 0 0 6px rgba(0, 255, 255, 0.4) !important;
}

:deep(.ol-attribution) {
  background-color: rgba(26, 35, 50, 0.9) !important;
  color: #00ffff !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
}

:deep(.ol-attribution a) {
  color: #40e0d0 !important;
}

.control-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, rgba(26, 35, 50, 0.95), rgba(20, 30, 45, 0.95));
  color: #ffffff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 
    0 4px 20px rgba(0, 255, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.3);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.panel-item {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.panel-item:last-child {
  margin-bottom: 0;
}

.panel-item label {
  font-weight: 500;
  white-space: nowrap;
  color: #00ffff;
  font-size: 14px;
}

/* 深蓝青色主题组件样式 */
.control-panel :deep(.ant-btn) {
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.08), rgba(0, 255, 255, 0.03));
  border: 1px solid rgba(0, 255, 255, 0.3);
  color: #00ffff;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 255, 255, 0.1);
}

.control-panel :deep(.ant-btn:hover) {
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.15), rgba(0, 255, 255, 0.08));
  border-color: #00ffff;
  color: #ffffff;
  box-shadow: 0 3px 12px rgba(0, 255, 255, 0.25);
  transform: translateY(-1px);
}

.control-panel :deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #00ffff, #00cccc);
  border-color: #00ffff;
  color: #1a2332;
  font-weight: 600;
}

.control-panel :deep(.ant-btn-primary:hover) {
  background: linear-gradient(135deg, #33ffff, #00ffff);
  border-color: #33ffff;
  color: #1a2332;
  box-shadow: 0 4px 16px rgba(0, 255, 255, 0.3);
}

.control-panel :deep(.ant-select .ant-select-selector) {
  background: linear-gradient(135deg, rgba(26, 35, 50, 0.9), rgba(20, 30, 45, 0.9)) !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
  color: #00ffff !important;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.control-panel :deep(.ant-select-arrow) {
  color: #00ffff;
}

.control-panel :deep(.ant-select-dropdown) {
  background: linear-gradient(135deg, rgba(26, 35, 50, 0.98), rgba(20, 30, 45, 0.98)) !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
  box-shadow: 0 6px 24px rgba(0, 255, 255, 0.15) !important;
  backdrop-filter: blur(10px) !important;
}

.control-panel :deep(.ant-select-item) {
  color: #00ffff !important;
  transition: all 0.3s ease !important;
}

.control-panel :deep(.ant-select-item:hover) {
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.12), rgba(0, 255, 255, 0.06)) !important;
  color: #ffffff !important;
}

.control-panel :deep(.ant-select-item-option-selected) {
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(0, 255, 255, 0.12)) !important;
  color: #ffffff !important;
  font-weight: 600 !important;
}
</style>

