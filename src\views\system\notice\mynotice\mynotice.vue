<template>
  <div class="ele-body">
    <div class="notice-wrapper" >
      <!-- 顶部导航 -->
      <div class="notice-header">
        <div class="left-nav">
          <span>消息中心</span>
        </div>
        <!-- <div class="right-nav">
          <a-button type="link" @click="setAlready">
            <span>全部标记为已读</span>
          </a-button>
        </div> -->
      </div>
      <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
        <div>


          <a-radio-group v-model:value="radioType" @change="onRadioTypeChange" class="custom-radio-group">
            <a-radio-button v-for="item in radioOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-radio-button>
          </a-radio-group>
        </div>
        <div>
   
          <a-select v-model:value="selectType" style="width: 120px; margin-left: 10px;" @change="onSelectTypeChange"
            size="small" :bordered="false" placeholder="全部"  class="print">
            <a-select-option value="">全部</a-select-option>
            <a-select-option v-for="item in selectOptions" :key="item.label" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </div>
      </div>


      <!-- 消息列表 -->
      <div class="notice-content" ref="noticeContentRef" @scroll="handleScroll" v-if="messageList.length > 0">
        <div class="notice-list1">
          <div v-for="item in messageList" :key="item.messageId"
            :class="item.readFlag === 0 ? 'notice-item' : 'notice-item2'">
            <div class="notice-item-header">
              <div class="notice-title">
                <span class="title-text">{{ item.messageTitle }}</span>
                <span class="unread-badge" v-if="item.readFlag === 0">●</span>
              </div>
              <span class="notice-time">{{ item.messageSendTime }}</span>
            </div>
            <div class="notice-body">
              <p class="notice-desc">{{ item.messageContent }}</p>
              <a-button type="link" class="detail-btn" @click="openEdit(item)">
                查看详情
              </a-button>
            </div>
          </div>
        </div>
        <div v-if="loading" class="loading-more">
          <a-spin size="small" />
          <span style="margin-left: 8px;">加载中...</span>
        </div>
        <div v-if="!hasMore && messageList.length > 0" class="no-more">
          <span>没有更多消息了</span>
        </div>
      </div>
      <!-- 消息详情抽屉 -->
      <!-- <my-notice-detail v-model:visible="showDetail" :data="current" @done="reload" /> -->
 
    <div v-else class="empty-container"  style="display: flex; align-items: center;justify-content: center;">
      <img src="@/assets/images/noData2.png" alt="">
    </div>
       </div>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, onBeforeMount } from 'vue';
import { message } from 'ant-design-vue';
import { LeftOutlined } from '@ant-design/icons-vue';
import { NoticeApi } from '@/api/system/notice/NoticeApi';
import MyNoticeDetail from './mynotice-detail.vue';
import { EnumApi } from '@/api/common/enum';

export default defineComponent({
  name: 'MyNotice',
  components: {
    MyNoticeDetail,
    LeftOutlined
  },
  setup() {
    const messageList = ref([]);
    // radio选项 - 消息类型
    const radioOptions = [
      { label: '全部', value: 'all' },
      { label: '任务', value: 'task' },
      { label: '通知', value: 'notice' },
      { label: '已读', value: 'read' }
    ];
    const radioType = ref('all');

    // select选项 - 消息子类型
    const selectOptions = ref([]);
    const selectType = ref("");

    // 初始化枚举数据
    const initEnumData = async () => {
      try {
        // 获取消息子类型枚举
        const statusData = await EnumApi.getEnumList({ enumName: 'NoticeSubTypeEnum' });
        selectOptions.value = statusData.data;
      } catch (error) {
        message.error('获取枚举数据失败');
      }
    };

    const onRadioTypeChange = (e) => {
      const val = e.target.value;
      radioType.value = val;
      selectType.value = "";
      loadMessages(true);
    };

    const onSelectTypeChange = (value) => {
      selectType.value = value;
      loadMessages(true);
    };
    const showDetail = ref(false);
    const current = ref(null);
    const noticeContentRef = ref(null);
    const loading = ref(false);
    const hasMore = ref(true);
    const pageNo = ref(1);
    const pageSize = ref(20);
    const total = ref(0);

    const loadMessages = async (isInitial = false) => {
      if (loading.value) return;

      try {
        loading.value = true;

        if (isInitial) {
          pageNo.value = 1;
          messageList.value = [];
        }

        let params = {
          pageNo: pageNo.value,
          pageSize: pageSize.value
        };

        // 根据radio类型设置参数
        switch (radioType.value) {
          case 'task':
            params.messageType = 'task';
            params.readFlag = 0;
            // readFlag不设置，保持为空
            break;
          case 'notice':
            params.messageType = 'notice';
            params.readFlag = 0;
            // readFlag不设置，保持为空
            break;
          case 'read':
            // messageType不设置，保持为空
            params.readFlag = 1;
            break;
          case 'all':
          default:
            // 全部：messageType和readFlag都不设置，保持为空
            break;
        }

        // 根据select分类设置参数
        if (selectType.value) {
          params.messageSubType = selectType.value; // 使用value作为参数
        }

        const result = await NoticeApi.findPage(params);

        // 更新总数和是否有更多数据
        total.value = result.totalRows || 0;
        hasMore.value = messageList.value.length < total.value;

        // 追加新消息到列表
        if (isInitial) {
          messageList.value = result.rows || [];
        } else {
          messageList.value = [...messageList.value, ...(result.rows || [])];
        }

        // 增加页码，为下次加载做准备
        pageNo.value++;
      } catch (error) {
        message.error('获取消息列表失败');
      } finally {
        loading.value = false;
      }
    };

    const handleScroll = (e) => {
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      // 当滚动到距离底部100px时，加载更多数据
      if (scrollHeight - scrollTop - clientHeight < 100 && hasMore.value && !loading.value) {
        loadMessages();
      }
    };

    const setAlready = async () => {
      try {
        const result = await NoticeApi.setAlreadyReadState();
        message.success(result.message);
        loadMessages(true); // 重新加载第一页
      } catch (error) {
        message.error('设置已读失败');
      }
    };

    const openEdit = async (record) => {
      try {
        // 检查是否是待办任务URL（包含todo路径）
        const isTodoTask = record.messageUrl.includes('/todo') || record.messageUrl.includes('?todo') || record.messageUrl.includes('&todo');

        // 如果不是待办任务，直接打开URL
        if (!isTodoTask) {
          console.log('非待办任务，直接打开URL');
          window.open(record.messageUrl, '_blank');

          // 标记消息为已读
          await NoticeApi.batchUpdateReadFlag({ messageIdList: [record.messageId] });
          return;
        }
        // 是待办任务，需要检查任务是否存在
        console.log('待办任务，检查任务是否存在');

        // 从URL中提取taskId
        const taskIdMatch = record.messageUrl.match(/taskId=([^&]+)/);
        if (!taskIdMatch || !taskIdMatch[1]) {
          console.error('无法从URL中提取taskId:', record.messageUrl);
          message.error('无法处理此待办任务，URL格式不正确');
          return;
        }

        const taskId = taskIdMatch[1];
        console.log('提取到的taskId:', taskId);

        // 检查任务是否存在
        const result = await EnumApi.checkTaskExist({ taskId: taskId });
        console.log('任务检查结果:', result);

        if (result.success) {
          // 任务存在，可以办理
          console.log('任务存在，打开待办页面');
          window.open(record.messageUrl, '_blank');
        } else {
          // 任务不存在，可能已经办理完成
          console.log('任务不存在，可能已办理完成');
          // 这里可以添加跳转到已办页面的逻辑
          // 例如：将URL中的/todo替换为/done
          const doneUrl = record.messageUrl.replace('/todo', '/done');
          window.open(doneUrl, '_blank');
        }

        // 标记消息为已读
        await NoticeApi.batchUpdateReadFlag({ messageIdList: [record.messageId] });

      } catch (error) {
        console.error('处理消息失败:', error);
        message.error('处理消息失败: ' + (error.message || '未知错误'));
      }
      //  await NoticeApi.batchUpdateReadFlag({ messageIdList: [record.messageId] });
      //整个页面重新刷新

      //location.reload();

    };

    onMounted(() => {
      initEnumData(); // 初始化枚举数据
      loadMessages(true);
      window.addEventListener('focus', () => loadMessages(true));
    });

    return {
      messageList,
      showDetail,
      current,
      noticeContentRef,
      loading,
      hasMore,
      setAlready,
      openEdit,
      handleScroll,
      reload: () => loadMessages(true),
      radioType,
      radioOptions,
      onRadioTypeChange,
      selectType,
      selectOptions,
      onSelectTypeChange
    };
  }
});
</script>

<style lang="less" scoped>
.notice-wrapper {
  height: calc(100vh - 100px);
  overflow: auto;
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
  border-radius: 6px;
  backdrop-filter: blur(398px);
  box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);
  padding: 20px;
}

.left-nav {
  font-family: HarmonyOS Sans SC;
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
  letter-spacing: normal;
  color: #1A1A1A;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 0 16px;

  .back-btn {
    font-size: 16px;
    color: #333;
    padding: 0;

    .anticon {
      margin-right: 8px;
    }
  }

  .right-nav {
    .ant-btn-link {
      color: #1890ff;
      font-size: 14px;
    }
  }
}

.notice-content {
  padding: 10px 16px;
  height: calc(100% - 80px);
  overflow-y: auto;
}


/* radiotype自定义按钮组样式 */
.custom-radio-group {
  margin-left: 15px;

  :deep(.ant-radio-button-wrapper) {
    background: transparent;
    border: none;
    color: #666;

    &-checked {
      background: #E2F0FF;
      color: #1890ff;
    }

    &::before {
      display: none;
    }
  }
}

:deep(.ant-select){
  color:#1890ff;
}

:deep(.print .ant-select-selection-placeholder){
    color: #1890ff;
}
:deep(.print){
  text-align: center;
}
.custom-radio-group .ant-radio-button-wrapper-checked {
  background: #FFFFFF !important;
  border-color: #1890ff !important;
  border-radius: 2px !important;
}

.custom-radio-group .ant-radio-button-wrapper {
  background: #E0E9F4;
}

.notice-list1 {
  .notice-item {
    margin-bottom: 10px;
    border-radius: 10px;
    border: 1px solid #FFFFFF;
    background: rgba(255, 255, 255, 1);
    box-sizing: border-box;
    backdrop-filter: blur(398px);
    //box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);
    padding: 6px 26px;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .notice-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin-bottom: 16px;

      .notice-title {
        display: flex;
        align-items: center;
        gap: 8px;
        max-width: 70%; // 限制标题宽度，留出时间显示空间

        .title-text {

          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
          /* 小标题 */
          font-family: HarmonyOS Sans SC;
          font-size: 16px;
          font-weight: 500;
          line-height: 22px;
          letter-spacing: normal;
          /* 字体/选中强调 */
          color: #303133;
        }

        .unread-badge {
          color: #ff4d4f;
          font-size: 12px;
          flex-shrink: 0; // 防止红点被压缩
        }
      }

      .notice-time {
        color: #666;
        font-size: 14px;
        flex-shrink: 0; // 防止时间被压缩
      }
    }

    .notice-body {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .notice-desc {
        color: #333;
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 50%; // 内容最多占50%宽度
      }
    }

    .notice-footer {
      margin-top: 0; // 移除上边距，因为不再需要

      .detail-btn {
        color: #1890ff;
        padding: 0;
        font-size: 14px;

        &:hover {
          color: #40a9ff;
        }
      }
    }
  }


  .notice-item2 {
    margin-bottom: 16px;
    border-radius: 10px;
    border: 1px solid #FFFFFF;
    background: rgba(245, 245, 245, 0.8);
    box-sizing: border-box;
    backdrop-filter: blur(398px);
    box-shadow: 0px 27px 28px 0px rgba(28, 163, 225, 0.1);
    padding: 6px 26px;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .notice-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .notice-title {
        display: flex;
        align-items: center;
        gap: 8px;
        max-width: 70%; // 限制标题宽度，留出时间显示空间

        .title-text {

          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
          /* 小标题 */
          font-family: HarmonyOS Sans SC;
          font-size: 16px;
          font-weight: 600;
          line-height: 22px;
          letter-spacing: normal;
          /* 字体/选中强调 */
          color: #303133;
        }

        .unread-badge {
          color: #ff4d4f;
          font-size: 12px;
          flex-shrink: 0; // 防止红点被压缩
        }
      }

      .notice-time {
        color: #666;
        font-size: 14px;
        flex-shrink: 0; // 防止时间被压缩
      }
    }

    .notice-body {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .notice-desc {
        color: #333;
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 50%; // 内容最多占50%宽度
      }
    }

    .notice-footer {
      margin-top: 0; // 移除上边距，因为不再需要

      .detail-btn {
        color: #1890ff;
        padding: 0;
        font-size: 14px;

        &:hover {
          color: #40a9ff;
        }
      }
    }
  }
}

.loading-more {
  text-align: center;
  padding: 10px 0;
  color: #999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-more {
  text-align: center;
  padding: 10px 0;
  color: #999;
}



// 响应式处理
@media screen and (max-width: 768px) {
  .notice-wrapper {
    padding: 16px;
  }

  .notice-item {
    padding: 16px !important;
  }
}

:deep(.ant-select-small) {
  .ant-select-selector {
    height: 24px !important;
    padding: 0 20px 0 8px !important;
    font-size: 12px !important;
    line-height: 22px !important;
    border: none !important;
    background: transparent !important;
  }

  .ant-select-selection-item {
    line-height: 22px !important;
  }

  .ant-select-arrow {
    font-size: 10px !important;
    color: #1890ff !important;
    right: 8px !important;
    /* 强制覆盖所有状态 */
    fill: #1890ff !important;
  }
}

/* 进一步提升优先级，确保箭头为蓝色 */
:deep(.ant-select .ant-select-arrow),
:deep(.ant-select .anticon-down) {
  color: #1890ff !important;
  fill: #1890ff !important;
}
.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100% - 80px); // 减去头部和筛选区域的高度
  min-height: 300px; // 设置最小高度，确保有足够空间显示
}
</style>




