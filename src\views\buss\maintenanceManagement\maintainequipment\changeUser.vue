<!-- 应用编辑弹窗 -->
<template>
  <div>
    <!-- 新增 -->
    <a-modal width="30%" :maskClosable="false" :visible="visible" :confirm-loading="loading" :forceRender="true"
      title="修改技术人员" :body-style="{ paddingBottom: '8px' }" @update:visible="updateVisible" centered="true">
      <div>
        <a-form :model="form" ref="formRef" :rules="rules">
          <!-- <a-form-item label="技术人员" name="maintenanceUser">
            <a-input v-model:value="form.maintenanceUser" placeholder="请输入机构名称" allow-clear />
          </a-form-item> -->
          <a-form-item label="技术人员" name="maintenanceUser">
            <a-select v-model:value="form.maintenanceUser" placeholder="请选择技术人员" style="width: 100%;">
              <a-select-option v-for="item in data.list" :key="item.label" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </div>
      <template #footer>
        <a-button key="back" @click="updateVisible(false)">取消</a-button>
        <a-button key="submit" type="primary" :loading="loading" @click="handleOk">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { useUserStore } from '@/store/modules/user';
import { maintainequipmentApi } from '@/api/maintenanceManagement/maintainequipmentApi'

export default {
  name: 'changeUser',
  components: {},
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  emits: ['done', 'update:visible'],
  data() {
    return {
      form: {
        maintenanceUser: '',
        equIds: []
      },
      equConditionOptions: [],
      rules: {
        // maintenanceUser: [{ required: true, message: '请选择技术人员', trigger: 'blur' }],
        maintenanceUser: [{ required: true, message: '请输入技术人员', trigger: 'blur' }],
      }
    };
  },
  // mounted() {
  //   this.init();
  // },
  watch: {
    data:{
    handler(newVal, oldVal) {
      console.log('form对象变化:', newVal);
      this.form = {
      maintenanceUser: '',
      equIds: []
    }
    },
    deep: true, // 深度监听对象内部属性变化
    immediate: false // 是否立即执行（初始值时）
  }
  },
  created() {
    this.form = {
      maintenanceUser: '',
      equIds: []
    }
    // this.resetForm()
  },
  methods: {
    resetForm() {
      this.$refs.formRef.clearValidate();
    },
    handleOk() {
      console.log(this.form);
      console.log('this.$refs.formRef.validate()', this.$refs.formRef.validate());
      this.$refs.formRef.validate().then(() => {
        const params = this.form
        params.equIds = this.data.equIds
        maintainequipmentApi.setMaintenanceUser(params).then(res => {
          message.success('修改成功');
          this.resetForm()
          this.form = {
            maintenanceUser: '',
            equIds: []
          }
          this.updateVisible(false);
          this.$emit('done');
        })
      })
    },
    /**
     * 更新编辑界面的弹框是否显示
     *
     * @param value true-显示，false-隐藏
     * <AUTHOR>
     * @date 2021/4/7 11:00
     */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  }
};
</script>

<style lang="less" scoped>
.search-form {

  padding: 16px 16px; // 统一内边距
  border-radius: 8px; // 增加圆角

  .search-row1 {
    display: flex;
    width: 80%;
    justify-content: space-between;
  }

  .search-row {
    display: flex;
    flex-wrap: wrap;
    gap: 24px; // 增加间距
    margin-bottom: 12px; // 增加行间距
    justify-content: space-between;

    &:last-child {
      margin-bottom: 0;
    }

    .search-item {
      display: flex;
      align-items: center;
      // min-width: 300px;
      // flex: 1;

      .label {
        min-width: 80px;
        // margin-right: 12px; // 增加标签和输入框的间距
        color: #666;
        font-size: 14px;
      }

      :deep(.ant-select),
      :deep(.ant-input) {
        width: 64%;
        height: 32px;

        .ant-select-selector {
          background: #fff; // 确保选择器背景为白色
          border-radius: 4px;
        }
      }

      :deep(.ant-input) {
        background: #fff; // 确保输入框背景为白色
        border-radius: 4px;
      }
    }

    .search-button,
    .reset-button {
      height: 32px;
      min-width: 80px;
      margin-left: auto;
      border-radius: 4px; // 统一按钮圆角
    }

    .search-button {
      background: #1890ff; // 查询按钮使用主题蓝色
    }

    .reset-button {
      background: #fff; // 重置按钮使用白色背景
      border: 1px solid #d9d9d9;
    }
  }
}

.custom-table {
  margin-top: 16px;
  min-height: 400px;

  :deep(.ant-table) {

    // 提高固定列的层级
    .ant-table-fixed-left,
    .ant-table-fixed-right {
      background: #fff;
      z-index: 3; // 增加层级
    }

    .ant-table-cell {
      white-space: nowrap !important; // 强制不换行
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      line-height: 1 !important;
      font-size: 14px !important;

      >span,
      >div {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }
    }

    // 大屏幕样式（默认）
    @media screen and (min-width: 1920px) {
      thead .ant-table-cell {
        padding: 20px 20px !important;
        height: 60px !important;
      }

      .ant-table-row {
        height: 60px !important;
      }
    }

    // 中等屏幕样式
    @media screen and (min-width: 1366px) and (max-width: 1919px) {
      thead .ant-table-cell {
        padding: 10px 20px !important;
        height: 40px !important;
      }

      .ant-table-row {
        height: 40px !important;
      }
    }

    // 小屏幕样式
    @media screen and (max-width: 1365px) {
      thead .ant-table-cell {
        padding: 4px 8px !important;
        height: 32px !important;
      }

      .ant-table-row {
        height: 32px !important;
      }
    }

    // 调整固定列单元格样式
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      z-index: 3 !important; // 增加层级
      background: #ECF4FE !important;
    }

    // 调整表头固定列样式
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right {
        z-index: 4 !important; // 确保表头在最上层
        background: #DAECFF !important;
      }
    }

    // 优化阴影效果
    .ant-table-fixed-right::before,
    .ant-table-fixed-left::before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      width: 10px;
      pointer-events: none;
      z-index: 2; // 阴影层级低于固定列
      transition: box-shadow .3s;
    }

    .ant-table-fixed-left::before {
      right: 0;
      box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
    }

    .ant-table-fixed-right::before {
      left: 0;
      box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
    }

    // 设置表格内容的层级
    .ant-table-content {
      z-index: 1;
    }

    // 确保滚动区域正确显示
    .ant-table-body {
      overflow-x: auto !important;
      overflow-y: auto !important;
    }

    // 固定列不换行
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .ant-table-row {
      height: 24px !important;
    }

    // 表头固定列不换行
    .ant-table-thead {

      th.ant-table-cell-fix-left,
      th.ant-table-cell-fix-right {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.table-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}

:deep(.ant-table-tbody .ant-table-cell) {
  padding: 10px 16px !important;
}

.custom-table {
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #FFFFFF !important;
  }
}
</style>
