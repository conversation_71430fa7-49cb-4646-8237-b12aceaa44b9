/*
 * @Author: nxy
 * @Date: 2022-10-26 11:16:44
 */
/**
 * 登录用户 store
 */
import { defineStore } from 'pinia';
import { formatMenus, formatTreeData } from 'ele-admin-pro';
import { getUserInfo } from '@/api/layout';
import { SESSION_KEY_MENU_TYPE } from '@/config/setting';
import { useSystemStore } from '@/store/modules/system';

export const useUserStore = defineStore({
  id: 'user',
  state: () => ({
    // 当前登录用户的信息
    info: null,
    // 当前登录用户的菜单
    menus: null,
    // 当前登录用户的权限
    authorities: [],
    // 当前登录用户的角色
    roles: []
  }),
  getters: {},


  actions: {
    /**
     * 请求用户信息、权限、角色、菜单
     */
    async fetchUserInfo() {
      // 获取加载的菜单类型，默认加载前台菜单
      let antdvMenuFrontType = 2;
      let menuType = localStorage.getItem('menuType');
      if (menuType) {
        menuType = parseInt(menuType);
        if (menuType == 1) {
          antdvMenuFrontType = 1;
        } else if (menuType == 2) {
          antdvMenuFrontType = 2;
        } else if (menuType == 3) {
          let sessionFrontType = sessionStorage.getItem(SESSION_KEY_MENU_TYPE);
          if (sessionFrontType) {
            antdvMenuFrontType = parseInt(sessionFrontType);
          }
        }
      }

      // 设置当前store值为session中的值
      let systemStore = useSystemStore();
      systemStore.setMenuFrontType(antdvMenuFrontType);

      // 调用获取用户信息接口
      const result = await getUserInfo(antdvMenuFrontType).catch(() => undefined);
      if (!result) {
        return {};
      }
      // 用户信息
      this.info = result;
      // 用户权限编码集合
      this.authorities = result.authCodes ?? [];
      // 用户角色
      this.roles = result.roles?.map(d => d.roleCode) ?? [];
      // 用户菜单, 过滤掉按钮类型并转为children形式
    // const { menus, homePath } = formatMenus(result.authorities ?? []);
    //为了修改页面缓存逻辑。可以在后台配置是否缓存。修改此方法
      const { menus, homePath } = formatMenus1(result.authorities ?? []);
      console.log("menus", menus)
      console.log("homePath", homePath)

      //如果登录账户是admin
      if (result.roles[0].roleCode === 'superAdmin') {
        this.menus = menus;
        console.log('menus44444', this.menus);
        return { menus, homePath };
      } else {
        this.menus = menus[0].children;
        console.log('menus44444', this.menus);
        this.menus = this.menus.filter(menu => menu.children && menu.children.length > 0);
        return { menus, homePath };
      }

    },



    /**
     * 更新用户信息
     */
    setInfo(value) {
      this.info = value;
    },
    /**
     * 更新菜单的 badge
     */
    setMenuBadge(path, value, color) {
      this.menus = formatTreeData(this.menus, m => {
        if (path === m.path) {
          return Object.assign({}, m, {
            meta: Object.assign({}, m.meta, {
              badge: value,
              badgeColor: color
            })
          });
        }
        return m;
      });
    }
  }
});

/**
 * 重写菜单格式化方法，保留后台meta配置
 */
const formatMenus1 = (authorities) => {
  if (!authorities || !Array.isArray(authorities)) {
    return { menus: [], homePath: undefined, homeTitle: undefined };
  }

  let homePath = undefined;
  let homeTitle = undefined;

  // 递归处理菜单数据，确保符合MenuItem接口
  const processMenus = (menuList) => {
    return menuList
      .filter(item => item.menuType !== 2) // 过滤掉按钮类型
      .map(item => {
        // 构建符合MenuItem接口的菜单项
        const menuItem = {
          path: item.path,
          name: item.name,
          component:(item.component+"").substring(1,(item.component+"").length),
          meta: {
            title: item.title,
            icon: item.icon,
            hide: item.hide,
            active: item.active,
            keepAlive: item.keepAlive, // 原有的keepAlive
            isAlive: item.isAlive, // 新增的isAlive
            componentName: item.componentName,
            // 其他MenuMeta属性
            
            routePath:item.path,
            color: item.color,
            hideFooter: item.hideFooter,
            hideSidebar: item.hideSidebar,
            closable: item.closable,
            tabUnique: item.tabUnique,
            breadcrumb: item.breadcrumb,
            fullPath: item.fullPath,
            parentPath: item.parentPath,
            iframe: item.iframe,
            badge: item.badge,
            badgeColor: item.badgeColor
          }
        };

        // 设置首页路径和标题
        if (item.isHome === 1 && !homePath) {
          homePath = item.path;
          homeTitle = item.menuName;
        }

        // 递归处理子菜单
        if (item.children && item.children.length > 0) {
          const childMenus = processMenus(item.children);
          if (childMenus.length > 0) {
            menuItem.children = childMenus;
          }
        }

        return menuItem;
      });
  };

  const menus = processMenus(authorities);

  
  // 修改homePath逻辑：取第一个应用的第一个子菜单的地址
  let defaultHomePath = undefined;
  if (menus.length > 0) {
    const firstApp = menus[0];
    if (firstApp.children && firstApp.children.length > 0) {
      if (firstApp.children[0].children && firstApp.children[0].children.length > 0) {
        defaultHomePath = firstApp.children[0].children[0].path;
      } else {
        defaultHomePath = firstApp.children[0].path;
      }

    } else {
      defaultHomePath = firstApp.path;
    }
  }

  return {
    menus,
    homePath: homePath || defaultHomePath,
    homeTitle: homeTitle || (menus.length > 0 ? menus[0]?.meta?.title : undefined)
  };
};


