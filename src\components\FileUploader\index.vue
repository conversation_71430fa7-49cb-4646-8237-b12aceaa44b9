<template>
  <div class="file-uploader">
    <!-- 上传区域 -->
    <div class="upload-section">
      <div :class="showUploadButton ? 'upload-header' : ''">
        <!-- <div class="row-layout"> -->

        <div :class="{ 'row-layout': tipRow, 'column-layout': !tipRow }">
          <div>
            <a-upload v-if="showUploadButton" v-model:file-list="fileList" :multiple="allowMultiple" :action="uploadUrl"
              :headers="headers" :showUploadList="false" :before-upload="beforeUpload" :max-count="maxCount"
              :accept="acceptString" :customRequest="customRequest" @change="handleChange">
              <div class="upload-button" :class="{ 'disabled': fileList.length >= maxCount || uploading }">
                <upload-outlined />
                <span>{{ buttonText }}</span>
              </div>
            </a-upload>
          </div>
          <div>
            <span class="upload-tip" v-if="showUploadButton">{{ tipText }}</span>
            <!-- <span class="upload-tip" v-if="showUploadButton">最多10个文件，每个不超过5MB，支持图片、视频、文档等常见文件格式（可执行文件、压缩文件除外）</span> -->
          </div>
        </div>
      </div>
      <!-- 文件列表 -->
      <div>
        <!-- <div :class="isSingleFile ? '' : 'file-grid'" :style="{ borderTop: fileViewList.length > 0 ? '1px solid rgba(30, 41, 64, 0.08)':''}">
          <div v-for="file in fileViewList" :key="file.uid" class="file-item">
            <div class="file-content">
              <div class="file-icon">
    
                <img src="/public/icon/icon-wrapper.svg"
                  v-if="file.fileName.endsWith('.xls') || file.fileName.endsWith('.xlsx')" />
                <img src="/public/icon/word.svg"
                  v-else-if="file.fileName.endsWith('.doc') || file.fileName.endsWith('.docx')" />
                <img src="/public/icon/pdf.svg" v-else-if="file.fileName.endsWith('.pdf')" />
                <img src="/public/icon/mp4.svg"
                  v-else-if="file.fileName.endsWith('.mov') || file.fileName.endsWith('.mp4')" />
                <img src="/public/icon/ppt.svg"
                  v-else-if="file.fileName.endsWith('.ppt') || file.fileName.endsWith('.pptx')" />
                <img src="/public/icon/jpg.svg"
                  v-else-if="file.fileName.endsWith('.png') || file.fileName.endsWith('.jpg') || file.fileName.endsWith('.jpeg') || file.fileName.endsWith('.heif')" />
                <img src="/public/icon/reader.svg" v-else />
              </div>
              <a :href="`${file.fileUrl}?filename=${file.fileName}`" target="_blank" :title="file.fileName">
                {{ formatFileName(file.fileName) }}
              </a>
              <div class="file-actions">
                <div class="progress-wrapper" v-if="file.percent !== 100">
                  <a-progress :percent="file.percent || 0" size="small" :show-info="false" />
                  <span class="progress-text">{{ file.percent || 0 }}%</span>
                </div>
              </div>
              <img src="/public/icon/delete1.svg" class="delete-icon" @click="handleRemove(file)"
                  v-if="showDel || file.percent === 100" />
            </div>
          </div>
          <div class="file-item" v-if="fileViewList.length % 2 !== 0"></div>
        </div> -->

        <div :class="['file-container', { 'single-file': isSingleFile, 'scrollable': fileViewList.length > 10 }]"
          :style="{ borderTop: fileViewList.length > 0 ? '1px solid rgba(30, 41, 64, 0.08)' : '' }">

          <div :class="isSingleFile ? '' : 'file-grid'">
            <div v-for="(file, index) in fileViewList" :key="file.uid" class="file-item-wrapper">
              <div class="file-item">
                <div class="file-content">
                  <div class="file-icon">
                    <img src="/public/icon/icon-wrapper.svg"
                      v-if="file.fileName.endsWith('.xls') || file.fileName.endsWith('.xlsx')" />
                    <img src="/public/icon/word.svg"
                      v-else-if="file.fileName.endsWith('.doc') || file.fileName.endsWith('.docx')" />
                    <img src="/public/icon/pdf.svg" v-else-if="file.fileName.endsWith('.pdf')" />
                    <img src="/public/icon/mp4.svg"
                      v-else-if="file.fileName.endsWith('.mov') || file.fileName.endsWith('.mp4')" />
                    <img src="/public/icon/ppt.svg"
                      v-else-if="file.fileName.endsWith('.ppt') || file.fileName.endsWith('.pptx')" />
                    <img src="/public/icon/jpg.svg"
                      v-else-if="file.fileName.endsWith('.png') || file.fileName.endsWith('.jpg') || file.fileName.endsWith('.jpeg') || file.fileName.endsWith('.heif')" />
                    <img src="/public/icon/reader.svg" v-else />
                  </div>

                  <div class="file-info">
                    <a :href="`${file.fileUrl}?filename=${file.fileName}`" target="_blank" :title="file.fileName"
                      class="file-link">
                      {{ formatFileName(file.fileName) }}
                    </a>
                    <div class="file-actions">
                      <div class="progress-wrapper" v-if="file.percent !== 100">
                        <a-progress :percent="file.percent || 0" size="small" :show-info="false" />
                        <span class="progress-text">{{ file.percent || 0 }}%</span>
                      </div>
                    </div>
                  </div>

                  <img src="/public/icon/delete1.svg" class="delete-icon" @click="handleRemove(file)"
                    v-if="showUploadButton" />
                </div>
              </div>

              <!-- 分隔线：只在奇数索引（每行第一个文件）后面显示 -->
              <div class="file-divider" v-if="index % 2 === 0 && index < fileViewList.length - 1"></div>
            </div>

            <!-- 占位元素，保持网格布局 -->
            <div class="file-item-wrapper" v-if="fileViewList.length % 2 !== 0">
              <div class="file-item placeholder"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { getToken } from '@/utils/token-util';
import { API_BASE_PREFIX } from '@/config/setting';
import { FileUploadUrl } from '@/api/system/operation/FileApi';
import axios from 'axios';
import {
  UploadOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  FilePdfOutlined,
  FileImageOutlined,
  VideoCameraOutlined,
  FileOutlined,
  CloseOutlined
} from '@ant-design/icons-vue';

const props = defineProps({
  // 上传URL，默认使用系统文件上传接口
  action: {
    type: String,
    default: `${API_BASE_PREFIX}${FileUploadUrl}?secretFlag=0`
  },
  // 允许的文件类型，如 ['.jpg', '.png', '.pdf']
  acceptTypes: {
    type: Array,
    default: () => []
  },
  // 最大文件大小（MB）
  maxSize: {
    type: Number,
    default: 5
  },
  // 最大上传数量
  maxCount: {
    type: Number,
    default: 10000000
  },
  // 是否允许多文件上传
  allowMultiple: {
    type: Boolean,
    default: true
  },
  // 上传按钮文本
  buttonText: {
    type: String,
    default: '点击上传'
  },
  isSingleFile: {
    type: Boolean,
    default: false
  },
  // 提示文本
  tipText: {
    type: String,
    default: ''
  },
  // 提示文本和上传按钮是否在同一行显示
  tipRow: {
    type: Boolean,
    default: true
  },
  showDel: {
    type: Boolean,
    default: true
  },
  // 初始文件列表 - 用于回显
  initialFiles: {
    type: Array,
    default: () => []
  },
  // 是否显示上传按钮
  showUploadButton: {
    type: Boolean,
    default: true
  }
});
const maxSize = ref(50)
console.log('tipRow', props.tipRow);
const emit = defineEmits(['update:files', 'file-uploaded', 'file-removed', 'upload-status-change']);

// 上传URL
const uploadUrl = computed(() => props.action);

// 计算提示文本
const tipText = computed(() => {
  if (props.tipText) return props.tipText;

  // const typeText = props.acceptTypes.length > 0
  //   ? `支持${props.acceptTypes.join('、')}格式`
  //   : '支持图片、视频、文档等常见文件格式(zip、rar等压缩文件除外)';
  const typeText = '最大1GB/个，支持图片、视频、文档等常见文件格式(zip、rar等压缩文件除外)';

  // return `上限${props.maxCount}个文件，最大${maxSize.value} MB/个，${typeText}`;
  return typeText;
});

// 计算accept属性字符串
const acceptString = computed(() => {
  if (props.acceptTypes && props.acceptTypes.length > 0) {
    return props.acceptTypes.join(',');
  }
  return '';
});
//const acceptString = ref('.jpg,.jpeg,.png,.heif,.pdf,.xls,.xlsx,.doc,.docx,.ppt,.pptx,.mp4,.mov');

// 上传相关状态
const fileList = ref([]);
const fileViewList = ref([]);
const uploading = ref(false);
const isInitializing = ref(false); // 新增：标记是否正在初始化

// 初始化文件列表 - 处理回显数据
onMounted(() => {
  initializeFiles();
});

// 修改watch，添加条件判断，避免循环更新
watch(() => props.initialFiles, (newFiles, oldFiles) => {
  // 只有当初始文件列表真正变化时才重新初始化
  if (!isInitializing.value && JSON.stringify(newFiles) !== JSON.stringify(oldFiles)) {
    initializeFiles();
  }
}, { deep: true });

// 初始化文件列表方法
const initializeFiles = () => {
  isInitializing.value = true; // 标记开始初始化

  if (props.initialFiles && props.initialFiles.length > 0) {
    // 转换初始文件列表格式以适应组件内部格式
    const initialFilesList = props.initialFiles.map(file => ({
      fileId: file.fileId || '',
      fileUrl: file.fileUrl || '',
      fileName: file.fileName || file.fileOriginName || '',
      uid: file.uid || file.fileId || Date.now().toString(),
      status: 'done',
      percent: 100 // 确保初始文件显示为100%
    }));

    // 更新文件视图列表 - 使用新数组而不是修改原数组
    fileViewList.value = [...initialFilesList];

    // 同步到a-upload的fileList
    fileList.value = initialFilesList.map(file => ({
      uid: file.uid,
      name: file.fileName,
      status: 'done',
      url: file.fileUrl,
      response: {
        data: {
          fileId: file.fileId,
          fileUrl: file.fileUrl
        }
      }
    }));
  } else {
    fileViewList.value = [];
    fileList.value = [];
  }

  // 向父组件发送初始化后的文件列表 - 但不触发更新
  // 注释掉这行，避免初始化时向父组件发送更新
  // emit('update:files', [...fileViewList.value]);

  isInitializing.value = false; // 标记初始化完成
};

// 上传请求头
const headers = {
  Authorization: getToken()
};

// 格式化文件名，如果太长则用省略号替代中间部分，但保留扩展名
const formatFileName = (fileName) => {
  if (!fileName) return '';

  const maxLength = 30; // 最大显示长度

  if (fileName.length <= maxLength) {
    return fileName;
  }

  // 获取文件扩展名
  const lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex === -1) {
    // 没有扩展名
    return fileName.substring(0, maxLength - 3) + '...';
  }

  const extension = fileName.substring(lastDotIndex);
  const nameWithoutExt = fileName.substring(0, lastDotIndex);

  // 计算名称部分应该保留的长度
  const remainingLength = maxLength - extension.length - 3; // 3是省略号的长度

  if (remainingLength <= 0) {
    // 如果扩展名太长，只显示部分扩展名
    return fileName.substring(0, 5) + '...' + extension.substring(extension.length - 5);
  }

  // 保留文件名开头部分 + ... + 扩展名
  return nameWithoutExt.substring(0, remainingLength) + '...' + extension;
};

// 文件类型判断函数
//const isExcel = (fileName) => /\.(xlsx|xls)$/i.test(fileName);
//const isWord = (fileName) => /\.(doc|docx)$/i.test(fileName);
//const isPdf = (fileName) => /\.pdf$/i.test(fileName);
//const isImage = (fileName) => /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(fileName);
//const isVideo = (fileName) => /\.(mp4|avi|mov|wmv|flv|mkv)$/i.test(fileName);

// 上传前验证
const beforeUpload = (file, fileList) => {
  // 验证文件类型
  if (props.acceptTypes.length > 0) {
    const fileName = file.name.toLowerCase();
    const isValidType = props.acceptTypes.some(type =>
      fileName.endsWith(type.toLowerCase())
    );

    if (!isValidType) {
      message.error(`请上传${props.acceptTypes.join('、')}格式的文件`);
      return false;
    }
  }

  // 验证文件大小
  const maxSizeBytes = maxSize.value * 1024 * 1024;
  if (file.size > maxSizeBytes) {
    message.error(`文件大小不能超过${maxSize.value}MB`);
    return false;
  }

  // 验证文件数量 - 考虑当前已有文件和新选择的所有文件
  const totalFilesCount = fileViewList.value.length + fileList.length;
  // if (totalFilesCount > props.maxCount) {
  //   message.error(`最多只能上传${props.maxCount}个文件`);
  //   return false;
  // }

  // 验证文件是否重复 - 根据文件名检查
  const isDuplicate = fileViewList.value.some(existingFile =>
    existingFile.fileName === file.name
  );

  if (isDuplicate) {
    message.error(`文件 ${file.name} 已存在`);
    return false;
  }

  return true;
};

// 处理文件上传状态变化
const handleChange = (info) => {
  console.log("调用了handleChange", info.file.name, info.file.status);

  // 获取当前变化的文件
  const currentFile = info.file;

  // 如果文件状态是done，我们完全跳过处理
  if (currentFile.status === 'done') {
    console.log(`文件 ${currentFile.name} 状态为done，跳过处理`);
    return;
  }

  // 保存原始fileList - 这只是为了与a-upload组件保持同步
  // 但我们不直接使用info.fileList，而是手动更新，避免影响其他文件
  const currentIndex = fileList.value.findIndex(item => item.uid === currentFile.uid);
  if (currentIndex >= 0) {
    const newFileList = [...fileList.value];
    newFileList[currentIndex] = currentFile;
    fileList.value = newFileList;
  } else {
    fileList.value = [...fileList.value, currentFile];
  }

  // 处理上传中状态 - 检查是否有任何文件正在上传
  uploading.value = fileList.value.some(file => file.status === 'uploading');

  // 向父组件发送上传状态
  emit('upload-status-change', uploading.value);

  // 处理单个文件的状态提示
  if (currentFile.status !== 'uploading') {
    console.log("文件状态变化:", currentFile.name, currentFile.status);
  }

  // 检查文件是否已存在于fileViewList中
  const existingIndex = fileViewList.value.findIndex(item => item.uid === currentFile.uid);

  // 当文件开始上传时，添加到fileViewList - 不影响其他文件
  if (currentFile.status === 'uploading') {
    if (existingIndex >= 0) {
      // 更新已存在文件的进度 - 使用不可变更新方式
      const newFileViewList = [...fileViewList.value];
      newFileViewList[existingIndex] = {
        ...newFileViewList[existingIndex],
        percent: currentFile.percent || 0
      };
      fileViewList.value = newFileViewList;
    } else {
      // 添加新文件到fileViewList - 使用不可变更新方式
      fileViewList.value = [...fileViewList.value, {
        fileId: '',
        fileUrl: '',
        fileName: currentFile.name,
        uid: currentFile.uid,
        status: 'uploading',
        percent: currentFile.percent || 0
      }];
    }
  } else if (currentFile.status === 'error') {
    // 文件上传失败
    message.error(`上传出错：${currentFile.error?.message || '未知错误'}`);

    // 更新fileViewList中的文件状态 - 只更新当前文件，不影响其他文件
    if (existingIndex >= 0) {
      // 使用不可变更新方式
      const newFileViewList = [...fileViewList.value];
      newFileViewList[existingIndex] = {
        ...newFileViewList[existingIndex],
        status: 'error'
      };
      fileViewList.value = newFileViewList;
    }
  }

  // 向父组件发送更新 - 包括上传中的文件
  // 创建一个全新的数组，避免引用问题
  emit('update:files', JSON.parse(JSON.stringify(fileViewList.value)));

  console.log('当前fileViewList:', fileViewList.value);
};

// 自定义上传方法
const customRequest = async (options) => {
  const { file, onProgress, onSuccess, onError } = options;

  // 检查文件是否已存在于fileViewList中
  const existingIndex = fileViewList.value.findIndex(item => item.uid === file.uid);

  // 准备文件信息对象
  const fileInfo = {
    fileId: '',
    fileUrl: '',
    fileName: file.name,
    uid: file.uid,
    status: 'uploading',
    percent: 0
  };

  // 添加或更新fileViewList - 不影响其他文件
  if (existingIndex >= 0) {
    // 更新已存在的文件 - 使用不可变更新方式
    const newFileViewList = [...fileViewList.value];
    newFileViewList[existingIndex] = {
      ...newFileViewList[existingIndex],
      status: 'uploading',
      percent: 0
    };
    fileViewList.value = newFileViewList;
  } else {
    // 添加新文件 - 使用不可变更新方式
    fileViewList.value = [...fileViewList.value, fileInfo];
  }

  // 向父组件发送更新 - 创建一个全新的数组，避免引用问题
  emit('update:files', JSON.parse(JSON.stringify(fileViewList.value)));

  // 开始上传时设置状态
  uploading.value = true;
  emit('upload-status-change', true);

  try {
    // 创建FormData对象
    const formData = new FormData();
    formData.append('file', file);

    // 发送请求
    const response = await axios.post(uploadUrl.value, formData, {
      headers: {
        ...headers,
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        // 计算上传进度
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);

        // 更新进度
        onProgress({ percent });

        // 更新fileViewList中的进度 - 只更新当前文件的进度，不影响其他文件
        const index = fileViewList.value.findIndex(item => item.uid === file.uid);
        if (index >= 0) {
          // 使用不可变更新方式
          const newFileViewList = [...fileViewList.value];
          newFileViewList[index] = {
            ...newFileViewList[index],
            percent
          };
          fileViewList.value = newFileViewList;

          // 向父组件发送更新 - 创建一个全新的数组，避免引用问题
          emit('update:files', JSON.parse(JSON.stringify(fileViewList.value)));
        }
      }
    });

    // 上传成功后，更新当前文件的信息
    const updatedFileInfo = {
      fileId: response.data.data.fileId,
      fileUrl: response.data.data.fileUrl,
      fileName: file.name,
      uid: file.uid,
      status: 'done',  // 修改为done状态
      percent: 100
    };

    // 更新fileViewList中的文件状态 - 只更新当前文件，不影响其他文件
    const updatedIndex = fileViewList.value.findIndex(item => item.uid === file.uid);
    if (updatedIndex >= 0) {
      // 使用不可变更新方式
      const newFileViewList = [...fileViewList.value];
      newFileViewList[updatedIndex] = updatedFileInfo;
      fileViewList.value = newFileViewList;
    }

    // 触发文件上传完成事件
    emit('file-uploaded', updatedFileInfo);

    // 向父组件发送更新 - 创建一个全新的数组，避免引用问题
    emit('update:files', JSON.parse(JSON.stringify(fileViewList.value)));

    // 调用onSuccess，但不传递response，避免触发handleChange
    onSuccess(response.data);

    message.success(`${file.name} 上传成功`);

    // 上传成功后
    // 检查是否还有其他文件在上传
    uploading.value = fileList.value.some(file => file.status === 'uploading');
    emit('upload-status-change', uploading.value);
  } catch (error) {
    // 上传失败
    onError(error);
    console.log(error)
    // 更新fileViewList中的文件状态 - 只更新当前文件，不影响其他文件
    const errorIndex = fileViewList.value.findIndex(item => item.uid === file.uid);
    if (errorIndex >= 0) {
      // 使用不可变更新方式
      const newFileViewList = [...fileViewList.value];
      newFileViewList[errorIndex] = {
        ...newFileViewList[errorIndex],
        status: 'error'
      };
      fileViewList.value = newFileViewList;
    }

    // 向父组件发送更新 - 创建一个全新的数组，避免引用问题
    emit('update:files', JSON.parse(JSON.stringify(fileViewList.value)));

    message.error(`上传出错：${error.message || '未知错误'}`);

    // 检查是否还有其他文件在上传
    uploading.value = fileList.value.some(file => file.status === 'uploading');
    emit('upload-status-change', uploading.value);
  }
};

// 删除文件
const handleRemove = (file) => {
  console.log('删除文件:', file.uid);

  // 从视图列表中删除 - 使用不可变更新方式
  const newFileViewList = fileViewList.value.filter(item => item.uid !== file.uid);
  fileViewList.value = newFileViewList;
  console.log('删除后的fileViewList:', fileViewList.value);

  // 从fileList中删除 - 使用不可变更新方式
  const newFileList = fileList.value.filter(item => item.uid !== file.uid);
  fileList.value = newFileList;

  // 向父组件发送更新 - 创建一个全新的数组，避免引用问题
  emit('update:files', JSON.parse(JSON.stringify(newFileViewList)));

  // 触发文件删除事件
  emit('file-removed', file);

  // 防止事件冒泡
  event?.stopPropagation();
};

</script>

<style scoped>
.file-uploader {
  width: 100%;
}

.file-link {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
  /* 根据您的布局调整 */
  display: inline-block;
}

.upload-section {
  /* border: 1px dashed #d9d9d9; */
  border-radius: 4px;
  /* padding: 16px; */
  /* background-color: #fafafa; */
}


.row-layout {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;
}

.column-layout {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.upload-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.upload-button {
  display: inline-flex;
  align-items: center;
  padding: .25rem .9375rem;
  background: #0085FF;
  border-radius: .125rem;
  color: #fff;
  cursor: pointer;
  font-size: .875rem;
}

.upload-button:hover {
  border-color: #fff;
  color: #fff;
}

.upload-tip {
  margin-left: 16px;
  color: #888;
  font-size: 12px;
}

.file-grid {
  /* background-color: rgba(30, 41, 64, 0.08); */
  background-color: #FBFDFF;
  display: flex;
  flex-wrap: wrap;
  /* border-top: 1px solid #1e294014; */
  border-right: 1px solid rgba(30, 41, 64, 0.08);
  border-left: 1px solid rgba(30, 41, 64, 0.08);
  /* 允许换行 */
}


.file-item {
  /* background-color: #FBFDFF; */
  height: 32px;
  width: 50%;
  /* box-sizing: border-box; */
  margin-bottom: 1px;
  /* margin-right: 1px; */
  /* 确保padding和border不影响宽度计算 */
  /* border-radius: 4px; */
  /* padding: 4px; */
  /* background-color: white; */
}

.file-content {
  height: 100%;
  display: flex;
  align-items: center;
}

.file-icon {
  font-size: 20px;
  margin: 0 8px;
  display: flex;
  align-items: center;
  color: #1890ff;
}

.file-link {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #333;
}

.file-actions {
  margin-left: 15px;
  display: flex;
  align-items: center;
  height: 100%;
}

.delete-icon {
  color: #999;
  cursor: pointer;
  transition: all 0.3s;
  width: 15px;
}

.delete-icon:hover {
  color: #ff4d4f;
}

.progress-wrapper {
  width: 60px;
  display: flex;
  align-items: center;
}

.progress-text {
  margin-left: 4px;
  font-size: 12px;
  color: #1890ff;
}


.file-container.scrollable {
  max-height: 200px; /* 5行的高度，每行约40px */
  overflow-y: auto;
  border-right: 1px solid rgba(30, 41, 64, 0.08);
  border-left: 1px solid rgba(30, 41, 64, 0.08);
}

/* 滚动条样式 */
.file-container.scrollable::-webkit-scrollbar {
  width: 6px;
}

.file-container.scrollable::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.file-container.scrollable::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.file-container.scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.file-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0;
  /* margin-top: 16px; */
}

.file-item-wrapper {
  display: flex;
  align-items: center;
  width: 50%;
  position: relative;
}

.file-item {
  flex: 1;
  padding: 8px 12px;
  min-height: 40px; /* 确保每行有固定高度 */
}

.file-item.placeholder {
  visibility: hidden;
}

.file-content {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;
  height: 100%;
}

.file-icon {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
}

.file-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.file-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-link {

  text-decoration: none;
  font-size: 14px;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-link:hover {
  text-decoration: underline;
}

.file-actions {
  display: flex;
  align-items: center;
}

.progress-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.progress-text {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.delete-icon {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  cursor: pointer;
  margin-left: auto;
}

.delete-icon:hover {
  opacity: 0.7;
}

/* 分隔线样式 */
.file-divider {
  width: 1px;
  height: 24px; /* 调整分隔线高度 */
  background-color: rgba(30, 41, 64, 0.08);
  flex-shrink: 0;
}

/* 单文件模式样式 */
.file-container.single-file .file-divider {
  display: none;
}
</style>
