<template>
  <div class="todo-container">
    <!-- 全局加载状态 -->
    <div v-if="globalLoading" class="global-loading">
      <div class="loading-mask"></div>
      <div class="loading-content">
        <span>正在加载必要组件，请耐心等待...</span>
        <a-progress :percent="loadingProgress" status="active" :stroke-width="16" class="big-progress" />
      </div>
    </div>

    <div v-if="loading" class="loading-state">
      <a-spin tip="加载中..."></a-spin>
    </div>
    <div v-else-if="error" class="error-state">
      <a-alert type="error" :message="error" />
    </div>

    <div v-else-if="!comp" class="error-state">
      <a-alert type="warning" message="找不到对应组件" />
    </div>
    <!-- 左侧组件区域 -->
    <div v-if="!loading && comp && !error" class="left-component">
      <component :is="comp" :value="componentProps" :jsonData="jsonData" :taskId="taskId" :procInsId="procInsId"
        :processDefinitionId="processDefinitionId" :actId="actId" :formData="formData" ref="process_f"
        @mounted="handleComponentMounted">
      </component>
    </div>

    <!-- 右侧审批记录区域 -->
    <div v-if="!loading && comp && !error" style="padding: 20px 10px 6px 0px;">
      <div class="right-panel" v-show="showRightPanel">
        <div class="right-panel-header">
          <div class="panel-title">
            <img src="@/assets/equipment/yinzhang.png" alt="" class="icon" />
            审批记录
          </div>
        </div>
        <div class="approval-record">
          <div class="timeline">
            <div v-for="(record, index) in approvalRecords" :key="'record-' + index + '-' + updateTrigger"
              class="timeline-item">

              <div :class="record.status === 10055 ? 'avatar1' : 'avatar'">
                <span class="avatar-text">{{ record.userInitial }}</span>
                <img v-if="record.status == 1002 || record.status == 1004" src="@/assets/equipment/shenpisuccess.png"
                  class="status-icon" alt="已通过" />
                <img v-if="record.status === 1003" src="@/assets/equipment/icon-check.png" class="status-icon"
                  alt="已通过" />
                <img v-if="record.status === 10055" src="@/assets/equipment/weidao.png" class="status-icon" alt="已通过" />
              </div>
              <div class="content">
                <div class="header">
                  <div class="title">
                    <span class="name" :style="record.status === 10055 ? 'color:#ACB4C9' : ''">{{
                      record.assigneeName.split(".")[0] }}</span>
                    <span class="role" v-if="record.assigneeName"
                      :style="record.status === 10055 ? 'color:#ACB4C9' : ''">({{
                        record.userName }})</span>
                  </div>
                  <span class="time" :style="record.status === 10055 ? 'color:#ACB4C9' : ''">
                    {{ (record.time || record.endTime).split(" ")[0] }}</span>
                </div>

                <div class="status-text" :class="record.status" :style="record.status === 10055 ? 'color:#ACB4C9' : ''">
                  {{record.statusText }}
                </div>
                <!-- <a-tooltip v-if="record.comment.length > 3" :title="record.comment" placement="topRight" :overlayStyle="{
                  maxHeight: '200px',
                  overflowY: 'auto',   // 垂直滚动
                  overflowX: 'hidden', // 隐藏水平滚动
                  borderRadius: '4px'
                }">
                  <div class="comment" v-if="record.comment && index !== 0">
                    <textarea style="height:70px;width: 100%;border: none;background-color: transparent;"
                      v-model="record.comment" readonly="readonly"></textarea>

                  </div>
                </a-tooltip>
                <div v-else>
                  <div class="comment" v-if="record.comment && index !== 0">
                    <textarea style="height:70px;width: 100%;border: none;background-color: transparent;"
                      v-model="record.comment" readonly="readonly"></textarea>

                  </div>
                </div> -->

                <div class="comment" v-if="record.comment && index !== 0">
  <div class="comment-content">
    <!-- 显示截断或完整内容 -->
    <div
      class="comment-text"
      :class="{ 'expanded': record.expanded, 'collapsed': !record.expanded && needsTruncation(record.comment) }"
      v-html="formatCommentText(record.comment)"
    ></div>

    <!-- 展开/收起按钮 -->
    <a-button
      v-if="needsTruncation(record.comment)"
      type="link"
      size="small"
      class="expand-btn"
      @click="toggleComment(record, index)"
    >
      {{ record.expanded ? '收起' : '展开' }}
    </a-button>
  </div>
</div>
              </div>
              <div class="timeline-line" v-if="index !== approvalRecords.length - 1"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 展开按钮 -->
    <div class="expand-button-container" v-if="!showRightPanel">
      <button class="expand-button" @click="showRightPanel = true">
        <HistoryOutlined />
        <span>审批记录</span>
      </button>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, nextTick, markRaw } from 'vue'
import { RegisteredEquipmentApi } from '@/api/workflow/RegisteredEquipmentApi'
import ApprovalRecord from '../components/ApprovalRecord.vue'
import { CloseOutlined, HistoryOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

const globalLoading1 = ref(false)
const setGlobalLoading1 = (val) => {
  globalLoading1.value = val
}

// 使用异步组件导入，避免直接引入可能导致的问题
const loadComponents = async () => {
  try {
    // 使用动态导入
    const modules = import.meta.glob('@/views/buss/taskApproval/*.vue')
    const resultComps = {}

    // 创建所有组件加载的Promise数组
    const loadPromises = Object.entries(modules).map(async ([path, importFn]) => {
      try {
        const module = await importFn()
        const fileName = path.split('/').pop().replace('.vue', '')
        if (module.default) {
          // 使用 markRaw 防止组件被转换为响应式对象
          resultComps[fileName] = markRaw(module.default)
          console.log(`成功注册组件: ${fileName}`)
        }
      } catch (err) {
        console.error(`加载组件失败 - ${path}:`, err)
      }
    })

    // 等待所有组件加载完成
    await Promise.all(loadPromises)
    console.log('所有组件加载完成:', Object.keys(resultComps))
    return resultComps
  } catch (error) {
    console.error('加载组件出错:', error)
    return {}
  }
}

// 修改为按需加载单个组件
// 修改为按需加载单个组件
const loadSingleComponent = async (componentName) => {
  try {
    // 使用Vite的import.meta.glob动态导入
    const modules = import.meta.glob('@/views/buss/taskApproval/*.vue');
    const targetPath = Object.keys(modules).find(path =>
      path.endsWith(`/${componentName}.vue`)
    );

    if (!targetPath) {
      console.error(`找不到组件: ${componentName}`);
      return null;
    }

    // 导入找到的组件
    const module = await modules[targetPath]();

    if (module.default) {
      // 使用 markRaw 防止组件被转换为响应式对象
      return markRaw(module.default);
    } else {
      console.error(`组件加载失败: ${componentName} - module.default 不存在`);
      return null;
    }
  } catch (error) {
    console.error(`加载组件失败: ${componentName}`, error);
    return null;
  }
};

export default defineComponent({
  name: 'FormProcessTask',
  components: {
    ApprovalRecord,
    CloseOutlined,
    HistoryOutlined,

  },
  data() {
    return {
      globalLoading: false,
      loadingProgress: 0,
      jsonData: {},
      buttonList: {},
      recordData: {},
      newData: {},
      showFlag: false,
      period: "",
      loading: true,
      error: null,
      resultComps: {},
      comp: null,
      procInsId: "",
      processDefinitionId: "",
      actId: "",
      taskId: "",
      componentProps: {
        isDone: 0,
      },
      formData: {
        isDone: 0,
        taskId: '',
        procInsId: '',
        processDefinitionId: '',
        actId: '',
      },
      showRightPanel: true,
      approvalRecords: [],
      updateTrigger: 0,
      flowableData: null,
      worksheetId: ''
    }
  },
  async mounted() {
    try {
      //     this.globalLoading = true;
      // this.loadingProgress = 0;
      // const duration = 3000; // 3秒
      // const start = Date.now();
      //   console.log(duration)
      //   console.log(start)
      // // 启动进度条动画
      // const progressInterval = setInterval(() => {
      //   const elapsed = Date.now() - start;
      //   this.loadingProgress = Math.min(100, Math.round((elapsed / duration) * 100));
      //   if (this.loadingProgress >= 100) {
      //     clearInterval(progressInterval);
      //   }
      // }, 30);

      // 并行加载数据
      await this.initializeData();

      // // 计算已用时间
      // const elapsed = Date.now() - start;
      // if (elapsed < duration) {
      //   // 不足3秒则补足
      //   await new Promise(resolve => setTimeout(resolve, duration - elapsed));
      // }

      // // 关闭loading
      // this.globalLoading = false;
      // this.loadingProgress = 100;
    } catch (error) {
      this.error = `初始化失败: ${error.message}`
      console.error('初始化失败:', error)
      this.globalLoading = false
      this.loading = false
    }
  },
  methods: {
    // 判断是否需要截断（超过3行）
  needsTruncation(text) {
    if (!text) return false;

    // 按换行符分割
    const lines = text.split('\n');

    // 如果有换行符且超过3行
    if (lines.length > 3) {
      return true;
    }

    // 如果没有换行符但字符数超过40个（大概3行的长度）
    if (lines.length === 1 && text.length > 40) {
      return true;
    }

    return false;
  },

  // 格式化评论文本，保持换行
  formatCommentText(text) {
    if (!text) return '';
    return text.replace(/\n/g, '<br>');
  },

  // 切换评论展开/收起状态
  toggleComment(record, index) {
    record.expanded = !record.expanded;
    this.updateTrigger++;
  },

  // 在获取审批记录时初始化展开状态
  async getApprovalRecords() {
    try {
      // ... 原有的获取审批记录逻辑

      // 为每条记录初始化展开状态
      this.approvalRecords = this.approvalRecords.map(record => ({
        ...record,
        expanded: false // 默认收起状态
      }));

    } catch (error) {
      console.error('获取审批记录失败:', error);
    }
  },
    // 加载组件并显示进度
    async loadComponentsWithProgress() {
      this.globalLoading = true
      this.loadingProgress = 0

      // 启动进度条动画
      const progressInterval = setInterval(() => {
        if (this.loadingProgress < 85) {
          this.loadingProgress += 5
        }
      }, 200)

      try {
        // 加载组件
        this.resultComps = await loadComponents()

        // 检查组件是否加载成功
        if (Object.keys(this.resultComps).length === 0) {
          throw new Error('未能加载任何组件')
        }

        // 完成进度条
        this.loadingProgress = 100
        await new Promise(resolve => setTimeout(resolve, 500))
      } catch (error) {
        console.error('加载组件失败:', error)
        this.error = `加载组件失败: ${error.message}`
      } finally {
        clearInterval(progressInterval)
        this.globalLoading = false
      }
    },

    // 初始化数据
    async initializeData() {
      try {
        // 从路由获取参数
        const route = this.$route
        this.processDefinitionId = route.query.processDefinitionId
        this.actId = route.query.actId
        this.taskId = route.query.taskId
        this.procInsId = route.query.procInsId
        this.worksheetId = route.query.worksheetId



        // 获取表单数据
        const res = await RegisteredEquipmentApi.taskFormData({
          processDefinitionId: this.processDefinitionId,
          actId: this.actId
        })

        const [flowableRes, historyRes] = await Promise.all([
          RegisteredEquipmentApi.getFlowAbleMap({ id: this.procInsId }),
          RegisteredEquipmentApi.getCommentHistory({ id: this.procInsId })
        ])

        const newList = JSON.parse(res.data)
        console.log('newList', newList);

        const list = newList.list;
        this.jsonData = list.slice(1);
        console.log('this.jsonData', this.jsonData);
        if (this.jsonData && this.jsonData.length === 0) {
          console.warn('jsonData is empty, no items to process');

        } else {
          if (this.jsonData)
            if (this.jsonData[0].model == "pagename") {
              this.jsonData[0] = {
                pagename: this.jsonData[0].label,
              }
            }
          if (this.jsonData[1].model == "applyTitle") {
            this.jsonData[1] = {
              applyTitleStr: this.jsonData[1].label,
              applyTitle: this.jsonData[1].model,
            }
          }
          console.log('this.jsonData', this.jsonData);
          // 处理jsonData，确保每个item都有list1属性



          this.jsonData.forEach(item => {
            console.log('item333', item);
            if (!item?.list) {
              console.warn('item is undefined or null', item);
            }
            if (item?.list) {
              item.list1 = item.list.flatMap(item => item.columns);
              item.list1 = item.list1.filter(item2 => {
                item2.list[0].span = item2.span
                if (item2.list[0]) {
                  return !(item2.list[0].options && item2.list[0].options.hidden);
                }
              })
              item.list1 = item.list1.flatMap(item => item.list);
            } else {
              console.log('item222', item);
            }
            console.log('item========', item);
            if (item.label == '申请信息' || item.label == '出库信息' || item.label == '调拨信息' || item.label == '退库信息'
              || item.label == '报停信息' || item.label == '自用信息' || item.label == '报废信息' || item.label == '处置信息'
              || item.label == '转让信息' || item.label == '平衡人信息' || item.label == '代管信息'
            ) {
              const remainder = item.list1.length % 3;
              console.log('remainder', remainder);

              if (remainder > 0) {
                console.log(11);

                const addCount = 3 - remainder;
                for (let i = 0; i < addCount; i++) {
                  item.list1.push({
                    help: "",
                    icon: "icon-write",
                    key: "",
                    label: "",
                    model: "",
                    span: 8,
                    options: {
                      addonAfter: "",
                      addonBefore: "",
                      clearable: false,
                      defaultValue: "",
                      disabled: true,
                      hidden: false,
                      maxLength: null,
                      placeholder: "",
                      type: "text",
                      width: "100%",
                    },
                    rules: [],
                    type: "input",
                  }); // 补充空对象，可根据实际需求修改默认值
                }
              }
            }
          })
          this.jsonData = this.jsonData.filter(item => !item.list1 || item.list1.length > 0);
          console.log('this.jsonData===========', this.jsonData);
        }
        // 从后台返回的数据中获取组件名称
        const componentName = newList.list[0].options.defaultValue;
        console.log('componentName3333333:', componentName)
        // 只加载需要的组件
        const componentModule = await loadSingleComponent(componentName);
        console.log(88, componentModule)
        if (!componentModule) {
          throw new Error(`无法加载组件: ${componentName}`);
        }

        // 设置组件
        this.comp = componentModule;

        // 更新要传递给子组件的数据
        this.formData = {
          isDone: 0,
          taskId: this.taskId,
          procInsId: this.procInsId,
          processDefinitionId: this.processDefinitionId,
          actId: this.actId,
          formData: this.jsonData,
          worksheetId: this.worksheetId,
        }


        console.log('流程图数据1:', flowableRes.data)
        console.log('审批记录数据1:', historyRes.data)
        //  // 安全处理流程图数据
        let processedElements = []
        let processedFlows = []

        if (flowableRes.data && flowableRes.data.elements) {
          processedElements = flowableRes.data.elements.length > 2
            ? flowableRes.data.elements.slice(1, -1)
            : [...flowableRes.data.elements]
        }

        if (flowableRes.data && flowableRes.data.flows) {
          processedFlows = flowableRes.data.flows.length > 2
            ? flowableRes.data.flows.slice(1, -1)
            : [...flowableRes.data.flows]
        }

        // // 合并数据 - 确保所有属性都有默认值
        this.flowableData = {
          elements: processedElements || [],
          flows: processedFlows || [],
          comments: (historyRes.data || [])
        }

        // 处理审批记录数据 - 确保数据安全

        // 创建新的记录数组
        var newRecords = []

        for (var i = 0; i < processedElements.length; i++) {
          console.log(11)
          var data = processedElements[i];
          var newRecord = {};
          if (historyRes.data[i]) {
            newRecord.userInitial = historyRes.data[i].assigneeName ? historyRes.data[i].assigneeName.charAt(0) : '?',
              newRecord.userName = historyRes.data[i].assigneeName || '未知用户',
              newRecord.time = historyRes.data[i].time || '',
              newRecord.status = historyRes.data[i].approveStatus || 10055,
              newRecord.statusText = historyRes.data[i].approveStatusStr || '待处理',
              newRecord.comment = historyRes.data[i].comment || '',
              newRecord.assigneeName = historyRes.data[i].name || '',
              newRecord.endTime = historyRes.data[i].endTime || '',
              newRecord.isCheck = true
          } else {
            newRecord = {
              userInitial: data.assigneeName ? data.assigneeName.charAt(0) : '?',
              userName: data.assigneeName || '未知用户',
              time: data.time || '',
              status: 10055,
              statusText: data.approveStatusStr || '待处理',
              comment: data.comment || '',
              assigneeName: data.name || '',
              endTime: data.endTime || '',
              isCheck: false
            }
          }



          newRecords.push(newRecord)

        }
        // const newRecords = flowableRes.data.map(item => ({
        //   userInitial: item.assigneeName ? item.assigneeName.charAt(0) : '?',
        //   userName: item.assigneeName || '未知用户',
        //   time: item.time || '',
        //   status: item.approveStatus || 'waiting',
        //   statusText: item.approveStatusStr || '待处理',
        //   comment: item.comment || '',
        //   assigneeName: item.name || '',
        //   endTime: item.endTime || ''
        // }))

        console.log('处理后的审批记录:', newRecords)

        // 直接更新数组，不使用复杂的异步处理
        this.approvalRecords = newRecords
        this.updateTrigger++

        console.log(this.formData)
        // if(componentName === 'equUploadApproval'){
        //   this.loading =  this.globalLoading1
        //   this.globalLoading = false;
        // }else{
        //   this.loading = false
        //   this.globalLoading = false;
        // }
        this.loading = false
      } catch (err) {
        console.error('初始化数据错误:', err)
        this.error = err.message
        this.loading = false
      }
    },

    // 获取流程树和审批记录
    async getTree(id) {
      if (!id) {
        console.error('获取流程图数据失败: 缺少流程ID')
        return null
      }

      try {
        // 并行发起两个请求
        const [flowableRes, historyRes] = await Promise.all([
          RegisteredEquipmentApi.getFlowAbleMap({ id }),
          RegisteredEquipmentApi.getCommentHistory({ id })
        ])

        console.log('流程图数据:', flowableRes.data)
        console.log('审批记录数据:', historyRes.data)

        // 安全处理流程图数据
        let processedElements = []
        let processedFlows = []

        if (flowableRes.data && flowableRes.data.elements) {
          processedElements = flowableRes.data.elements.length > 2
            ? flowableRes.data.elements.slice(1, -1)
            : [...flowableRes.data.elements]
        }

        if (flowableRes.data && flowableRes.data.flows) {
          processedFlows = flowableRes.data.flows.length > 2
            ? flowableRes.data.flows.slice(1, -1)
            : [...flowableRes.data.flows]
        }

        // 合并数据 - 确保所有属性都有默认值
        this.flowableData = {
          elements: processedElements || [],
          flows: processedFlows || [],
          comments: (historyRes.data || [])
        }

        // 处理审批记录数据 - 确保数据安全
        if (historyRes.data && Array.isArray(historyRes.data)) {
          // 创建新的记录数组
          const newRecords = historyRes.data.map(item => ({
            userInitial: item.assigneeInfo ? item.assigneeInfo.charAt(0) : '?',
            userName: item.assigneeInfo || '未知用户',
            time: item.time || '',
            status: item.status || 'waiting',
            statusText: item.statusText || '待处理',
            comment: item.comment || '',
            assigneeName: item.name || '',
            endTime: item.endTime || ''
          }))

          console.log('处理后的审批记录:', newRecords)

          // 直接更新数组，不使用复杂的异步处理
          this.approvalRecords = newRecords
          this.updateTrigger++
        }

        return this.flowableData
      } catch (error) {
        console.error('获取流程数据失败:', error)
        message.error('获取流程数据失败')
        return null
      }
    },

    // 组件挂载完成的回调
    handleComponentMounted() {
      console.log('动态组件已挂载，props:', {
        componentProps: this.componentProps,
        jsonData: this.jsonData,
        taskId: this.taskId,
        procInsId: this.procInsId,
        processDefinitionId: this.processDefinitionId,
        actId: this.actId
      })

      // 组件挂载后刷新审批记录
      if (this.procInsId && !this.approvalRecords.length) {
        this.getTree(this.procInsId)
      }
    },

    // 手动刷新审批记录
    refreshApprovalRecords() {
      if (this.procInsId) {
        this.getTree(this.procInsId)
      } else {
        message.warning('流程实例ID不存在，无法刷新')
      }
    }
  }
})
</script>

<style lang="less" scoped>
.todo-container {
  display: flex;
  position: relative;
  height: 100%;
  width: 100%;
  min-height: 100vh;
  background-image: url(/src/assets/equipment/back.jpg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.left-component {
  flex: 1;
  overflow: auto;
  padding: 20px 10px 20px 10px;
  // padding-right: 2px;
}

.right-panel {
  width: 300px;
  //background-color: rgba(255, 255, 255, 0.8);
  //background: linear-gradient(180deg, rgba(235, 245, 255, 0.8) 0%, rgba(235, 245, 255, 0.4) 100%);
  background: #F4F9FF !important;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: calc(100vh - 40px);
}

.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  pointer-events: all;
}

.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(255, 255, 255, 0.5);
  /* 半透明白色 */
  z-index: 1;
}

.loading-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  /* 一定要比mask高 */
  width: 400px;
  padding: 40px 0;
  font-size: 18px;

  text-align: center;
}

.loading-state,
.error-state {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.approval-record {
  padding: 15px;
  height: 95%;
  overflow: auto;
}

.timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 60px;
  position: relative;
}

.avatar1 {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #ACB4C9 !important;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
  position: relative;
  flex-shrink: 0;
}

.avatar1.waiting {
  background-color: #ACB4C9;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #1890ff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
  position: relative;
  flex-shrink: 0;
}

.avatar.waiting {
  background-color: #1890ff;
}

.avatar.rejected {
  background-color: #ff4d4f;
}

.avatar-text {
  color: white;
  font-weight: bold;
}

.status-icon {
  position: absolute;
  bottom: -6px;
  right: -5px;
  width: 18px;
  height: 18px;
}

.content {
  flex: 1;
}

.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.title {
  font-weight: bold;
}

.role {
  color: #666;
  margin-left: 5px;
}

.time {
  color: #999;
  font-size: 12px;
}

.status-text {
  color: #1890ff;
  margin-bottom: 5px;
  font-weight: 500;
}

.status-text.waiting {
  color: #1890ff;
}

.status-text.rejected {
  color: #ff4d4f;
}

.comment {
  background-color: rgba(255, 255, 255, 0.6);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  /* 限制显示的行数为 3 行 */
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  /* 允许长单词换行到下一行 */
  word-break: break-all;
  /* 强制换行 */
}

.timeline-line {
  position: absolute;
  left: 20px;
  top: 40px;
  bottom: -60px;
  width: 2px;
  background-color: #1890ff;
}

.timeline-line.dashed {
  background: repeating-linear-gradient(to bottom,
      #1890ff 0px,
      #1890ff 4px,
      transparent 4px,
      transparent 8px);
}

.expand-button-container {
  position: fixed;
  right: 20px;
  bottom: 20px;
}

.expand-button {
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.expand-button span {
  margin-left: 5px;
}

.right-panel-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
}

.panel-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;

  .icon {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    color: #1890ff;
  }
}

.comment {
  margin-top: 8px;
}

.comment-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.comment-text {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  word-break: break-word;
  white-space: pre-wrap; /* 保持换行和空格 */
  max-width: 100%;
}

/* 收起状态：最多显示3行 */
.comment-text.collapsed {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 展开状态：显示全部内容 */
.comment-text.expanded {
  display: block;
  overflow: visible;
}

.expand-btn {
  color: #1890ff !important;
  padding: 0 !important;
  height: auto !important;
  font-size: 12px;
  margin-top: 4px;

  &:hover {
    color: #40a9ff !important;
  }
}

.expand-btn:focus {
  color: #1890ff !important;
}
</style>
