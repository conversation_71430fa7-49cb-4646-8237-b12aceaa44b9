import Request from '@/utils/request-util';

/**
 * 需求计划API
 *
 * <AUTHOR>
 * @date 2024/01/10
 */
export class DemandPlanApi {
  /**
   * 获取新建数据
   *
   * <AUTHOR>
   * @date 2024/01/10
   */
  static getNewBuiltJDC() {
    return Request.get('/apiBus/demandPlanTransferFormEquipment/newBuilt');
  }

  static getNewBuiltJDK() {
    return Request.get('/apiBus/demandPlanTransferFormEquipment/jdk/newBuilt');
  }

  static getNewBuiltXMB() {
    return Request.get('/apiBus/demandPlanTransferFormEquipment/xmb/newBuilt');
  }

  static saveJDC(params) {
    return Request.post('/apiBus/demandPlanTransferFormEquipment/saveDraft', params);
  }

  static saveJDK(params) {
    return Request.post('/apiBus/demandPlanTransferFormEquipment/jdk/saveDraft', params);
  }

  static saveXMB(params) {
    return Request.post('/apiBus/demandPlanTransferFormEquipment/xmb/saveDraft', params);
  }

  // static submit(params) {
  //   return Request.post('/flowableHandleTask/start', params);
  // }

  static submit(params) {
    return Request.post('/flowableHandleTask/submit', params);
  }

  // static end(params) {
  //           return Request.post('/flowableInstance/end', params);
  //         }

  static approval(params) {
    return Request.post('/flowableHandleTask/submit', params);
  }

  static end(params) {
    return Request.post('/flowableInstance/end', params);
  }

  static getData(params) {
    return Request.getAndLoadData('/apiBus/demandPlanTransferFormEquipment/jdc/page', params);
  }

  static getDataJDK(params) {
    return Request.getAndLoadData('/apiBus/demandPlanTransferFormEquipment/jdk/page', params);
  }

  static getDataXMB(params) {
    return Request.getAndLoadData('/apiBus/demandPlanTransferFormEquipment/xmb/page', params);
  }

  static exportData(params) {
    return Request.downLoad('/api/apiBus/demandPlanTransferFormEquipment/jdc/export', params);
  }

  static uploadExcel(params) {
    return Request.post('/apiBus/demandPlanTransferFormEquipment/jdc/importExcel', params);
  }


  static getEquipmentByCode(params) {
    return Request.get('/apiBus/equipmentClassificationCode/detail', params);
  }

  static getFlowAbleMap(params) {
    return Request.get('/flowableInstance/trace', params);
  }


  static getToDo(params) {
    return Request.get('/apiBus/demandPlanTransferFormEquipment/getWorksheetInfo', params);
  }

  static taskFormData(params) {
    return Request.get('/flowableForm/taskFormData', params);
  }

  static globalFormData(params) {
    return Request.get('/flowableForm/globalFormData', params);
  }

  static getEditToDo(params) {
    return Request.get('/apiBus/demandPlanTransferFormEquipment/getWorksheetInfo', params);
  }


  static start(params) {
    return Request.post('/flowableHandleTask/start', params);
  }


}

