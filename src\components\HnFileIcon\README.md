# 文件图标展示组件 
> 文件图标展示组件  王文胜 20250709  
文件全名称默认超过20个字符（含后缀名）时，会自动将超过的字符截掉，并显示省略号(代码实现)。若宽度不足，也自动显示省略号(css原生实现)。


## 使用方法
<hn-file-icon :file-info="file" :file-info-reader="{fileOriginName:'fileName'}" :show-del="true" class="file-item" @handle-remove="handleRemove" />

## 参数说明：
属性:
- fileInfo: 文件信息对象 {}
- fileInfoReader: 文件信息对象字段映射关系，默认为{fileOriginName:'fileName',fileName: 'fileName', fileUrl: 'fileUrl',fileStatus:'status'} 可以覆盖全部或某些字段。<a style="color:red"> **fileOriginName** 为文件原始名称，会根据此名称进行文件类型识别。</a>
- iconKv: 文件图标映射关系,默认为 {"xls":"wrapper","xlsx":"wrapper","doc":'word',docx:'word',pdf:'pdf',mov:'mp4',mp4:'mp4',ppt:'ppt',pptx:'ppt',png:'jpg',jpg:'jpg',jpeg:'jpg',heif:'jpg'}可以覆盖全部或某些字段。
- showDel: 是否显示删除按钮

事件:
- handleRemove: 删除文件事件

## 使用示例:

approval/proxiesApprovalC.vue 大约 140 行
``` html
<!-- filelist 文件列表 -->
<div class="file-list">
<template v-for="(file, index) in fileViewList" :key="file.uid">
    <hn-file-icon :file-info="file" :file-info-reader="{fileOriginName:'fileName'}" :show-del="true" class="file-item" @handle-remove="handleRemove" />
</template>
</div><!-- /filelist 文件列表 -->
```
